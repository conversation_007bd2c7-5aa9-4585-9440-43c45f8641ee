package com.ruoyi.weimo.config;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.ruoyi.weimo.domain.dto.MsgBodyOrder;
import com.ruoyi.weimo.domain.dto.WmMessage;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

@Slf4j
class WeiMoMessageListenTest {

    String str = "";

    @Test
    void test() {

        MsgBodyOrder msgBodyOrder = new MsgBodyOrder();
        msgBodyOrder.setOrderType(1);
        msgBodyOrder.setOrderStatus(7);

        WmMessage<MsgBodyOrder> wmMessage = new WmMessage<>();
        wmMessage.setTopic("weimob_shop.order");
        wmMessage.setVersion(1);
        wmMessage.setEvent("create");
        wmMessage.setMsgBody(msgBodyOrder);

        String jsonString = JSON.toJSONString(wmMessage);
        log.info("jsonString: {}", jsonString);

//        WmMessage<MsgBodyOrder> parse = JSONObject.parseObject(jsonString, new TypeReference<WmMessage<MsgBodyOrder>>() {
        WmMessage parse = JSONObject.parseObject(jsonString, WmMessage.class);
        JSONObject msgBody = (JSONObject)parse.getMsgBody();

        MsgBodyOrder msgBodyOrderParse = msgBody.to(MsgBodyOrder.class);
        log.info("parse: {}", parse);


    }

}