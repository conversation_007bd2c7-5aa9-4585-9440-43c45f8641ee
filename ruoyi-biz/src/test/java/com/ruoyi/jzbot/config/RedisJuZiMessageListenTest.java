package com.ruoyi.jzbot.config;


import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.jzbot.domain.JzChatMessage;
import org.junit.jupiter.api.Test;

import java.util.HashMap;

class RedisJuZiMessageListenTest {


    @Test
    void test() {
        String messageBody = "{\"data\":{\"messageId\":\"655da885d900289c251412604e1088b8\",\"chatId\":\"664c65debd25acec2f254c71\",\"avatar\":\"https://wx.qlogo.cn/mmhead/Iic9WLWEQMg1R1ZM9gvk7LTU9uDibjKeB3HHwlUByNPDBK9J57IQgZloNc0AKG3xEYpEqhKicVNQdE/0\",\"roomTopic\":\"（营业中…）太阳神AI智创惠购商城\",\"roomId\":\"R:10876763858212992\",\"contactName\":\"田薇靖\",\"contactId\":\"7881301561995233\",\"payload\":{\"text\":\"@一生萍安 美女学会了啊[强][强]这里购物放心吧[玫瑰][玫瑰]\",\"mention\":[\"7881301798072624\"]},\"type\":7,\"timestamp\":1720149024606,\"token\":\"6561e8e3a021642303600561\",\"contactType\":1,\"coworker\":false,\"botId\":\"66305f3b878918be09bdc2a4\",\"botWxid\":\"1688858281557957\",\"botWeixin\":\"wxid_hscjx04395fa22\",\"isSelf\":false,\"externalUserId\":\"\",\"roomWecomChatId\":\"wrIasIBwAA_x1B-5DtkRkmE59Rm-sMYQ\",\"mentionSelf\":false}}";

        JSONObject params = JSONObject.parseObject(messageBody);

        JzChatMessage jzChatMessage = params.getJSONObject("data").toJavaObject(JzChatMessage.class);

        System.out.println(jzChatMessage);
    }

    @Test
    void testGetWid() {
        String url = "https://dopen.weimob.com/apigw/bos/v2.0/user/superwid/get?accesstoken=439ac7c6-a8b1-4d8c-adae-fb0a7d65037d";
        HashMap<String, Object> param = new HashMap<>();
        param.put("source", "2");
        param.put("originalId", "oAeM56vH4JmmpJTTL0G4tTumnq50");
        JSONObject jsonObject =  HttpUtils.postToJsonObject(url, param);
        System.out.println(jsonObject.getJSONObject("data").getString("superWid"));
    }

}