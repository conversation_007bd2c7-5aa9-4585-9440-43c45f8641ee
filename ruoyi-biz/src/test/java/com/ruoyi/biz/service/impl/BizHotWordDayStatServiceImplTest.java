package com.ruoyi.biz.service.impl;

import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

class BizHotWordDayStatServiceImplTest {

    @Test
    void dailyHotWordStatistics() {

        // 初始 map 数据
        Map<String, Integer> map1 = new HashMap<>();
        map1.put("hot1", 2);
        map1.put("hot2", 3);
        map1.put("hot3", 1);
        map1.put("hot4", 10);

        Map<String, Integer> map2 = new HashMap<>();
        map2.put("hot1", 5);
        map2.put("hot2", 8);
        map2.put("hot3", 3);
        map2.put("hot4", 1);

        // 获取 map1 排序后的排名
        Map<String, Integer> rankMap1 = getRankMap(map1);
        // 获取 map2 排序后的排名
        Map<String, Integer> rankMap2 = getRankMap(map2);

        // 找出两个 map 中排名差值超过 5 的条目
        List<String> result = new ArrayList<>();
        for (String key : map1.keySet()) {
            if (rankMap2.containsKey(key)) {
                // 昨日排名比前日上升为数超过 5, 创建待办提醒
                if (rankMap1.get(key) - rankMap2.get(key) > 5) {


                }
            } else {
                // 前天未生成线索, 昨天生成了线索, 创建待办提醒
            }
        }

        // 打印结果
        result.forEach(System.out::println);

    }

    private Map<String, Integer> getRankMap(Map<String, Integer> map) {
        List<Map.Entry<String, Integer>> sortedEntries = map.entrySet()
                .stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .collect(Collectors.toList());

        Map<String, Integer> rankMap = new HashMap<>();
        for (int i = 0; i < sortedEntries.size(); i++) {
            rankMap.put(sortedEntries.get(i).getKey(), i + 1); // 排名从1开始
        }

        return rankMap;
    }

}