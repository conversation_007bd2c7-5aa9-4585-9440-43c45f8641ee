package com.ruoyi.framework.config;
import java.nio.charset.Charset;
import java.time.LocalDate;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;
import com.google.common.collect.Maps;

import com.ruoyi.biz.domain.dto.ChatStatisticsCache;
import com.ruoyi.common.constant.Constants;
import org.junit.jupiter.api.Test;

class FastJson2JsonRedisSerializerTest {

    @Test
    void serialize() {
        ChatStatisticsCache chatStatisticsCache = new ChatStatisticsCache();
        chatStatisticsCache.setChatId("");
        chatStatisticsCache.setMessageSource("");
        chatStatisticsCache.setStartDate(LocalDate.now());
        chatStatisticsCache.setAlertedInfo(Maps.newHashMap());
        chatStatisticsCache.setChatCount(0);
        chatStatisticsCache.setExitNum(0);
        chatStatisticsCache.setEnterNum(0);
        chatStatisticsCache.setRoomTotal(0);
        chatStatisticsCache.setBannedWordFrequency(0);
        chatStatisticsCache.setBannedWordInfo(Maps.newHashMap());

        String jsonString = JSON.toJSONString(chatStatisticsCache, JSONWriter.Feature.WriteClassName);
        System.out.println(jsonString);


        final String[] JSON_WHITELIST_STR = {"org.springframework", "com.ruoyi", "java.util", "java.util.HashMap", "java.util.Map"};
        ChatStatisticsCache obj = JSON.parseObject(jsonString, ChatStatisticsCache.class, JSONReader.autoTypeFilter(JSON_WHITELIST_STR));
        System.out.println(obj);
    }

    @Test
    void deserialize() {

    }
}