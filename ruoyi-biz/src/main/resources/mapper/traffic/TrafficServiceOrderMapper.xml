<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.traffic.mapper.TrafficServiceOrderMapper">

    <resultMap type="com.ruoyi.traffic.domain.TrafficServiceOrder" id="TrafficServiceOrderResult">
        <result property="id"    column="id"    jdbcType="VARCHAR" />
        <result property="orderNumber"    column="order_number"    />
        <result property="orderType"    column="order_type"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="finalAmount"    column="final_amount"    />
        <result property="totalPackage"    column="total_package"    />
        <result property="availablePackage"    column="available_package"    />
        <result property="usedPackage"    column="used_package"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.order_number, t.order_type, t.order_status, t.total_amount, t.final_amount, t.total_package, t.available_package, t.used_package, t.create_by, t.create_time, t.update_by, t.update_time, t.remark    </sql>

    <sql id="selectTrafficServiceOrderVo">
        select <include refid="Base_Column_List"/>  from traffic_service_order t
    </sql>

    <select id="selectTrafficServiceOrderList" parameterType="com.ruoyi.traffic.domain.TrafficServiceOrder" resultMap="TrafficServiceOrderResult">
        <include refid="selectTrafficServiceOrderVo"/>
        <where>
            <if test="orderNumber != null  and orderNumber != ''"> and t.order_number = #{orderNumber}</if>
            <if test="orderType != null  and orderType != ''"> and t.order_type = #{orderType}</if>
            <if test="orderStatus != null "> and t.order_status = #{orderStatus}</if>
            <if test="totalAmount != null "> and t.total_amount = #{totalAmount}</if>
            <if test="finalAmount != null "> and t.final_amount = #{finalAmount}</if>
            <if test="totalPackage != null "> and t.total_package = #{totalPackage}</if>
            <if test="availablePackage != null "> and t.available_package = #{availablePackage}</if>
            <if test="usedPackage != null "> and t.used_package = #{usedPackage}</if>
        </where>
    </select>


    <select id="selectTrafficServiceOrderByIdForUpdate" parameterType="Long" resultMap="TrafficServiceOrderResult">
        <include refid="selectTrafficServiceOrderVo"/>
        where t.id = #{id}
        FOR UPDATE
    </select>

    <select id="selectTrafficServiceOrderById" parameterType="Long" resultMap="TrafficServiceOrderResult">
        <include refid="selectTrafficServiceOrderVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertTrafficServiceOrder" parameterType="com.ruoyi.traffic.domain.TrafficServiceOrder" useGeneratedKeys="true" keyProperty="id">
        insert into traffic_service_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNumber != null and orderNumber != ''">order_number,</if>
            <if test="orderType != null and orderType != ''">order_type,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="finalAmount != null">final_amount,</if>
            <if test="totalPackage != null">total_package,</if>
            <if test="availablePackage != null">available_package,</if>
            <if test="usedPackage != null">used_package,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNumber != null and orderNumber != ''">#{orderNumber},</if>
            <if test="orderType != null and orderType != ''">#{orderType},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="finalAmount != null">#{finalAmount},</if>
            <if test="totalPackage != null">#{totalPackage},</if>
            <if test="availablePackage != null">#{availablePackage},</if>
            <if test="usedPackage != null">#{usedPackage},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTrafficServiceOrder" parameterType="com.ruoyi.traffic.domain.TrafficServiceOrder">
        update traffic_service_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNumber != null and orderNumber != ''">order_number = #{orderNumber},</if>
            <if test="orderType != null and orderType != ''">order_type = #{orderType},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="finalAmount != null">final_amount = #{finalAmount},</if>
            <if test="totalPackage != null">total_package = #{totalPackage},</if>
            <if test="availablePackage != null">available_package = #{availablePackage},</if>
            <if test="usedPackage != null">used_package = #{usedPackage},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTrafficServiceOrderById" parameterType="Long">
        delete from traffic_service_order where id = #{id}
    </delete>

    <delete id="deleteTrafficServiceOrderByIds" parameterType="String">
        delete from traffic_service_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectAvailableOrders" parameterType="com.ruoyi.traffic.domain.TrafficServiceOrder" resultMap="TrafficServiceOrderResult">
        <include refid="selectTrafficServiceOrderVo"/>
        <where>
            <if test="orderType != null and orderType != ''">
                AND t.order_type = #{orderType}
            </if>
            AND t.order_status != #{orderStatus}
            AND t.available_package > #{availablePackage}
        </where>
        ORDER BY t.create_time ASC
    </select>

    <select id="selectLatestCompletedOrder" resultMap="TrafficServiceOrderResult">
        select
            <include refid="selectTrafficServiceOrderVo"/>
        from traffic_service_order t
        where t.order_type = #{orderType}
        and t.order_status = 2
        order by t.create_time desc
        limit 1
    </select>
</mapper>
