<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.traffic.mapper.TrafficServiceCreditMapper">

    <resultMap type="com.ruoyi.traffic.domain.TrafficServiceCredit" id="TrafficServiceCreditResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="refId"    column="ref_id"    />
        <result property="creditType"    column="credit_type"    />
        <result property="overdraftStatus"    column="overdraft_status"    />
        <result property="availableCredit"    column="available_credit"    />
        <result property="usedCredit"    column="used_credit"    />
        <result property="remainingCredits"    column="remaining_credits"    />
        <result property="status"    column="status"    />
        <result property="expiryDate"    column="expiry_date"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.order_id, t.ref_id, t.credit_type, t.overdraft_status, t.available_credit, t.used_credit, t.remaining_credits, t.status, t.expiry_date, t.create_time, t.update_time, t.remark    </sql>

    <sql id="selectTrafficServiceCreditVo">
        select <include refid="Base_Column_List"/>  from traffic_service_credit t
    </sql>

    <select id="selectTrafficServiceCreditList" parameterType="com.ruoyi.traffic.domain.TrafficServiceCredit" resultMap="TrafficServiceCreditResult">
        <include refid="selectTrafficServiceCreditVo"/>
        <choose>
            <when test="creditType == 1">
                left join jz_contact jc on t.ref_id = jc.chat_id
            </when>
            <when test="creditType == 2">
                left join jz_room jr on t.ref_id = jr.chat_id
            </when>
            <otherwise>
                left join jz_contact jc on t.ref_id = jc.chat_id
                left join jz_room jr on t.ref_id = jr.chat_id
            </otherwise>
        </choose>
        <where>
            <if test="name != null and name != ''">
                <choose>
                    <when test="creditType == 1">
                        and jc.nickname like concat('%', #{name}, '%')
                    </when>
                    <when test="creditType == 2">
                        and jr.topic like concat('%', #{name}, '%')
                    </when>
                    <otherwise>
                        and (jc.nickname like concat('%', #{name}, '%') or jr.topic like concat('%', #{name}, '%'))
                    </otherwise>
                </choose>
            </if>
            <if test="orderId != null "> and t.order_id = #{orderId}</if>
            <if test="refId != null "> and t.ref_id = #{refId}</if>
            <if test="creditType != null  and creditType != ''"> and t.credit_type = #{creditType}</if>
            <if test="overdraftStatus != null "> and t.overdraft_status = #{overdraftStatus}</if>
            <if test="availableCredit != null "> and t.available_credit = #{availableCredit}</if>
            <if test="usedCredit != null "> and t.used_credit = #{usedCredit}</if>
            <if test="remainingCredits != null "> and t.remaining_credits = #{remainingCredits}</if>
            <if test="status != null "> and t.status = #{status}</if>
            <if test="expiryDate != null "> and t.expiry_date = #{expiryDate}</if>
        </where>
        order by t.id desc
    </select>

    <select id="selectTrafficServiceCreditById" parameterType="Long" resultMap="TrafficServiceCreditResult">
        <include refid="selectTrafficServiceCreditVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertTrafficServiceCredit" parameterType="com.ruoyi.traffic.domain.TrafficServiceCredit" useGeneratedKeys="true" keyProperty="id">
        insert into traffic_service_credit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="refId != null">ref_id,</if>
            <if test="creditType != null and creditType != ''">credit_type,</if>
            <if test="overdraftStatus != null">overdraft_status,</if>
            <if test="availableCredit != null">available_credit,</if>
            <if test="usedCredit != null">used_credit,</if>
            <if test="remainingCredits != null">remaining_credits,</if>
            <if test="status != null">status,</if>
            <if test="expiryDate != null">expiry_date,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="refId != null">#{refId},</if>
            <if test="creditType != null and creditType != ''">#{creditType},</if>
            <if test="overdraftStatus != null">#{overdraftStatus},</if>
            <if test="availableCredit != null">#{availableCredit},</if>
            <if test="usedCredit != null">#{usedCredit},</if>
            <if test="remainingCredits != null">#{remainingCredits},</if>
            <if test="status != null">#{status},</if>
            <if test="expiryDate != null">#{expiryDate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTrafficServiceCredit" parameterType="com.ruoyi.traffic.domain.TrafficServiceCredit">
        update traffic_service_credit
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="refId != null">ref_id = #{refId},</if>
            <if test="creditType != null and creditType != ''">credit_type = #{creditType},</if>
            <if test="overdraftStatus != null">overdraft_status = #{overdraftStatus},</if>
            <if test="availableCredit != null">available_credit = #{availableCredit},</if>
            <if test="usedCredit != null">used_credit = #{usedCredit},</if>
            <if test="remainingCredits != null">remaining_credits = #{remainingCredits},</if>
            <if test="status != null">status = #{status},</if>
            <if test="expiryDate != null">expiry_date = #{expiryDate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTrafficServiceCreditById" parameterType="Long">
        delete from traffic_service_credit where id = #{id}
    </delete>

    <delete id="deleteTrafficServiceCreditByIds" parameterType="String">
        delete from traffic_service_credit where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="findOverdraftCredits" resultMap="TrafficServiceCreditResult">
        <include refid="selectTrafficServiceCreditVo"/>
        where t.credit_type = #{creditType}
        and t.overdraft_status = 1
    </select>

    <update id="batchUpdateTrafficServiceCredit" parameterType="java.util.List">
        update traffic_service_credit
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="order_id = case" suffix="end,">
                <foreach collection="list" item="item">
                    <if test="item.orderId != null">
                        when id = #{item.id} then #{item.orderId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="status = case" suffix="end,">
                <foreach collection="list" item="item">
                    <if test="item.status != null">
                        when id = #{item.id} then #{item.status}
                    </if>
                </foreach>
            </trim>
            <trim prefix="overdraft_status = case" suffix="end,">
                <foreach collection="list" item="item">
                    <if test="item.overdraftStatus != null">
                        when id = #{item.id} then #{item.overdraftStatus}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id} then #{item.updateTime}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>
</mapper>
