<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.traffic.mapper.CreditDetailsMapper">

    <resultMap type="com.ruoyi.traffic.domain.CreditDetails" id="CreditDetailsResult">
        <result property="id"    column="id"    />
        <result property="creditId"    column="credit_id"    />
        <result property="messageId"    column="message_id"    />
        <result property="usageType"    column="usage_type"    />
        <result property="usageAmount"    column="usage_amount"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.credit_id, t.message_id, t.usage_type, t.usage_amount, t.create_time, t.update_time, t.remark    </sql>

    <sql id="selectCreditDetailsVo">
        select <include refid="Base_Column_List"/>  from credit_details t
    </sql>

    <select id="selectCreditDetailsList" parameterType="com.ruoyi.traffic.domain.CreditDetails" resultMap="CreditDetailsResult">
        <include refid="selectCreditDetailsVo"/>
        <where>
            <if test="creditId != null "> and t.credit_id = #{creditId}</if>
            <if test="messageId != null "> and t.message_id = #{messageId}</if>
            <if test="usageType != null  and usageType != ''"> and t.usage_type = #{usageType}</if>
            <if test="usageAmount != null "> and t.usage_amount = #{usageAmount}</if>
            <if test="params.beginTime != null and params.beginTime != ''"> and t.create_time &gt;= #{params.beginTime}</if>
            <if test="params.endTime != null and params.endTime != ''"> and t.create_time &lt;= #{params.endTime}</if>
        </where>
        order by t.create_time desc
    </select>

    <select id="selectCreditDetailsById" parameterType="Long" resultMap="CreditDetailsResult">
        <include refid="selectCreditDetailsVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertCreditDetails" parameterType="com.ruoyi.traffic.domain.CreditDetails" useGeneratedKeys="true" keyProperty="id">
        insert into credit_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="creditId != null">credit_id,</if>
            <if test="messageId != null">message_id,</if>
            <if test="usageType != null and usageType != ''">usage_type,</if>
            <if test="usageAmount != null">usage_amount,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="creditId != null">#{creditId},</if>
            <if test="messageId != null">#{messageId},</if>
            <if test="usageType != null and usageType != ''">#{usageType},</if>
            <if test="usageAmount != null">#{usageAmount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCreditDetails" parameterType="com.ruoyi.traffic.domain.CreditDetails">
        update credit_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="creditId != null">credit_id = #{creditId},</if>
            <if test="messageId != null">message_id = #{messageId},</if>
            <if test="usageType != null and usageType != ''">usage_type = #{usageType},</if>
            <if test="usageAmount != null">usage_amount = #{usageAmount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCreditDetailsById" parameterType="Long">
        delete from credit_details where id = #{id}
    </delete>

    <delete id="deleteCreditDetailsByIds" parameterType="String">
        delete from credit_details where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
