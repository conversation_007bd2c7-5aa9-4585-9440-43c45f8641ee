<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.traffic.mapper.MultiplierConfigMapper">

    <resultMap type="com.ruoyi.traffic.domain.MultiplierConfig" id="MultiplierConfigResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="operationType"    column="operation_type"    />
        <result property="multiplier"    column="multiplier"    />
        <result property="baseUnit"    column="base_unit"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.name, t.operation_type, t.multiplier, t.base_unit, t.status, t.create_time, t.update_time, t.remark    </sql>

    <sql id="selectMultiplierConfigVo">
        select <include refid="Base_Column_List"/>  from multiplier_config t
    </sql>

    <select id="selectMultiplierConfigList" parameterType="com.ruoyi.traffic.domain.MultiplierConfig" resultMap="MultiplierConfigResult">
        <include refid="selectMultiplierConfigVo"/>
        <where>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="operationType != null  and operationType != ''"> and t.operation_type = #{operationType}</if>
            <if test="multiplier != null "> and t.multiplier = #{multiplier}</if>
            <if test="baseUnit != null "> and t.base_unit = #{baseUnit}</if>
            <if test="status != null "> and t.status = #{status}</if>
        </where>
    </select>

    <select id="selectMultiplierConfigById" parameterType="Long" resultMap="MultiplierConfigResult">
        <include refid="selectMultiplierConfigVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertMultiplierConfig" parameterType="com.ruoyi.traffic.domain.MultiplierConfig" useGeneratedKeys="true" keyProperty="id">
        insert into multiplier_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="operationType != null and operationType != ''">operation_type,</if>
            <if test="multiplier != null">multiplier,</if>
            <if test="baseUnit != null">base_unit,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="operationType != null and operationType != ''">#{operationType},</if>
            <if test="multiplier != null">#{multiplier},</if>
            <if test="baseUnit != null">#{baseUnit},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMultiplierConfig" parameterType="com.ruoyi.traffic.domain.MultiplierConfig">
        update multiplier_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="operationType != null and operationType != ''">operation_type = #{operationType},</if>
            <if test="multiplier != null">multiplier = #{multiplier},</if>
            <if test="baseUnit != null">base_unit = #{baseUnit},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMultiplierConfigById" parameterType="Long">
        delete from multiplier_config where id = #{id}
    </delete>

    <delete id="deleteMultiplierConfigByIds" parameterType="String">
        delete from multiplier_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectActiveConfig" resultType="MultiplierConfig" resultMap="MultiplierConfigResult">
        <include refid="selectMultiplierConfigVo"/>
        where operation_type = #{operationType}
          and status = 1
        limit 1
    </select>

</mapper>
