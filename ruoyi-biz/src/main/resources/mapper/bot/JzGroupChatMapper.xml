<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jzbot.mapper.JzGroupChatMapper">

    <resultMap type="com.ruoyi.jzbot.domain.JzGroupChat" id="JzGroupChatResult">
        <result property="id" column="id"/>
        <result property="topic" column="topic"/>
        <result property="avatarUrl" column="avatar_url"/>
        <result property="ownerId" column="owner_id"/>
        <result property="memberCount" column="member_count"/>
        <result property="deleted" column="deleted"/>
        <result property="wecomChatId" column="wecom_chat_id"/>
        <result property="inviteQrUrl" column="invite_qr_url"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.topic, t.avatar_url, t.owner_id, t.member_count, t.deleted, t.wecom_chat_id, t.invite_qr_url, t.create_time  </sql>

    <sql id="selectJzGroupChatVo">
        select
        <include refid="Base_Column_List"/>
        from jz_group_chat t
    </sql>

    <select id="selectJzGroupChatList" parameterType="com.ruoyi.jzbot.domain.JzGroupChat" resultMap="JzGroupChatResult">
        <include refid="selectJzGroupChatVo"/>
        <if test="tagSearchIdList != null  and tagSearchIdList.size() > 0  and tagSearchType != null ">
            left join `biz_taggable` tag ON t.`id` = tag.`ref_id` AND tag.`ref_type` ='room'
        </if>
        <where>
            <if test="topic != null  and topic != ''">and t.topic like concat('%', #{topic},'%')</if>
            <if test="avatarUrl != null  and avatarUrl != ''">and t.avatar_url = #{avatarUrl}</if>
            <if test="ownerId != null  and ownerId != ''">and t.owner_id = #{ownerId}</if>
            <if test="memberCount != null ">and t.member_count = #{memberCount}</if>
            <if test="deleted != null  and deleted != ''">and t.deleted = #{deleted}</if>
            <if test="wecomChatId != null  and wecomChatId != ''">and t.wecom_chat_id = #{wecomChatId}</if>
            <if test="tagSearchIdList != null  and tagSearchIdList.size() > 0  and tagSearchType != null ">
                and tag.tag_id in
                <foreach collection="tagSearchIdList" open="(" close=")" separator="," item="tagId">
                    #{tagId}
                </foreach>
            </if>
            <if test="wecomChatIdList != null  and wecomChatIdList.size() > 0">
                and t.wecom_chat_id in
                <foreach collection="wecomChatIdList" open="(" close=")" separator="," item="wecomChatId">
                    #{wecomChatId}
                </foreach>
            </if>
        </where>
        <if test="tagSearchIdList != null  and tagSearchIdList.size() > 0  and tagSearchType != null ">
            <if test="tagSearchType == '0'.toString()">
                GROUP BY t.`id` HAVING COUNT(1) > 0
            </if>
            <if test="tagSearchType == '1'.toString()">
                GROUP BY t.`id` HAVING COUNT(1) = ${tagSearchIdList.size()}
            </if>
        </if>
        order by t.create_time desc
    </select>

    <select id="selectJzGroupChatById" parameterType="String" resultMap="JzGroupChatResult">
        <include refid="selectJzGroupChatVo"/>
        where t.id = #{id}
    </select>
    <select id="selectJzGroupChatListByIds" resultMap="JzGroupChatResult">
        <include refid="selectJzGroupChatVo"/>
        where t.id in
        <foreach item="id" collection="roomIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by t.create_time desc
    </select>

    <insert id="insertJzGroupChat" parameterType="com.ruoyi.jzbot.domain.JzGroupChat">
        insert into jz_group_chat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="topic != null and topic != ''">topic,</if>
            <if test="avatarUrl != null and avatarUrl != ''">avatar_url,</if>
            <if test="ownerId != null and ownerId != ''">owner_id,</if>
            <if test="memberCount != null">member_count,</if>
            <if test="deleted != null and deleted != ''">deleted,</if>
            <if test="wecomChatId != null and wecomChatId != ''">wecom_chat_id,</if>
            <if test="inviteQrUrl != null and inviteQrUrl != ''">invite_qr_url,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="topic != null and topic != ''">#{topic},</if>
            <if test="avatarUrl != null and avatarUrl != ''">#{avatarUrl},</if>
            <if test="ownerId != null and ownerId != ''">#{ownerId},</if>
            <if test="memberCount != null">#{memberCount},</if>
            <if test="deleted != null and deleted != ''">#{deleted},</if>
            <if test="wecomChatId != null and wecomChatId != ''">#{wecomChatId},</if>
            <if test="inviteQrUrl != null and inviteQrUrl != ''">#{inviteQrUrl},</if>
        </trim>
    </insert>

    <insert id="createOrUpdateGroupChat">
        <foreach collection="list" item="group" separator=";">
            insert into jz_group_chat
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="group.id != null">id,</if>
                <if test="group.topic != null and group.topic != ''">topic,</if>
                <if test="group.avatarUrl != null and group.avatarUrl != ''">avatar_url,</if>
                <if test="group.ownerId != null and group.ownerId != ''">owner_id,</if>
                <if test="group.memberCount != null">member_count,</if>
                <if test="group.wecomChatId != null and group.wecomChatId != ''">wecom_chat_id,</if>
                <if test="group.inviteQrUrl != null and group.inviteQrUrl != ''">invite_qr_url,</if>
                <if test="group.createTime != null">create_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="group.id != null">#{group.id},</if>
                <if test="group.topic != null and group.topic != ''">#{group.topic},</if>
                <if test="group.avatarUrl != null and group.avatarUrl != ''">#{group.avatarUrl},</if>
                <if test="group.ownerId != null and group.ownerId != ''">#{group.ownerId},</if>
                <if test="group.memberCount != null">#{group.memberCount},</if>
                <if test="group.wecomChatId != null and group.wecomChatId != ''">#{group.wecomChatId},</if>
                <if test="group.inviteQrUrl != null and group.inviteQrUrl != ''">#{group.invite_qr_url},</if>
                <if test="group.createTime != null">#{group.createTime},</if>
            </trim>
            on duplicate key update
            `topic`= VALUES(topic),
            `avatar_url`= VALUES(avatar_url),
            `member_count`= VALUES(member_count)
        </foreach>

    </insert>

    <update id="updateJzGroupChat" parameterType="com.ruoyi.jzbot.domain.JzGroupChat">
        update jz_group_chat
        <trim prefix="SET" suffixOverrides=",">
            <if test="topic != null and topic != ''">topic = #{topic},</if>
            <if test="avatarUrl != null and avatarUrl != ''">avatar_url = #{avatarUrl},</if>
            <if test="ownerId != null and ownerId != ''">owner_id = #{ownerId},</if>
            <if test="memberCount != null">member_count = #{memberCount},</if>
            <if test="deleted != null and deleted != ''">deleted = #{deleted},</if>
            <if test="wecomChatId != null and wecomChatId != ''">wecom_chat_id = #{wecomChatId},</if>
            <if test="inviteQrUrl != null and inviteQrUrl != ''">invite_qr_url = #{inviteQrUrl},</if>
        </trim>
        where id = #{id}
    </update>


    <update id="checkDeleted">
        UPDATE `jz_group_chat`
        SET deleted = CASE
        WHEN id IN
            <foreach collection="list" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            THEN '0'
            ELSE '1'
            END
    </update>

    <delete id="deleteJzGroupChatById" parameterType="String">
        delete
        from jz_group_chat
        where id = #{id}
    </delete>

    <delete id="deleteJzGroupChatByIds" parameterType="String">
        delete from jz_group_chat where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectGroupChatListByPartner" parameterType="com.ruoyi.jzbot.domain.JzGroupChat"
            resultMap="JzGroupChatResult">
        select
        <include refid="Base_Column_List"/>
        from biz_partner bp
        join biz_partner_group bpg on bp.id = bpg.partner_id
        join jz_group_chat t on bpg.room_id = t.id
        <where>
            <if test="wid != null  and wid != ''">and bp.wid = #{wid}</if>
            and t.deleted = '0'
        </where>
    </select>
    <select id="selectJzGroupChatListByWecomChatIds" resultMap="JzGroupChatResult">
        <include refid="selectJzGroupChatVo"/>
        where wecom_chat_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
