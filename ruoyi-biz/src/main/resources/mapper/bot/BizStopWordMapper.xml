<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jzbot.mapper.BizStopWordMapper">

    <resultMap type="com.ruoyi.jzbot.domain.BizStopWord" id="BizStopWordResult">
        <result property="id"    column="id"    />
        <result property="keyword"    column="keyword"    />
        <result property="createDatetime"    column="create_datetime"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.keyword, t.create_datetime    </sql>

    <sql id="selectBizStopWordVo">
        select <include refid="Base_Column_List"/>  from biz_stop_word t
    </sql>

    <select id="selectBizStopWordList" parameterType="com.ruoyi.jzbot.domain.BizStopWord" resultMap="BizStopWordResult">
        <include refid="selectBizStopWordVo"/>
        <where>
            <if test="keyword != null  and keyword != ''"> and t.keyword like concat('%', #{keyword}, '%')</if>
            <if test="createDatetime != null "> and t.create_datetime = #{createDatetime}</if>
        </where>
        order by t.id desc
    </select>

    <select id="selectBizStopWordById" parameterType="Long" resultMap="BizStopWordResult">
        <include refid="selectBizStopWordVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizStopWord" parameterType="com.ruoyi.jzbot.domain.BizStopWord" useGeneratedKeys="true" keyProperty="id">
        insert into biz_stop_word
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="keyword != null and keyword != ''">keyword,</if>
            <if test="createDatetime != null">create_datetime,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="keyword != null and keyword != ''">#{keyword},</if>
            <if test="createDatetime != null">#{createDatetime},</if>
         </trim>
    </insert>

    <update id="updateBizStopWord" parameterType="com.ruoyi.jzbot.domain.BizStopWord">
        update biz_stop_word
        <trim prefix="SET" suffixOverrides=",">
            <if test="keyword != null and keyword != ''">keyword = #{keyword},</if>
            <if test="createDatetime != null">create_datetime = #{createDatetime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizStopWordById" parameterType="Long">
        delete from biz_stop_word where id = #{id}
    </delete>

    <delete id="deleteBizStopWordByIds" parameterType="String">
        delete from biz_stop_word where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
