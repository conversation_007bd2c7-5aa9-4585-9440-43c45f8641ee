<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jzbot.mapper.BizJiebaDictMapper">

    <resultMap type="com.ruoyi.jzbot.domain.BizJiebaDict" id="BizJiebaDictResult">
        <result property="id"    column="id"    />
        <result property="keyword"    column="keyword"    />
        <result property="frequency"    column="frequency"    />
        <result property="wordClass"    column="word_class"    />
        <result property="createDatetime"    column="create_datetime"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.keyword, t.frequency, t.word_class, t.create_datetime    </sql>

    <sql id="selectBizJiebaDictVo">
        select <include refid="Base_Column_List"/>  from biz_jieba_dict t
    </sql>

    <select id="selectBizJiebaDictList" parameterType="com.ruoyi.jzbot.domain.BizJiebaDict" resultMap="BizJiebaDictResult">
        <include refid="selectBizJiebaDictVo"/>
        <where>
            <if test="keyword != null  and keyword != ''"> and t.keyword like concat('%', #{keyword}, '%')</if>
            <if test="frequency != null "> and t.frequency = #{frequency}</if>
            <if test="wordClass != null  and wordClass != ''"> and t.word_class = #{wordClass}</if>
            <if test="params.beginCreateDatetime != null and params.beginCreateDatetime != '' and params.endCreateDatetime != null and params.endCreateDatetime != ''"> and t.create_datetime between #{params.beginCreateDatetime} and #{params.endCreateDatetime}</if>
        </where>
        order by t.create_datetime desc
    </select>

    <select id="selectBizJiebaDictById" parameterType="Long" resultMap="BizJiebaDictResult">
        <include refid="selectBizJiebaDictVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizJiebaDict" parameterType="com.ruoyi.jzbot.domain.BizJiebaDict" useGeneratedKeys="true" keyProperty="id">
        insert into biz_jieba_dict
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="keyword != null and keyword != ''">keyword,</if>
            <if test="frequency != null">frequency,</if>
            <if test="wordClass != null and wordClass != ''">word_class,</if>
            <if test="createDatetime != null">create_datetime,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="keyword != null and keyword != ''">#{keyword},</if>
            <if test="frequency != null">#{frequency},</if>
            <if test="wordClass != null and wordClass != ''">#{wordClass},</if>
            <if test="createDatetime != null">#{createDatetime},</if>
         </trim>
    </insert>


    <insert id="insertBizJiebaDictBatch" parameterType="java.util.List">
        insert into biz_jieba_dict
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list != null and list.size() > 0">
                <if test="list[0].keyword != null and list[0].keyword != ''">keyword,</if>
                <if test="list[0].frequency != null">frequency,</if>
                <if test="list[0].wordClass != null and list[0].wordClass != ''">word_class,</if>
                <if test="list[0].createDatetime != null">create_datetime,</if>
            </if>
        </trim>
        <trim prefix="values">
            <foreach collection="list" item="item" separator=",">
                <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="item.keyword != null and item.keyword != ''">#{item.keyword},</if>
                    <if test="item.frequency != null">#{item.frequency},</if>
                    <if test="item.wordClass != null and item.wordClass != ''">#{item.wordClass},</if>
                    <if test="item.createDatetime != null">#{item.createDatetime},</if>
                </trim>
            </foreach>
        </trim>
    </insert>


    <update id="updateBizJiebaDict" parameterType="com.ruoyi.jzbot.domain.BizJiebaDict">
        update biz_jieba_dict
        <trim prefix="SET" suffixOverrides=",">
            <if test="keyword != null and keyword != ''">keyword = #{keyword},</if>
            <if test="frequency != null">frequency = #{frequency},</if>
            <if test="wordClass != null and wordClass != ''">word_class = #{wordClass},</if>
            <if test="createDatetime != null">create_datetime = #{createDatetime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizJiebaDictById" parameterType="Long">
        delete from biz_jieba_dict where id = #{id}
    </delete>

    <delete id="deleteBizJiebaDictByIds" parameterType="String">
        delete from biz_jieba_dict where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
