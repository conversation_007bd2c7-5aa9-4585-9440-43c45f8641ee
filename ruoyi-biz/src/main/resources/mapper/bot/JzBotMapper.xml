<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jzbot.mapper.JzBotMapper">

    <resultMap type="com.ruoyi.jzbot.domain.JzBot" id="JzBotResult">
        <result property="botId"    column="bot_id"    />
        <result property="wxid"    column="wxid"    />
        <result property="weixin"    column="weixin"    />
        <result property="nickname"    column="nickname"    />
        <result property="avatar"    column="avatar"    />
        <result property="online"    column="online"    />
        <result property="token"    column="token"    />
        <result property="qrCode"    column="qr_code"    />
        <result property="joinTime"    column="join_time"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectJzBotVo">
        select bot_id, wxid, weixin, nickname, avatar, online, token,qr_code ,join_time,status  from jz_bot t
    </sql>

    <select id="selectJzBotList" parameterType="com.ruoyi.jzbot.domain.JzBot" resultMap="JzBotResult">
        <include refid="selectJzBotVo"/>
        <where>
            <if test="wxid != null  and wxid != ''"> and wxid = #{wxid}</if>
            <if test="weixin != null  and weixin != ''"> and weixin = #{weixin}</if>
            <if test="nickname != null  and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="online != null "> and online = #{online}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="weixinList != null  and weixinList.size() >0 ">
                and t.weixin in
                <foreach collection="weixinList" item="weixin" separator="," open="(" close=")" >
                    #{weixin}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectJzBotByBotId" parameterType="String" resultMap="JzBotResult">
        <include refid="selectJzBotVo"/>
        where bot_id = #{botId}
    </select>

    <select id="selectJzBotByBotIds" parameterType="String" resultMap="JzBotResult">
        <include refid="selectJzBotVo"/>
        where bot_id in
        <foreach item="botId" collection="array" open="(" separator="," close=")">
            #{botId}
        </foreach>
    </select>

    <insert id="insertJzBot" parameterType="com.ruoyi.jzbot.domain.JzBot">
        insert into jz_bot
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="botId != null">bot_id,</if>
            <if test="wxid != null">wxid,</if>
            <if test="weixin != null">weixin,</if>
            <if test="nickname != null">nickname,</if>
            <if test="avatar != null">avatar,</if>
            <if test="online != null">online,</if>
            <if test="token != null">token,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="botId != null">#{botId},</if>
            <if test="wxid != null">#{wxid},</if>
            <if test="weixin != null">#{weixin},</if>
            <if test="nickname != null">#{nickname},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="online != null">#{online},</if>
            <if test="token != null">#{token},</if>
         </trim>
    </insert>

    <insert id="batchAdd">
        <foreach collection="list" item="bot" separator=";">
            insert into jz_bot (
            <if test="bot.botId != null and bot.botId != ''"> bot_id, </if>
            <if test="bot.wxid != null and bot.wxid != ''"> wxid, </if>
            <if test="bot.weixin != null and bot.weixin != ''"> weixin, </if>
            <if test="bot.nickname != null and bot.nickname != ''"> `nickname`, </if>
            <if test="bot.avatar != null and bot.avatar != ''"> avatar, </if>
            <if test="bot.online != null and bot.online != ''"> online, </if>
            <if test="bot.token != null and bot.token != ''"> token, </if>
            <if test="bot.joinTime != null"> join_time </if>
            )
            values (
            <if test="bot.botId != null and bot.botId != ''"> #{bot.botId,jdbcType=VARCHAR}, </if>
            <if test="bot.wxid != null and bot.wxid != ''"> #{bot.wxid,jdbcType=VARCHAR}, </if>
            <if test="bot.weixin != null and bot.weixin != ''"> #{bot.weixin,jdbcType=VARCHAR}, </if>
            <if test="bot.nickname != null and bot.nickname != ''"> #{bot.nickname,jdbcType=VARCHAR}, </if>
            <if test="bot.avatar != null and bot.avatar != ''"> #{bot.avatar,jdbcType=VARCHAR}, </if>
            <if test="bot.online != null and bot.online != ''"> #{bot.online,jdbcType=VARCHAR}, </if>
            <if test="bot.token != null and bot.token != ''"> #{bot.token,jdbcType=VARCHAR}, </if>
            <if test="bot.joinTime != null"> #{bot.joinTime} </if>
            )
            on duplicate key update
            `nickname` = values(`nickname`),
            avatar = values(avatar),
            online = values(online)
        </foreach>
    </insert>

    <update id="updateJzBot" parameterType="com.ruoyi.jzbot.domain.JzBot">
        update jz_bot
        <trim prefix="SET" suffixOverrides=",">
            <if test="wxid != null">wxid = #{wxid},</if>
            <if test="weixin != null">weixin = #{weixin},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="qrCode != null">qr_code = #{qrCode},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="online != null">online = #{online},</if>
            <if test="token != null">token = #{token},</if>
            <if test="status != null and status != '' ">status = #{status},</if>
        </trim>
        where bot_id = #{botId}
    </update>

    <delete id="deleteJzBotByBotId" parameterType="String">
        delete from jz_bot where bot_id = #{botId}
    </delete>

    <delete id="deleteJzBotByBotIds" parameterType="String">
        delete from jz_bot where bot_id in
        <foreach item="botId" collection="array" open="(" separator="," close=")">
            #{botId}
        </foreach>
    </delete>

    <select id="selectLeastContactBot" resultType="String">
        select jb.bot_id
        from jz_contact jc
                 join jz_bot jb on jc.bot_id = jb.bot_id
        where deleted = 0
          and jb.online = 1
          and jb.status = 1
        group by bot_id
        order by count(1)
            limit 1;
    </select>
</mapper>
