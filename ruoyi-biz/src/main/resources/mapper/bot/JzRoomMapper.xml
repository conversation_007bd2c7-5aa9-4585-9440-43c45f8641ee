<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jzbot.mapper.JzRoomMapper">

    <resultMap type="com.ruoyi.jzbot.domain.JzRoom" id="JzRoomResult">
        <result property="chatId"    column="chat_id"    />
        <result property="botId"    column="bot_id"    />
        <result property="wxid"    column="wxid"    />
        <result property="topic"    column="topic"    />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="ownerId"    column="owner_id"    />
        <result property="memberCount"    column="member_count"    />
        <result property="friendCount"    column="friend_count"    />
        <result property="deleted"    column="deleted"    />
        <result property="status"    column="status"    />
        <result property="agent"    column="agent"    />
        <result property="instruction"    column="instruction"    />
        <result property="excludeAtReply"    column="exclude_at_reply"    />
        <result property="noticeEnv"    column="notice_env"    />
        <result property="joinTime"    column="join_time"    />
        <association property="jzBot" javaType="com.ruoyi.jzbot.domain.JzBot" >
            <result property="botId"    column="b_bot_id"    />
            <result property="wxid"    column="b_wxid"    />
            <result property="weixin"    column="b_weixin"    />
            <result property="nickname"    column="b_nickname"    />
            <result property="avatar"    column="b_avatar"    />
            <result property="online"    column="b_online"    />
            <result property="token"    column="b_token"    />
            <result property="status"    column="b_status"    />
        </association>
    </resultMap>


    <sql id="Base_Column_List">
        t.chat_id, t.bot_id, t.wxid, t.topic, t.avatar_url, t.owner_id, t.member_count,t.friend_count, t.deleted, t.status,
                 t.agent,t.instruction,t.exclude_at_reply,t.notice_env,t.join_time
    </sql>


    <sql id="selectJzRoomVo">
        select   <include refid="Base_Column_List"/>
        ,
        b.bot_id as b_bot_id,  b.wxid as  b_wxid,  b.weixin b_weixin,  b.nickname b_nickname,  b.avatar b_avatar,  b.online b_online,  b.token b_token,b.status b_status
        from jz_room t left join jz_bot b on t.bot_id =  b.bot_id
    </sql>

    <select id="selectJzRoomList" parameterType="com.ruoyi.jzbot.domain.JzRoom" resultMap="JzRoomResult">
        <include refid="selectJzRoomVo"/>
        <if test="tagSearchIdList != null  and tagSearchIdList.size() > 0  and tagSearchType != null ">
            left join `biz_taggable` tag ON t.`chat_id` = tag.`ref_id` AND tag.`ref_type` ='room'
        </if>
        <where>
            <if test="chatId != null  and chatId != ''"> and t.chat_id = #{chatId}</if>
            <if test="botId != null  and botId != ''"> and t.bot_id = #{botId}</if>
            <if test="wxid != null  and wxid != ''"> and t.wxid = #{wxid}</if>
            <if test="topic != null  and topic != ''"> and t.topic like concat('%', #{topic}, '%')</if>
            <if test="jzBot != null  and jzBot.nickname != null  and jzBot.nickname != ''"> and b.nickname like concat('%', #{jzBot.nickname}, '%')</if>
            <if test="avatarUrl != null  and avatarUrl != ''"> and t.avatar_url = #{avatarUrl}</if>
            <if test="deleted != null  and deleted != ''"> and t.deleted = #{deleted}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="agent != null  and agent != ''"> and t.agent = #{agent}</if>
            <if test="excludeAtReply != null  and excludeAtReply != ''"> and t.exclude_at_reply = #{excludeAtReply}</if>
            <if test="chatIdList != null  and chatIdList.size() > 0   ">
                and t.chat_id in
                <foreach collection="chatIdList" open="(" close=")" separator="," item="chatId" >
                    #{chatId}
                </foreach>
            </if>
            <if test="tagSearchIdList != null  and tagSearchIdList.size() > 0  and tagSearchType != null ">
              and tag.tag_id in
              <foreach collection="tagSearchIdList" open="(" close=")" separator="," item="tagId" >
                  #{tagId}
              </foreach>
            </if>
        </where>
        <if test="tagSearchIdList != null  and tagSearchIdList.size() > 0  and tagSearchType != null ">
            <if test="tagSearchType == '0'.toString()">
                GROUP BY t.`chat_id`  HAVING COUNT(1) > 0
            </if>
            <if test="tagSearchType == '1'.toString()">
                GROUP BY t.`chat_id`  HAVING COUNT(1) =  ${tagSearchIdList.size()}
            </if>
        </if>
    </select>

    <select id="selectJzRoomByChatId" parameterType="String" resultMap="JzRoomResult">
        <include refid="selectJzRoomVo"/>
        where t.chat_id = #{chatId}
    </select>

    <select id="selectTopicByChatId" resultType="java.lang.String">
        select topic
        from jz_room
        where chat_id = #{chatId}
    </select>

    <select id="selectJzRoomListSimple" parameterType="com.ruoyi.jzbot.domain.JzRoom" resultMap="JzRoomResult">
        select   <include refid="Base_Column_List"/>
            from jz_room t
        <where>
            <if test="botId != null  and botId != ''"> and t.bot_id = #{botId}</if>
            <if test="wxid != null  and wxid != ''"> and t.wxid = #{wxid}</if>
            <if test="topic != null  and topic != ''"> and t.topic like concat('%', #{topic}, '%')</if>
            <if test="deleted != null  and deleted != ''"> and t.deleted = #{deleted}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="excludeAtReply != null  and excludeAtReply != ''"> and t.exclude_at_reply = #{excludeAtReply}</if>
            <if test="chatIdList != null  and chatIdList.size() > 0   ">
                and t.chat_id in
                <foreach collection="chatIdList" open="(" close=")" separator="," item="chatId" >
                    #{chatId}
                </foreach>
            </if>
            <if test="wxidList != null  and wxidList.size() > 0   ">
                and t.wxid in
                <foreach collection="wxidList" open="(" close=")" separator="," item="wxid" >
                    #{wxid}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectJzRoomListByWxIds" resultMap="JzRoomResult">
        select   <include refid="Base_Column_List"/>
        from jz_room t
        where t.wxid in
        <foreach collection="ids" item="wxid" open="(" close=")" separator=",">
            #{wxid}
        </foreach>
    </select>
    <select id="selectJzRoomListByChatIds" resultMap="JzRoomResult">
        select   <include refid="Base_Column_List"/>
        from jz_room t
        where t.chat_id in
        <foreach collection="chatIds" item="chatId" open="(" close=")" separator=",">
            #{chatId}
        </foreach>

    </select>

    <insert id="insertJzRoom" parameterType="com.ruoyi.jzbot.domain.JzRoom">
        insert into jz_room
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roomId != null">room_id,</if>
            <if test="chatId != null">chat_id,</if>
            <if test="botId != null">bot_id,</if>
            <if test="wxid != null">wxid,</if>
            <if test="topic != null">topic,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="ownerId != null">owner_id,</if>
            <if test="memberCount != null">member_count,</if>
            <if test="deleted != null">deleted,</if>
            <if test="status != null and status != ''">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roomId != null">#{roomId},</if>
            <if test="chatId != null">#{chatId},</if>
            <if test="botId != null">#{botId},</if>
            <if test="wxid != null">#{wxid},</if>
            <if test="topic != null">#{topic},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="ownerId != null">#{ownerId},</if>
            <if test="memberCount != null">#{memberCount},</if>
            <if test="deleted != null">#{deleted},</if>
            <if test="status != null and status != ''">#{status},</if>
         </trim>
        on duplicate key update
        `topic`= #{topic},
        `avatar_url`=#{avatarUrl},
        `member_count`=#{memberCount},
        `deleted` =  #{deleted}
    </insert>

    <insert id="createOrUpdate">
        <foreach collection="list" item="rm" separator=";">
            insert into jz_room
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="rm.chatId != null">chat_id,</if>
                <if test="rm.botId != null">bot_id,</if>
                <if test="rm.wxid != null">wxid,</if>
                <if test="rm.topic != null">topic,</if>
                <if test="rm.avatarUrl != null">avatar_url,</if>
                <if test="rm.ownerId != null">owner_id,</if>
                <if test="rm.memberCount != null">member_count,</if>
                <if test="rm.friendCount != null">friend_count,</if>
                <if test="rm.deleted != null">deleted,</if>
                <if test="rm.status != null and rm.status != ''">status,</if>
                <if test="rm.joinTime != null">join_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="rm.chatId != null">#{rm.chatId},</if>
                <if test="rm.botId != null">#{rm.botId},</if>
                <if test="rm.wxid != null">#{rm.wxid},</if>
                <if test="rm.topic != null">#{rm.topic},</if>
                <if test="rm.avatarUrl != null">#{rm.avatarUrl},</if>
                <if test="rm.ownerId != null">#{rm.ownerId},</if>
                <if test="rm.memberCount != null">#{rm.memberCount},</if>
                <if test="rm.friendCount != null">#{rm.friendCount},</if>
                <if test="rm.deleted != null">#{rm.deleted},</if>
                <if test="rm.status != null and rm.status != ''">#{rm.status},</if>
                <if test="rm.joinTime != null">#{rm.joinTime},</if>
            </trim>
            on duplicate key update
            `topic`=  #{rm.topic},
            `avatar_url`= #{rm.avatarUrl},
            `member_count`= #{rm.memberCount},
            `friend_count`= #{rm.friendCount},
            `deleted` =  #{rm.deleted},
            `join_time` =  #{rm.joinTime}
        </foreach>
    </insert>


    <update id="updateJzRoom" parameterType="com.ruoyi.jzbot.domain.JzRoom">
        update jz_room
        <trim prefix="SET" suffixOverrides=",">
            <if test="chatId != null">chat_id = #{chatId},</if>
            <if test="botId != null">bot_id = #{botId},</if>
            <if test="wxid != null">wxid = #{wxid},</if>
            <if test="topic != null">topic = #{topic},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="ownerId != null">owner_id = #{ownerId},</if>
            <if test="memberCount != null">member_count = #{memberCount},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
            <if test="agent != null">agent = #{agent},</if>
            <if test="instruction != null">instruction = #{instruction},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="excludeAtReply != null and excludeAtReply != ''">exclude_at_reply = #{excludeAtReply},</if>
            <if test="noticeEnv != null and noticeEnv != ''">notice_env = #{noticeEnv},</if>
            <if test="lastTime != null ">last_time = #{lastTime},</if>
            <if test="lastMessage != null and lastMessage != ''">last_message = #{lastMessage},</if>
        </trim>
        where chat_id = #{chatId}
    </update>

    <update id="checkDeleted">
        UPDATE `jz_room`
        SET deleted = CASE
        WHEN chat_id IN
        <foreach collection="list" item="chat" open="(" separator="," close=")">
            #{chat}
        </foreach>
        THEN '0'
        ELSE '1'
        END
    </update>

    <delete id="deleteJzRoomByChatId" parameterType="String">
        delete from jz_room where chat_id = #{chatId}
    </delete>

    <delete id="deleteJzRoomByChatIds" parameterType="String">
        delete from jz_room where chat_id in
        <foreach item="chatId" collection="array" open="(" separator="," close=")">
            #{chatId}
        </foreach>
    </delete>

</mapper>
