<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jzbot.mapper.JzRoomMembersMapper">

    <resultMap type="com.ruoyi.jzbot.domain.JzRoomMembers" id="JzRoomMembersResult">
        <result property="wxid"    column="wxid"    />
        <result property="roomId"    column="room_id"    />
        <result property="nickname"    column="nickname"    />
        <result property="photo"    column="photo"    />
        <result property="isFriend"    column="is_friend"    />
        <result property="roomAlias"    column="room_alias"    />
        <result property="status"    column="status"    />
        <result property="joinScene"    column="join_scene"    />
        <result property="joinTime"    column="join_time"    />
        <result property="quitTime"    column="quit_time"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.wxid, t.room_id, t.nickname, t.photo, t.is_friend, t.room_alias, t.status, t.join_scene, t.join_time, t.quit_time    </sql>

    <sql id="selectJzRoomMembersVo">
        select <include refid="Base_Column_List"/>  from jz_room_members t
    </sql>

    <select id="selectJzRoomMembersList" parameterType="com.ruoyi.jzbot.domain.JzRoomMembers" resultMap="JzRoomMembersResult">
        <include refid="selectJzRoomMembersVo"/>
        <where>
            <if test="wxid != null  and wxid != ''"> and t.wxid = #{wxid}</if>
            <if test="roomId != null  and roomId != ''"> and t.room_id = #{roomId}</if>
            <if test="nickname != null  and nickname != ''"> and t.nickname like concat('%', #{nickname}, '%')</if>
            <if test="photo != null  and photo != ''"> and t.photo = #{photo}</if>
            <if test="isFriend != null "> and t.is_friend = #{isFriend}</if>
            <if test="roomAlias != null  and roomAlias != ''"> and t.room_alias = #{roomAlias}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="joinScene != null  and joinScene != ''"> and t.join_scene = #{joinScene}</if>
            <if test="joinTime != null "> and t.join_time = #{joinTime}</if>
            <if test="quitTime != null "> and t.quit_time = #{quitTime}</if>
        </where>
    </select>

    <select id="selectJzRoomMembersByWxid" parameterType="String" resultMap="JzRoomMembersResult">
        <include refid="selectJzRoomMembersVo"/>
        where t.wxid = #{wxid}
    </select>

    <insert id="insertJzRoomMembers" parameterType="com.ruoyi.jzbot.domain.JzRoomMembers">
        insert into jz_room_members
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="wxid != null">wxid,</if>
            <if test="roomId != null">room_id,</if>
            <if test="nickname != null">nickname,</if>
            <if test="photo != null">photo,</if>
            <if test="isFriend != null">is_friend,</if>
            <if test="roomAlias != null">room_alias,</if>
            <if test="status != null">status,</if>
            <if test="joinScene != null">join_scene,</if>
            <if test="joinTime != null">join_time,</if>
            <if test="quitTime != null">quit_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="wxid != null">#{wxid},</if>
            <if test="roomId != null">#{roomId},</if>
            <if test="nickname != null">#{nickname},</if>
            <if test="photo != null">#{photo},</if>
            <if test="isFriend != null">#{isFriend},</if>
            <if test="roomAlias != null">#{roomAlias},</if>
            <if test="status != null">#{status},</if>
            <if test="joinScene != null">#{joinScene},</if>
            <if test="joinTime != null">#{joinTime},</if>
            <if test="quitTime != null">#{quitTime},</if>
         </trim>
    </insert>

    <insert id="batchAddUpdate">
        <foreach collection="list" item="item" separator=";">
            INSERT INTO jz_room_members
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.wxid != null">wxid,</if>
                <if test="item.roomId != null">room_id,</if>
                <if test="item.nickname != null">nickname,</if>
                <if test="item.photo != null">photo,</if>
                <if test="item.isFriend != null">is_friend,</if>
                <if test="item.roomAlias != null">room_alias,</if>
                <if test="item.status != null">status,</if>
                <if test="item.joinScene != null">join_scene,</if>
                <if test="item.joinTime != null">join_time,</if>
                <if test="item.quitTime != null">quit_time,</if>
            </trim>
            <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
                <if test="item.wxid != null">#{item.wxid},</if>
                <if test="item.roomId != null">#{item.roomId},</if>
                <if test="item.nickname != null">#{item.nickname},</if>
                <if test="item.photo != null">#{item.photo},</if>
                <if test="item.isFriend != null">#{item.isFriend},</if>
                <if test="item.roomAlias != null">#{item.roomAlias},</if>
                <if test="item.status != null">#{item.status},</if>
                <if test="item.joinScene != null">#{item.joinScene},</if>
                <if test="item.joinTime != null">#{item.joinTime},</if>
                <if test="item.quitTime != null">#{item.quitTime},</if>
            </trim>
            ON DUPLICATE KEY UPDATE
            <trim suffixOverrides=",">
                <if test="item.nickname != null">nickname = VALUES(nickname),</if>
                <if test="item.photo != null">photo = VALUES(photo),</if>
                <if test="item.isFriend != null">is_friend = VALUES(is_friend),</if>
                <if test="item.roomAlias != null">room_alias = VALUES(room_alias),</if>
                <if test="item.status != null">status = VALUES(status),</if>
                <if test="item.joinTime != null">join_time = VALUES(join_time),</if>
                <if test="item.quitTime != null">quit_time = VALUES(quit_time),</if>
            </trim>
        </foreach>
    </insert>

    <update id="updateJzRoomMembers" parameterType="com.ruoyi.jzbot.domain.JzRoomMembers">
        update jz_room_members
        <trim prefix="SET" suffixOverrides=",">
            <if test="roomId != null">room_id = #{roomId},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="isFriend != null">is_friend = #{isFriend},</if>
            <if test="roomAlias != null">room_alias = #{roomAlias},</if>
            <if test="status != null">status = #{status},</if>
            <if test="joinScene != null">join_scene = #{joinScene},</if>
            <if test="joinTime != null">join_time = #{joinTime},</if>
            <if test="quitTime != null">quit_time = #{quitTime},</if>
        </trim>
        where wxid = #{wxid}
    </update>

    <delete id="deleteJzRoomMembersByWxid" parameterType="String">
        delete from jz_room_members where wxid = #{wxid}
    </delete>

    <delete id="deleteJzRoomMembersByWxids" parameterType="String">
        delete from jz_room_members where wxid in
        <foreach item="wxid" collection="array" open="(" separator="," close=")">
            #{wxid}
        </foreach>
    </delete>
</mapper>
