<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jzbot.mapper.JzChatMessageMapper">

    <resultMap type="com.ruoyi.jzbot.domain.JzChatMessage" id="JzChatMessageResult">
        <result property="id" column="id"/>
        <result property="chatId" column="chat_id"/>
        <result property="botId" column="bot_id"/>
        <result property="messageId" column="message_id"/>
        <result property="messageType" column="message_type"/>
        <result property="message" column="message"/>
        <result property="contactId" column="contact_id"/>
        <result property="contactName" column="contact_name"/>
        <result property="contactImageUrl" column="contact_image_url"/>
        <result property="isSelf" column="is_self"/>
        <result property="segmentedWords" column="segmented_words"/>
        <result property="rate" column="rate"/>
        <result property="time" column="time"/>
        <result property="messageSource" column="message_source"/>
        <result property="fMessageId" column="f_message_id"/>
        <result property="sendName" column="send_name"/>
        <result property="aiExtra" column="ai_extra"/>
        <result property="rowNum" column="row_num"/>
        <result property="page" column="page"/>
    </resultMap>

    <resultMap type="com.ruoyi.jzbot.domain.vo.JzChatMessageVo" id="JzChatMessageResultVo">
        <result property="id" column="id"/>
        <result property="chatId" column="chat_id"/>
        <result property="botId" column="bot_id"/>
        <result property="messageId" column="message_id"/>
        <result property="messageType" column="message_type"/>
        <result property="message" column="message"/>
        <result property="contactId" column="contact_id"/>
        <result property="contactName" column="contact_name"/>
        <result property="contactImageUrl" column="contact_image_url"/>
        <result property="isSelf" column="is_self"/>
        <result property="isEt" column="is_et"/>
        <result property="segmentedWords" column="segmented_words"/>
        <result property="rate" column="rate"/>
        <result property="time" column="time"/>
        <result property="messageSource" column="message_source"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.chat_id, t.bot_id, t.message_id, t.message_type, t.message, t.contact_id, t.contact_name, t.contact_image_url, t.is_self, t.is_et, t.segmented_words, t.rate, t.time,t.message_source
        ,t.f_message_id,t.send_name,t.ai_extra
        </sql>

    <sql id="selectJzChatMessageVo">
        select
        <include refid="Base_Column_List"/>
        from jz_chat_message t
    </sql>

    <select id="selectJzChatMessageList" parameterType="com.ruoyi.jzbot.domain.req.JzChatMessageReq"
            resultMap="JzChatMessageResult">
        <include refid="selectJzChatMessageVo"/>
        <where>
            <if test="chatId != null  and chatId != ''">and t.chat_id = #{chatId}</if>
            <if test="botId != null  and botId != ''">and t.bot_id = #{botId}</if>
            <if test="messageId != null  and messageId != ''">and t.message_id = #{messageId}</if>
            <if test="messageType != null ">and t.message_type = #{messageType}</if>
            <if test="message != null  and message != ''">and t.message like concat('%', #{message}, '%')</if>
            <if test="contactName != null  and contactName != ''">and t.contact_name like concat('%', #{contactName},
                '%')
            </if>
            <if test="isSelf != null ">and t.is_self = #{isSelf}</if>
            <if test="isEt != null ">and t.is_et = #{isEt}</if>
            <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''">
                and t.time between #{beginTime} and #{endTime}
            </if>
            <if test="params.beginTime != null and params.endTime != null">
                and t.time between #{params.beginTime} and #{params.endTime}
            </if>
            <if test="messageIdList != null  and messageIdList.size() > 0 ">
                and t.message_id in
                <foreach collection="messageIdList" open="(" close=")" separator="," item="messageId" >
                    #{messageId}
                </foreach>
            </if>
        </where>
        order by t.id desc
    </select>

    <select id="selectJzChatMessageHistoryList"   resultMap="JzChatMessageResult">
        SELECT
             <include refid="Base_Column_List"/>,
            row_num,
            CEIL(row_num / 20) AS page
        FROM
            (
                SELECT
                 <include refid="Base_Column_List"/>,
                    @row_num := IF(@prev_chat_id = chat_id, @row_num + 1, 1) AS row_num,  -- 行号计算
                   @prev_chat_id := chat_id
                FROM
                    (SELECT * FROM jz_chat_message ORDER BY chat_id, id DESC) AS t, -- 先排序
                    (SELECT @row_num := 0, @prev_chat_id := NULL) AS vars  -- 初始化变量
            ) AS t
        <where>
            <if test="message != null  and message != ''">and message like concat('%', #{message}, '%')</if>
            <if test="params.beginTime != null and params.endTime != null">
                and `time` between #{params.beginTime} and #{params.endTime}
            </if>
        </where>
        ORDER BY
            chat_id, row_num
    </select>

    <select id="selectJzChatMessageById" parameterType="Long" resultMap="JzChatMessageResult">
        <include refid="selectJzChatMessageVo"/>
        where t.id = #{id}
    </select>

    <select id="statistics" resultMap="JzChatMessageResultVo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM jz_chat_message t
        WHERE t.message_type ='7' and t.chat_id =#{chatId}   and t.is_self ='0' and  DATE_FORMAT(t.time,'%Y-%m-%d')  =  #{yesterday}
    </select>

    <select id="selectJzChatMessageByTimeAndChatIds" resultMap="JzChatMessageResult">
        <include refid="selectJzChatMessageVo"/>
        <where>
            is_self = '0'
            <if test="time != null and time != null">AND t.time BETWEEN #{time} AND DATE_ADD(#{time}, INTERVAL 1 DAY)</if>
            <if test="chatIds != null and chatIds.size() > 0 ">
                and t.chat_id in
                <foreach collection="chatIds" item="chatId" open="(" close=")" separator=",">
                    #{chatId}
                </foreach>
            </if>
        </where>
    </select>


    <insert id="insertJzChatMessage" parameterType="com.ruoyi.jzbot.domain.JzChatMessage" useGeneratedKeys="true" keyProperty="id">
        insert into jz_chat_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="chatId != null and chatId != ''">chat_id,</if>
            <if test="botId != null and botId != ''">bot_id,</if>
            <if test="messageId != null and messageId != ''">message_id,</if>
            <if test="messageType != null">message_type,</if>
            <if test="message != null">message,</if>
            <if test="contactId != null">contact_id,</if>
            <if test="contactName != null">contact_name,</if>
            <if test="contactImageUrl != null">contact_image_url,</if>
            <if test="isSelf != null">is_self,</if>
            <if test="isEt != null">is_et,</if>
            <if test="segmentedWords != null">segmented_words,</if>
            <if test="rate != null">rate,</if>
            <if test="time != null">time,</if>
            <if test="messageSource != null">message_source,</if>
            <if test="fMessageId != null and fMessageId != '' ">f_message_id,</if>
            <if test="sendName != null and sendName != '' ">send_name,</if>
            <if test="aiExtra != null and aiExtra != '' ">ai_extra,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chatId != null and chatId != ''">#{chatId},</if>
            <if test="botId != null and botId != ''">#{botId},</if>
            <if test="messageId != null and messageId != ''">#{messageId},</if>
            <if test="messageType != null">#{messageType},</if>
            <if test="message != null">#{message},</if>
            <if test="contactId != null">#{contactId},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="contactImageUrl != null">#{contactImageUrl},</if>
            <if test="isSelf != null">#{isSelf},</if>
            <if test="isEt != null">#{isEt},</if>
            <if test="segmentedWords != null">#{segmentedWords},</if>
            <if test="rate != null">#{rate},</if>
            <if test="time != null">#{time},</if>
            <if test="messageSource != null">#{messageSource},</if>
            <if test="fMessageId != null and fMessageId != '' ">#{fMessageId},</if>
            <if test="sendName != null and sendName != '' ">#{sendName},</if>
            <if test="aiExtra != null and aiExtra != '' ">#{aiExtra},</if>
        </trim>
        on duplicate key update
        chat_id = VALUES(chat_id),
        f_message_id = IFNULL(VALUES(f_message_id), f_message_id),
        is_et = IFNULL(VALUES(is_et), is_et),
        send_name = IFNULL(VALUES(send_name), send_name),
        ai_extra = IFNULL(VALUES(ai_extra), ai_extra)
    </insert>


    <insert id="insertBatch">
        <foreach collection="list" item="message" separator=";">
            insert into jz_chat_message
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="message.chatId != null and message.chatId != ''">chat_id,</if>
                <if test="message.botId != null and message.botId != ''">bot_id,</if>
                <if test="message.messageId != null and message.messageId != ''">message_id,</if>
                <if test="message.messageType != null">message_type,</if>
                <if test="message.message != null">message,</if>
                <if test="message.contactId != null">contact_id,</if>
                <if test="message.contactName != null">contact_name,</if>
                <if test="message.contactImageUrl != null">contact_image_url,</if>
                <if test="message.isSelf != null">is_self,</if>
                <if test="message.isEt != null">is_et,</if>
                <if test="message.segmentedWords != null">segmented_words,</if>
                <if test="message.rate != null">rate,</if>
                <if test="message.time != null">`time`,</if>
                <if test="message.messageSource != null">message_source,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="message.chatId != null and message.chatId != ''">#{message.chatId},</if>
                <if test="message.botId != null and message.botId != ''">#{message.botId},</if>
                <if test="message.messageId != null and message.messageId != ''">#{message.messageId},</if>
                <if test="message.messageType != null">#{message.messageType},</if>
                <if test="message.message != null">#{message.message},</if>
                <if test="message.contactId != null">#{message.contactId},</if>
                <if test="message.contactName != null">#{message.contactName},</if>
                <if test="message.contactImageUrl != null">#{message.contactImageUrl},</if>
                <if test="message.isSelf != null">#{message.isSelf},</if>
                <if test="message.isEt != null">#{message.isEt},</if>
                <if test="message.segmentedWords != null">#{message.segmentedWords},</if>
                <if test="message.rate != null">#{message.rate},</if>
                <if test="message.time != null">#{message.time},</if>
                <if test="message.messageSource != null">#{message.messageSource},</if>
            </trim>
            on duplicate key update
            chat_id = VALUES(chat_id),
            is_et = VALUES(is_et)
        </foreach>
    </insert>

    <update id="updateJzChatMessage" parameterType="com.ruoyi.jzbot.domain.JzChatMessage">
        update jz_chat_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="chatId != null and chatId != ''">chat_id = #{chatId},</if>
            <if test="botId != null and botId != ''">bot_id = #{botId},</if>
            <if test="messageId != null and messageId != ''">message_id = #{messageId},</if>
            <if test="messageType != null">message_type = #{messageType},</if>
            <if test="message != null">message = #{message},</if>
            <if test="contactId != null">contact_id = #{contactId},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="contactImageUrl != null">contact_image_url = #{contactImageUrl},</if>
            <if test="isSelf != null">is_self = #{isSelf},</if>
            <if test="isEt != null">is_et = #{isEt},</if>
            <if test="segmentedWords != null">segmented_words = #{segmentedWords},</if>
            <if test="rate != null">rate = #{rate},</if>
            <if test="time != null">time = #{time},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateJzChatMessageByMessageId">
        update jz_chat_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="chatId != null and chatId != ''">chat_id = #{chatId},</if>
            <if test="botId != null and botId != ''">bot_id = #{botId},</if>
            <if test="messageType != null">message_type = #{messageType},</if>
            <if test="message != null">message = #{message},</if>
            <if test="contactId != null">contact_id = #{contactId},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="contactImageUrl != null">contact_image_url = #{contactImageUrl},</if>
            <if test="isSelf != null">is_self = #{isSelf},</if>
            <if test="isEt != null">is_et = #{isEt},</if>
            <if test="segmentedWords != null">segmented_words = #{segmentedWords},</if>
            <if test="rate != null">rate = #{rate},</if>
            <if test="time != null">time = #{time},</if>
            <if test="aiExtra != null and aiExtra != '' ">ai_extra = #{aiExtra},</if>
        </trim>
        where message_id = #{messageId}
    </update>

    <delete id="deleteJzChatMessageById" parameterType="Long">
        delete
        from jz_chat_message
        where id = #{id}
    </delete>

    <delete id="deleteJzChatMessageByIds" parameterType="String">
        delete from jz_chat_message where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
