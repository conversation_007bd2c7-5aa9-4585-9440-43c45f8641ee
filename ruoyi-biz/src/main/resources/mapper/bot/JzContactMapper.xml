<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.jzbot.mapper.JzContactMapper">

    <resultMap type="com.ruoyi.jzbot.domain.JzContact" id="JzContactResult">
        <result property="chatId" column="chat_id"/>
        <result property="botId" column="bot_id"/>
        <result property="wxid" column="wxid"/>
        <result property="weixin" column="weixin"/>
        <result property="nickname" column="nickname"/>
        <result property="alias" column="alias"/>
        <result property="avatarUrl" column="avatar_url"/>
        <result property="gender" column="gender"/>
        <result property="deleted" column="deleted"/>
        <result property="unionId" column="union_id"/>
        <result property="friendTime" column="friend_time"/>
        <result property="status" column="status"/>
        <result property="agent" column="agent"/>
        <result property="instruction" column="instruction"/>
        <result property="noticeEnv" column="notice_env"/>
        <association property="jzBot" javaType="com.ruoyi.jzbot.domain.JzBot">
            <result property="botId" column="b_bot_id"/>
            <result property="wxid" column="b_wxid"/>
            <result property="weixin" column="b_weixin"/>
            <result property="nickname" column="b_nickname"/>
            <result property="avatar" column="b_avatar"/>
            <result property="online" column="b_online"/>
            <result property="token" column="b_token"/>
            <result property="status" column="b_status"/>
        </association>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        chat_id
        , t.bot_id, t.wxid, t.weixin, t.nickname, t.alias, t.avatar_url, t.gender, t.deleted, t.union_id, t.friend_time,t.status
                     ,t.agent,t.instruction,t.notice_env
    </sql>

    <sql id="selectJzContactVo">
        select
        <include refid="Base_Column_List"/>
        ,
        b.bot_id as b_bot_id, b.wxid as b_wxid, b.weixin b_weixin, b.nickname b_nickname, b.avatar b_avatar, b.online
        b_online, b.token b_token,b.status b_status
        from jz_contact t left join jz_bot b on t.bot_id = b.bot_id
    </sql>

    <select id="selectJzContactList" parameterType="com.ruoyi.jzbot.domain.JzContact" resultMap="JzContactResult">
        <include refid="selectJzContactVo"/>
        <where>
            <if test="chatId != null  and chatId != ''">and t.chat_id = #{chatId}</if>
            <if test="botId != null  and botId != ''">and t.bot_id = #{botId}</if>
            <if test="nickname != null  and nickname != ''">and LOWER(t.nickname) like concat('%', LOWER(#{nickname}),
                '%')
            </if>
            <if test="jzBot != null  and jzBot.nickname != null  and jzBot.nickname != ''">and LOWER(b.nickname) like
                concat('%', LOWER(#{jzBot.nickname}), '%')
            </if>
            <if test="alias != null  and alias != ''">and t.alias like concat('%', #{alias}, '%')</if>
            <if test="gender != null  and gender != ''">and t.gender = #{gender}</if>
            <if test="wxid != null  and wxid != ''">and t.wxid = #{wxid}</if>
            <if test="deleted != null  and deleted != ''">and t.deleted = #{deleted}</if>
            <if test="unionId != null  and unionId != ''">and t.union_id = #{unionId}</if>
            <if test="status != null  and status != ''">and t.status = #{status}</if>
            <if test="agent != null  and agent != ''">and t.agent = #{agent}</if>
            <if test="unionIdList != null  and unionIdList.size() >0 ">
                and t.union_id in
                <foreach collection="unionIdList" item="unionId" separator="," open="(" close=")">
                    #{unionId}
                </foreach>
            </if>
            <if test="chatIdList != null  and chatIdList.size() > 0   ">
                and t.chat_id in
                <foreach collection="chatIdList" open="(" close=")" separator="," item="chatId">
                    #{chatId}
                </foreach>
            </if>
            <if test="weixinList != null  and weixinList.size() >0 ">
                and t.weixin in
                <foreach collection="weixinList" item="weixin" separator="," open="(" close=")">
                    #{weixin}
                </foreach>
            </if>
        </where>
        order by t.friend_time desc
    </select>

    <select id="selectJzContactById" parameterType="String" resultMap="JzContactResult">
        <include refid="selectJzContactVo"/>
        where t.chat_id = #{chatId}
    </select>

    <select id="selectNonUnionIdList" resultMap="JzContactResult">
        select
        <include refid="Base_Column_List"/>
        from jz_contact t
        left join biz_user bu on t.union_id = bu.union_id
        where bu.union_id is null;
    </select>

    <select id="selectJzContactByUnionId" resultMap="JzContactResult">
        <include refid="selectJzContactVo"/>
        where t.union_id = #{unionId} order by t.last_time desc
    </select>

    <select id="selectJzContactListSimple" resultMap="JzContactResult">
        select
        <include refid="Base_Column_List"/>
        from jz_contact t
        <where>
            <if test="botId != null  and botId != ''">and t.bot_id = #{botId}</if>
            <if test="nickname != null  and nickname != ''">and t.nickname like concat('%', #{nickname}, '%')</if>
            <if test="alias != null  and alias != ''">and t.alias like concat('%', #{alias}, '%')</if>
            <if test="gender != null  and gender != ''">and t.gender = #{gender}</if>
            <if test="wxid != null  and wxid != ''">and t.wxid = #{wxid}</if>
            <if test="unionId != null  and unionId != ''">and t.union_id = #{unionId}</if>
            <if test="status != null  and status != ''">and t.status = #{status}</if>
            <if test="deleted != null  and deleted != ''">and t.deleted = #{deleted}</if>
            <if test="chatIdList != null  and chatIdList.size() >0 ">
                and t.chat_id in
                <foreach collection="chatIdList" item="chatId" separator="," open="(" close=")">
                    #{chatId}
                </foreach>
            </if>
            <if test="unionIdList != null  and unionIdList.size() >0 ">
                and t.union_id in
                <foreach collection="unionIdList" item="unionId" separator="," open="(" close=")">
                    #{unionId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectJzContactListByWxId" resultMap="JzContactResult">
        <include refid="selectJzContactVo"/>
        where t.wxid = #{wxid}
    </select>



    <select id="listAll" resultType="com.ruoyi.jzbot.domain.dto.JzContactDto">
        <choose>
            <!-- 当 contactType = 1 时，只查询联系人表 -->
            <when test="contactType == '1'.toString()">
                SELECT
                '1' AS contactType,  -- 私聊类型
                bu.`id`  userId,
                jc.chat_id AS chatId,
                jc.avatar_url AS avatarUrl,  -- 联系人头像
                jc.nickname AS nickname,  -- 联系人昵称
                jc.bot_id AS botId ,
                jb.`nickname` AS botName,
                jb.token as token,
                jc.last_time lastTime,
                jc.last_message lastMessage
                FROM
                jz_contact jc inner join jz_bot jb on jc.bot_id = jb.bot_id
                INNER JOIN biz_user bu ON jc.`union_id` =bu.`union_id`
                WHERE  1 = 1
                <if test="botId != null">
                    AND jc.bot_id = #{botId}  -- 根据 bot_id 筛选
                </if>
                <if test="keywords != null and keywords != '' ">
                    AND jc.nickname like concat('%',#{keywords},'%')   -- 根据 keywords 筛选
                </if>
                order by
                <if test="chatIdListStr != null and chatIdListStr != '' ">
                    FIELD(jc.chat_id, #{chatIdListStr}) desc,
                </if>
                  jc.last_time desc
            </when>
            <!-- 当 contactType = 2 时，只查询群聊表 -->
            <when test="contactType == '2'.toString()">
                SELECT
                '2' AS contactType,  -- 群聊类型
                jr.wxid AS userId,
                jr.chat_id AS chatId,
                jr.avatar_url AS avatarUrl,  -- 群头像
                jr.topic AS nickname,  -- 群名称作为昵称
                jr.bot_id AS botId,
                jb.`nickname` AS botName,
                jb.token as token,
                jr.last_time lastTime,
                jr.last_message lastMessage
                FROM
                jz_room jr inner join jz_bot jb on jr.bot_id = jb.bot_id
                WHERE   1 = 1
                <if test="botId != null">
                    AND jr.bot_id = #{botId}  -- 根据 bot_id 筛选
                </if>
                <if test="keywords != null and keywords != '' ">
                    AND jr.topic like concat('%',#{keywords},'%')   -- 根据 keywords 筛选
                </if>
                order by
                <if test="chatIdListStr != null and chatIdListStr != '' ">
                    FIELD(jr.chat_id, #{chatIdListStr}) desc,
                </if>
                   jr.last_time desc
            </when>
            <!-- 当 contactType 为空时，联合查询联系人表和群聊表 -->
            <otherwise>
             select * from (
                SELECT
                '1' AS contactType,  -- 私聊类型
                bu.`id`  userId,
                jc.chat_id AS chatId,
                jc.avatar_url AS avatarUrl,  -- 联系人头像
                jc.nickname AS nickname,  -- 联系人昵称
                jc.bot_id AS botId ,
                jb.`nickname` AS botName,
                jb.token as token,
                jc.last_time lastTime,
                jc.last_message lastMessage
                FROM
                jz_contact jc inner join jz_bot jb on jc.bot_id = jb.bot_id
                INNER JOIN biz_user bu ON jc.`union_id` =bu.`union_id`
                WHERE  1 = 1
                <if test="botId != null">
                    AND jc.bot_id = #{botId}  -- 根据 bot_id 筛选
                </if>
                <if test="keywords != null and keywords != '' ">
                    AND jc.nickname like concat('%',#{keywords},'%')   -- 根据 keywords 筛选
                </if>
                UNION ALL
                SELECT
                '2' AS contactType,  -- 群聊类型
                jr.wxid AS userId,
                jr.chat_id AS chatId,
                jr.avatar_url AS avatarUrl,  -- 群头像
                jr.topic AS nickname,  -- 群名称作为昵称
                jr.bot_id AS botId,
                jb.`nickname` AS botName,
                jb.token as token,
                jr.last_time lastTime,
                jr.last_message lastMessage
                FROM
                jz_room jr inner join jz_bot jb on jr.bot_id = jb.bot_id
                WHERE   1 =1
                <if test="botId != null">
                    AND jr.bot_id = #{botId}  -- 根据 bot_id 筛选
                </if>
                <if test="keywords != null and keywords != '' ">
                    AND jr.topic like concat('%',#{keywords},'%')   -- 根据 keywords 筛选
                </if>
                ) t
             order by
                <if test="chatIdListStr != null and chatIdListStr != '' ">
                    FIELD(t.chatId, #{chatIdListStr}) desc,
                </if>
                 t.lastTime desc
            </otherwise>
        </choose>


    </select>
    <select id="selectJzContactListByWeixinIds" resultMap="JzContactResult">
        select
        <include refid="Base_Column_List"/>
        from jz_contact t
        where t.weixin in
        <foreach collection="ids" item="weixin" separator="," open="(" close=")">
            #{weixin}
        </foreach>
    </select>

    <select id="selectJzContactListByWxIds" resultMap="JzContactResult">
        select
        <include refid="Base_Column_List"/>
        from jz_contact t
        where t.wxid in
        <foreach collection="ids" item="wxid" separator="," open="(" close=")">
            #{wxid}
        </foreach>
    </select>

    <select id="selectJzConcatListByChatIds" resultMap="JzContactResult">
        select
        <include refid="Base_Column_List"/>
        from jz_contact t
        where t.chat_id in
        <foreach collection="chatIds" item="chatId" separator="," open="(" close=")">
            #{chatId}
        </foreach>
    </select>


    <select id="getWaitActiveUser" resultMap="JzContactResult">
        SELECT
             <include refid="Base_Column_List"/>
        FROM
            `jz_contact` t
        WHERE
            t.`deleted` = '0'
            AND (
                (
                t.`status` = '0'
                AND t.`union_id` IN (
                SELECT
                m.`union_id`
                FROM
                `biz_membership_token` m
                WHERE
                m.`status` = 'unused'
                )
                )
            OR (
                t.`status` = '1'
                AND t.`union_id` IN (
                SELECT
                m.`union_id`
                FROM
                `biz_membership_token` m
                WHERE
                m.`status` = 'unused'
                AND m.`membership_type` = '2'
                )
                )
            )

    </select>

    <insert id="insertJzContact" parameterType="com.ruoyi.jzbot.domain.JzContact" useGeneratedKeys="true"
            keyProperty="id">
        insert into jz_contact
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="chatId != null">chat_id,</if>
            <if test="botId != null">bot_id,</if>
            <if test="wxid != null">wxid,</if>
            <if test="weixin != null">weixin,</if>
            <if test="nickname != null and nickname != ''">nickname,</if>
            <if test="alias != null">alias,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="gender != null and gender != ''">gender,</if>
            <if test="deleted != null and deleted != ''">deleted,</if>
            <if test="unionId != null">union_id,</if>
            <if test="friendTime != null">friend_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chatId != null">#{chatId},</if>
            <if test="botId != null">#{botId},</if>
            <if test="wxid != null">#{wxid},</if>
            <if test="weixin != null">#{weixin},</if>
            <if test="nickname != null and nickname != ''">#{nickname},</if>
            <if test="alias != null">#{alias},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="gender != null and gender != ''">#{gender},</if>
            <if test="deleted != null and deleted != ''">#{deleted},</if>
            <if test="unionId != null">#{unionId},</if>
            <if test="friendTime != null">#{friendTime},</if>
        </trim>
    </insert>

    <insert id="createOrUpdate">
        <foreach collection="list" item="item" separator=";">
            insert into jz_contact
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.chatId != null">chat_id,</if>
                <if test="item.botId != null">bot_id,</if>
                <if test="item.wxid != null">wxid,</if>
                <if test="item.weixin != null">weixin,</if>
                <if test="item.nickname != null and item.nickname != ''">nickname,</if>
                <if test="item.alias != null">alias,</if>
                <if test="item.avatarUrl != null">avatar_url,</if>
                <if test="item.gender != null and item.gender != ''">gender,</if>
                <if test="item.deleted != null and item.deleted != ''">deleted,</if>
                <if test="item.unionId != null">union_id,</if>
                <if test="item.friendTime != null">friend_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.chatId != null">#{item.chatId},</if>
                <if test="item.botId != null">#{item.botId},</if>
                <if test="item.wxid != null">#{item.wxid},</if>
                <if test="item.weixin != null">#{item.weixin},</if>
                <if test="item.nickname != null and item.nickname != ''">#{item.nickname},</if>
                <if test="item.alias != null">#{item.alias},</if>
                <if test="item.avatarUrl != null">#{item.avatarUrl},</if>
                <if test="item.gender != null and item.gender != ''">#{item.gender},</if>
                <if test="item.deleted != null and item.deleted != ''">#{item.deleted},</if>
                <if test="item.unionId != null">#{item.unionId},</if>
                <if test="item.friendTime != null">#{item.friendTime},</if>
            </trim>
            on duplicate key update
            `nickname`= #{item.nickname},
            weixin = #{item.weixin},
            `alias`= #{item.alias},
            `avatar_url`= #{item.avatarUrl},
            `deleted` = #{item.deleted},
            `friend_time` = VALUES(friend_time)
        </foreach>
    </insert>

    <update id="updateJzContact" parameterType="com.ruoyi.jzbot.domain.JzContact">
        update jz_contact
        <trim prefix="SET" suffixOverrides=",">
            <if test="chatId != null">chat_id = #{chatId},</if>
            <if test="botId != null">bot_id = #{botId},</if>
            <if test="wxid != null">wxid = #{wxid},</if>
            <if test="weixin != null">weixin = #{weixin},</if>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="alias != null">alias = #{alias},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="gender != null and gender != ''">gender = #{gender},</if>
            <if test="deleted != null and deleted != ''">deleted = #{deleted},</if>
            <if test="unionId != null">union_id = #{unionId},</if>
            <if test="friendTime != null">friend_time = #{friendTime},</if>
            <if test="status != null and status != '' ">status = #{status},</if>
            <if test="agent != null">agent = #{agent},</if>
            <if test="instruction != null">instruction = #{instruction},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="noticeEnv != null and noticeEnv != ''">notice_env = #{noticeEnv},</if>
            <if test="lastTime != null ">last_time = #{lastTime},</if>
            <if test="lastMessage != null and lastMessage != ''">last_message = #{lastMessage},</if>
        </trim>
        where chat_id = #{chatId}
    </update>

    <update id="checkDeleted">
        UPDATE `jz_contact`
        SET deleted = CASE
        WHEN chat_id IN
        <foreach collection="list" item="chat" open="(" separator="," close=")">
            #{chat}
        </foreach>
        THEN '0'
        ELSE '1'
        END
    </update>

    <delete id="deleteJzContactById" parameterType="String">
        delete
        from jz_contact
        where chat_id = #{chatId}
    </delete>

    <delete id="deleteJzContactByIds" parameterType="String">
        delete from jz_contact where chat_id in
        <foreach item="chatId" collection="array" open="(" separator="," close=")">
            #{chatId}
        </foreach>
    </delete>
</mapper>
