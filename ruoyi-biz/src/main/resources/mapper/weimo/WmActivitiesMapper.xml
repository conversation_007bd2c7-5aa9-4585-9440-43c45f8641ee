<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.weimo.mapper.WmActivitiesMapper">

    <resultMap type="com.ruoyi.weimo.domain.WmActivities" id="WmActivitiesResult">
        <result property="id"    column="id"    />
        <result property="activityId"    column="activity_id"    />
        <result property="activityType"    column="activity_type"    />
        <result property="activitySubType"    column="activity_sub_type"    />
        <result property="promotionName"    column="promotion_name"    />
        <result property="timeType"    column="time_type"    />
        <result property="startTime"    column="start_time"    />
        <result property="day"    column="day"    />
        <result property="endTime"    column="end_time"    />
        <result property="cycleTimeList"    column="cycle_time_list"    />
        <result property="promotionStatus"    column="promotion_status"    />
        <result property="promotionImage"    column="promotion_image"    />
        <result property="isPromotionLongTime"    column="is_promotion_long_time"    />
        <result property="source"    column="source"    />
        <result property="keywords"    column="keywords"    />
        <result property="isAiPurchaseGuide" column="is_ai_purchase_guide"    />
        <result property="refType"    column="ref_type"    />
        <result property="refId"    column="ref_id"    />
        <result property="promotionDescription" column="promotion_description"  />
        <result property="rule"    column="rule"    />
        <result property="details"    column="details"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.activity_id, t.activity_type, t.activity_sub_type, t.promotion_name, t.time_type, t.start_time, t.day, t.end_time, t.cycle_time_list, t.promotion_status, t.promotion_image, t.is_promotion_long_time, t.source, t.keywords, t.is_ai_purchase_guide, t.ref_type, t.ref_id, t.promotion_description, t.rule, t.details, t.create_by, t.create_time, t.update_by, t.update_time    </sql>

    <sql id="selectWmActivitiesVo">
        select <include refid="Base_Column_List"/>  from wm_activities t
    </sql>

    <select id="selectWmActivitiesList" parameterType="com.ruoyi.weimo.domain.WmActivities" resultMap="WmActivitiesResult">
        <include refid="selectWmActivitiesVo"/>
        <where>
            <if test="id != null "> and t.id = #{id}</if>
            <if test="activityId != null "> and t.activity_id like concat('%', #{activityId}, '%')</if>
            <if test="activityType != null "> and t.activity_type = #{activityType}</if>
            <if test="promotionName != null  and promotionName != ''"> and t.promotion_name like concat('%', #{promotionName}, '%')</if>
            <if test="startTime != null "> and t.start_time &gt;= #{startTime}</if>
            <if test="endTime != null "> and t.end_time &lt;= #{endTime} </if>
            <if test="promotionStatus != null "> and t.promotion_status = #{promotionStatus}</if>
            <if test="isPromotionLongTime != null "> and t.is_promotion_long_time = #{isPromotionLongTime}</if>
            <if test="source != null  and source != ''"> and t.source = #{source}</if>
            <if test="keywords != null  and keywords != ''"> and t.keywords like concat('%', #{keywords}, '%')</if>
            <if test="isAiPurchaseGuide != null "> and t.is_ai_purchase_guide = #{isAiPurchaseGuide}</if>
        </where>
        order by t.create_time desc
    </select>

    <select id="selectWmActivitiesById" parameterType="Long" resultMap="WmActivitiesResult">
        <include refid="selectWmActivitiesVo"/>
        where t.id = #{id}
    </select>

    <select id="getIdByActivityId" resultType="java.lang.Long">
        select id from wm_activities where activity_id = #{activityId}
    </select>

        <select id="selectWmActivitiesByIds" resultMap="WmActivitiesResult">
            <include refid="selectWmActivitiesVo"/>
            where id in
            <foreach collection="activityIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </select>

    <select id="selectWmActivitiesByActivityIds" resultMap="WmActivitiesResult">
        <include refid="selectWmActivitiesVo"/>
        where activity_id in
        <foreach collection="activityIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectOngoingActivityIds" resultType="java.lang.Long">
        select id from wm_activities where promotion_status = 5 order by create_time desc limit 10
    </select>

    <insert id="insertWmActivities" parameterType="com.ruoyi.weimo.domain.WmActivities" useGeneratedKeys="true" keyProperty="id">
        insert into wm_activities
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityId != null">activity_id,</if>
            <if test="activityType != null">activity_type,</if>
            <if test="activitySubType != null">activity_sub_type,</if>
            <if test="promotionName != null and promotionName != ''">promotion_name,</if>
            <if test="timeType != null">time_type,</if>
            <if test="startTime != null">start_time,</if>
            <if test="day != null">day,</if>
            <if test="endTime != null">end_time,</if>
            <if test="cycleTimeList != null">cycle_time_list,</if>
            <if test="promotionStatus != null">promotion_status,</if>
            <if test="promotionImage != null and promotionImage != ''">promotion_image,</if>
            <if test="isPromotionLongTime != null">is_promotion_long_time,</if>
            <if test="source != null">source,</if>
            <if test="keywords != null and keywords != ''">keywords,</if>
            <if test="isAiPurchaseGuide != null">is_ai_purchase_guide,</if>
            <if test="refType != null">ref_type,</if>
            <if test="refId != null">ref_id,</if>
            <if test="promotionDescription != null">promotion_description,</if>
            <if test="rule != null">rule,</if>
            <if test="details != null">details,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityId != null">#{activityId},</if>
            <if test="activityType != null">#{activityType},</if>
            <if test="activitySubType != null">#{activitySubType},</if>
            <if test="promotionName != null and promotionName != ''">#{promotionName},</if>
            <if test="timeType != null">#{timeType},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="day != null">#{day},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="cycleTimeList != null">#{cycleTimeList},</if>
            <if test="promotionStatus != null">#{promotionStatus},</if>
            <if test="promotionImage != null and promotionImage != ''">#{promotionImage},</if>
            <if test="isPromotionLongTime != null">#{isPromotionLongTime},</if>
            <if test="source != null">#{source},</if>
            <if test="keywords != null and keywords != ''">#{keywords},</if>
            <if test="isAiPurchaseGuide != null">#{isAiPurchaseGuide},</if>
            <if test="refType != null">#{refType},</if>
            <if test="refId != null">#{refId},</if>
            <if test="promotionDescription != null">#{promotionDescription},</if>
            <if test="rule != null">#{rule},</if>
            <if test="details != null">#{details},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
        ON DUPLICATE KEY UPDATE
        activity_id =ifnull( VALUES(activity_id),activity_id),
        activity_type =ifnull( VALUES(activity_type),activity_type),
        activity_sub_type =ifnull( VALUES(activity_sub_type),activity_sub_type),
        promotion_name =ifnull( VALUES(promotion_name),promotion_name),
        time_type =ifnull( VALUES(time_type),time_type),
        start_time =ifnull( VALUES(start_time),start_time),
        day =ifnull( VALUES(day),day),
        end_time =ifnull( VALUES(end_time),end_time),
        cycle_time_list =ifnull( VALUES(cycle_time_list),cycle_time_list),
        promotion_status =ifnull( VALUES(promotion_status),promotion_status),
        promotion_image =ifnull( VALUES(promotion_image),promotion_image),
        is_promotion_long_time =ifnull( VALUES(is_promotion_long_time),is_promotion_long_time),
        source =ifnull( VALUES(source),source),
        keywords =ifnull( VALUES(keywords),keywords),
        is_ai_purchase_guide =ifnull( VALUES(is_ai_purchase_guide),is_ai_purchase_guide),
        ref_type =ifnull( VALUES(ref_type),ref_type),
        ref_id =ifnull( VALUES(ref_id),ref_id),
        promotion_description =ifnull( VALUES(promotion_description),promotion_description),
        rule =ifnull( VALUES(rule),rule),
        details =ifnull( VALUES(details),details)
    </insert>

    <update id="updateWmActivities" parameterType="com.ruoyi.weimo.domain.WmActivities">
        update wm_activities
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityId != null">activity_id = #{activityId},</if>
            <if test="activityType != null">activity_type = #{activityType},</if>
            <if test="activitySubType != null">activity_sub_type = #{activitySubType},</if>
            <if test="promotionName != null and promotionName != ''">promotion_name = #{promotionName},</if>
            <if test="timeType != null">time_type = #{timeType},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="day != null">day = #{day},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="cycleTimeList != null">cycle_time_list = #{cycleTimeList},</if>
            <if test="promotionStatus != null">promotion_status = #{promotionStatus},</if>
            <if test="promotionImage != null and promotionImage != ''">promotion_image = #{promotionImage},</if>
            <if test="isPromotionLongTime != null">is_promotion_long_time = #{isPromotionLongTime},</if>
            <if test="source != null">source = #{source},</if>
            <if test="keywords != null and keywords != ''">keywords = #{keywords},</if>
            <if test="isAiPurchaseGuide != null">is_ai_purchase_guide = #{isAiPurchaseGuide},</if>
            <if test="refType != null">ref_type = #{refType},</if>
            <if test="refId != null">ref_id = #{refId},</if>
            <if test="promotionDescription != null">promotion_description = #{promotionDescription},</if>
            <if test="rule != null">rule = #{rule},</if>
            <if test="details != null">details = #{details},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateAiRepurchase" parameterType="java.util.Map">
        update wm_activities set is_ai_purchase_guide = #{status}
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <delete id="deleteWmActivitiesById" parameterType="Long">
        delete from wm_activities where id = #{id}
    </delete>

    <delete id="deleteWmActivitiesByIds" parameterType="String">
        delete from wm_activities where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
