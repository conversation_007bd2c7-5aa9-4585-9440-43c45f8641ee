<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.weimo.mapper.WmGoodsMapper">

    <resultMap type="com.ruoyi.weimo.domain.WmGoods" id="WmGoodsResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="subTitle" column="sub_title"/>
        <result property="defaultImageUrl" column="default_image_url"/>
        <result property="outerGoodsCode" column="outer_goods_code"/>
        <result property="category" column="category"/>
        <result property="subCategory" column="sub_category"/>
        <result property="goodsStockNum" column="goods_stock_num"/>
        <result property="maxSalePrice" column="max_sale_price"/>
        <result property="minSalePrice" column="min_sale_price"/>
        <result property="realSaleNum" column="real_sale_num"/>
        <result property="soldType" column="sold_type"/>
        <result property="isMultiSku" column="is_multi_sku"/>
        <result property="isOnline" column="is_online"/>
        <result property="isCanSell" column="is_can_sell"/>
        <result property="sort" column="sort"/>
        <result property="onlineTime" column="online_time"/>
        <result property="detailInfo" column="detail_info"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="skuList" column="sku_list"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.title, t.sub_title, t.default_image_url, t.outer_goods_code, t.category, t.sub_category, t.goods_stock_num, t.max_sale_price, t.min_sale_price, t.real_sale_num, t.sold_type, t.is_multi_sku, t.is_online, t.is_can_sell, t.sort, t.online_time, t.detail_info, t.create_time, t.update_time, t.delete_flag, t.sku_list    </sql>

    <sql id="selectWmGoodsVo">
        select
        <include refid="Base_Column_List"/>
        from wm_goods t
    </sql>

    <select id="selectWmGoodsList" parameterType="com.ruoyi.weimo.domain.WmGoods" resultMap="WmGoodsResult">
        <include refid="selectWmGoodsVo"/>
        <where>
            <if test="title != null  and title != ''">and t.title = #{title}</if>
            <if test="subTitle != null  and subTitle != ''">and t.sub_title = #{subTitle}</if>
            <if test="defaultImageUrl != null  and defaultImageUrl != ''">and t.default_image_url = #{defaultImageUrl}
            </if>
            <if test="outerGoodsCode != null  and outerGoodsCode != ''">and t.outer_goods_code = #{outerGoodsCode}</if>
            <if test="category != null ">and t.category = #{category}</if>
            <if test="subCategory != null ">and t.sub_category = #{subCategory}</if>
            <if test="goodsStockNum != null ">and t.goods_stock_num = #{goodsStockNum}</if>
            <if test="maxSalePrice != null ">and t.max_sale_price = #{maxSalePrice}</if>
            <if test="minSalePrice != null ">and t.min_sale_price = #{minSalePrice}</if>
            <if test="realSaleNum != null ">and t.real_sale_num = #{realSaleNum}</if>
            <if test="soldType != null ">and t.sold_type = #{soldType}</if>
            <if test="isMultiSku != null ">and t.is_multi_sku = #{isMultiSku}</if>
            <if test="isOnline != null ">and t.is_online = #{isOnline}</if>
            <if test="isCanSell != null ">and t.is_can_sell = #{isCanSell}</if>
            <if test="sort != null ">and t.sort = #{sort}</if>
            <if test="onlineTime != null ">and t.online_time = #{onlineTime}</if>
            <if test="detailInfo != null  and detailInfo != ''">and t.detail_info = #{detailInfo}</if>
            <if test="deleteFlag != null ">and t.delete_flag = #{deleteFlag}</if>
            <if test="skuList != null  and skuList != ''">and t.sku_list = #{skuList}</if>
        </where>
    </select>

    <select id="selectWmGoodsById" parameterType="Long" resultMap="WmGoodsResult">
        <include refid="selectWmGoodsVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertWmGoods" parameterType="com.ruoyi.weimo.domain.WmGoods">
        insert into wm_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="subTitle != null">sub_title,</if>
            <if test="defaultImageUrl != null">default_image_url,</if>
            <if test="outerGoodsCode != null">outer_goods_code,</if>
            <if test="category != null">category,</if>
            <if test="subCategory != null">sub_category,</if>
            <if test="goodsStockNum != null">goods_stock_num,</if>
            <if test="maxSalePrice != null">max_sale_price,</if>
            <if test="minSalePrice != null">min_sale_price,</if>
            <if test="realSaleNum != null">real_sale_num,</if>
            <if test="soldType != null">sold_type,</if>
            <if test="isMultiSku != null">is_multi_sku,</if>
            <if test="isOnline != null">is_online,</if>
            <if test="isCanSell != null">is_can_sell,</if>
            <if test="sort != null">sort,</if>
            <if test="onlineTime != null">online_time,</if>
            <if test="detailInfo != null">detail_info,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="skuList != null">sku_list,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="subTitle != null">#{subTitle},</if>
            <if test="defaultImageUrl != null">#{defaultImageUrl},</if>
            <if test="outerGoodsCode != null">#{outerGoodsCode},</if>
            <if test="category != null">#{category},</if>
            <if test="subCategory != null">#{subCategory},</if>
            <if test="goodsStockNum != null">#{goodsStockNum},</if>
            <if test="maxSalePrice != null">#{maxSalePrice},</if>
            <if test="minSalePrice != null">#{minSalePrice},</if>
            <if test="realSaleNum != null">#{realSaleNum},</if>
            <if test="soldType != null">#{soldType},</if>
            <if test="isMultiSku != null">#{isMultiSku},</if>
            <if test="isOnline != null">#{isOnline},</if>
            <if test="isCanSell != null">#{isCanSell},</if>
            <if test="sort != null">#{sort},</if>
            <if test="onlineTime != null">#{onlineTime},</if>
            <if test="detailInfo != null">#{detailInfo},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="skuList != null">#{skuList},</if>
        </trim>
        on duplicate key update
            `title`=#{title},
            sub_title=#{subTitle},
            default_image_url=#{defaultImageUrl},
            outer_goods_code =#{outerGoodsCode},
            category=#{category},
            sub_category=#{subCategory},
            goods_stock_num=#{goodsStockNum},
            max_sale_price=#{maxSalePrice},
            min_sale_price=#{minSalePrice},
            real_sale_num=#{realSaleNum},
            sold_type=#{soldType},
            is_multi_sku=#{isMultiSku},
            is_online=#{isOnline},
            is_can_sell=#{isCanSell},
            sort=#{sort},
            online_time=#{onlineTime},
            detail_info=#{detailInfo},
            sku_list =#{skuList}
    </insert>

    <update id="updateWmGoods" parameterType="com.ruoyi.weimo.domain.WmGoods">
        update wm_goods
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="subTitle != null">sub_title = #{subTitle},</if>
            <if test="defaultImageUrl != null">default_image_url = #{defaultImageUrl},</if>
            <if test="outerGoodsCode != null">outer_goods_code = #{outerGoodsCode},</if>
            <if test="category != null">category = #{category},</if>
            <if test="subCategory != null">sub_category = #{subCategory},</if>
            <if test="goodsStockNum != null">goods_stock_num = #{goodsStockNum},</if>
            <if test="maxSalePrice != null">max_sale_price = #{maxSalePrice},</if>
            <if test="minSalePrice != null">min_sale_price = #{minSalePrice},</if>
            <if test="realSaleNum != null">real_sale_num = #{realSaleNum},</if>
            <if test="soldType != null">sold_type = #{soldType},</if>
            <if test="isMultiSku != null">is_multi_sku = #{isMultiSku},</if>
            <if test="isOnline != null">is_online = #{isOnline},</if>
            <if test="isCanSell != null">is_can_sell = #{isCanSell},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="onlineTime != null">online_time = #{onlineTime},</if>
            <if test="detailInfo != null">detail_info = #{detailInfo},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteFlag != null">delete_flag = #{deleteFlag},</if>
            <if test="skuList != null">sku_list = #{skuList},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWmGoodsById" parameterType="Long">
        delete
        from wm_goods
        where id = #{id}
    </delete>

    <delete id="deleteWmGoodsByIds" parameterType="String">
        delete from wm_goods where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
