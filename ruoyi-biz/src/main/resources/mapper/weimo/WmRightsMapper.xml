<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.weimo.mapper.WmRightsMapper">

    <resultMap type="com.ruoyi.weimo.domain.WmRights" id="WmRightsResult">
        <result property="rightsId"    column="rights_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="vid"    column="vid"    />
        <result property="processVid"    column="process_vid"    />
        <result property="rightsStatus"    column="rights_status"    />
        <result property="rightsType"    column="rights_type"    />
        <result property="rightsSource"    column="rights_source"    />
        <result property="rightsCauseType"    column="rights_cause_type"    />
        <result property="refundType"    column="refund_type"    />
        <result property="currency"    column="currency"    />
        <result property="bizProcessType"    column="biz_process_type"    />
        <result property="hasProcess"    column="has_process"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.rights_id, t.order_no, t.vid, t.process_vid, t.rights_status, t.rights_type, t.rights_source, t.rights_cause_type, t.refund_type, t.currency, t.biz_process_type, t.has_process, t.create_time, t.create_by, t.update_time, t.update_by    </sql>

    <sql id="selectWmRightsVo">
        select <include refid="Base_Column_List"/>  from wm_rights t
    </sql>

    <select id="selectWmRightsList" parameterType="com.ruoyi.weimo.domain.WmRights" resultMap="WmRightsResult">
        <include refid="selectWmRightsVo"/>
        <where>
            <if test="orderNo != null "> and t.order_no = #{orderNo}</if>
            <if test="vid != null "> and t.vid = #{vid}</if>
            <if test="processVid != null "> and t.process_vid = #{processVid}</if>
            <if test="rightsStatus != null "> and t.rights_status = #{rightsStatus}</if>
            <if test="rightsType != null "> and t.rights_type = #{rightsType}</if>
            <if test="rightsSource != null "> and t.rights_source = #{rightsSource}</if>
            <if test="rightsCauseType != null "> and t.rights_cause_type = #{rightsCauseType}</if>
            <if test="refundType != null "> and t.refund_type = #{refundType}</if>
            <if test="currency != null "> and t.currency = #{currency}</if>
            <if test="bizProcessType != null "> and t.biz_process_type = #{bizProcessType}</if>
            <if test="hasProcess != null "> and t.has_process = #{hasProcess}</if>
        </where>
    </select>

    <select id="selectWmRightsByRightsId" parameterType="Long" resultMap="WmRightsResult">
        <include refid="selectWmRightsVo"/>
        where t.rights_id = #{rightsId}
    </select>

    <insert id="insertOrUpdateWmRights" parameterType="com.ruoyi.weimo.domain.WmRights">
        insert into wm_rights
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rightsId != null">rights_id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="vid != null">vid,</if>
            <if test="processVid != null">process_vid,</if>
            <if test="rightsStatus != null">rights_status,</if>
            <if test="rightsType != null">rights_type,</if>
            <if test="rightsSource != null">rights_source,</if>
            <if test="rightsCauseType != null">rights_cause_type,</if>
            <if test="refundType != null">refund_type,</if>
            <if test="currency != null">currency,</if>
            <if test="bizProcessType != null">biz_process_type,</if>
            <if test="hasProcess != null">has_process,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rightsId != null">#{rightsId},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="vid != null">#{vid},</if>
            <if test="processVid != null">#{processVid},</if>
            <if test="rightsStatus != null">#{rightsStatus},</if>
            <if test="rightsType != null">#{rightsType},</if>
            <if test="rightsSource != null">#{rightsSource},</if>
            <if test="rightsCauseType != null">#{rightsCauseType},</if>
            <if test="refundType != null">#{refundType},</if>
            <if test="currency != null">#{currency},</if>
            <if test="bizProcessType != null">#{bizProcessType},</if>
            <if test="hasProcess != null">#{hasProcess},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
        ON DUPLICATE KEY
        <trim prefix="UPDATE" suffixOverrides=",">
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="vid != null">vid = #{vid},</if>
            <if test="processVid != null">process_vid = #{processVid},</if>
            <if test="rightsStatus != null">rights_status = #{rightsStatus},</if>
            <if test="rightsType != null">rights_type = #{rightsType},</if>
            <if test="rightsSource != null">rights_source = #{rightsSource},</if>
            <if test="rightsCauseType != null">rights_cause_type = #{rightsCauseType},</if>
            <if test="refundType != null">refund_type = #{refundType},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="bizProcessType != null">biz_process_type = #{bizProcessType},</if>
            <if test="hasProcess != null">has_process = #{hasProcess},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
    </insert>

    <update id="updateWmRights" parameterType="com.ruoyi.weimo.domain.WmRights">
        update wm_rights
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="vid != null">vid = #{vid},</if>
            <if test="processVid != null">process_vid = #{processVid},</if>
            <if test="rightsStatus != null">rights_status = #{rightsStatus},</if>
            <if test="rightsType != null">rights_type = #{rightsType},</if>
            <if test="rightsSource != null">rights_source = #{rightsSource},</if>
            <if test="rightsCauseType != null">rights_cause_type = #{rightsCauseType},</if>
            <if test="refundType != null">refund_type = #{refundType},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="bizProcessType != null">biz_process_type = #{bizProcessType},</if>
            <if test="hasProcess != null">has_process = #{hasProcess},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where rights_id = #{rightsId}
    </update>

    <delete id="deleteWmRightsByRightsId" parameterType="Long">
        delete from wm_rights where rights_id = #{rightsId}
    </delete>

    <delete id="deleteWmRightsByRightsIds" parameterType="String">
        delete from wm_rights where rights_id in
        <foreach item="rightsId" collection="array" open="(" separator="," close=")">
            #{rightsId}
        </foreach>
    </delete>
</mapper>
