<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.weimo.mapper.WmUserMapper">

    <resultMap type="com.ruoyi.weimo.domain.WmUser" id="WmUserResult">
        <result property="wid"    column="wid"    />
        <result property="name"    column="name"    />
        <result property="nickname"    column="nickname"    />
        <result property="phone"    column="phone"    />
        <result property="headUrl"    column="head_url"    />
        <result property="unionId"    column="union_id"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.wid, t.name, t.nickname, t.phone, t.head_url, t.union_id    </sql>

    <sql id="selectWmUserVo">
        select <include refid="Base_Column_List"/>  from wm_user t
    </sql>

    <select id="selectWmUserList" parameterType="com.ruoyi.weimo.domain.WmUser" resultMap="WmUserResult">
        <include refid="selectWmUserVo"/>
        <where>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="nickname != null  and nickname != ''"> and t.nickname like concat('%', #{nickname}, '%')</if>
            <if test="phone != null  and phone != ''"> and t.phone = #{phone}</if>
            <if test="headUrl != null  and headUrl != ''"> and t.head_url = #{headUrl}</if>
            <if test="unionId != null  and unionId != ''"> and t.union_id = #{unionId}</if>
        </where>
    </select>

    <select id="selectWmUserByWid" parameterType="Long" resultMap="WmUserResult">
        <include refid="selectWmUserVo"/>
        where t.wid = #{wid}
    </select>

    <select id="selectWmUserNonUnionIdList" resultMap="WmUserResult">
        <include refid="selectWmUserVo"/>
        where  t.union_id is null
    </select>

    <insert id="insertWmUser" parameterType="com.ruoyi.weimo.domain.WmUser">
        insert into wm_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="wid != null">wid,</if>
            <if test="name != null">name,</if>
            <if test="nickname != null">nickname,</if>
            <if test="phone != null">phone,</if>
            <if test="headUrl != null">head_url,</if>
            <if test="unionId != null">union_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="wid != null">#{wid},</if>
            <if test="name != null">#{name},</if>
            <if test="nickname != null">#{nickname},</if>
            <if test="phone != null">#{phone},</if>
            <if test="headUrl != null">#{headUrl},</if>
            <if test="unionId != null">#{unionId},</if>
         </trim>
    </insert>

    <insert id="insertBatch">
        <foreach collection="list" item="user" separator=";">
            insert into wm_user
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="user.wid != null  ">
                    wid,
                </if>
                <if test="user.name != null and user.name != '' ">
                    `name`,
                </if>
                <if test="user.nickname != null and user.nickname != '' ">
                    nickname,
                </if>
                <if test="user.phone != null and user.phone != '' ">
                    `phone`,
                </if>
                <if test="user.headUrl != null and user.headUrl != '' ">
                    head_url,
                </if>
                <if test="user.createTime != null  ">
                    create_time,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="user.wid != null  ">
                    #{user.wid,jdbcType=BIGINT},
                </if>
                <if test="user.name != null and user.name != '' ">
                    #{user.name,jdbcType=VARCHAR},
                </if>
                <if test="user.nickname != null and user.nickname != '' ">
                    #{user.nickname,jdbcType=VARCHAR},
                </if>
                <if test="user.phone != null and user.phone != '' ">
                    #{user.phone,jdbcType=VARCHAR},
                </if>
                <if test="user.headUrl != null and user.headUrl != '' ">
                    #{user.headUrl,jdbcType=VARCHAR},
                </if>
                <if test="user.createTime != null  ">
                    #{user.createTime,jdbcType=TIMESTAMP},
                </if>
            </trim>
            on duplicate key update
            `name`=#{user.name},
            nickname=#{user.nickname},
            phone=#{user.phone},
            head_url=#{user.headUrl}
        </foreach>
    </insert>

    <update id="updateWmUser" parameterType="com.ruoyi.weimo.domain.WmUser">
        update wm_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="headUrl != null">head_url = #{headUrl},</if>
            <if test="unionId != null">union_id = #{unionId},</if>
        </trim>
        where wid = #{wid}
    </update>

    <delete id="deleteWmUserByWid" parameterType="Long">
        delete from wm_user where wid = #{wid}
    </delete>

    <delete id="deleteWmUserByWids" parameterType="String">
        delete from wm_user where wid in
        <foreach item="wid" collection="array" open="(" separator="," close=")">
            #{wid}
        </foreach>
    </delete>
</mapper>
