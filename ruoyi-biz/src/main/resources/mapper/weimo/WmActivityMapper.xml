<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.weimo.mapper.WmActivityMapper">

    <resultMap type="com.ruoyi.weimo.domain.WmActivity" id="WmActivityResult">
        <result property="id"    column="id"    />
        <result property="targetProductInstanceId"    column="target_product_instance_id"    />
        <result property="visitDuration"    column="visit_duration"    />
        <result property="cuid"    column="cuid"    />
        <result property="unionid"    column="unionid"    />
        <result property="sourceEnd"    column="source_end"    />
        <result property="openId"    column="open_id"    />
        <result property="ip"    column="ip"    />
        <result property="sessionId"    column="session_id"    />
        <result property="pageName"    column="page_name"    />
        <result property="url"    column="url"    />
        <result property="scene"    column="scene"    />
        <result property="vid"    column="vid"    />
        <result property="targetProductId"    column="target_product_id"    />
        <result property="wid"    column="wid"    />
        <result property="jsonData"    column="json_data"    />
        <result property="etSource"    column="et_source"    />
        <result property="createTime"    column="create_time"    />
        <result property="bosId"    column="bos_id"    />
        <result property="dataSource"    column="data_source"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.target_product_instance_id, t.visit_duration, t.cuid, t.unionid, t.source_end, t.open_id, t.ip, t.session_id, t.page_name, t.url, t.scene, t.vid, t.target_product_id, t.wid, t.json_data, t.et_source, t.create_time, t.bos_id, t.data_source    </sql>

    <sql id="selectWmActivityVo">
        select <include refid="Base_Column_List"/>  from wm_activity t
    </sql>

    <select id="selectWmActivityList" parameterType="com.ruoyi.weimo.domain.WmActivity" resultMap="WmActivityResult">
        <include refid="selectWmActivityVo"/>
        <where>
            <if test="targetProductInstanceId != null  and targetProductInstanceId != ''"> and t.target_product_instance_id = #{targetProductInstanceId}</if>
            <if test="visitDuration != null  and visitDuration != ''"> and t.visit_duration = #{visitDuration}</if>
            <if test="cuid != null  and cuid != ''"> and t.cuid = #{cuid}</if>
            <if test="unionid != null  and unionid != ''"> and t.unionid = #{unionid}</if>
            <if test="sourceEnd != null  and sourceEnd != ''"> and t.source_end = #{sourceEnd}</if>
            <if test="openId != null  and openId != ''"> and t.open_id = #{openId}</if>
            <if test="ip != null  and ip != ''"> and t.ip = #{ip}</if>
            <if test="sessionId != null  and sessionId != ''"> and t.session_id = #{sessionId}</if>
            <if test="pageName != null  and pageName != ''"> and t.page_name like concat('%', #{pageName}, '%')</if>
            <if test="url != null  and url != ''"> and t.url = #{url}</if>
            <if test="scene != null  and scene != ''"> and t.scene = #{scene}</if>
            <if test="vid != null  and vid != ''"> and t.vid = #{vid}</if>
            <if test="targetProductId != null  and targetProductId != ''"> and t.target_product_id = #{targetProductId}</if>
            <if test="wid != null  and wid != ''"> and t.wid = #{wid}</if>
            <if test="jsonData != null  and jsonData != ''"> and t.json_data = #{jsonData}</if>
            <if test="etSource != null "> and t.et_source = #{etSource}</if>
            <if test="bosId != null  and bosId != ''"> and t.bos_id = #{bosId}</if>
            <if test="dataSource != null  and dataSource != ''"> and t.data_source = #{dataSource}</if>
        </where>
    </select>

    <select id="selectWmActivityById" parameterType="Long" resultMap="WmActivityResult">
        <include refid="selectWmActivityVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertWmActivity" parameterType="com.ruoyi.weimo.domain.WmActivity" useGeneratedKeys="true" keyProperty="id">
        insert into wm_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="targetProductInstanceId != null">target_product_instance_id,</if>
            <if test="visitDuration != null">visit_duration,</if>
            <if test="cuid != null">cuid,</if>
            <if test="unionid != null">unionid,</if>
            <if test="sourceEnd != null">source_end,</if>
            <if test="openId != null">open_id,</if>
            <if test="ip != null">ip,</if>
            <if test="sessionId != null">session_id,</if>
            <if test="pageName != null">page_name,</if>
            <if test="url != null">url,</if>
            <if test="scene != null">scene,</if>
            <if test="vid != null">vid,</if>
            <if test="targetProductId != null">target_product_id,</if>
            <if test="wid != null">wid,</if>
            <if test="jsonData != null">json_data,</if>
            <if test="etSource != null">et_source,</if>
            <if test="createTime != null">create_time,</if>
            <if test="bosId != null">bos_id,</if>
            <if test="dataSource != null">data_source,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="targetProductInstanceId != null">#{targetProductInstanceId},</if>
            <if test="visitDuration != null">#{visitDuration},</if>
            <if test="cuid != null">#{cuid},</if>
            <if test="unionid != null">#{unionid},</if>
            <if test="sourceEnd != null">#{sourceEnd},</if>
            <if test="openId != null">#{openId},</if>
            <if test="ip != null">#{ip},</if>
            <if test="sessionId != null">#{sessionId},</if>
            <if test="pageName != null">#{pageName},</if>
            <if test="url != null">#{url},</if>
            <if test="scene != null">#{scene},</if>
            <if test="vid != null">#{vid},</if>
            <if test="targetProductId != null">#{targetProductId},</if>
            <if test="wid != null">#{wid},</if>
            <if test="jsonData != null">#{jsonData},</if>
            <if test="etSource != null">#{etSource},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="bosId != null">#{bosId},</if>
            <if test="dataSource != null">#{dataSource},</if>
         </trim>
    </insert>

    <insert id="batchInsertWmActivity" parameterType="java.util.List">
        insert into wm_activity (
        target_product_instance_id, visit_duration, cuid, unionid,
        source_end, open_id, ip, session_id, page_name, url,
        scene, vid, target_product_id, wid, json_data, et_source,
        create_time, bos_id, data_source
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.targetProductInstanceId},
            #{item.visitDuration},
            #{item.cuid},
            #{item.unionid},
            #{item.sourceEnd},
            #{item.openId},
            #{item.ip},
            #{item.sessionId},
            #{item.pageName},
            #{item.url},
            #{item.scene},
            #{item.vid},
            #{item.targetProductId},
            #{item.wid},
            #{item.jsonData},
            #{item.etSource},
            #{item.createTime},
            #{item.bosId},
            #{item.dataSource}
            )
        </foreach>
    </insert>

    <update id="updateWmActivity" parameterType="com.ruoyi.weimo.domain.WmActivity">
        update wm_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="targetProductInstanceId != null">target_product_instance_id = #{targetProductInstanceId},</if>
            <if test="visitDuration != null">visit_duration = #{visitDuration},</if>
            <if test="cuid != null">cuid = #{cuid},</if>
            <if test="unionid != null">unionid = #{unionid},</if>
            <if test="sourceEnd != null">source_end = #{sourceEnd},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="sessionId != null">session_id = #{sessionId},</if>
            <if test="pageName != null">page_name = #{pageName},</if>
            <if test="url != null">url = #{url},</if>
            <if test="scene != null">scene = #{scene},</if>
            <if test="vid != null">vid = #{vid},</if>
            <if test="targetProductId != null">target_product_id = #{targetProductId},</if>
            <if test="wid != null">wid = #{wid},</if>
            <if test="jsonData != null">json_data = #{jsonData},</if>
            <if test="etSource != null">et_source = #{etSource},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="bosId != null">bos_id = #{bosId},</if>
            <if test="dataSource != null">data_source = #{dataSource},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWmActivityById" parameterType="Long">
        delete from wm_activity where id = #{id}
    </delete>

    <delete id="deleteWmActivityByIds" parameterType="String">
        delete from wm_activity where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
