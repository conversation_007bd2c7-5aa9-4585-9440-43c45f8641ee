<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizChannelCodeMapper">

    <resultMap type="com.ruoyi.biz.domain.BizChannelCode" id="BizChannelCodeResult">
        <result property="id"    column="id"    />
        <result property="configId"    column="config_id"    />
        <result property="channelId"    column="channel_id"    />
        <result property="code"    column="code"    />
        <result property="qrUrl"    column="qr_url"    />
        <result property="type"    column="type"    />
        <result property="linkId"    column="link_id"    />
        <result property="linkUrl"    column="link_url"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.config_id, t.channel_id, t.code, t.qr_url, t.type, t.link_id, t.link_url    </sql>

    <sql id="selectBizChannelCodeVo">
        select <include refid="Base_Column_List"/>  from biz_channel_code t
    </sql>

    <select id="selectBizChannelCodeList" parameterType="com.ruoyi.biz.domain.BizChannelCode" resultMap="BizChannelCodeResult">
        <include refid="selectBizChannelCodeVo"/>
        <where>
            <if test="configId != null "> and t.config_id = #{configId}</if>
            <if test="channelId != null "> and t.channel_id = #{channelId}</if>
            <if test="code != null  and code != ''"> and t.code = #{code}</if>
            <if test="qrUrl != null  and qrUrl != ''"> and t.qr_url = #{qrUrl}</if>
            <if test="type != null  and type != ''"> and t.type = #{type}</if>
            <if test="linkId != null  and linkId != ''"> and t.link_id = #{linkId}</if>
            <if test="linkUrl != null  and linkUrl != ''"> and t.link_url = #{linkUrl}</if>
        </where>
        order by t.id desc
    </select>

    <select id="selectBizChannelCodeById" parameterType="Long" resultMap="BizChannelCodeResult">
        <include refid="selectBizChannelCodeVo"/>
        where t.id = #{id}
    </select>

    <select id="selectBizChannelCodeByCode" resultMap="BizChannelCodeResult">
        <include refid="selectBizChannelCodeVo"/>
        where t.code = #{code}
    </select>

    <insert id="insertBizChannelCode" parameterType="com.ruoyi.biz.domain.BizChannelCode" useGeneratedKeys="true" keyProperty="id">
        insert into biz_channel_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configId != null">config_id,</if>
            <if test="channelId != null">channel_id,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="qrUrl != null and qrUrl != ''">qr_url,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="linkId != null">link_id,</if>
            <if test="linkUrl != null">link_url,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configId != null">#{configId},</if>
            <if test="channelId != null">#{channelId},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="qrUrl != null and qrUrl != ''">#{qrUrl},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="linkId != null">#{linkId},</if>
            <if test="linkUrl != null">#{linkUrl},</if>
        </trim>
    </insert>

    <update id="updateBizChannelCode" parameterType="com.ruoyi.biz.domain.BizChannelCode">
        update biz_channel_code
        <trim prefix="SET" suffixOverrides=",">
            <if test="configId != null">config_id = #{configId},</if>
            <if test="channelId != null">channel_id = #{channelId},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="qrUrl != null and qrUrl != ''">qr_url = #{qrUrl},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="linkId != null">link_id = #{linkId},</if>
            <if test="linkUrl != null">link_url = #{linkUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizChannelCodeById" parameterType="Long">
        delete from biz_channel_code where id = #{id}
    </delete>

    <delete id="deleteBizChannelCodeByConfigId" parameterType="Long">
        delete from biz_channel_code where config_id = #{configId}
    </delete>

    <delete id="deleteBizChannelCodeByIds" parameterType="String">
        delete from biz_channel_code where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
