<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizNicheMapper">

    <resultMap type="com.ruoyi.biz.domain.BizNiche" id="BizNicheResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="keyword"    column="keyword"    />
        <result property="goodsId"    column="goods_id"    />
        <result property="heat"    column="heat"    />
        <result property="status"    column="status"    />
        <result property="clueId"    column="clue_id"    />
        <result property="userGroupId"    column="user_group_id"    />
        <result property="buyRate"    column="buy_rate"    />
        <result property="buyCount"    column="buy_count"    />
        <result property="totalCount"    column="total_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.name, t.keyword, t.goods_id, t.heat, t.status,t.clue_id,t.user_group_id, t.buy_rate, t.buy_count, t.total_count, t.create_by, t.create_time, t.update_by, t.update_time    </sql>

    <sql id="selectBizNicheVo">
        select <include refid="Base_Column_List"/>  from biz_niche t
    </sql>

    <select id="selectBizNicheList" parameterType="com.ruoyi.biz.domain.BizNiche" resultMap="BizNicheResult">
        <include refid="selectBizNicheVo"/>
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="keyword != null  and keyword != ''"> and t.keyword = #{keyword}</if>
            <if test="goodsId != null  and goodsId != ''"> and t.goods_id = #{goodsId}</if>
            <if test="heat != null  and heat != ''"> and t.heat = #{heat}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="clueId != null "> and t.clue_id = #{clueId}</if>
            <if test="userGroupId != null "> and t.user_group_id = #{userGroupId}</if>
            <if test="buyRate != null "> and t.buy_rate = #{buyRate}</if>
            <if test="buyCount != null "> and t.buy_count = #{buyCount}</if>
            <if test="totalCount != null "> and t.total_count = #{totalCount}</if>
        </where>
        order by t.id desc
    </select>

    <select id="selectBizNicheById" parameterType="Long" resultMap="BizNicheResult">
        <include refid="selectBizNicheVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizNiche" parameterType="com.ruoyi.biz.domain.BizNiche" useGeneratedKeys="true" keyProperty="id">
        insert into biz_niche
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="keyword != null and keyword != ''">keyword,</if>
            <if test="goodsId != null and goodsId != ''">goods_id,</if>
            <if test="heat != null">heat,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="clueId != null">clue_id,</if>
            <if test="userGroupId != null">user_group_id,</if>
            <if test="buyRate != null">buy_rate,</if>
            <if test="buyCount != null">buy_count,</if>
            <if test="totalCount != null">total_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="keyword != null and keyword != ''">#{keyword},</if>
            <if test="goodsId != null and goodsId != ''">#{goodsId},</if>
            <if test="heat != null">#{heat},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="clueId != null">#{clueId},</if>
            <if test="userGroupId != null">#{userGroupId},</if>
            <if test="buyRate != null">#{buyRate},</if>
            <if test="buyCount != null">#{buyCount},</if>
            <if test="totalCount != null">#{totalCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateBizNiche" parameterType="com.ruoyi.biz.domain.BizNiche">
        update biz_niche
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="keyword != null and keyword != ''">keyword = #{keyword},</if>
            <if test="goodsId != null and goodsId != ''">goods_id = #{goodsId},</if>
            <if test="heat != null">heat = #{heat},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="buyRate != null">buy_rate = #{buyRate},</if>
            <if test="buyCount != null">buy_count = #{buyCount},</if>
            <if test="totalCount != null">total_count = #{totalCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizNicheById" parameterType="Long">
        delete from biz_niche where id = #{id}
    </delete>

    <delete id="deleteBizNicheByIds" parameterType="String">
        delete from biz_niche where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
