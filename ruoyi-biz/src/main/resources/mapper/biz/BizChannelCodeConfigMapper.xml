<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizChannelCodeConfigMapper">

    <resultMap type="com.ruoyi.biz.domain.BizChannelCodeConfig" id="BizChannelCodeConfigResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="planId"    column="plan_id"    />
        <result property="goodsId"    column="goods_id"    />
        <result property="botList"    column="bot_list"    />
        <result property="groupChatList"    column="group_chat_list"    />
        <result property="distributor"    column="distributor"    />
        <result property="checkBuyMember"    column="check_buy_member"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.name, t.type, t.plan_id, t.goods_id, t.bot_list, t.group_chat_list, t.distributor, t.check_buy_member, t.create_by, t.create_time    </sql>

    <sql id="selectBizChannelCodeConfigVo">
        select <include refid="Base_Column_List"/>  from biz_channel_code_config t
    </sql>

    <select id="selectBizChannelCodeConfigList" parameterType="com.ruoyi.biz.domain.BizChannelCodeConfig" resultMap="BizChannelCodeConfigResult">
        <include refid="selectBizChannelCodeConfigVo"/>
        <if test="tagSearchIdList != null  and tagSearchIdList.size() > 0  and tagSearchType != null ">
            left join `biz_taggable` tag ON t.`id` = tag.`ref_id` AND (tag.`ref_type` ='channel_config' or tag.`ref_type` ='channel')
        </if>
        <where>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="type != null "> and t.type = #{type}</if>
            <if test="planId != null "> and t.plan_id = #{planId}</if>
            <if test="goodsId != null "> and t.goods_id = #{goodsId}</if>
            <if test="botList != null  and botList != ''"> and t.bot_list = #{botList}</if>
            <if test="distributor != null  and distributor != ''"> and t.distributor = #{distributor}</if>
            <if test="checkBuyMember != null"> and t.check_buy_member = #{checkBuyMember}</if>
            <if test="tagSearchIdList != null  and tagSearchIdList.size() > 0  and tagSearchType != null ">
                and tag.tag_id in
                <foreach collection="tagSearchIdList" open="(" close=")" separator="," item="tagId" >
                    #{tagId}
                </foreach>
            </if>
            <if test="displayDistribution != null and !displayDistribution">
                and t.type != '99'
            </if>
        </where>
        <if test="tagSearchIdList != null  and tagSearchIdList.size() > 0  and tagSearchType != null ">
            <if test="tagSearchType == '0'.toString()">
                GROUP BY t.`id`  HAVING COUNT(1) > 0
            </if>
            <if test="tagSearchType == '1'.toString()">
                GROUP BY t.`id`  HAVING COUNT(1) =  ${tagSearchIdList.size()}
            </if>
        </if>
        order by t.id desc
    </select>

    <select id="selectBizChannelCodeConfigById" parameterType="Long" resultMap="BizChannelCodeConfigResult">
        <include refid="selectBizChannelCodeConfigVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizChannelCodeConfig" parameterType="com.ruoyi.biz.domain.BizChannelCodeConfig" useGeneratedKeys="true" keyProperty="id">
        insert into biz_channel_code_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="planId != null">plan_id,</if>
            <if test="goodsId != null">goods_id,</if>
            <if test="botList != null and botList != ''">bot_list,</if>
            <if test="groupChatList != null and groupChatList != ''">group_chat_list,</if>
            <if test="distributor != null and distributor != ''">distributor,</if>
            <if test="checkBuyMember != null ">check_buy_member,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="planId != null">#{planId},</if>
            <if test="goodsId != null">#{goodsId},</if>
            <if test="botList != null and botList != ''">#{botList},</if>
            <if test="groupChatList != null and groupChatList != ''">#{groupChatList},</if>
            <if test="distributor != null and distributor != ''">#{distributor},</if>
            <if test="checkBuyMember != null">#{checkBuyMember},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateBizChannelCodeConfig" parameterType="com.ruoyi.biz.domain.BizChannelCodeConfig">
        update biz_channel_code_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="goodsId != null">goods_id = #{goodsId},</if>
            <if test="botList != null and botList != ''">bot_list = #{botList},</if>
            <if test="groupChatList != null and groupChatList != ''">group_chat_list = #{groupChatList},</if>
            <if test="distributor != null and distributor != ''">distributor = #{distributor},</if>
            <if test="checkBuyMember != null ">check_buy_member = #{checkBuyMember},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizChannelCodeConfigById" parameterType="Long">
        delete from biz_channel_code_config where id = #{id}
    </delete>

    <delete id="deleteBizChannelCodeConfigByIds" parameterType="String">
        delete from biz_channel_code_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
