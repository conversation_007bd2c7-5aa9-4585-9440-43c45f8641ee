<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.CorpusDocumentMapper">

    <resultMap type="com.ruoyi.biz.domain.CorpusDocument" id="CorpusDocumentResult">
        <result property="id"    column="id"    />
        <result property="knowledgeEnum"    column="knowledge_enum"    />
        <result property="datasetId"    column="dataset_id"    />
        <result property="datasetType"    column="dataset_type"    />
        <result property="name"    column="name"    />
        <result property="docForm"    column="doc_form"    />
        <result property="metadata"    column="metadata"    />
        <result property="contentData"    column="content_data"    />
        <result property="createTime"    column="create_time"    />
        <result property="isDisclose"    column="is_disclose"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.knowledge_enum, t.dataset_id, t.dataset_type, t.name, t.doc_form, t.metadata, t.content_data, t.create_time, t.is_disclose    </sql>

    <sql id="selectCorpusDocumentVo">
        select <include refid="Base_Column_List"/>  from corpus_document t
    </sql>

    <select id="selectCorpusDocumentList" parameterType="com.ruoyi.biz.domain.CorpusDocument" resultMap="CorpusDocumentResult">
        <include refid="selectCorpusDocumentVo"/>
        <where>
            <if test="knowledgeEnum != null  and knowledgeEnum != ''"> and t.knowledge_enum = #{knowledgeEnum}</if>
            <if test="datasetId != null  and datasetId != ''"> and t.dataset_id = #{datasetId}</if>
            <if test="datasetType != null  and datasetType != ''"> and t.dataset_type = #{datasetType}</if>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="docForm != null  and docForm != ''"> and t.doc_form = #{docForm}</if>
            <if test="metadata != null  and metadata != ''"> and t.metadata = #{metadata}</if>
            <if test="contentData != null  and contentData != ''"> and t.content_data = #{contentData}</if>
            <if test="isDisclose != null "> and t.is_disclose = #{isDisclose}</if>
        </where>
        order by id desc
    </select>

    <select id="selectCorpusDocumentById" parameterType="Long" resultMap="CorpusDocumentResult">
        <include refid="selectCorpusDocumentVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertCorpusDocument" parameterType="com.ruoyi.biz.domain.CorpusDocument" useGeneratedKeys="true" keyProperty="id">
        insert into corpus_document
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null  ">id,</if>
            <if test="knowledgeEnum != null and knowledgeEnum != ''">knowledge_enum,</if>
            <if test="datasetId != null and datasetId != ''">dataset_id,</if>
            <if test="datasetType != null and datasetType != ''">dataset_type,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="docForm != null and docForm != ''">doc_form,</if>
            <if test="metadata != null">metadata,</if>
            <if test="contentData != null and contentData != ''">content_data,</if>
            <if test="createTime != null">create_time,</if>
            <if test="isDisclose != null">is_disclose,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null  ">#{id},</if>
            <if test="knowledgeEnum != null and knowledgeEnum != ''">#{knowledgeEnum},</if>
            <if test="datasetId != null and datasetId != ''">#{datasetId},</if>
            <if test="datasetType != null and datasetType != ''">#{datasetType},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="docForm != null and docForm != ''">#{docForm},</if>
            <if test="metadata != null">#{metadata},</if>
            <if test="contentData != null and contentData != ''">#{contentData},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="isDisclose != null">#{isDisclose},</if>
        </trim>
    </insert>

    <update id="updateCorpusDocument" parameterType="com.ruoyi.biz.domain.CorpusDocument">
        update corpus_document
        <trim prefix="SET" suffixOverrides=",">
            <if test="knowledgeEnum != null and knowledgeEnum != ''">knowledge_enum = #{knowledgeEnum},</if>
            <if test="datasetId != null and datasetId != ''">dataset_id = #{datasetId},</if>
            <if test="datasetType != null and datasetType != ''">dataset_type = #{datasetType},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="docForm != null and docForm != ''">doc_form = #{docForm},</if>
            <if test="metadata != null">metadata = #{metadata},</if>
            <if test="contentData != null and contentData != ''">content_data = #{contentData},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="isDisclose != null">is_disclose = #{isDisclose},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCorpusDocumentById" parameterType="Long">
        delete from corpus_document where id = #{id}
    </delete>

    <delete id="deleteCorpusDocumentByIds" parameterType="String">
        delete from corpus_document where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <insert id="batchInsertOrUpdate" parameterType="java.util.List" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO corpus_document (id,
        knowledge_enum,
        dataset_id,
        dataset_type,
        name,
        doc_form,
        metadata,
        content_data,
        create_time)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.knowledgeEnum}, #{item.datasetId}, #{item.datasetType}, #{item.name},
            #{item.docForm}, #{item.metadata}, #{item.contentData}, #{item.createTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        content_data = VALUES(content_data)

    </insert>
</mapper>
