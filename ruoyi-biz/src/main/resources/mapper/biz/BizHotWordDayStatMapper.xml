<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizHotWordDayStatMapper">

    <resultMap type="com.ruoyi.biz.domain.BizHotWordDayStat" id="BizHotWordDayStatResult">
        <result property="id" column="id"/>
        <result property="dayTime" column="day_time"/>
        <result property="type" column="type"/>
        <result property="wordSort" column="word_sort"/>
        <result property="business" column="business"/>
        <result property="waitAiWord" column="wait_ai_word"/>
        <result property="aiFinishWord" column="ai_finish_word"/>
        <result property="isHandle" column="is_handle"/>
        <result property="createDatetime" column="create_datetime"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.day_time,t.type, t.word_sort, t.business, t.wait_ai_word, t.ai_finish_word, t.is_handle, t.create_datetime, t.update_time    </sql>

    <sql id="selectBizHotWordDayStatVo">
        select
        <include refid="Base_Column_List"/>
        from biz_hot_word_day_stat t
    </sql>

    <sql id="selectBizHotWordDayStatVoByRoom">
        select
        <include refid="Base_Column_List"/>
        from biz_hot_word_day_stat t
    </sql>

    <select id="selectBizHotWordDayStatList" parameterType="com.ruoyi.biz.domain.BizHotWordDayStat"
            resultMap="BizHotWordDayStatResult">
        <include refid="selectBizHotWordDayStatVo"/>
        <where>
            <if test="dayTime != null  and dayTime != ''">and t.day_time = #{dayTime}</if>
            <if test="params.beginDayTime != null and params.beginDayTime != '' and params.endDayTime != null and params.endDayTime != ''">
                and t.day_time between #{params.beginDayTime} and #{params.endDayTime}
            </if>
            <if test="type != null  and type != ''">and t.type = #{type}</if>
            <if test="isHandle != null  and isHandle != ''">and t.is_handle = #{isHandle}</if>
            <if test="params.beginCreateDatetime != null and params.beginCreateDatetime != '' and params.endCreateDatetime != null and params.endCreateDatetime != ''">
                and t.create_datetime between #{params.beginCreateDatetime} and #{params.endCreateDatetime}
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and t.update_time between #{params.beginUpdateTime} and #{params.endUpdateTime}
            </if>
        </where>
        order by t.id desc
    </select>

    <select id="selectBizHotWordDayStatById" parameterType="Long" resultMap="BizHotWordDayStatResult">
        <include refid="selectBizHotWordDayStatVo"/>
        where t.id = #{id}
    </select>

    <select id="selectBizHotWordDayStatListByTime" resultType="com.ruoyi.biz.domain.dto.BizHotWordClueDto"
            resultMap="BizHotWordDayStatResult">
        /*根据create_datetime区间查询*/
        <include refid="selectBizHotWordDayStatVoByRoom"/>
        <where>
            <if test="startingTime != null  and endingTime != ''">
                t.create_datetime between #{startingTime} and #{endingTime}
                and t.type= #{type}
            </if>

        </where>
    </select>

    <insert id="insertBizHotWordDayStat" parameterType="com.ruoyi.biz.domain.BizHotWordDayStat" useGeneratedKeys="true"
            keyProperty="id">
        insert into biz_hot_word_day_stat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dayTime != null and dayTime != ''">day_time,</if>
            <if test="type != null and type != ''">`type`,</if>
            <if test="wordSort != null and wordSort != ''">word_sort,</if>
            <if test="business != null and business != ''">business,</if>
            <if test="waitAiWord != null">wait_ai_word,</if>
            <if test="aiFinishWord != null">ai_finish_word,</if>
            <if test="isHandle != null">is_handle,</if>
            <if test="createDatetime != null">create_datetime,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dayTime != null and dayTime != ''">#{dayTime},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="wordSort != null and wordSort != ''">#{wordSort},</if>
            <if test="business != null and business != ''">#{business},</if>
            <if test="waitAiWord != null">#{waitAiWord},</if>
            <if test="aiFinishWord != null">#{aiFinishWord},</if>
            <if test="isHandle != null">#{isHandle},</if>
            <if test="createDatetime != null">#{createDatetime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateBizHotWordDayStat" parameterType="com.ruoyi.biz.domain.BizHotWordDayStat">
        update biz_hot_word_day_stat
        <trim prefix="SET" suffixOverrides=",">
            <if test="dayTime != null and dayTime != ''">day_time = #{dayTime},</if>
            <if test="wordSort != null and wordSort != ''">word_sort = #{wordSort},</if>
            <if test="business != null and business != ''">business = #{business},</if>
            <if test="waitAiWord != null">wait_ai_word = #{waitAiWord},</if>
            <if test="aiFinishWord != null">ai_finish_word = #{aiFinishWord},</if>
            <if test="isHandle != null">is_handle = #{isHandle},</if>
            <if test="createDatetime != null">create_datetime = #{createDatetime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizHotWordDayStatById" parameterType="Long">
        delete
        from biz_hot_word_day_stat
        where id = #{id}
    </delete>

    <delete id="deleteBizHotWordDayStatByIds" parameterType="String">
        delete from biz_hot_word_day_stat where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
