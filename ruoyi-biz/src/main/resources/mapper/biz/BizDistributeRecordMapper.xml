<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizDistributeRecordMapper">

    <resultMap type="com.ruoyi.biz.domain.BizDistributeRecord" id="BizDistributeRecordResult">
        <result property="id"    column="id"    />
        <result property="upstream"    column="upstream"    />
        <result property="downstream"    column="downstream"    />
        <result property="botId"    column="bot_id"    />
        <result property="boughtMember"    column="bought_member"    />
        <result property="boundRelation"    column="bound_relation"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.upstream, t.downstream, t.bot_id, t.bought_member, t.bound_relation, t.create_time, t.create_by, t.update_time, t.update_by    </sql>

    <sql id="selectBizDistributeRecordVo">
        select <include refid="Base_Column_List"/>  from biz_distribute_record t
    </sql>

    <select id="selectBizDistributeRecordList" parameterType="com.ruoyi.biz.domain.BizDistributeRecord" resultMap="BizDistributeRecordResult">
        <include refid="selectBizDistributeRecordVo"/>
        <where>
            <if test="upstream != null  and upstream != ''"> and t.upstream = #{upstream}</if>
            <if test="downstream != null  and downstream != ''"> and t.downstream = #{downstream}</if>
            <if test="botId != null  and botId != ''"> and t.bot_id = #{botId}</if>
            <if test="boughtMember != null "> and t.bought_member = #{boughtMember}</if>
            <if test="boundRelation != null "> and t.bound_relation = #{boundRelation}</if>
        </where>
    </select>

    <select id="selectBizDistributeRecordById" parameterType="Long" resultMap="BizDistributeRecordResult">
        <include refid="selectBizDistributeRecordVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizDistributeRecord" parameterType="com.ruoyi.biz.domain.BizDistributeRecord" useGeneratedKeys="true" keyProperty="id">
        insert into biz_distribute_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="upstream != null and upstream != ''">upstream,</if>
            <if test="downstream != null and downstream != ''">downstream,</if>
            <if test="botId != null and botId != ''">bot_id,</if>
            <if test="boughtMember != null">bought_member,</if>
            <if test="boundRelation != null">bound_relation,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="upstream != null and upstream != ''">#{upstream},</if>
            <if test="downstream != null and downstream != ''">#{downstream},</if>
            <if test="botId != null and botId != ''">#{botId},</if>
            <if test="boughtMember != null">#{boughtMember},</if>
            <if test="boundRelation != null">#{boundRelation},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>

        on duplicate key update
        <trim suffixOverrides=",">
            update_time = now()
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
    </insert>

    <update id="updateBizDistributeRecord" parameterType="com.ruoyi.biz.domain.BizDistributeRecord">
        update biz_distribute_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="upstream != null and upstream != ''">upstream = #{upstream},</if>
            <if test="downstream != null and downstream != ''">downstream = #{downstream},</if>
            <if test="botId != null and botId != ''">bot_id = #{botId},</if>
            <if test="boughtMember != null">bought_member = #{boughtMember},</if>
            <if test="boundRelation != null">bound_relation = #{boundRelation},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateByDownstream" parameterType="com.ruoyi.biz.domain.BizDistributeRecord">
        update biz_distribute_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="upstream != null and upstream != ''">upstream = #{upstream},</if>
            <if test="botId != null and botId != ''">bot_id = #{botId},</if>
            <if test="boughtMember != null">bought_member = #{boughtMember},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where downstream = #{downstream}
    </update>

    <delete id="deleteBizDistributeRecordById" parameterType="Long">
        delete from biz_distribute_record where id = #{id}
    </delete>

    <delete id="deleteBizDistributeRecordByIds" parameterType="String">
        delete from biz_distribute_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByDownstream" parameterType="com.ruoyi.biz.domain.BizDistributeRecord" resultMap="BizDistributeRecordResult">
        <include refid="selectBizDistributeRecordVo"/>
        where t.downstream = #{downstream}
    </select>

    <select id="findWaitOpenAIUserGenerateToken">
        INSERT INTO `biz_membership_token`(union_id,source_id,source_type,membership_type,purchase_date,create_by)
        SELECT t.`union_id` union_id,t.`chat_id` AS source_id ,'system' source_type,'1' membership_type,NOW() purchase_date,'默认激活' AS create_by
        FROM `jz_contact` t WHERE t.`status` ='0' AND t.`deleted` ='0'
        AND t.`chat_id` NOT IN (
            SELECT m.`source_id`  FROM  `biz_membership_token` m
        )
    </select>
</mapper>
