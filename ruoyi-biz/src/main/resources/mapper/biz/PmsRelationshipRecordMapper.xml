<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.PmsRelationshipRecordMapper">

    <resultMap type="com.ruoyi.system.domain.PmsRelationshipRecord" id="PmsRelationshipRecordResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="productName"    column="product_name"    />
        <result property="relProductId"    column="rel_product_id"    />
        <result property="relProductName"    column="rel_product_name"    />
        <result property="beforeScore"    column="before_score"    />
        <result property="currentScore"    column="current_score"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.product_id, t.product_name, t.rel_product_id, t.rel_product_name, t.before_score, t.current_score, t.create_time, t.create_by, t.update_time, t.update_by    </sql>

    <sql id="selectPmsRelationshipRecordVo">
        select <include refid="Base_Column_List"/>  from pms_relationship_record t
    </sql>

    <select id="selectPmsRelationshipRecordList" parameterType="com.ruoyi.system.domain.PmsRelationshipRecord" resultMap="PmsRelationshipRecordResult">
        <include refid="selectPmsRelationshipRecordVo"/>
        <where>
            <if test="productId != null "> and t.product_id = #{productId}</if>
            <if test="productName != null "> and t.product_name like concat('%', #{productName}, '%')</if>
            <if test="relProductId != null "> and t.rel_product_id = #{relProductId}</if>
            <if test="relProductName != null  and relProductName != ''"> and t.rel_product_name like concat('%', #{relProductName}, '%')</if>
            <if test="beforeScore != null "> and t.before_score = #{beforeScore}</if>
            <if test="currentScore != null "> and t.current_score = #{currentScore}</if>
        </where>
    </select>

    <select id="selectPmsRelationshipRecordById" parameterType="Long" resultMap="PmsRelationshipRecordResult">
        <include refid="selectPmsRelationshipRecordVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertPmsRelationshipRecord" parameterType="com.ruoyi.system.domain.PmsRelationshipRecord" useGeneratedKeys="true" keyProperty="id">
        insert into pms_relationship_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="productName != null">product_name,</if>
            <if test="relProductId != null">rel_product_id,</if>
            <if test="relProductName != null">rel_product_name,</if>
            <if test="beforeScore != null">before_score,</if>
            <if test="currentScore != null">current_score,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="productName != null">#{productName},</if>
            <if test="relProductId != null">#{relProductId},</if>
            <if test="relProductName != null">#{relProductName},</if>
            <if test="beforeScore != null">#{beforeScore},</if>
            <if test="currentScore != null">#{currentScore},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updatePmsRelationshipRecord" parameterType="com.ruoyi.system.domain.PmsRelationshipRecord">
        update pms_relationship_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="relProductId != null">rel_product_id = #{relProductId},</if>
            <if test="relProductName != null">rel_product_name = #{relProductName},</if>
            <if test="beforeScore != null">before_score = #{beforeScore},</if>
            <if test="currentScore != null">current_score = #{currentScore},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePmsRelationshipRecordById" parameterType="Long">
        delete from pms_relationship_record where id = #{id}
    </delete>

    <delete id="deletePmsRelationshipRecordByIds" parameterType="String">
        delete from pms_relationship_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
