<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizPlanStatisticsMapper">

    <resultMap type="com.ruoyi.biz.domain.BizPlanStatistics" id="BizPlanStatisticsResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="planId"    column="plan_id"    />
        <result property="pointId"    column="point_id"    />
        <result property="recordId"    column="record_id"    />
        <result property="reachCount"    column="reach_count"    />
        <result property="replayCount"    column="replay_count"    />
        <result property="replayRate"    column="replay_rate"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.name, t.type, t.plan_id, t.point_id, t.record_id, t.reach_count, t.replay_count, t.replay_rate, t.create_time, t.update_time    </sql>

    <sql id="selectBizPlanStatisticsVo">
        select <include refid="Base_Column_List"/>  from biz_plan_statistics t
    </sql>

    <select id="selectBizPlanStatisticsList" parameterType="com.ruoyi.biz.domain.BizPlanStatistics" resultMap="BizPlanStatisticsResult">
        <include refid="selectBizPlanStatisticsVo"/>
        <where>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and t.type = #{type}</if>
            <if test="planId != null "> and t.plan_id = #{planId}</if>
            <if test="pointId != null "> and t.point_id</if>
            <if test="reachCount != null "> and t.reach_count = #{reachCount}</if>
            <if test="replayCount != null "> and t.replay_count = #{replayCount}</if>
            <if test="replayRate != null  and replayRate != ''"> and t.replay_rate = #{replayRate}</if>
            <if test="createTime != null "> and t.create_time = #{createTime}</if>
        </where>
    </select>

    <select id="selectBizPlanStatisticsById" parameterType="Long" resultMap="BizPlanStatisticsResult">
        <include refid="selectBizPlanStatisticsVo"/>
        where t.id = #{id}
    </select>
    <select id="selectBizPlanStatisticsByRecordId" parameterType="Long" resultMap="BizPlanStatisticsResult">
        <include refid="selectBizPlanStatisticsVo"/>
        where t.record_id = #{recordId}
    </select>

    <insert id="insertBizPlanStatistics" parameterType="com.ruoyi.biz.domain.BizPlanStatistics">
        insert into biz_plan_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="planId != null">plan_id,</if>
            <if test="pointId != null">point_id,</if>
            <if test="recordId != null">record_id,</if>
            <if test="reachCount != null">reach_count,</if>
            <if test="replayCount != null">replay_count,</if>
            <if test="replayRate != null">replay_rate,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="planId != null">#{planId},</if>
            <if test="pointId != null">#{pointId},</if>
            <if test="recordId != null">#{recordId},</if>
            <if test="reachCount != null">#{reachCount},</if>
            <if test="replayCount != null">#{replayCount},</if>
            <if test="replayRate != null">#{replayRate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
        ON DUPLICATE KEY UPDATE
        plan_id = IFNULL(values(plan_id), plan_id),
        point_id = IFNULL(values(point_id), point_id),
        record_id = IFNULL(values(record_id), record_id),
        reach_count = IFNULL(values(reach_count), reach_count),
        replay_count = IFNULL(values(replay_count), replay_count),
        replay_rate = IFNULL(values(replay_rate), replay_rate),
        update_time = values(update_time)
    </insert>

    <update id="updateBizPlanStatistics" parameterType="com.ruoyi.biz.domain.BizPlanStatistics">
        update biz_plan_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="pointId != null">point_id = #{pointId},</if>
            <if test="recordId != null">record_id = #{recordId},</if>
            <if test="reachCount != null">reach_count = #{reachCount},</if>
            <if test="replayCount != null">replay_count = #{replayCount},</if>
            <if test="replayRate != null">replay_rate = #{replayRate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizPlanStatisticsById" parameterType="Long">
        delete from biz_plan_statistics where id = #{id}
    </delete>

    <delete id="deleteBizPlanStatisticsByIds" parameterType="String">
        delete from biz_plan_statistics where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
