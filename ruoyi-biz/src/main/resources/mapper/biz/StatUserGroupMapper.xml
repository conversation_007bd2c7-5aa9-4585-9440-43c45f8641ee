<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.StatUserGroupMapper">

    <resultMap type="com.ruoyi.biz.domain.StatUserGroup" id="StatUserGroupResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="target" column="target"/>
        <result property="chatIdList" column="chat_id_list"/>
        <result property="createTime" column="create_time"/>
        <result property="sourceId" column="source_id"/>
        <result property="sourceType" column="source_type"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.name, t.target, t.chat_id_list, t.create_time, t.source_id, t.source_type    </sql>

    <sql id="selectStatUserGroupVo">
        select
        <include refid="Base_Column_List"/>
        from stat_user_group t
    </sql>

    <select id="selectStatUserGroupList" parameterType="com.ruoyi.biz.domain.StatUserGroup"
            resultMap="StatUserGroupResult">
        <include refid="selectStatUserGroupVo"/>
        <where>
            <if test="name != null  and name != ''">and t.name like concat('%', #{name}, '%')</if>
            <if test="target != null  and target != ''">and t.target = #{target}</if>
            <if test="sourceId != null ">and t.source_id = #{sourceId}</if>
            <if test="sourceType != null  and sourceType != ''">and t.source_type = #{sourceType}</if>
            <if test="idList != null  and idList.size() >0 != ''">
                and t.id in
                <foreach collection="idList" open="(" close=")" separator="," item="id">
                    #{id}
                </foreach>
            </if>
        </where>
        order by t.id desc
    </select>

    <select id="selectStatUserGroupById" parameterType="Long" resultMap="StatUserGroupResult">
        <include refid="selectStatUserGroupVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertStatUserGroup" parameterType="com.ruoyi.biz.domain.StatUserGroup" useGeneratedKeys="true"
            keyProperty="id">
        insert into stat_user_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="target != null and target != ''">target,</if>
            <if test="chatIdList != null and chatIdList != ''">chat_id_list,</if>
            <if test="createTime != null">create_time,</if>
            <if test="sourceId != null">source_id,</if>
            <if test="sourceType != null and sourceType != ''">source_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="target != null and target != ''">#{target},</if>
            <if test="chatIdList != null and chatIdList != ''">#{chatIdList},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="sourceId != null">#{sourceId},</if>
            <if test="sourceType != null and sourceType != ''">#{sourceType},</if>
        </trim>
    </insert>

    <update id="updateStatUserGroup" parameterType="com.ruoyi.biz.domain.StatUserGroup">
        update stat_user_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="target != null and target != ''">target = #{target},</if>
            <if test="chatIdList != null and chatIdList != ''">chat_id_list = #{chatIdList},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="sourceId != null">source_id = #{sourceId},</if>
            <if test="sourceType != null and sourceType != ''">source_type = #{sourceType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStatUserGroupById" parameterType="Long">
        delete
        from stat_user_group
        where id = #{id}
    </delete>

    <delete id="deleteStatUserGroupByIds" parameterType="String">
        delete from stat_user_group where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
