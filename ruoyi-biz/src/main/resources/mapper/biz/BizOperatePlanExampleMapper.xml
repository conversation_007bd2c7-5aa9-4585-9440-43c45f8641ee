<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizOperatePlanExampleMapper">

    <resultMap type="com.ruoyi.biz.domain.BizOperatePlanExample" id="BizOperatePlanExampleResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="suitKeyword"    column="suit_keyword"    />
        <result property="suitCategory"    column="suit_category"    />
        <result property="suitProduct"    column="suit_product"    />
        <result property="suitCategoryName"    column="suit_category_name"    />
        <result property="suitProductName"    column="suit_product_name"    />
        <result property="example"    column="example"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.name, t.suit_keyword, t.suit_category, t.suit_product, t.suit_category_name, t.suit_product_name, t.example, t.create_time, t.create_by, t.update_time, t.update_by    </sql>

    <sql id="selectBizOperatePlanExampleVo">
        select <include refid="Base_Column_List"/>  from biz_operate_plan_example t
    </sql>

    <select id="selectBizOperatePlanExampleList" parameterType="com.ruoyi.biz.domain.BizOperatePlanExample" resultMap="BizOperatePlanExampleResult">
        <include refid="selectBizOperatePlanExampleVo"/>
        <where>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="suitKeyword != null  and suitKeyword != ''"> and t.suit_keyword = #{suitKeyword}</if>
            <if test="suitCategory != null  and suitCategory != ''"> and t.suit_category = #{suitCategory}</if>
            <if test="suitProduct != null  and suitProduct != ''"> and t.suit_product = #{suitProduct}</if>
            <if test="suitCategoryName != null  and suitCategoryName != ''"> and t.suit_category_name like concat('%', #{suitCategoryName}, '%')</if>
            <if test="suitProductName != null  and suitProductName != ''"> and t.suit_product_name like concat('%', #{suitProductName}, '%')</if>
            <if test="example != null  and example != ''"> and t.example = #{example}</if>
        </where>
    </select>

    <select id="selectBizOperatePlanExampleById" parameterType="Long" resultMap="BizOperatePlanExampleResult">
        <include refid="selectBizOperatePlanExampleVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizOperatePlanExample" parameterType="com.ruoyi.biz.domain.BizOperatePlanExample" useGeneratedKeys="true" keyProperty="id">
        insert into biz_operate_plan_example
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="suitKeyword != null">suit_keyword,</if>
            <if test="suitCategory != null">suit_category,</if>
            <if test="suitProduct != null">suit_product,</if>
            <if test="suitCategoryName != null">suit_category_name,</if>
            <if test="suitProductName != null">suit_product_name,</if>
            <if test="example != null and example != ''">example,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="suitKeyword != null">#{suitKeyword},</if>
            <if test="suitCategory != null">#{suitCategory},</if>
            <if test="suitProduct != null">#{suitProduct},</if>
            <if test="suitCategoryName != null">#{suitCategoryName},</if>
            <if test="suitProductName != null">#{suitProductName},</if>
            <if test="example != null and example != ''">#{example},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateBizOperatePlanExample" parameterType="com.ruoyi.biz.domain.BizOperatePlanExample">
        update biz_operate_plan_example
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="suitKeyword != null">suit_keyword = #{suitKeyword},</if>
            <if test="suitCategory != null">suit_category = #{suitCategory},</if>
            <if test="suitProduct != null">suit_product = #{suitProduct},</if>
            <if test="suitCategoryName != null">suit_category_name = #{suitCategoryName},</if>
            <if test="suitProductName != null">suit_product_name = #{suitProductName},</if>
            <if test="example != null and example != ''">example = #{example},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizOperatePlanExampleById" parameterType="Long">
        delete from biz_operate_plan_example where id = #{id}
    </delete>

    <delete id="deleteBizOperatePlanExampleByIds" parameterType="String">
        delete from biz_operate_plan_example where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="matchExampleByCategoryId" parameterType="String" resultMap="BizOperatePlanExampleResult">
        <include refid="selectBizOperatePlanExampleVo"/>
        WHERE JSON_CONTAINS(suit_category, #{categoryId})
        order by id desc limit 1
    </select>

    <select id="matchExampleByProductId" parameterType="String" resultMap="BizOperatePlanExampleResult">
        <include refid="selectBizOperatePlanExampleVo"/>
        WHERE JSON_CONTAINS(suit_product, #{productId})
        order by id desc limit 1
    </select>

</mapper>
