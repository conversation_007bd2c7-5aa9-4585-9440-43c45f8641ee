<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizCollectProductMapper">

    <resultMap type="com.ruoyi.biz.domain.BizCollectProduct" id="BizCollectProductResult">
        <result property="id"    column="id"    />
        <result property="refId"    column="ref_id"    />
        <result property="refType"    column="ref_type"    />
        <result property="productTitle"    column="product_title"    />
        <result property="productKeyWords"    column="product_key_words"    />
        <result property="productDescription"    column="product_description"    />
        <result property="productDetailImgUrl"    column="product_detail_img_url"    />
        <result property="productDetailTextOcr"    column="product_detail_text_ocr"    />
        <result property="productDescClean"    column="product_desc_clean"    />
        <result property="productFabClean"    column="product_fab_clean"    />
        <result property="sourceUrl"    column="source_url"    />
        <result property="crawlTime"    column="crawl_time"    />
        <result property="status"    column="status"    />
        <result property="isExample"    column="is_example"    />
        <result property="deconstruction"    column="deconstruction"    />
        <result property="secondCategoryId"    column="second_category_id"    />
        <result property="secondCategoryName"    column="second_category_name"    />
        <result property="cleanedDescScore"    column="cleaned_desc_score"    />
        <result property="extractedFabScore"    column="extracted_fab_score"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.ref_id, t.ref_type, t.product_title, t.product_key_words, t.product_description, t.product_detail_img_url, t.product_detail_text_ocr, t.product_desc_clean, t.product_fab_clean, t.source_url, t.crawl_time, t.status, t.is_example, t.deconstruction, t.second_category_id, t.second_category_name, t.cleaned_desc_score, t.extracted_fab_score, t.create_time, t.create_by, t.update_time, t.update_by    </sql>

    <sql id="selectBizCollectProductVo">
        select <include refid="Base_Column_List"/>  from biz_collect_product t
    </sql>

    <select id="selectBizCollectProductList" parameterType="com.ruoyi.biz.domain.BizCollectProduct" resultMap="BizCollectProductResult">
        <include refid="selectBizCollectProductVo"/>
        <where>
            <if test="refId != null  and refId != ''"> and t.ref_id = #{refId}</if>
            <if test="refType != null  and refType != ''"> and t.ref_type = #{refType}</if>
            <if test="productTitle != null  and productTitle != ''"> and t.product_title = #{productTitle}</if>
            <if test="productKeyWords != null  and productKeyWords != ''"> and t.product_key_words = #{productKeyWords}</if>
            <if test="productDescription != null  and productDescription != ''"> and t.product_description = #{productDescription}</if>
            <if test="productDetailImgUrl != null  and productDetailImgUrl != ''"> and t.product_detail_img_url = #{productDetailImgUrl}</if>
            <if test="productDetailTextOcr != null  and productDetailTextOcr != ''"> and t.product_detail_text_ocr = #{productDetailTextOcr}</if>
            <if test="productDescClean != null  and productDescClean != ''"> and t.product_desc_clean = #{productDescClean}</if>
            <if test="productFabClean != null  and productFabClean != ''"> and t.product_fab_clean = #{productFabClean}</if>
            <if test="sourceUrl != null  and sourceUrl != ''"> and t.source_url = #{sourceUrl}</if>
            <if test="params.beginCrawlTime != null and params.beginCrawlTime != '' and params.endCrawlTime != null and params.endCrawlTime != ''"> and t.crawl_time between #{params.beginCrawlTime} and #{params.endCrawlTime}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="isExample != null  and isExample != ''"> and t.is_example = #{isExample}</if>
            <if test="deconstruction != null  and deconstruction != ''"> and t.deconstruction = #{deconstruction}</if>
            <if test="secondCategoryId != null  and secondCategoryId != ''"> and t.second_category_id = #{secondCategoryId}</if>
            <if test="secondCategoryName != null  and secondCategoryName != ''"> and t.second_category_name like concat('%', #{secondCategoryName}, '%')</if>
            <if test="cleanedDescScore != null "> and t.cleaned_desc_score = #{cleanedDescScore}</if>
            <if test="extractedFabScore != null "> and t.extracted_fab_score = #{extractedFabScore}</if>
        </where>
    </select>

    <select id="selectBizCollectProductById" parameterType="Long" resultMap="BizCollectProductResult">
        <include refid="selectBizCollectProductVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizCollectProduct" parameterType="com.ruoyi.biz.domain.BizCollectProduct" useGeneratedKeys="true" keyProperty="id">
        insert into biz_collect_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="refId != null">ref_id,</if>
            <if test="refType != null">ref_type,</if>
            <if test="productTitle != null">product_title,</if>
            <if test="productKeyWords != null">product_key_words,</if>
            <if test="productDescription != null">product_description,</if>
            <if test="productDetailImgUrl != null">product_detail_img_url,</if>
            <if test="productDetailTextOcr != null">product_detail_text_ocr,</if>
            <if test="productDescClean != null">product_desc_clean,</if>
            <if test="productFabClean != null">product_fab_clean,</if>
            <if test="sourceUrl != null">source_url,</if>
            <if test="crawlTime != null">crawl_time,</if>
            <if test="status != null">status,</if>
            <if test="isExample != null">is_example,</if>
            <if test="deconstruction != null">deconstruction,</if>
            <if test="secondCategoryId != null">second_category_id,</if>
            <if test="secondCategoryName != null">second_category_name,</if>
            <if test="cleanedDescScore != null">cleaned_desc_score,</if>
            <if test="extractedFabScore != null">extracted_fab_score,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="refId != null">#{refId},</if>
            <if test="refType != null">#{refType},</if>
            <if test="productTitle != null">#{productTitle},</if>
            <if test="productKeyWords != null">#{productKeyWords},</if>
            <if test="productDescription != null">#{productDescription},</if>
            <if test="productDetailImgUrl != null">#{productDetailImgUrl},</if>
            <if test="productDetailTextOcr != null">#{productDetailTextOcr},</if>
            <if test="productDescClean != null">#{productDescClean},</if>
            <if test="productFabClean != null">#{productFabClean},</if>
            <if test="sourceUrl != null">#{sourceUrl},</if>
            <if test="crawlTime != null">#{crawlTime},</if>
            <if test="status != null">#{status},</if>
            <if test="isExample != null">#{isExample},</if>
            <if test="deconstruction != null">#{deconstruction},</if>
            <if test="secondCategoryId != null">#{secondCategoryId},</if>
            <if test="secondCategoryName != null">#{secondCategoryName},</if>
            <if test="cleanedDescScore != null">#{cleanedDescScore},</if>
            <if test="extractedFabScore != null">#{extractedFabScore},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateBizCollectProduct" parameterType="com.ruoyi.biz.domain.BizCollectProduct">
        update biz_collect_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="refId != null">ref_id = #{refId},</if>
            <if test="refType != null">ref_type = #{refType},</if>
            <if test="productTitle != null">product_title = #{productTitle},</if>
            <if test="productKeyWords != null">product_key_words = #{productKeyWords},</if>
            <if test="productDescription != null">product_description = #{productDescription},</if>
            <if test="productDetailImgUrl != null">product_detail_img_url = #{productDetailImgUrl},</if>
            <if test="productDetailTextOcr != null">product_detail_text_ocr = #{productDetailTextOcr},</if>
            <if test="productDescClean != null">product_desc_clean = #{productDescClean},</if>
            <if test="productFabClean != null">product_fab_clean = #{productFabClean},</if>
            <if test="sourceUrl != null">source_url = #{sourceUrl},</if>
            <if test="crawlTime != null">crawl_time = #{crawlTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isExample != null">is_example = #{isExample},</if>
            <if test="deconstruction != null">deconstruction = #{deconstruction},</if>
            <if test="secondCategoryId != null">second_category_id = #{secondCategoryId},</if>
            <if test="secondCategoryName != null">second_category_name = #{secondCategoryName},</if>
            <if test="cleanedDescScore != null">cleaned_desc_score = #{cleanedDescScore},</if>
            <if test="extractedFabScore != null">extracted_fab_score = #{extractedFabScore},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizCollectProductById" parameterType="Long">
        delete from biz_collect_product where id = #{id}
    </delete>

    <delete id="deleteBizCollectProductByIds" parameterType="String">
        delete from biz_collect_product where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="matchExample" parameterType="String" resultMap="BizCollectProductResult">
        <include refid="selectBizCollectProductVo"/>
        WHERE
        (
            <foreach collection="secondCategoryIdList" item="categoryId" separator="OR">
                JSON_CONTAINS(second_category_id, CONCAT('"', #{categoryId}, '"'))
            </foreach>
        )
        and is_example = '1'
        order by cleaned_desc_score desc limit 1
    </select>
</mapper>
