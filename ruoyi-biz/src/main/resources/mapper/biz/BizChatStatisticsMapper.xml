<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizChatStatisticsMapper">

    <resultMap type="com.ruoyi.biz.domain.BizChatStatistics" id="BizChatStatisticsResult">
        <result property="id"    column="id"    />
        <result property="chatId"    column="chat_id"    />
        <result property="startDate"    column="start_date"    />
        <result property="chatCount"    column="chat_count"    />
        <result property="exitNum"    column="exit_num"    />
        <result property="enterNum"    column="enter_num"    />
        <result property="roomTotal"    column="room_total"    />
        <result property="bannedWordFrequency"    column="banned_word_frequency"    />
        <result property="bannedWordInfo"    column="banned_word_info"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.chat_id, t.start_date, t.chat_count, t.exit_num, t.enter_num, t.room_total, t.banned_word_frequency, t.banned_word_info, t.create_time, t.create_by, t.update_time, t.update_by    </sql>

    <sql id="selectBizChatStatisticsVo">
        select <include refid="Base_Column_List"/>  from biz_chat_statistics t
    </sql>

    <select id="selectBizChatStatisticsList" parameterType="com.ruoyi.biz.domain.BizChatStatistics" resultMap="BizChatStatisticsResult">
        <include refid="selectBizChatStatisticsVo"/>
        <where>
            <if test="chatId != null  and chatId != ''"> and t.chat_id = #{chatId}</if>
            <if test="startDate != null "> and t.start_date = #{startDate}</if>
            <if test="chatCount != null "> and t.chat_count = #{chatCount}</if>
            <if test="exitNum != null "> and t.exit_num = #{exitNum}</if>
            <if test="enterNum != null "> and t.enter_num = #{enterNum}</if>
            <if test="roomTotal != null "> and t.room_total = #{roomTotal}</if>
            <if test="bannedWordFrequency != null "> and t.banned_word_frequency = #{bannedWordFrequency}</if>
            <if test="bannedWordInfo != null  and bannedWordInfo != ''"> and t.banned_word_info = #{bannedWordInfo}</if>
            <if test="params.beginStartDate != null"> and t.start_date >= #{params.beginStartDate}</if>
        </where>
    </select>

    <select id="selectBizChatStatisticsById" parameterType="Long" resultMap="BizChatStatisticsResult">
        <include refid="selectBizChatStatisticsVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizChatStatistics" parameterType="com.ruoyi.biz.domain.BizChatStatistics" useGeneratedKeys="true" keyProperty="id">
        insert into biz_chat_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="chatId != null and chatId != ''">chat_id,</if>
            <if test="startDate != null">start_date,</if>
            <if test="chatCount != null">chat_count,</if>
            <if test="exitNum != null">exit_num,</if>
            <if test="enterNum != null">enter_num,</if>
            <if test="roomTotal != null">room_total,</if>
            <if test="bannedWordFrequency != null">banned_word_frequency,</if>
            <if test="bannedWordInfo != null and bannedWordInfo != ''">banned_word_info,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chatId != null and chatId != ''">#{chatId},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="chatCount != null">#{chatCount},</if>
            <if test="exitNum != null">#{exitNum},</if>
            <if test="enterNum != null">#{enterNum},</if>
            <if test="roomTotal != null">#{roomTotal},</if>
            <if test="bannedWordFrequency != null">#{bannedWordFrequency},</if>
            <if test="bannedWordInfo != null and bannedWordInfo != ''">#{bannedWordInfo},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateBizChatStatistics" parameterType="com.ruoyi.biz.domain.BizChatStatistics">
        update biz_chat_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="chatId != null and chatId != ''">chat_id = #{chatId},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="chatCount != null">chat_count = #{chatCount},</if>
            <if test="exitNum != null">exit_num = #{exitNum},</if>
            <if test="enterNum != null">enter_num = #{enterNum},</if>
            <if test="roomTotal != null">room_total = #{roomTotal},</if>
            <if test="bannedWordFrequency != null">banned_word_frequency = #{bannedWordFrequency},</if>
            <if test="bannedWordInfo != null and bannedWordInfo != ''">banned_word_info = #{bannedWordInfo},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizChatStatisticsById" parameterType="Long">
        delete from biz_chat_statistics where id = #{id}
    </delete>

    <delete id="deleteBizChatStatisticsByIds" parameterType="String">
        delete from biz_chat_statistics where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
