<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizUserMaterialRecordMapper">

    <resultMap type="com.ruoyi.biz.domain.BizUserMaterialRecord" id="BizUserMaterialRecordResult">
        <result property="code"    column="code"    />
        <result property="chatId"    column="chat_id"    />
        <result property="wxid"    column="wxid"    />
        <result property="isFriend"    column="is_friend"    />
        <result property="unionId"    column="union_id"    />
        <result property="wid"    column="wid"    />
        <result property="type"    column="type"    />
        <result property="userInput"    column="user_input"    />
        <result property="aiResponse"    column="ai_response"    />
        <result property="createTime"    column="create_time"    />
        <result property="creater"    column="creater"    />
        <result property="sourceMessageid"    column="source_messageid"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.code, t.chat_id, t.wxid, t.is_friend, t.union_id, t.wid, t.type, t.user_input, t.ai_response, t.create_time, t.creater, t.source_messageid    </sql>

    <sql id="selectBizUserMaterialRecordVo">
        select <include refid="Base_Column_List"/>  from biz_user_material_record t
    </sql>

    <select id="selectBizUserMaterialRecordList" parameterType="com.ruoyi.biz.domain.BizUserMaterialRecord" resultMap="BizUserMaterialRecordResult">
        <include refid="selectBizUserMaterialRecordVo"/>
        <where>
            <if test="chatId != null  and chatId != ''"> and t.chat_id = #{chatId}</if>
            <if test="wxid != null  and wxid != ''"> and t.wxid = #{wxid}</if>
            <if test="isFriend != null  and isFriend != ''"> and t.is_friend = #{isFriend}</if>
            <if test="unionId != null  and unionId != ''"> and t.union_id = #{unionId}</if>
            <if test="wid != null  and wid != ''"> and t.wid = #{wid}</if>
            <if test="type != null  and type != ''"> and t.type = #{type}</if>
            <if test="userInput != null  and userInput != ''"> and t.user_input = #{userInput}</if>
            <if test="aiResponse != null  and aiResponse != ''"> and t.ai_response = #{aiResponse}</if>
            <if test="creater != null  and creater != ''"> and t.creater = #{creater}</if>
            <if test="sourceMessageid != null  and sourceMessageid != ''"> and t.source_messageid = #{sourceMessageid}</if>
        </where>
    </select>

    <select id="selectBizUserMaterialRecordByCode" parameterType="String" resultMap="BizUserMaterialRecordResult">
        <include refid="selectBizUserMaterialRecordVo"/>
        where t.code = #{code}
    </select>

    <insert id="insertBizUserMaterialRecord" parameterType="com.ruoyi.biz.domain.BizUserMaterialRecord">
        insert into biz_user_material_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="chatId != null and chatId != ''">chat_id,</if>
            <if test="wxid != null and wxid != ''">wxid,</if>
            <if test="isFriend != null and isFriend != ''">is_friend,</if>
            <if test="unionId != null and unionId != ''">union_id,</if>
            <if test="wid != null">wid,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="userInput != null and userInput != ''">user_input,</if>
            <if test="aiResponse != null and aiResponse != ''">ai_response,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creater != null and creater != ''">creater,</if>
            <if test="sourceMessageid != null and sourceMessageid != ''">source_messageid,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="chatId != null and chatId != ''">#{chatId},</if>
            <if test="wxid != null and wxid != ''">#{wxid},</if>
            <if test="isFriend != null and isFriend != ''">#{isFriend},</if>
            <if test="unionId != null and unionId != ''">#{unionId},</if>
            <if test="wid != null">#{wid},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="userInput != null and userInput != ''">#{userInput},</if>
            <if test="aiResponse != null and aiResponse != ''">#{aiResponse},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creater != null and creater != ''">#{creater},</if>
            <if test="sourceMessageid != null and sourceMessageid != ''">#{sourceMessageid},</if>
         </trim>
    </insert>

    <update id="updateBizUserMaterialRecord" parameterType="com.ruoyi.biz.domain.BizUserMaterialRecord">
        update biz_user_material_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="chatId != null and chatId != ''">chat_id = #{chatId},</if>
            <if test="wxid != null and wxid != ''">wxid = #{wxid},</if>
            <if test="isFriend != null and isFriend != ''">is_friend = #{isFriend},</if>
            <if test="unionId != null and unionId != ''">union_id = #{unionId},</if>
            <if test="wid != null">wid = #{wid},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="userInput != null and userInput != ''">user_input = #{userInput},</if>
            <if test="aiResponse != null and aiResponse != ''">ai_response = #{aiResponse},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creater != null and creater != ''">creater = #{creater},</if>
            <if test="sourceMessageid != null and sourceMessageid != ''">source_messageid = #{sourceMessageid},</if>
        </trim>
        where code = #{code}
    </update>

    <delete id="deleteBizUserMaterialRecordByCode" parameterType="String">
        delete from biz_user_material_record where code = #{code}
    </delete>

    <delete id="deleteBizUserMaterialRecordByCodes" parameterType="String">
        delete from biz_user_material_record where code in
        <foreach item="code" collection="array" open="(" separator="," close=")">
            #{code}
        </foreach>
    </delete>
</mapper>
