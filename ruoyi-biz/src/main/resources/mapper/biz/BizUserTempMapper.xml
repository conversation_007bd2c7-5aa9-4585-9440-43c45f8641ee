<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizUserTempMapper">

    <resultMap type="com.ruoyi.biz.domain.BizUserTemp" id="BizUserTempResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="userId"    />
        <result property="wxid"    column="wxid"    />
        <result property="refId"    column="refId"    />
        <result property="beforeTemp"    column="before_temp"    />
        <result property="changeTemp"    column="change_temp"    />
        <result property="afterTemp"    column="after_temp"    />
        <result property="nowTemp"    column="now_temp"    />
        <result property="changeType"    column="change_type"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.userId, t.wxid, t.ref_id, t.before_temp, t.change_temp, t.after_temp, t.now_temp, t.change_type, t.status, t.remark, t.create_time, t.update_time    </sql>

    <sql id="selectBizUserTempVo">
        select <include refid="Base_Column_List"/>  from biz_user_temp t
    </sql>

    <select id="selectBizUserTempList" parameterType="com.ruoyi.biz.domain.BizUserTemp" resultMap="BizUserTempResult">
        <include refid="selectBizUserTempVo"/>
        <where>
            <if test="userId != null "> and t.userId = #{userId}</if>
            <if test="wxid != null  and wxid != ''"> and t.wxid = #{wxid}</if>
            <if test="refId != null  and refId != ''"> and t.ref_id = #{refId}</if>
            <if test="beforeTemp != null "> and t.before_temp = #{beforeTemp}</if>
            <if test="changeTemp != null "> and t.change_temp = #{changeTemp}</if>
            <if test="afterTemp != null "> and t.after_temp = #{afterTemp}</if>
            <if test="nowTemp != null "> and t.now_temp = #{nowTemp}</if>
            <if test="changeType != null  and changeType != ''"> and t.change_type = #{changeType}</if>
            <if test="status != null "> and t.status = #{status}</if>
            <if test="remark != null  and remark != ''"> and t.remark = #{remark}</if>
        </where>
        order by t.update_time desc, t.id desc
    </select>

    <select id="selectBizUserTempById" parameterType="Long" resultMap="BizUserTempResult">
        <include refid="selectBizUserTempVo"/>
        where t.id = #{id}
    </select>

    <select id="getNewBizUserTempByUserId" resultType="com.ruoyi.biz.domain.BizUserTemp">
        <include refid="selectBizUserTempVo"/>
        <where>
            <if test="userId != null "> and t.userId = #{userId}</if>
            <if test="wxid != null  and wxid != ''"> and t.wxid = #{wxid}</if>
            <if test="refId != null  and refId != ''"> and t.ref_id = #{refId}</if>
            <if test="beforeTemp != null "> and t.before_temp = #{beforeTemp}</if>
            <if test="changeTemp != null "> and t.change_temp = #{changeTemp}</if>
            <if test="afterTemp != null "> and t.after_temp = #{afterTemp}</if>
            <if test="nowTemp != null "> and t.now_temp = #{nowTemp}</if>
            <if test="changeType != null  and changeType != ''"> and t.change_type = #{changeType}</if>
            <if test="status != null "> and t.status = #{status}</if>
            <if test="remark != null  and remark != ''"> and t.remark = #{remark}</if>
        </where>
        order by t.update_time desc limit 1
    </select>

    <select id="geTempByTime" resultType="com.ruoyi.biz.domain.BizUserTemp">
        <include refid="selectBizUserTempVo"/>
        <where>
            <if test="userId != null "> and t.userId = #{userId}</if>
            <if test="changeType != null  and changeType != ''"> and t.change_type = #{changeType}</if>
            <if test="startTime != null  and startTime != ''"> and t.create_time &gt;= #{startTime}</if>
            <if test="endTime != null  and endTime != ''"> and t.create_time &lt;= #{endTime}</if>
        </where>
    </select>

    <insert id="insertBizUserTemp" parameterType="com.ruoyi.biz.domain.BizUserTemp" useGeneratedKeys="true" keyProperty="id">
        insert into biz_user_temp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">userId,</if>
            <if test="wxid != null">wxid,</if>
            <if test="refId != null">ref_id,</if>
            <if test="beforeTemp != null">before_temp,</if>
            <if test="changeTemp != null">change_temp,</if>
            <if test="afterTemp != null">after_temp,</if>
            <if test="nowTemp != null">now_temp,</if>
            <if test="changeType != null">change_type,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="wxid != null">#{wxid},</if>
            <if test="refId != null">#{refId},</if>
            <if test="beforeTemp != null">#{beforeTemp},</if>
            <if test="changeTemp != null">#{changeTemp},</if>
            <if test="afterTemp != null">#{afterTemp},</if>
            <if test="nowTemp != null">#{nowTemp},</if>
            <if test="changeType != null">#{changeType},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBizUserTemp" parameterType="com.ruoyi.biz.domain.BizUserTemp">
        update biz_user_temp
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">userId = #{userId},</if>
            <if test="wxid != null">wxid = #{wxid},</if>
            <if test="refId != null">ref_id = #{refId},</if>
            <if test="beforeTemp != null">before_temp = #{beforeTemp},</if>
            <if test="changeTemp != null">change_temp = #{changeTemp},</if>
            <if test="afterTemp != null">after_temp = #{afterTemp},</if>
            <if test="nowTemp != null">now_temp = #{nowTemp},</if>
            <if test="changeType != null">change_type = #{changeType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizUserTempById" parameterType="Long">
        delete from biz_user_temp where id = #{id}
    </delete>

    <delete id="deleteBizUserTempByIds" parameterType="String">
        delete from biz_user_temp where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
