<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizQuestionnaireTaskMapper">

    <resultMap type="com.ruoyi.biz.domain.BizQuestionnaireTask" id="BizQuestionnaireTaskResult">
        <result property="id"    column="id"    />
        <result property="wid"    column="wid"    />
        <result property="oldFile"    column="old_file"    />
        <result property="newFile"    column="new_file"    />
        <result property="type"    column="type"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.wid, t.old_file, t.new_file, t.type, t.status, t.remark, t.create_time, t.update_time    </sql>

    <sql id="selectBizQuestionnaireTaskVo">
        select <include refid="Base_Column_List"/>  from biz_questionnaire_task t
    </sql>

    <select id="selectBizQuestionnaireTaskList" parameterType="com.ruoyi.biz.domain.BizQuestionnaireTask" resultMap="BizQuestionnaireTaskResult">
        <include refid="selectBizQuestionnaireTaskVo"/>
        <where>
            <if test="wid != null  and wid != ''"> and t.wid = #{wid}</if>
            <if test="oldFile != null  and oldFile != ''"> and t.old_file = #{oldFile}</if>
            <if test="newFile != null  and newFile != ''"> and t.new_file = #{newFile}</if>
            <if test="type != null  and type != ''"> and t.type = #{type}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
        </where>
    </select>

    <select id="selectBizQuestionnaireTaskById" parameterType="Long" resultMap="BizQuestionnaireTaskResult">
        <include refid="selectBizQuestionnaireTaskVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizQuestionnaireTask" parameterType="com.ruoyi.biz.domain.BizQuestionnaireTask" useGeneratedKeys="true" keyProperty="id">
        insert into biz_questionnaire_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="wid != null">wid,</if>
            <if test="oldFile != null">old_file,</if>
            <if test="newFile != null">new_file,</if>
            <if test="type != null">type,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="wid != null">#{wid},</if>
            <if test="oldFile != null">#{oldFile},</if>
            <if test="newFile != null">#{newFile},</if>
            <if test="type != null">#{type},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
        ON DUPLICATE KEY UPDATE
        <trim suffixOverrides=",">
            <if test="oldFile != null">old_file = #{oldFile},</if>
            <if test="newFile != null">new_file = #{newFile},</if>
            <if test="type != null">type = #{type},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
    </insert>

    <update id="updateBizQuestionnaireTask" parameterType="com.ruoyi.biz.domain.BizQuestionnaireTask">
        update biz_questionnaire_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="wid != null">wid = #{wid},</if>
            <if test="oldFile != null">old_file = #{oldFile},</if>
            <if test="newFile != null">new_file = #{newFile},</if>
            <if test="type != null">type = #{type},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizQuestionnaireTaskById" parameterType="Long">
        delete from biz_questionnaire_task where id = #{id}
    </delete>

    <delete id="deleteBizQuestionnaireTaskByIds" parameterType="String">
        delete from biz_questionnaire_task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
