<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizAppTopicArticleMapper">

    <resultMap type="com.ruoyi.biz.domain.BizAppTopicArticle" id="BizAppTopicArticleResult">
        <result property="id"    column="id"    />
        <result property="topicId"    column="topic_id"    />
        <result property="topicName"    column="topic_name"    />
        <result property="title"    column="title"    />
        <result property="type"    column="type"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="createDatetime"    column="create_datetime"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.topic_id, t.topic_name, t.title, t.type, t.content, t.status, t.create_datetime    </sql>

    <sql id="selectBizAppTopicArticleVo">
        select <include refid="Base_Column_List"/>  from biz_app_topic_article t
    </sql>

    <select id="selectBizAppTopicArticleList" parameterType="com.ruoyi.biz.domain.BizAppTopicArticle" resultMap="BizAppTopicArticleResult">
        <include refid="selectBizAppTopicArticleVo"/>
        <where>
            <if test="topicId != null "> and t.topic_id = #{topicId}</if>
            <if test="topicName != null  and topicName != ''"> and t.topic_name like concat('%', #{topicName}, '%')</if>
            <if test="title != null  and title != ''"> and t.title = #{title}</if>
            <if test="type != null  and type != ''"> and t.type = #{type}</if>
            <if test="content != null  and content != ''"> and t.content = #{content}</if>
            <if test="createDatetime != null "> and t.create_datetime = #{createDatetime}</if>
        </where>
    </select>

    <select id="selectBizAppTopicArticleById" parameterType="Long" resultMap="BizAppTopicArticleResult">
        <include refid="selectBizAppTopicArticleVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizAppTopicArticle" parameterType="com.ruoyi.biz.domain.BizAppTopicArticle" useGeneratedKeys="true" keyProperty="id">
        insert into biz_app_topic_article
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="topicId != null">topic_id,</if>
            <if test="topicName != null and topicName != ''">topic_name,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="status != null">status,</if>
            <if test="createDatetime != null">create_datetime,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="topicId != null">#{topicId},</if>
            <if test="topicName != null and topicName != ''">#{topicName},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="status != null">#{status},</if>
            <if test="createDatetime != null">#{createDatetime},</if>
         </trim>
    </insert>

    <update id="updateBizAppTopicArticle" parameterType="com.ruoyi.biz.domain.BizAppTopicArticle">
        update biz_app_topic_article
        <trim prefix="SET" suffixOverrides=",">
            <if test="topicId != null">topic_id = #{topicId},</if>
            <if test="topicName != null and topicName != ''">topic_name = #{topicName},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createDatetime != null">create_datetime = #{createDatetime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizAppTopicArticleById" parameterType="Long">
        delete from biz_app_topic_article where id = #{id}
    </delete>

    <delete id="deleteBizAppTopicArticleByIds" parameterType="String">
        delete from biz_app_topic_article where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
