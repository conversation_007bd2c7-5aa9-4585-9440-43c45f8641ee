<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizOperatePointMapper">

    <resultMap type="com.ruoyi.biz.domain.BizOperatePoint" id="BizOperatePointResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="planId"    column="plan_id"    />
        <result property="planType"    column="plan_type"    />
        <result property="time"    column="time"    />
        <result property="batchNum"    column="batch_num"    />
        <result property="batchInterval"    column="batch_interval"    />
        <result property="nextTime"    column="next_time"    />
        <result property="status"    column="status"    />
        <result property="nodeInfoList"    column="node_info_list"    />
        <result property="isDelete"  column="is_delete"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.name,t.type, t.plan_id, t.plan_type, t.time, t.batch_num, t.batch_interval, t.next_time, t.status, t.is_delete, t.node_info_list, t.create_time
    </sql>

    <sql id="selectBizOperatePointVo">
        select <include refid="Base_Column_List"/>  from biz_operate_point t
    </sql>

    <select id="selectBizOperatePointList" parameterType="com.ruoyi.biz.domain.BizOperatePoint" resultMap="BizOperatePointResult">
        <include refid="selectBizOperatePointVo"/>
        <where>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="planId != null "> and t.plan_id = #{planId}</if>
            <if test="planType != null  and planType != ''"> and t.plan_type = #{planType}</if>
            <if test="params.beginTime != null and params.beginTime != '' and params.endTime != null and params.endTime != ''"> and t.time between #{params.beginTime} and #{params.endTime}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="nodeInfoList != null  and nodeInfoList != ''"> and t.node_info_list = #{nodeInfoList}</if>
            <if test="executeFlag != null  and executeFlag != ''"> and t.time &lt;= now()</if>
            and t.is_delete = '0'
        </where>
        order by t.id
    </select>

    <select id="selectBizOperatePointById" parameterType="Long" resultMap="BizOperatePointResult">
        <include refid="selectBizOperatePointVo"/>
        where t.id = #{id}
    </select>

    <select id="selectBizOperatePointByIdForUpdate" parameterType="Long" resultMap="BizOperatePointResult">
        <include refid="selectBizOperatePointVo"/>
        where t.id = #{id} for update
    </select>
    <select id="selectBizOperatePointsByIds" resultMap="BizOperatePointResult">
        <include refid="selectBizOperatePointVo"/>
        <where>
            t.id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
            and t.is_delete = '0'
        </where>
    </select>

    <insert id="insertBizOperatePoint" parameterType="com.ruoyi.biz.domain.BizOperatePoint" useGeneratedKeys="true" keyProperty="id">
        insert into biz_operate_point
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="planId != null">plan_id,</if>
            <if test="planType != null and planType != ''">plan_type,</if>
            <if test="time != null and time != ''">time,</if>
            <if test="batchNum != null">batch_num,</if>
            <if test="batchInterval != null">batch_interval,</if>
            <if test="nextTime != null and nextTime != ''">next_time,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="nodeInfoList != null and nodeInfoList != ''">node_info_list,</if>
            <if test="isDelete != null and isDelete != ''">is_delete,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="planId != null">#{planId},</if>
            <if test="planType != null and planType != ''">#{planType},</if>
            <if test="time != null and time != ''">#{time},</if>
            <if test="batchNum != null">#{batchNum},</if>
            <if test="batchInterval != null">#{batchInterval},</if>
            <if test="nextTime != null and nextTime != ''">#{nextTime},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="nodeInfoList != null and nodeInfoList != ''">#{nodeInfoList},</if>
            <if test="isDelete != null and isDelete != ''">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateBizOperatePoint" parameterType="com.ruoyi.biz.domain.BizOperatePoint">
        update biz_operate_point
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="planType != null and planType != ''">plan_type = #{planType},</if>
            <if test="time != null and time != ''">time = #{time},</if>
            <if test="batchNum != null">batch_num = #{batchNum},</if>
            <if test="batchInterval != null">batch_interval = #{batchInterval},</if>
            <if test="nextTime != null and nextTime != ''">next_time = #{nextTime},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="nodeInfoList != null and nodeInfoList != ''">node_info_list = #{nodeInfoList},</if>
            <if test="isDelete != null and isDelete != ''">is_delete = #{isDelete},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="stop">
        update biz_operate_point set status = '2'  where plan_id = #{planId}
    </update>
    <update id="start">
        update biz_operate_point set status = '0'  where plan_id = #{planId}
    </update>
    <update id="batchUpdateStatusToDeleteByPlanIds">
        update biz_operate_point set is_delete = '1' where plan_id in
        <foreach item="planId" collection="ids" open="(" separator="," close=")">
            #{planId}
        </foreach>
    </update>

    <delete id="deleteBizOperatePointById" parameterType="Long">
        delete from biz_operate_point where id = #{id}
    </delete>

    <delete id="deleteBizOperatePointByIds" parameterType="String">
        delete from biz_operate_point where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBizOperatePointByPlanId">
        delete from biz_operate_point where plan_id = #{planId}
    </delete>

    <update id="updateBizOperatePointByPlanId" parameterType="com.ruoyi.biz.domain.BizOperatePoint">
        update biz_operate_point
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="planType != null and planType != ''">plan_type = #{planType},</if>
            <if test="time != null and time != ''">time = #{time},</if>
            <if test="batchNum != null">batch_num = #{batchNum},</if>
            <if test="batchInterval != null">batch_interval = #{batchInterval},</if>
            <if test="nextTime != null and nextTime != ''">next_time = #{nextTime},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="nodeInfoList != null and nodeInfoList != ''">node_info_list = #{nodeInfoList},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where plan_id = #{planId}
    </update>
</mapper>
