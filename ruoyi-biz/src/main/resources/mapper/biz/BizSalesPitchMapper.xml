<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizSalesPitchMapper">

    <resultMap type="com.ruoyi.biz.domain.BizSalesPitch" id="BizSalesPitchResult">
        <result property="id"    column="id"    />
        <result property="refId"    column="ref_id"    />
        <result property="refType"    column="ref_type"    />
        <result property="refName"    column="ref_name"    />
        <result property="serNo"    column="ser_no"    />
        <result property="content"    column="content"    />
        <result property="likeStatus"    column="like_status"    />
        <result property="copyCount"    column="copy_count"    />
        <result property="totalScore"    column="total_score"    />
        <result property="exampleId"    column="example_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="Base_Column_List">
        id, ref_id, ref_type, ref_name, ser_no, content, like_status, copy_count, total_score, example_id, create_time, create_by, update_time, update_by    </sql>

    <sql id="selectBizSalesPitchVo">
        select <include refid="Base_Column_List"/>  from biz_sales_pitch t
    </sql>

    <select id="selectBizSalesPitchList" parameterType="com.ruoyi.biz.domain.BizSalesPitch" resultMap="BizSalesPitchResult">
        <include refid="selectBizSalesPitchVo"/>
        <where>
            <if test="refId != null "> and ref_id = #{refId}</if>
            <if test="refType != null "> and ref_type = #{refType}</if>
            <if test="refName != null  and refName != ''"> and ref_name like concat('%', #{refName}, '%')</if>
            <if test="serNo != null "> and ser_no = #{serNo}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="likeStatus != null "> and like_status = #{likeStatus}</if>
            <if test="copyCount != null "> and copy_count = #{copyCount}</if>
            <if test="totalScore != null "> and total_score = #{totalScore}</if>
            <if test="exampleId != null "> and example_id = #{exampleId}</if>
        </where>
    </select>

    <select id="selectBizSalesPitchById" parameterType="Long" resultMap="BizSalesPitchResult">
        <include refid="selectBizSalesPitchVo"/>
        where id = #{id}
    </select>

    <insert id="insertBizSalesPitch" parameterType="com.ruoyi.biz.domain.BizSalesPitch" useGeneratedKeys="true" keyProperty="id">
        insert into biz_sales_pitch
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="refId != null">ref_id,</if>
            <if test="refType != null">ref_type,</if>
            <if test="refName != null">ref_name,</if>
            <if test="serNo != null">ser_no,</if>
            <if test="content != null">content,</if>
            <if test="likeStatus != null">like_status,</if>
            <if test="copyCount != null">copy_count,</if>
            <if test="totalScore != null">total_score,</if>
            <if test="exampleId != null">example_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="refId != null">#{refId},</if>
            <if test="refType != null">#{refType},</if>
            <if test="refName != null">#{refName},</if>
            <if test="serNo != null">#{serNo},</if>
            <if test="content != null">#{content},</if>
            <if test="likeStatus != null">#{likeStatus},</if>
            <if test="copyCount != null">#{copyCount},</if>
            <if test="totalScore != null">#{totalScore},</if>
            <if test="exampleId != null">#{exampleId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateBizSalesPitch" parameterType="com.ruoyi.biz.domain.BizSalesPitch">
        update biz_sales_pitch
        <trim prefix="SET" suffixOverrides=",">
            <if test="refId != null">ref_id = #{refId},</if>
            <if test="refType != null">ref_type = #{refType},</if>
            <if test="refName != null">ref_name = #{refName},</if>
            <if test="serNo != null">ser_no = #{serNo},</if>
            <if test="content != null">content = #{content},</if>
            <if test="likeStatus != null">like_status = #{likeStatus},</if>
            <if test="copyCount != null">copy_count = #{copyCount},</if>
            <if test="totalScore != null">total_score = #{totalScore},</if>
            <if test="exampleId != null">example_id = #{exampleId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizSalesPitchById" parameterType="Long">
        delete from biz_sales_pitch where id = #{id}
    </delete>

    <delete id="deleteBizSalesPitchByIds" parameterType="String">
        delete from biz_sales_pitch where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertOrUpdate" parameterType="java.util.List" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO biz_sales_pitch (id, ref_id, ref_type, ref_name, ser_no, content, like_status, copy_count, total_score, example_id, create_time, create_by, update_time, update_by)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.refId}, #{item.refType}, #{item.refName}, #{item.serNo}, #{item.content},
            #{item.likeStatus}, #{item.copyCount}, #{item.totalScore}, #{item.exampleId}, #{item.createTime},
            #{item.createBy}, #{item.updateTime}, #{item.updateBy})
        </foreach>
        ON DUPLICATE KEY UPDATE

            ref_id = VALUES(ref_id),
            ref_type = VALUES(ref_type),
            ref_name = VALUES(ref_name),
            ser_no = VALUES(ser_no),
            content = VALUES(content),
            update_by = VALUES(update_by)

    </insert>
</mapper>
