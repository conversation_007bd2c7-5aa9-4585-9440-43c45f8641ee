<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizImageTaskMapper">

    <resultMap type="com.ruoyi.biz.domain.BizImageTask" id="BizImageTaskResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="fId"    column="f_id"    />
        <result property="userId"    column="user_id"    />
        <result property="messageId"    column="message_id"    />
        <result property="action"    column="action"    />
        <result property="prompt"    column="prompt"    />
        <result property="promptEn"    column="promptEn"    />
        <result property="description"    column="description"    />
        <result property="state"    column="state"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="finishTime"    column="finish_time"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="resultImage"    column="result_image"    />
        <result property="status"    column="status"    />
        <result property="progress"    column="progress"    />
        <result property="failReason"    column="failReason"    />
        <result property="properties"    column="properties"    />
        <result property="buttons"    column="buttons"    />
        <result property="platform"    column="platform"    />
        <result property="source"    column="source"    />
        <result property="messageBody"    column="message_body"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.task_id, t.f_id, t.user_id, t.message_id, t.action, t.prompt, t.promptEn, t.description, t.state, t.submit_time, t.start_time, t.finish_time, t.image_url, t.result_image, t.status, t.progress, t.failReason, t.properties, t.buttons, t.platform, t.source, t.message_body, t.remark, t.create_time, t.update_time    </sql>

    <sql id="selectBizImageTaskVo">
        select <include refid="Base_Column_List"/>  from biz_image_task t
    </sql>

    <select id="selectBizImageTaskList" parameterType="com.ruoyi.biz.domain.BizImageTask" resultMap="BizImageTaskResult">
        <include refid="selectBizImageTaskVo"/>
        <where>
            <if test="taskId != null  and taskId != ''"> and t.task_id = #{taskId}</if>
            <if test="fId != null  and fId != ''"> and t.f_id = #{fId}</if>
            <if test="userId != null  and userId != ''"> and t.user_id = #{userId}</if>
            <if test="messageId != null  and messageId != ''"> and t.message_id = #{messageId}</if>
            <if test="action != null  and action != ''"> and t.action = #{action}</if>
            <if test="prompt != null  and prompt != ''"> and t.prompt = #{prompt}</if>
            <if test="promptEn != null  and promptEn != ''"> and t.promptEn = #{promptEn}</if>
            <if test="description != null  and description != ''"> and t.description = #{description}</if>
            <if test="state != null  and state != ''"> and t.state = #{state}</if>
            <if test="submitTime != null "> and t.submit_time = #{submitTime}</if>
            <if test="startTime != null "> and t.start_time = #{startTime}</if>
            <if test="finishTime != null "> and t.finish_time = #{finishTime}</if>
            <if test="imageUrl != null  and imageUrl != ''"> and t.image_url = #{imageUrl}</if>
            <if test="resultImage != null  and resultImage != ''"> and t.result_image = #{resultImage}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="progress != null  and progress != ''"> and t.progress = #{progress}</if>
            <if test="failReason != null  and failReason != ''"> and t.failReason = #{failReason}</if>
            <if test="properties != null  and properties != ''"> and t.properties = #{properties}</if>
            <if test="buttons != null  and buttons != ''"> and t.buttons = #{buttons}</if>
            <if test="platform != null  and platform != ''"> and t.platform = #{platform}</if>
            <if test="source != null  and source != ''"> and t.source = #{source}</if>
            <if test="messageBody != null  and messageBody != ''"> and t.message_body = #{messageBody}</if>
        </where>
        order by t.id desc
    </select>

    <select id="selectBizImageTaskById" resultMap="BizImageTaskResult">
        <include refid="selectBizImageTaskVo"/>
        where t.id = #{id}
    </select>

    <select id="selectBizImageTaskForUpdate" resultMap="BizImageTaskResult">
        <include refid="selectBizImageTaskVo"/>
        where t.task_id = #{taskId} for update
    </select>

    <select id="selectBizImageTaskListByUserIdAndState" resultMap="BizImageTaskResult">
        <include refid="selectBizImageTaskVo"/>
        <where>
            <if test="userId != null  and userId != ''"> and t.user_id = #{userId}</if>
            <if test="states != null  and states.size() > 0">
                and t.state in
                <foreach collection="states" open="(" close=")" separator="," item="state" >
                    #{state}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectBizImageTasksByFIds" resultMap="BizImageTaskResult">
        <include refid="selectBizImageTaskVo"/>
        <where>
            t.f_id in
            <foreach collection="taskIdList" open="(" close=")" separator="," item="fId" >
                #{fId}
            </foreach>
        </where>
    </select>

    <insert id="insertBizImageTask" parameterType="com.ruoyi.biz.domain.BizImageTask" keyProperty="id">
        insert into biz_image_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="fId != null">f_id,</if>
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="messageId != null and messageId != ''">message_id,</if>
            <if test="action != null">action,</if>
            <if test="prompt != null">prompt,</if>
            <if test="promptEn != null">promptEn,</if>
            <if test="description != null">description,</if>
            <if test="state != null">state,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="finishTime != null">finish_time,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="resultImage != null">result_image,</if>
            <if test="status != null">status,</if>
            <if test="progress != null">progress,</if>
            <if test="failReason != null">failReason,</if>
            <if test="properties != null">properties,</if>
            <if test="buttons != null">buttons,</if>
            <if test="platform != null">platform,</if>
            <if test="source != null">source,</if>
            <if test="messageBody != null">message_body,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="fId != null">#{fId},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="messageId != null and messageId != ''">#{messageId},</if>
            <if test="action != null">#{action},</if>
            <if test="prompt != null">#{prompt},</if>
            <if test="promptEn != null">#{promptEn},</if>
            <if test="description != null">#{description},</if>
            <if test="state != null">#{state},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="finishTime != null">#{finishTime},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="resultImage != null">#{resultImage},</if>
            <if test="status != null">#{status},</if>
            <if test="progress != null">#{progress},</if>
            <if test="failReason != null">#{failReason},</if>
            <if test="properties != null">#{properties},</if>
            <if test="buttons != null">#{buttons},</if>
            <if test="platform != null">#{platform},</if>
            <if test="source != null">#{source},</if>
            <if test="messageBody != null">#{messageBody},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBizImageTask" parameterType="com.ruoyi.biz.domain.BizImageTask">
        update biz_image_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="fId != null">f_id = #{fId},</if>
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="messageId != null and messageId != ''">message_id = #{messageId},</if>
            <if test="action != null">action = #{action},</if>
            <if test="prompt != null">prompt = #{prompt},</if>
            <if test="promptEn != null">promptEn = #{promptEn},</if>
            <if test="description != null">description = #{description},</if>
            <if test="state != null">state = #{state},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="resultImage != null">result_image = #{resultImage},</if>
            <if test="status != null">status = #{status},</if>
            <if test="progress != null">progress = #{progress},</if>
            <if test="failReason != null">failReason = #{failReason},</if>
            <if test="properties != null">properties = #{properties},</if>
            <if test="buttons != null">buttons = #{buttons},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="source != null">source = #{source},</if>
            <if test="messageBody != null">message_body = #{messageBody},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizImageTaskById">
        delete from biz_image_task where id = #{id}
    </delete>

    <delete id="deleteBizImageTaskByIds" parameterType="String">
        delete from biz_image_task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
