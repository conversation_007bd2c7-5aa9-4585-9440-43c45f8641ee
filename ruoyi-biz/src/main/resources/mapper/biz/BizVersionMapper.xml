<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizVersionMapper">

    <resultMap type="com.ruoyi.biz.domain.BizVersion" id="BizVersionResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="Base_Column_List">
     t.id, t.name,t.type,t.remark, t.create_by, t.create_time
    </sql>

    <sql id="selectBizVersionVo">
        select <include refid="Base_Column_List"/>  from biz_version t
    </sql>

    <select id="selectBizVersionList" parameterType="com.ruoyi.biz.domain.BizVersion" resultMap="BizVersionResult">
        <include refid="selectBizVersionVo"/>
        <where>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and t.type = #{type}</if>
            <if test="remark != null  and remark != ''"> and t.remark like concat('%', #{remark}, '%') </if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and t.create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        </where>
        order by id desc
    </select>

    <select id="selectBizVersionById" parameterType="Long" resultMap="BizVersionResult">
        <include refid="selectBizVersionVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizVersion" parameterType="com.ruoyi.biz.domain.BizVersion" useGeneratedKeys="true" keyProperty="id">
        insert into biz_version
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>

    </insert>

    <update id="updateBizVersion" parameterType="com.ruoyi.biz.domain.BizVersion">
        update biz_version
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizVersionById" parameterType="Long">
        delete from biz_version where id = #{id}
    </delete>

    <delete id="deleteBizVersionByIds" parameterType="String">
        delete from biz_version where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
