<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizPartnerGroupMapper">

    <resultMap type="com.ruoyi.biz.domain.BizPartnerGroup" id="BizPartnerGroupResult">
        <result property="id"    column="id"    />
        <result property="partnerId"    column="partner_id"    />
        <result property="roomId"    column="room_id"    />
    </resultMap>

    <sql id="Base_Column_List">
            t.id, t.partner_id, t.room_id
    </sql>

    <sql id="selectBizPartnerGroupVo">
        select <include refid="Base_Column_List"/>  from biz_partner_group t
    </sql>

    <select id="selectBizPartnerGroupList" parameterType="com.ruoyi.biz.domain.BizPartnerGroup" resultMap="BizPartnerGroupResult">
        <include refid="selectBizPartnerGroupVo"/>
        <where>
            <if test="partnerId != null "> and t.partner_id = #{partnerId}</if>
            <if test="roomId != null  and roomId != ''"> and t.room_id = #{roomId}</if>
        </where>
    </select>

    <select id="selectBizPartnerGroupById" parameterType="Long" resultMap="BizPartnerGroupResult">
        <include refid="selectBizPartnerGroupVo"/>
        where t.id = #{id}
    </select>
    <select id="selectBizPartnerGroupByRoomId" resultMap="BizPartnerGroupResult">
        <include refid="selectBizPartnerGroupVo"/>
        where t.room_id = #{roomId}
    </select>

    <insert id="insertBizPartnerGroup" parameterType="com.ruoyi.biz.domain.BizPartnerGroup" useGeneratedKeys="true" keyProperty="id">
        insert into biz_partner_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="partnerId != null">partner_id,</if>
            <if test="roomId != null and roomId != ''">room_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="partnerId != null">#{partnerId},</if>
            <if test="roomId != null and roomId != ''">#{roomId},</if>
         </trim>
    </insert>

    <update id="updateBizPartnerGroup" parameterType="com.ruoyi.biz.domain.BizPartnerGroup">
        update biz_partner_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="partnerId != null">partner_id = #{partnerId},</if>
            <if test="roomId != null and roomId != ''">room_id = #{roomId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizPartnerGroupById" parameterType="Long">
        delete from biz_partner_group where id = #{id}
    </delete>

    <delete id="deleteBizPartnerGroupByIds" parameterType="String">
        delete from biz_partner_group where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteBizPartnerGroupByRoomId">
        delete from biz_partner_group where room_id = #{roomId}
    </delete>
</mapper>
