<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizNewContactOperatePlanMapper">

    <resultMap type="com.ruoyi.biz.domain.BizNewContactOperatePlan" id="BizNewContactOperatePlanResult">
        <result property="chatId"    column="chat_id"    />
        <result property="planId"    column="plan_id"    />
        <result property="goodsId"    column="goods_id"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.chat_id, t.plan_id, t.goods_id    </sql>

    <sql id="selectBizNewContactOperatePlanVo">
        select <include refid="Base_Column_List"/>  from biz_new_contact_operate_plan t
    </sql>

    <select id="selectBizNewContactOperatePlanList" parameterType="com.ruoyi.biz.domain.BizNewContactOperatePlan" resultMap="BizNewContactOperatePlanResult">
        <include refid="selectBizNewContactOperatePlanVo"/>
        <where>
            <if test="planId != null "> and t.plan_id = #{planId}</if>
            <if test="goodsId != null "> and t.goods_id = #{goodsId}</if>
            <if test="chatIdList != null  and chatIdList.size() > 0 ">
                and t.chat_id in
                <foreach collection="chatIdList" item="chatId" separator="," open="(" close=")">
                    #{chatId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectBizNewContactOperatePlanByChatId" parameterType="String" resultMap="BizNewContactOperatePlanResult">
        <include refid="selectBizNewContactOperatePlanVo"/>
        where t.chat_id = #{chatId}
    </select>

    <insert id="insertBizNewContactOperatePlan" parameterType="com.ruoyi.biz.domain.BizNewContactOperatePlan">
        insert into biz_new_contact_operate_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="chatId != null">chat_id,</if>
            <if test="planId != null">plan_id,</if>
            <if test="goodsId != null">goods_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chatId != null">#{chatId},</if>
            <if test="planId != null">#{planId},</if>
            <if test="goodsId != null">#{goodsId},</if>
         </trim>
    </insert>

    <update id="updateBizNewContactOperatePlan" parameterType="com.ruoyi.biz.domain.BizNewContactOperatePlan">
        update biz_new_contact_operate_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="goodsId != null">goods_id = #{goodsId},</if>
        </trim>
        where chat_id = #{chatId}
    </update>

    <delete id="deleteBizNewContactOperatePlanByChatId" parameterType="String">
        delete from biz_new_contact_operate_plan where chat_id = #{chatId}
    </delete>

    <delete id="deleteBizNewContactOperatePlanByChatIds" parameterType="String">
        delete from biz_new_contact_operate_plan where chat_id in
        <foreach item="chatId" collection="array" open="(" separator="," close=")">
            #{chatId}
        </foreach>
    </delete>
</mapper>
