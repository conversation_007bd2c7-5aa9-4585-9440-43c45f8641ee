<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizMomentsMapper">

    <resultMap type="com.ruoyi.biz.domain.BizMoments" id="BizMomentsResult">
        <result property="id"    column="id"    />
        <result property="planId"    column="plan_id"    />
        <result property="nodeInfo"    column="node_info"    />
        <result property="msgtype"    column="msgtype"    />
        <result property="weixinList"    column="weixin_list"    />
        <result property="tempTag"    column="temp_tag"    />
        <result property="tagList"    column="tag_list"    />
        <result property="sendBotWeixin"    column="send_bot_weixin"    />
        <result property="jobId"    column="job_id"    />
        <result property="momentsId"    column="moments_id"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="taskInfo"    column="task_info"    />

    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.plan_id, t.node_info, t.msgtype, t.weixin_list, t.temp_tag, t.tag_list, t.send_bot_weixin, t.job_id, t.moments_id, t.status, t.create_time,t.task_info  </sql>

    <sql id="selectBizMomentsVo">
        select <include refid="Base_Column_List"/>  from biz_moments t
    </sql>

    <select id="selectBizMomentsList" parameterType="com.ruoyi.biz.domain.BizMoments" resultMap="BizMomentsResult">
        <include refid="selectBizMomentsVo"/>
        <where>
            <if test="planId != null "> and t.plan_id = #{planId}</if>
            <if test="nodeInfo != null  and nodeInfo != ''"> and t.node_info = #{nodeInfo}</if>
            <if test="msgtype != null  and msgtype != ''"> and t.msgtype = #{msgtype}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="statusList != null  and statusList.size() > 0 ">
                and t.status in
                <foreach collection="statusList" item="status" separator="," open="(" close=")">
                    #{status}
                </foreach>
            </if>
            <if test="endTimeStr != null  and endTimeStr != ''">
             and  DATE_FORMAT(t.create_time ,'%Y-%m-%d')  >= #{endTimeStr}</if>
        </where>
        order by t.id desc
    </select>

    <select id="selectBizMomentsById" parameterType="Long" resultMap="BizMomentsResult">
        <include refid="selectBizMomentsVo"/>
        where t.id = #{id}
    </select>

    <select id="findJobIdIsNotNUll" resultMap="BizMomentsResult">
        <include refid="selectBizMomentsVo"/>
        where t.job_id is not null
        and t.moments_id is null
    </select>

    <select id="findMomentsIdIsNotNUll" resultMap="BizMomentsResult">
        <include refid="selectBizMomentsVo"/>
        where t.moments_id is not null
    </select>


    <insert id="insertBizMoments" parameterType="com.ruoyi.biz.domain.BizMoments" useGeneratedKeys="true" keyProperty="id">
        insert into biz_moments
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planId != null">plan_id,</if>
            <if test="nodeInfo != null and nodeInfo != ''">node_info,</if>
            <if test="msgtype != null">msgtype,</if>
            <if test="weixinList != null and weixinList != ''">weixin_list,</if>
            <if test="tempTag != null and tempTag != ''">temp_tag,</if>
            <if test="tagList != null and tagList != ''">tag_list,</if>
            <if test="sendBotWeixin != null and sendBotWeixin != ''">send_bot_weixin,</if>
            <if test="jobId != null">job_id,</if>
            <if test="momentsId != null">moments_id,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planId != null">#{planId},</if>
            <if test="nodeInfo != null and nodeInfo != ''">#{nodeInfo},</if>
            <if test="msgtype != null">#{msgtype},</if>
            <if test="weixinList != null and weixinList != ''">#{weixinList},</if>
            <if test="tempTag != null and tempTag != ''">#{tempTag},</if>
            <if test="tagList != null and tagList != ''">#{tagList},</if>
            <if test="sendBotWeixin != null and sendBotWeixin != ''">#{sendBotWeixin},</if>
            <if test="jobId != null">#{jobId},</if>
            <if test="momentsId != null">#{momentsId},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateBizMoments" parameterType="com.ruoyi.biz.domain.BizMoments">
        update biz_moments
        <trim prefix="SET" suffixOverrides=",">
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="nodeInfo != null and nodeInfo != ''">node_info = #{nodeInfo},</if>
            <if test="msgtype != null">msgtype = #{msgtype},</if>
            <if test="weixinList != null and weixinList != ''">weixin_list = #{weixinList},</if>
            <if test="tempTag != null and tempTag != ''">temp_tag = #{tempTag},</if>
            <if test="tagList != null and tagList != ''">tag_list = #{tagList},</if>
            <if test="sendBotWeixin != null and sendBotWeixin != ''">send_bot_weixin = #{sendBotWeixin},</if>
            <if test="jobId != null">job_id = #{jobId},</if>
            <if test="momentsId != null">moments_id = #{momentsId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="taskInfo != null">task_info = #{taskInfo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizMomentsById" parameterType="Long">
        delete from biz_moments where id = #{id}
    </delete>

    <delete id="deleteBizMomentsByIds" parameterType="String">
        delete from biz_moments where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
