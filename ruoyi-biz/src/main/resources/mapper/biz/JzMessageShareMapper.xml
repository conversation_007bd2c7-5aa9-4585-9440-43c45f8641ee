<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.JzMessageShareMapper">

    <resultMap type="com.ruoyi.biz.domain.JzMessageShare" id="JzMessageShareResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="chatId"    column="chat_id"    />
        <result property="botId"    column="bot_id"    />
        <result property="messageIdList"    column="message_id_list"    />
        <result property="createTime"    column="create_time"    />
        <result property="creater"    column="creater"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.name, t.chat_id, t.bot_id, t.message_id_list, t.create_time, t.creater    </sql>

    <sql id="selectJzMessageShareVo">
        select <include refid="Base_Column_List"/>  from jz_message_share t
    </sql>

    <select id="selectJzMessageShareList" parameterType="com.ruoyi.biz.domain.JzMessageShare" resultMap="JzMessageShareResult">
        <include refid="selectJzMessageShareVo"/>
        <where>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="chatId != null  and chatId != ''"> and t.chat_id = #{chatId}</if>
            <if test="botId != null  and botId != ''"> and t.bot_id = #{botId}</if>
            <if test="messageIdList != null  and messageIdList != ''"> and t.message_id_list = #{messageIdList}</if>
            <if test="creater != null  and creater != ''"> and t.creater = #{creater}</if>
        </where>
    </select>

    <select id="selectJzMessageShareById" parameterType="String" resultMap="JzMessageShareResult">
        <include refid="selectJzMessageShareVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertJzMessageShare" parameterType="com.ruoyi.biz.domain.JzMessageShare">
        insert into jz_message_share
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="chatId != null and chatId != ''">chat_id,</if>
            <if test="botId != null and botId != ''">bot_id,</if>
            <if test="messageIdList != null and messageIdList != ''">message_id_list,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creater != null and creater != ''">creater,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="chatId != null and chatId != ''">#{chatId},</if>
            <if test="botId != null and botId != ''">#{botId},</if>
            <if test="messageIdList != null and messageIdList != ''">#{messageIdList},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creater != null and creater != ''">#{creater},</if>
         </trim>
    </insert>

    <update id="updateJzMessageShare" parameterType="com.ruoyi.biz.domain.JzMessageShare">
        update jz_message_share
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="chatId != null and chatId != ''">chat_id = #{chatId},</if>
            <if test="botId != null and botId != ''">bot_id = #{botId},</if>
            <if test="messageIdList != null and messageIdList != ''">message_id_list = #{messageIdList},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creater != null and creater != ''">creater = #{creater},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteJzMessageShareById" parameterType="String">
        delete from jz_message_share where id = #{id}
    </delete>

    <delete id="deleteJzMessageShareByIds" parameterType="String">
        delete from jz_message_share where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
