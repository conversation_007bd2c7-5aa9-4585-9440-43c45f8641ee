<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizPartnerMapper">

    <resultMap type="com.ruoyi.biz.domain.BizPartner" id="BizPartnerResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="wid"    column="wid"    />
        <result property="personalCode"    column="personal_code"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="creater"    column="creater"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.name, t.wid, t.personal_code, t.remark, t.create_time, t.creater    </sql>

    <sql id="selectBizPartnerVo">
        select <include refid="Base_Column_List"/>  from biz_partner t
    </sql>

    <select id="selectBizPartnerList" parameterType="com.ruoyi.biz.domain.BizPartner" resultMap="BizPartnerResult">
        <include refid="selectBizPartnerVo"/>
        <where>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="wid != null  and wid != ''"> and t.wid = #{wid}</if>
        </where>
    </select>

    <select id="selectBizPartnerById" parameterType="Long" resultMap="BizPartnerResult">
        <include refid="selectBizPartnerVo"/>
        where t.id = #{id}
    </select>
    <select id="selectBizPartnerByWid" resultMap="BizPartnerResult">
        <include refid="selectBizPartnerVo"/>
        where t.wid = #{wid}
    </select>

    <insert id="insertBizPartner" parameterType="com.ruoyi.biz.domain.BizPartner" useGeneratedKeys="true" keyProperty="id">
        insert into biz_partner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="wid != null and wid != ''">wid,</if>
            <if test="personalCode != null and personalCode != ''">personal_code,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creater != null">creater,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="wid != null and wid != ''">#{wid},</if>
            <if test="personalCode != null and personalCode != ''">#{personalCode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creater != null">#{creater},</if>
         </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            personal_code = #{personalCode},
        </trim>
    </insert>

    <update id="updateBizPartner" parameterType="com.ruoyi.biz.domain.BizPartner">
        update biz_partner
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="wid != null and wid != ''">wid = #{wid},</if>
            <if test="personalCode != null and personalCode != ''">personal_code = #{personalCode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creater != null">creater = #{creater},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizPartnerById" parameterType="Long">
        delete from biz_partner where id = #{id}
    </delete>

    <delete id="deleteBizPartnerByIds" parameterType="String">
        delete from biz_partner where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
