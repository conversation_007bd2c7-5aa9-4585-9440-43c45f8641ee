<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizH5AppMapper">

    <resultMap type="com.ruoyi.biz.domain.BizH5App" id="BizH5App">
        <result property="id"    column="id"    />
        <result property="appName"    column="app_name"    />
        <result property="apiKey"    column="api_key"    />
        <result property="keywordCode"    column="keyword_code"    />
        <result property="introText"    column="intro_text"    />
        <result property="deleted"    column="deleted"    />
        <result property="configJson"    column="config_json"    />
    </resultMap>

    <insert id="insertBizH5App">
        insert into biz_h5_app(app_name, api_key, keyword_code, inter_text,background_image,config_json)
    </insert>
    <update id="updateBizH5App">
        update biz_h5_app set app_name = #{appName}, api_key = #{apiKey}, keyword_code = #{keywordCode}, inter_text = #{interText}
    </update>
    <select id="selectBizH5AppAll" resultMap="BizH5App">
        select * from biz_h5_app where deleted = 0
    </select>
    <select id="selectBizH5AppById" resultMap="BizH5App">
        select * from biz_h5_app where id = #{id}
    </select>
    <select id="selectBizH5AppList" resultMap="BizH5App">
        select * from biz_h5_app where deleted = 0
    </select>
    <select id="selectBizH5AppByCode"  resultMap="BizH5App">
        select id, app_name, api_key, keyword_code, intro_text, deleted,background_image,config_json from biz_h5_app where keyword_code = #{keywordCode} and deleted = 0 limit 1
    </select>

    <delete id="deleteBizH5AppById">
        delete from biz_h5_app where id = #{id}
    </delete>
    <delete id="deleteBizH5AppByIds">
        delete from biz_h5_app where id in
    </delete>
</mapper>
