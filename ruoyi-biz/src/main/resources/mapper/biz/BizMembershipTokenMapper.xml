<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizMembershipTokenMapper">

    <resultMap type="com.ruoyi.biz.domain.BizMembershipToken" id="BizMembershipTokenResult">
        <result property="id"    column="id"    />
        <result property="unionId"    column="union_id"    />
        <result property="sourceId"    column="source_id"    />
        <result property="sourceType"    column="source_type"    />
        <result property="membershipType"    column="membership_type"    />
        <result property="purchaseDate"    column="purchase_date"    />
        <result property="expirationDate"    column="expiration_date"    />
        <result property="status"    column="status"    />
        <result property="consumptionDate"    column="consumption_date"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.union_id, t.source_id, t.source_type, t.membership_type, t.purchase_date, t.expiration_date, t.status, t.consumption_date, t.create_time, t.create_by, t.update_time, t.update_by    </sql>

    <sql id="selectBizMembershipTokenVo">
        select <include refid="Base_Column_List"/>  from biz_membership_token t
    </sql>

    <select id="selectBizMembershipTokenList" parameterType="com.ruoyi.biz.domain.BizMembershipToken" resultMap="BizMembershipTokenResult">
        <include refid="selectBizMembershipTokenVo"/>
        <where>
            <if test="unionId != null  and unionId != ''"> and t.union_id = #{unionId}</if>
            <if test="sourceId != null  and sourceId != ''"> and t.source_id = #{sourceId}</if>
            <if test="sourceType != null  and sourceType != ''"> and t.source_type = #{sourceType}</if>
            <if test="membershipType != null  and membershipType != ''"> and t.membership_type = #{membershipType}</if>
            <if test="purchaseDate != null "> and t.purchase_date = #{purchaseDate}</if>
            <if test="expirationDate != null "> and t.expiration_date = #{expirationDate}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="consumptionDate != null "> and t.consumption_date = #{consumptionDate}</if>
        </where>
    </select>

    <select id="selectBizMembershipTokenById" parameterType="Long" resultMap="BizMembershipTokenResult">
        <include refid="selectBizMembershipTokenVo"/>
        where t.id = #{id}
    </select>

    <select id="selectBySourceId" parameterType="String" resultMap="BizMembershipTokenResult">
        <include refid="selectBizMembershipTokenVo"/>
        where t.source_id = #{sourceId}
    </select>

    <insert id="insertBizMembershipToken" parameterType="com.ruoyi.biz.domain.BizMembershipToken" useGeneratedKeys="true" keyProperty="id">
        insert into biz_membership_token
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unionId != null and unionId != ''">union_id,</if>
            <if test="sourceId != null and sourceId != ''">source_id,</if>
            <if test="sourceType != null and sourceType != ''">source_type,</if>
            <if test="membershipType != null and membershipType != ''">membership_type,</if>
            <if test="purchaseDate != null">purchase_date,</if>
            <if test="expirationDate != null">expiration_date,</if>
            <if test="status != null">status,</if>
            <if test="consumptionDate != null">consumption_date,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="unionId != null and unionId != ''">#{unionId},</if>
            <if test="sourceId != null and sourceId != ''">#{sourceId},</if>
            <if test="sourceType != null and sourceType != ''">#{sourceType},</if>
            <if test="membershipType != null and membershipType != ''">#{membershipType},</if>
            <if test="purchaseDate != null">#{purchaseDate},</if>
            <if test="expirationDate != null">#{expirationDate},</if>
            <if test="status != null">#{status},</if>
            <if test="consumptionDate != null">#{consumptionDate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
        on duplicate key update
            update_time = now()
    </insert>

    <update id="updateBizMembershipToken" parameterType="com.ruoyi.biz.domain.BizMembershipToken">
        update biz_membership_token
        <trim prefix="SET" suffixOverrides=",">
            <if test="unionId != null and unionId != ''">union_id = #{unionId},</if>
            <if test="sourceId != null and sourceId != ''">source_id = #{sourceId},</if>
            <if test="sourceType != null and sourceType != ''">source_type = #{sourceType},</if>
            <if test="membershipType != null and membershipType != ''">membership_type = #{membershipType},</if>
            <if test="purchaseDate != null">purchase_date = #{purchaseDate},</if>
            <if test="expirationDate != null">expiration_date = #{expirationDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="consumptionDate != null">consumption_date = #{consumptionDate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizMembershipTokenById" parameterType="Long">
        delete from biz_membership_token where id = #{id}
    </delete>

    <delete id="deleteBizMembershipTokenByIds" parameterType="String">
        delete from biz_membership_token where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectLastUsedBizMembershipToken" resultMap="BizMembershipTokenResult">
        select <include refid="Base_Column_List"/>  from biz_membership_token t
        where union_id = #{unionId}
            and status = 'unused'
        order by membership_type desc
        limit 1
    </select>

    <select id="selectSomeAvailableToken" resultMap="BizMembershipTokenResult">
        select <include refid="Base_Column_List"/>  from biz_membership_token t
        where union_id = #{unionId}
        and status = 'unused'
        order by membership_type desc
        limit 1
    </select>

    <select id="selectMemberStoreUser" resultMap="BizMembershipTokenResult">
        select <include refid="Base_Column_List"/>
        from biz_membership_token t
                 left join biz_user bu on t.union_id = bu.union_id
                 left join zx_store_user zsu on bu.wid = zsu.wid
        where t.status = 'used'
          and t.source_type = 'weimob'
          and t.membership_type = 2
          and zsu.id is null
    </select>

</mapper>
