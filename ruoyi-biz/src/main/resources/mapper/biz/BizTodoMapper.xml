<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizTodoMapper">

    <resultMap type="com.ruoyi.biz.domain.BizTodo" id="BizTodoResult">
        <result property="id"    column="id"    />
        <result property="refId"    column="ref_id"    />
        <result property="refType"    column="ref_type"    />
        <result property="refName"    column="ref_name"    />
        <result property="content"    column="content"    />
        <result property="state"    column="state"    />
        <result property="processor"    column="processor"    />
        <result property="processorTime"    column="processor_time"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.ref_id, t.ref_type, ref_name, t.content, t.state, t.processor, t.processor_time, t.remark, t.create_time, t.create_by, t.update_time, t.update_by    </sql>

    <sql id="selectBizTodoVo">
        select <include refid="Base_Column_List"/>  from biz_todo t
    </sql>

    <select id="selectBizTodoList" parameterType="com.ruoyi.biz.domain.BizTodo" resultMap="BizTodoResult">
        <include refid="selectBizTodoVo"/>
        <where>
            <if test="refId != null  and refId != ''"> and t.ref_id = #{refId}</if>
            <if test="refType != null "> and t.ref_type = #{refType}</if>
            <if test="refName != null and refName != ''"> and t.ref_name = #{refName}</if>
            <if test="content != null  and content != ''"> and t.content = #{content}</if>
            <if test="state != null "> and t.state = #{state}</if>
            <if test="processor != null  and processor != ''"> and t.processor = #{processor}</if>
            <if test="params.beginProcessorTime != null and params.beginProcessorTime != '' and params.endProcessorTime != null and params.endProcessorTime != ''"> and t.processor_time between #{params.beginProcessorTime} and #{params.endProcessorTime}</if>
        </where>
        order by id desc
    </select>

    <select id="selectBizTodoById" parameterType="Long" resultMap="BizTodoResult">
        <include refid="selectBizTodoVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizTodo" parameterType="com.ruoyi.biz.domain.BizTodo" useGeneratedKeys="true" keyProperty="id">
        insert into biz_todo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="refId != null">ref_id,</if>
            <if test="refType != null">ref_type,</if>
            <if test="refName != null and refName != ''">ref_name,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="state != null">state,</if>
            <if test="processor != null">processor,</if>
            <if test="processorTime != null">processor_time,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="refId != null">#{refId},</if>
            <if test="refType != null">#{refType},</if>
            <if test="refName != null and refName != ''">#{ref_name},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="state != null">#{state},</if>
            <if test="processor != null">#{processor},</if>
            <if test="processorTime != null">#{processorTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateBizTodo" parameterType="com.ruoyi.biz.domain.BizTodo">
        update biz_todo
        <trim prefix="SET" suffixOverrides=",">
            <if test="refId != null">ref_id = #{refId},</if>
            <if test="refType != null">ref_type = #{refType},</if>
            <if test="refName != null and refName != ''">ref_name = #{refName},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="state != null">state = #{state},</if>
            <if test="processor != null">processor = #{processor},</if>
            <if test="processorTime != null">processor_time = #{processorTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizTodoById" parameterType="Long">
        delete from biz_todo where id = #{id}
    </delete>

    <delete id="deleteBizTodoByIds" parameterType="String">
        delete from biz_todo where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
