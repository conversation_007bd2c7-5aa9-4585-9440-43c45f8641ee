<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.CorpusDatasetMapper">

    <resultMap type="com.ruoyi.biz.domain.CorpusDataset" id="CorpusDatasetResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="knowledgeEnum"    column="knowledge_enum"    />
        <result property="type"    column="type"    />
        <result property="docForm"    column="doc_form"    />
        <result property="datasetId"    column="dataset_id"    />
        <result property="isShow"    column="is_show"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.name, t.knowledge_enum,t.type, t.doc_form, t.dataset_id, t.is_show
    </sql>

    <sql id="selectCorpusDatasetVo">
        select <include refid="Base_Column_List"/>  from corpus_dataset t
    </sql>

    <select id="selectCorpusDatasetList" parameterType="com.ruoyi.biz.domain.CorpusDataset" resultMap="CorpusDatasetResult">
        <include refid="selectCorpusDatasetVo"/>
        <where>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="knowledgeEnum != null  and knowledgeEnum != ''"> and t.knowledge_enum = #{knowledgeEnum}</if>
            <if test="type != null  and type != ''"> and t.type = #{type}</if>
            <if test="docForm != null  and docForm != ''"> and t.doc_form = #{docForm}</if>
            <if test="datasetId != null  and datasetId != ''"> and t.dataset_id = #{datasetId}</if>
            <if test="isShow != null  and isShow != ''"> and t.is_show = #{isShow}</if>
        </where>
        order by id desc
    </select>

    <select id="selectCorpusDatasetById" parameterType="Long" resultMap="CorpusDatasetResult">
        <include refid="selectCorpusDatasetVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertCorpusDataset" parameterType="com.ruoyi.biz.domain.CorpusDataset" useGeneratedKeys="true" keyProperty="id">
        insert into corpus_dataset
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="knowledgeEnum != null">knowledge_enum,</if>
            <if test="type != null">`type`,</if>
            <if test="docForm != null">doc_form,</if>
            <if test="datasetId != null">dataset_id,</if>
            <if test="isShow != null and isShow != ''">is_show,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="knowledgeEnum != null">#{knowledgeEnum},</if>
            <if test="type != null">#{type},</if>
            <if test="docForm != null">#{docForm},</if>
            <if test="datasetId != null">#{datasetId},</if>
            <if test="isShow != null and isShow != ''">#{isShow},</if>
         </trim>
    </insert>

    <update id="updateCorpusDataset" parameterType="com.ruoyi.biz.domain.CorpusDataset">
        update corpus_dataset
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="knowledgeEnum != null">knowledge_enum = #{knowledgeEnum},</if>
            <if test="docForm != null">doc_form = #{docForm},</if>
            <if test="datasetId != null">dataset_id = #{datasetId},</if>
            <if test="isShow != null and isShow != ''">is_show = #{isShow},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCorpusDatasetById" parameterType="Long">
        delete from corpus_dataset where id = #{id}
    </delete>

    <delete id="deleteCorpusDatasetByIds" parameterType="String">
        delete from corpus_dataset where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
