<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizCelebrityMaterialMapper">

    <resultMap type="com.ruoyi.biz.domain.BizCelebrityMaterial" id="BizCelebrityMaterialResult">
        <result property="id" column="id"/>
        <result property="starIds" column="star_ids"/>
        <result property="starNameList" column="star_names"/>
        <result property="type" column="type"/>
        <result property="name" column="name"/>
        <result property="url" column="url"/>
        <result property="remark" column="remark"/>
        <result property="publishTime" column="publish_time"/>
        <result property="createTime" column="create_time"/>
        <result property="isDisclose" column="is_disclose"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.star_ids, t.type, t.name, t.url,t.remark, t.publish_time, t.create_time,is_disclose
    </sql>


    <select id="selectBizCelebrityMaterialList" parameterType="com.ruoyi.biz.domain.BizCelebrityMaterial"
            resultMap="BizCelebrityMaterialResult">
            SELECT
                    <include refid="Base_Column_List"/>
                    ,GROUP_CONCAT(bc.name) AS star_names -- 关联的明星姓名
                    FROM
                    biz_celebrity_material t
                    LEFT JOIN
                    biz_celebrity bc
                    ON
                    FIND_IN_SET(bc.id, t.star_ids) > 0
                <where>
                    <if test="type != null  and type != ''">and t.type = #{type}</if>
                    <if test="name != null  and name != ''">and t.name like concat('%', #{name}, '%')</if>
                    <if test="url != null  and url != ''">and t.url like concat('%', #{url}, '%')</if>
                    <if test="publishTime != null ">and DATE_FORMAT(t.publish_time,'%Y-%m-%d') = DATE_FORMAT(#{publishTime},'%Y-%m-%d') </if>
                    <if test="isDisclose != null ">and t.is_disclose = #{isDisclose}</if>
                    <if test="statName != null  and statName != ''">and bc.name like concat('%', #{statName}, '%')</if>
                </where>
                GROUP BY  t.id
                order by t.id desc
    </select>

    <select id="selectBizCelebrityMaterialById" parameterType="Long" resultMap="BizCelebrityMaterialResult">
        SELECT
        <include refid="Base_Column_List"/>
        ,GROUP_CONCAT(bc.name) AS star_names -- 关联的明星姓名
        FROM
        biz_celebrity_material t
        LEFT JOIN
        biz_celebrity bc
        ON
        FIND_IN_SET(bc.id, t.star_ids) > 0
        where t.id = #{id}
        GROUP BY  t.id
    </select>

    <insert id="insertBizCelebrityMaterial" parameterType="com.ruoyi.biz.domain.BizCelebrityMaterial">
        insert into biz_celebrity_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="starIds != null">star_ids,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="url != null and url != ''">url,</if>
            <if test="remark != null">remark,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="isDisclose != null">is_disclose,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="starIds != null">#{starIds},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="url != null and url != ''">#{url},</if>
            <if test="remark != null">#{remark},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="isDisclose != null">#{isDisclose},</if>
        </trim>
    </insert>

    <insert id="batchInsert">
        insert into biz_celebrity_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id, star_ids, type, name, url, remark, publish_time, create_time, is_disclose
        </trim>
        values
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <choose>
                    <when test="item.id != null">#{item.id},</when>
                    <otherwise>null,</otherwise>
                </choose>
                <choose>
                    <when test="item.starIds != null">#{item.starIds},</when>
                    <otherwise>null,</otherwise>
                </choose>
                <choose>
                    <when test="item.type != null and item.type != ''">#{item.type},</when>
                    <otherwise>null,</otherwise>
                </choose>
                <choose>
                    <when test="item.name != null and item.name != ''">#{item.name},</when>
                    <otherwise>null,</otherwise>
                </choose>
                <choose>
                    <when test="item.url != null and item.url != ''">#{item.url},</when>
                    <otherwise>null,</otherwise>
                </choose>
                <choose>
                    <when test="item.remark != null">#{item.remark},</when>
                    <otherwise>null,</otherwise>
                </choose>
                <choose>
                    <when test="item.publishTime != null">#{item.publishTime},</when>
                    <otherwise>null,</otherwise>
                </choose>
                <choose>
                    <when test="item.createTime != null">#{item.createTime},</when>
                    <otherwise>null,</otherwise>
                </choose>
                <choose>
                    <when test="item.isDisclose != null">#{item.isDisclose},</when>
                    <otherwise>null,</otherwise>
                </choose>
            </trim>
        </foreach>
    </insert>

    <update id="updateBizCelebrityMaterial" parameterType="com.ruoyi.biz.domain.BizCelebrityMaterial">
        update biz_celebrity_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="starIds != null">star_ids = #{starIds},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="url != null and url != ''">url = #{url},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="isDisclose != null">is_disclose = #{isDisclose},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizCelebrityMaterialById" parameterType="Long">
        delete
        from biz_celebrity_material
        where id = #{id}
    </delete>

    <delete id="deleteBizCelebrityMaterialByIds" parameterType="String">
        delete from biz_celebrity_material where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
