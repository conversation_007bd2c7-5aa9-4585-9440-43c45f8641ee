<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizHotWordDbMapper">

    <resultMap type="com.ruoyi.biz.domain.BizHotWordDb" id="BizHotWordDbResult">
        <result property="id"    column="id"    />
        <result property="hotWordName"    column="hot_word_name"    />
        <result property="derivativesJson"    column="derivatives_json"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.hot_word_name, t.derivatives_json, t.create_by, t.create_time, t.update_by, t.update_time    </sql>

    <sql id="selectBizHotWordDbVo">
        select <include refid="Base_Column_List"/>  from biz_hot_word_db t
    </sql>

    <select id="selectBizHotWordDbList" parameterType="com.ruoyi.biz.domain.BizHotWordDb" resultMap="BizHotWordDbResult">
        <include refid="selectBizHotWordDbVo"/>
        <where>
            <if test="hotWordName != null  and hotWordName != ''"> and t.hot_word_name like concat('%', #{hotWordName}, '%')</if>
            <if test="derivativesJson != null  and derivativesJson != ''"> and t.derivatives_json like concat('%', #{derivativesJson}, '%')</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and t.create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        </where>
        order by t.id desc
    </select>

    <select id="selectBizHotWordDbById" parameterType="Long" resultMap="BizHotWordDbResult">
        <include refid="selectBizHotWordDbVo"/>
        where t.id = #{id}
    </select>
    <select id="selectBizHotWordDbAll" resultType="com.ruoyi.biz.domain.vo.BizHotWordDbVo">
        select t.id, t.hot_word_name as hotWordName
        from biz_hot_word_db t
    </select>


    <select id="checkIsHotWords" resultType="java.lang.String">
        select  t.hot_word_name
        from biz_hot_word_db t
        where t.hot_word_name in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBizHotWordDb" parameterType="com.ruoyi.biz.domain.BizHotWordDb" useGeneratedKeys="true" keyProperty="id">
        insert into biz_hot_word_db
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hotWordName != null and hotWordName != ''">hot_word_name,</if>
            <if test="derivativesJson != null">derivatives_json,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hotWordName != null and hotWordName != ''">#{hotWordName},</if>
            <if test="derivativesJson != null">#{derivativesJson},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
        on duplicate key update
        `hot_word_name`=  #{hotWordName}
    </insert>

    <update id="updateBizHotWordDb" parameterType="com.ruoyi.biz.domain.BizHotWordDb">
        update biz_hot_word_db
        <trim prefix="SET" suffixOverrides=",">
            <if test="hotWordName != null and hotWordName != ''">hot_word_name = #{hotWordName},</if>
            <if test="derivativesJson != null">derivatives_json = #{derivativesJson},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizHotWordDbById" parameterType="Long">
        delete from biz_hot_word_db where id = #{id}
    </delete>

    <delete id="deleteBizHotWordDbByIds" parameterType="String">
        delete from biz_hot_word_db where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
