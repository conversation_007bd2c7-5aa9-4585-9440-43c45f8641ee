<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizAppTopicMapper">

    <resultMap type="com.ruoyi.biz.domain.BizAppTopic" id="BizAppTopicResult">
        <result property="id"    column="id"    />
        <result property="appKey"    column="app_key"    />
        <result property="topicName"    column="topic_name"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.app_key, t.topic_name, t.remark, t.status    </sql>

    <sql id="selectBizAppTopicVo">
        select <include refid="Base_Column_List"/>  from biz_app_topic t
    </sql>

    <select id="selectBizAppTopicList" parameterType="com.ruoyi.biz.domain.BizAppTopic" resultMap="BizAppTopicResult">
        <include refid="selectBizAppTopicVo"/>
        <where>
            <if test="appKey != null  and appKey != ''"> and t.app_key = #{appKey}</if>
            <if test="topicName != null  and topicName != ''"> and t.topic_name like concat('%', #{topicName}, '%')</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
        </where>
    </select>

    <select id="selectBizAppTopicById" parameterType="Long" resultMap="BizAppTopicResult">
        <include refid="selectBizAppTopicVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizAppTopic" parameterType="com.ruoyi.biz.domain.BizAppTopic" useGeneratedKeys="true" keyProperty="id">
        insert into biz_app_topic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appKey != null and appKey != ''">app_key,</if>
            <if test="topicName != null and topicName != ''">topic_name,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="status != null and status != ''">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appKey != null and appKey != ''">#{appKey},</if>
            <if test="topicName != null and topicName != ''">#{topicName},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="status != null and status != ''">#{status},</if>
         </trim>
    </insert>

    <update id="updateBizAppTopic" parameterType="com.ruoyi.biz.domain.BizAppTopic">
        update biz_app_topic
        <trim prefix="SET" suffixOverrides=",">
            <if test="appKey != null and appKey != ''">app_key = #{appKey},</if>
            <if test="topicName != null and topicName != ''">topic_name = #{topicName},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="status != null and status != ''">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizAppTopicById" parameterType="Long">
        delete from biz_app_topic where id = #{id}
    </delete>

    <delete id="deleteBizAppTopicByIds" parameterType="String">
        delete from biz_app_topic where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
