<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizWordAnalyzedMapper">

    <resultMap type="com.ruoyi.biz.domain.BizWordAnalyzed" id="BizWordAnalyzedResult">
        <result property="id"    column="id"    />
        <result property="wordName"    column="word_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.word_name, t.create_time, t.create_by, t.update_time, t.update_by    </sql>

    <sql id="selectBizWordAnalyzedVo">
        select <include refid="Base_Column_List"/>  from biz_word_analyzed t
    </sql>

    <select id="selectBizWordAnalyzedList" parameterType="com.ruoyi.biz.domain.BizWordAnalyzed" resultMap="BizWordAnalyzedResult">
        <include refid="selectBizWordAnalyzedVo"/>
        <where>
            <if test="wordName != null  and wordName != ''"> and t.word_name like concat('%', #{wordName}, '%')</if>
        </where>
    </select>

    <select id="selectBizWordAnalyzedById" parameterType="Long" resultMap="BizWordAnalyzedResult">
        <include refid="selectBizWordAnalyzedVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizWordAnalyzed" parameterType="com.ruoyi.biz.domain.BizWordAnalyzed" useGeneratedKeys="true" keyProperty="id">
        insert into biz_word_analyzed
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="wordName != null and wordName != ''">word_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="wordName != null and wordName != ''">#{wordName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>


    <insert id="batchInsertBizWordAnalyzed" keyProperty="id" useGeneratedKeys="true">
        insert into biz_word_analyzed (id,word_name,create_time,create_by)
        values
        <foreach collection="bizWordAnalyzedList" item="entity" separator=",">
            (#{entity.id}, #{entity.wordName}, #{entity.createTime}, #{entity.createBy})
        </foreach>
        on duplicate key update
        update_time=now()
    </insert>


    <update id="updateBizWordAnalyzed" parameterType="com.ruoyi.biz.domain.BizWordAnalyzed">
        update biz_word_analyzed
        <trim prefix="SET" suffixOverrides=",">
            <if test="wordName != null and wordName != ''">word_name = #{wordName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizWordAnalyzedById" parameterType="Long">
        delete from biz_word_analyzed where id = #{id}
    </delete>

    <delete id="deleteBizWordAnalyzedByIds" parameterType="String">
        delete from biz_word_analyzed where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectBizWordAnalyzedByWordNameList" resultMap="BizWordAnalyzedResult">
        select <include refid="Base_Column_List"/>  from biz_word_analyzed t
        WHERE word_name IN
        <foreach collection="wordNameList" item="wordName" open="(" separator="," close=")">
            #{wordName}
        </foreach>
    </select>
</mapper>
