<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.StatChatHotWordDayMapper">

    <resultMap type="com.ruoyi.biz.domain.StatChatHotWordDay" id="StatChatHotWordDayResult">
        <result property="id"    column="id"    />
        <result property="dayTime"    column="day_time"    />
        <result property="chatId"    column="chat_id"    />
        <result property="type"    column="type"    />
        <result property="wordSort"    column="word_sort"    />
        <result property="business"    column="business"    />
        <result property="waitAiWord"    column="wait_ai_word"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.day_time, t.chat_id, t.type, t.word_sort, t.business, t.wait_ai_word, t.create_time    </sql>

    <sql id="selectStatChatHotWordDayVo">
        select <include refid="Base_Column_List"/>  from stat_chat_hot_word_day t
    </sql>

    <select id="selectStatChatHotWordDayList" parameterType="com.ruoyi.biz.domain.StatChatHotWordDay" resultMap="StatChatHotWordDayResult">
        <include refid="selectStatChatHotWordDayVo"/>
        <where>
            <if test="params.beginDayTime != null and params.beginDayTime != '' and params.endDayTime != null and params.endDayTime != ''"> and t.day_time between #{params.beginDayTime} and #{params.endDayTime}</if>
            <if test="chatId != null  and chatId != ''"> and t.chat_id = #{chatId}</if>
            <if test="type != null  and type != ''"> and t.type = #{type}</if>
            <if test="wordSort != null  and wordSort != ''"> and t.word_sort = #{wordSort}</if>
            <if test="business != null  and business != ''"> and t.business = #{business}</if>
            <if test="waitAiWord != null  and waitAiWord != ''"> and t.wait_ai_word = #{waitAiWord}</if>
        </where>
        order by t.id desc
    </select>

    <select id="selectStatChatHotWordDayById" parameterType="Long" resultMap="StatChatHotWordDayResult">
        <include refid="selectStatChatHotWordDayVo"/>
        where t.id = #{id}
    </select>


    <insert id="insertStatChatHotWordDay" parameterType="com.ruoyi.biz.domain.StatChatHotWordDay" useGeneratedKeys="true" keyProperty="id">
        insert into stat_chat_hot_word_day
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dayTime != null and dayTime != ''">day_time,</if>
            <if test="chatId != null and chatId != ''">chat_id,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="wordSort != null and wordSort != ''">word_sort,</if>
            <if test="business != null">business,</if>
            <if test="waitAiWord != null">wait_ai_word,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dayTime != null and dayTime != ''">#{dayTime},</if>
            <if test="chatId != null and chatId != ''">#{chatId},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="wordSort != null and wordSort != ''">#{wordSort},</if>
            <if test="business != null">#{business},</if>
            <if test="waitAiWord != null">#{waitAiWord},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateStatChatHotWordDay" parameterType="com.ruoyi.biz.domain.StatChatHotWordDay">
        update stat_chat_hot_word_day
        <trim prefix="SET" suffixOverrides=",">
            <if test="dayTime != null and dayTime != ''">day_time = #{dayTime},</if>
            <if test="chatId != null and chatId != ''">chat_id = #{chatId},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="wordSort != null and wordSort != ''">word_sort = #{wordSort},</if>
            <if test="business != null">business = #{business},</if>
            <if test="waitAiWord != null">wait_ai_word = #{waitAiWord},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStatChatHotWordDayById" parameterType="Long">
        delete from stat_chat_hot_word_day where id = #{id}
    </delete>

    <delete id="deleteStatChatHotWordDayByIds" parameterType="String">
        delete from stat_chat_hot_word_day where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
