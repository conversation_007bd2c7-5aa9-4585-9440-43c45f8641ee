<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizMomentsCommentMapper">

    <resultMap type="com.ruoyi.biz.domain.BizMomentsComment" id="BizMomentsCommentResult">
        <result property="id" column="id"/>
        <result property="commentList" column="comment_list"/>
        <result property="likeList" column="like_list"/>
        <result property="momentsId" column="moments_id"/>
        <result property="botId" column="bot_id"/>
    </resultMap>

    <resultMap type="com.ruoyi.biz.domain.vo.BizMomentsCommentVo" id="BizMomentsCommentResultVo">
        <result property="id" column="id"/>
        <result property="commentList" column="comment_list"/>
        <result property="likeList" column="like_list"/>
        <result property="momentsId" column="moments_id"/>
        <result property="botId" column="bot_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.comment_list, t.like_list, t.moments_id, t.bot_id    </sql>

    <sql id="selectBizMomentsCommentVo">
        select
        <include refid="Base_Column_List"/>
        from biz_moments_comment t
    </sql>

    <select id="selectBizMomentsCommentList" parameterType="com.ruoyi.biz.domain.BizMomentsComment"
            resultMap="BizMomentsCommentResult">
        <include refid="selectBizMomentsCommentVo"/>
        <where>
            <if test="commentList != null  and commentList != ''">and t.comment_list = #{commentList}</if>
            <if test="likeList != null  and likeList != ''">and t.like_list = #{likeList}</if>
            <if test="momentsId != null  and momentsId != ''">and t.moments_id = #{momentsId}</if>
            <if test="botId != null  and botId != ''">and t.bot_id = #{botId}</if>
            <if test="momentsIdList != null  and momentsIdList.size() > 0">
                and t.moments_id in
                <foreach collection="momentsIdList" open="(" close=")" separator="," item="momentsId" >
                    #{momentsId}
                </foreach>
            </if>
        </where>
        order by t.id desc
    </select>

    <select id="selectBizMomentsCommentById" parameterType="Long" resultMap="BizMomentsCommentResult">
        <include refid="selectBizMomentsCommentVo"/>
        where t.id = #{id}
    </select>
    <select id="selectByBotIdAndMomentsId" resultMap="BizMomentsCommentResult">
        <include refid="selectBizMomentsCommentVo"/>
        where
        t.bot_id = #{botId}
        and t.moments_id = #{momentsId}
    </select>

    <insert id="insertBizMomentsComment" parameterType="com.ruoyi.biz.domain.BizMomentsComment" useGeneratedKeys="true"
            keyProperty="id">
        insert into biz_moments_comment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="commentList != null">comment_list,</if>
            <if test="likeList != null">like_list,</if>
            <if test="momentsId != null">moments_id,</if>
            <if test="botId != null">bot_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="commentList != null">#{commentList},</if>
            <if test="likeList != null">#{likeList},</if>
            <if test="momentsId != null">#{momentsId},</if>
            <if test="botId != null">#{botId},</if>
        </trim>
        ON DUPLICATE KEY UPDATE
        comment_list = IFNULL(values(comment_list),comment_list),
        like_list=IFNULL(values(like_list),like_list)
    </insert>

    <update id="updateBizMomentsComment" parameterType="com.ruoyi.biz.domain.BizMomentsComment">
        update biz_moments_comment
        <trim prefix="SET" suffixOverrides=",">
            <if test="commentList != null">comment_list = #{commentList},</if>
            <if test="likeList != null">like_list = #{likeList},</if>
            <if test="momentsId != null">moments_id = #{momentsId},</if>
            <if test="botId != null">bot_id = #{botId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizMomentsCommentById" parameterType="Long">
        delete
        from biz_moments_comment
        where id = #{id}
    </delete>

    <delete id="deleteBizMomentsCommentByIds" parameterType="String">
        delete from biz_moments_comment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="delete">
        delete
        from biz_moments_comment
        where bot_id = #{botId}
          and moments_id = #{momentsId}
    </delete>
</mapper>
