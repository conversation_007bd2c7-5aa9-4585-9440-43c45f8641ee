<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizWorkOrderMapper">

    <resultMap type="com.ruoyi.biz.domain.BizWorkOrder" id="BizWorkOrderResult">
        <result property="id"    column="id"    />
        <result property="chatId"    column="chat_id"    />
        <result property="messageId"    column="message_id"    />
        <result property="contactId"    column="contact_id"    />
        <result property="contactName"    column="contact_name"    />
        <result property="messageText"    column="message_text"    />
        <result property="status"    column="status"    />
        <result property="type"    column="type"    />
        <result property="processor"    column="processor"    />
        <result property="description"    column="description"    />
        <result property="expandInfo"    column="expand_info"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.chat_id, t.message_id, t.contact_id, t.contact_name, t.message_text, t.status, t.type, t.processor, t.description, t.expand_info, t.create_time, t.create_by, t.update_time, t.update_by    </sql>

    <sql id="selectBizWorkOrderVo">
        select <include refid="Base_Column_List"/>  from biz_work_order t
    </sql>

    <select id="selectBizWorkOrderList" parameterType="com.ruoyi.biz.domain.BizWorkOrder" resultMap="BizWorkOrderResult">
        <include refid="selectBizWorkOrderVo"/>
        <where>
            <if test="chatId != null  and chatId != ''"> and t.chat_id = #{chatId}</if>
            <if test="messageId != null  and messageId != ''"> and t.message_id = #{messageId}</if>
            <if test="contactId != null "> and t.contact_id = #{contactId}</if>
            <if test="contactName != null  and contactName != ''"> and t.contact_name like concat('%', #{contactName}, '%')</if>
            <if test="messageText != null  and messageText != ''"> and t.message_text = #{messageText}</if>
            <if test="status != null "> and t.status = #{status}</if>
            <if test="type != null "> and t.type = #{type}</if>
            <if test="processor != null  and processor != ''"> and t.processor = #{processor}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and t.create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        </where>
        order by id desc
    </select>

    <select id="selectBizWorkOrderById" parameterType="Long" resultMap="BizWorkOrderResult">
        <include refid="selectBizWorkOrderVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizWorkOrder" parameterType="com.ruoyi.biz.domain.BizWorkOrder" useGeneratedKeys="true" keyProperty="id">
        insert into biz_work_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="chatId != null and chatId != ''">chat_id,</if>
            <if test="messageId != null and messageId != ''">message_id,</if>
            <if test="contactId != null">contact_id,</if>
            <if test="contactName != null and contactName != ''">contact_name,</if>
            <if test="messageText != null and messageText != ''">message_text,</if>
            <if test="status != null">status,</if>
            <if test="type != null">type,</if>
            <if test="processor != null">processor,</if>
            <if test="description != null">description,</if>
            <if test="expandInfo != null">expand_info,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chatId != null and chatId != ''">#{chatId},</if>
            <if test="messageId != null and messageId != ''">#{messageId},</if>
            <if test="contactId != null">#{contactId},</if>
            <if test="contactName != null and contactName != ''">#{contactName},</if>
            <if test="messageText != null and messageText != ''">#{messageText},</if>
            <if test="status != null">#{status},</if>
            <if test="type != null">#{type},</if>
            <if test="processor != null">#{processor},</if>
            <if test="description != null">#{description},</if>
            <if test="expandInfo != null">#{expandInfo},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateBizWorkOrder" parameterType="com.ruoyi.biz.domain.BizWorkOrder">
        update biz_work_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="chatId != null and chatId != ''">chat_id = #{chatId},</if>
            <if test="messageId != null and messageId != ''">message_id = #{messageId},</if>
            <if test="contactId != null">contact_id = #{contactId},</if>
            <if test="contactName != null and contactName != ''">contact_name = #{contactName},</if>
            <if test="messageText != null and messageText != ''">message_text = #{messageText},</if>
            <if test="status != null">status = #{status},</if>
            <if test="type != null">type = #{type},</if>
            <if test="processor != null">processor = #{processor},</if>
            <if test="description != null">description = #{description},</if>
            <if test="expandInfo != null">expand_info = #{expandInfo},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizWorkOrderById" parameterType="Long">
        delete from biz_work_order where id = #{id}
    </delete>

    <delete id="deleteBizWorkOrderByIds" parameterType="String">
        delete from biz_work_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
