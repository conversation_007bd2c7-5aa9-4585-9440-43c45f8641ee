<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizWordAnalyzedRecordMapper">

    <resultMap type="com.ruoyi.biz.domain.BizWordAnalyzedRecord" id="BizWordAnalyzedRecordResult">
        <result property="id"    column="id"    />
        <result property="refId"    column="ref_id"    />
        <result property="refType"    column="ref_type"    />
        <result property="wordList"    column="word_list"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.ref_id, t.ref_type, t.word_list, t.create_time, t.create_by    </sql>

    <sql id="selectBizWordAnalyzedRecordVo">
        select <include refid="Base_Column_List"/>  from biz_word_analyzed_record t
    </sql>

    <select id="selectBizWordAnalyzedRecordList" parameterType="com.ruoyi.biz.domain.BizWordAnalyzedRecord" resultMap="BizWordAnalyzedRecordResult">
        <include refid="selectBizWordAnalyzedRecordVo"/>
        <where>
            <if test="refId != null  and refId != ''"> and t.ref_id = #{refId}</if>
            <if test="refType != null  and refType != ''"> and t.ref_type = #{refType}</if>
            <if test="wordList != null  and wordList != ''"> and t.word_list = #{wordList}</if>
        </where>
    </select>

    <select id="selectBizWordAnalyzedRecordById" parameterType="Long" resultMap="BizWordAnalyzedRecordResult">
        <include refid="selectBizWordAnalyzedRecordVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizWordAnalyzedRecord" parameterType="com.ruoyi.biz.domain.BizWordAnalyzedRecord" useGeneratedKeys="true" keyProperty="id">
        insert into biz_word_analyzed_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="refId != null and refId != ''">ref_id,</if>
            <if test="refType != null and refType != ''">ref_type,</if>
            <if test="wordList != null and wordList != ''">word_list,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="refId != null and refId != ''">#{refId},</if>
            <if test="refType != null and refType != ''">#{refType},</if>
            <if test="wordList != null and wordList != ''">#{wordList},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
         </trim>
    </insert>

    <update id="updateBizWordAnalyzedRecord" parameterType="com.ruoyi.biz.domain.BizWordAnalyzedRecord">
        update biz_word_analyzed_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="refId != null and refId != ''">ref_id = #{refId},</if>
            <if test="refType != null and refType != ''">ref_type = #{refType},</if>
            <if test="wordList != null and wordList != ''">word_list = #{wordList},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizWordAnalyzedRecordById" parameterType="Long">
        delete from biz_word_analyzed_record where id = #{id}
    </delete>

    <delete id="deleteBizWordAnalyzedRecordByIds" parameterType="String">
        delete from biz_word_analyzed_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
