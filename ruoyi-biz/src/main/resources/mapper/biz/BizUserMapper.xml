<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizUserMapper">

    <resultMap type="com.ruoyi.biz.domain.BizUser" id="BizUserResult">
        <result property="id"    column="id"    />
        <result property="wid"    column="wid"    />
        <result property="wmNickname"    column="wm_nickname"    />
        <result property="wmPhoto"    column="wm_photo"    />
        <result property="wxid"    column="wxid"    />
        <result property="wxNickname"    column="wx_nickname"    />
        <result property="wxPhoto"    column="wx_photo"    />
        <result property="unionId"    column="union_id"    />
        <result property="membershipType"    column="membership_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="temp"    column="temp"    />
        <result property="tempState"    column="temp_state"    />
        <result property="isSaints"    column="is_saints"    />
        <result property="dueDate"    column="due_date"    />
        <result property="planId"    column="plan_id"    />
        <result property="friendTime" column="friend_time"    />
        <result property="accountInfo" column="account_info"    />

    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.wid, t.wm_nickname, t.wm_photo, t.wxid, t.wx_nickname, t.wx_photo, t.temp, t.temp_state, t.is_saints, t.due_date, t.plan_id, t.union_id, t.friend_time, t.create_time, t.membership_type,account_info   </sql>

    <sql id="selectBizUserVo">
        select <include refid="Base_Column_List"/>  from biz_user t
    </sql>


    <select id="selectBizUserList" parameterType="com.ruoyi.biz.domain.BizUser" resultMap="BizUserResult">
        <include refid="selectBizUserVo"/>
        <if test="tagSearchIdList != null  and tagSearchIdList.size() > 0  and tagSearchType != null ">
            left join `biz_taggable` tag ON t.`union_id` = tag.`ref_id` AND tag.`ref_type` ='user'
        </if>
        <if test="params.friendType != null and params.friendType != ''">
            left join jz_contact c on c.union_id = t.union_id
        </if>
        <where>
            <if test="wid != null "> and t.wid = #{wid}</if>
            <if test="wmNickname != null  and wmNickname != ''"> and t.wm_nickname like concat('%', #{wmNickname}, '%')</if>
            <if test="wmPhoto != null  and wmPhoto != ''"> and t.wm_photo = #{wmPhoto}</if>
            <if test="wxid != null  and wxid != ''"> and t.wxid = #{wxid}</if>
            <if test="wxNickname != null  and wxNickname != ''"> AND LOWER(t.wx_nickname) LIKE CONCAT('%', LOWER(#{wxNickname}), '%')</if>
            <if test="wxPhoto != null  and wxPhoto != ''"> and t.wx_photo = #{wxPhoto}</if>
            <if test="unionId != null  and unionId != ''"> and t.union_id = #{unionId}</if>
            <if test="membershipType != null  and membershipType != ''"> and t.membership_type = #{membershipType}</if>
            <if test="temp != null and temp != ''">t.temp = #{temp}</if>
            <if test="tempState != null  and tempState != ''"> and t.temp_state = #{tempState}</if>
            <if test="isSaints != null  and isSaints != ''"> and t.is_saints = #{isSaints}</if>
            <if test="params.beginJoinTime != null and params.beginJoinTime != '' and params.endJoinTime != null and params.endJoinTime != ''">
                and t.friend_time between #{params.beginJoinTime} and #{params.endJoinTime}
            </if>
            <if test="state != null and state != ''">
                <choose>
                    <when test="state == 0">
                        AND t.membership_type = '0'
                    </when>
                    <when test="state == 1">
                        AND t.membership_type &gt; '0'
                    </when>
                </choose>
            </if>
            <if test="params.userType != null  and params.userType != ''">
                <choose>
                    <when test="params.userType == '0'.toString()">
                        and t.wxid is not null and t.union_id is not null
                    </when>
                    <when test="params.userType == '1'.toString()">
                        and t.wxid is not null and t.union_id is  null
                    </when>
                    <when test="params.userType == '2'.toString()">
                        and t.wid is not null and t.wxid is  null
                    </when>
                    <when test="params.userType == '3'.toString()">
                        and t.wid is not null and t.wxid is not null
                    </when>
                </choose>
            </if>
            <if test="params.friendType != null  and params.friendType != ''">
                <choose>
                    <when test="params.friendType == '0'.toString()">
                        and c.deleted = 0
                    </when>
                    <when test="params.friendType == '1'.toString()">
                        and c.deleted = 1
                    </when>
                    <when test="params.friendType == '2'.toString()">
                        and t.friend_time is null
                    </when>
                </choose>
            </if>
            <if test="params.wmUserType != null  and params.wmUserType != ''">
                <choose>
                    <when test="params.wmUserType == '0'.toString()">
                        and t.wid is not null
                    </when>
                    <when test="params.wmUserType == '1'.toString()">
                        and t.wid is null
                    </when>
                </choose>
            </if>
            <if test="tagSearchIdList != null  and tagSearchIdList.size() > 0  and tagSearchType != null ">
                and tag.tag_id in
                <foreach collection="tagSearchIdList" open="(" close=")" separator="," item="tagId" >
                    #{tagId}
                </foreach>
            </if>
            <if test="widList != null  and widList.size() > 0">
                and t.wid in
                <foreach collection="widList" open="(" close=")" separator="," item="wid" >
                    #{wid}
                </foreach>
            </if>
            <if test="unionIdList != null  and unionIdList.size() > 0">
                and t.union_id in
                <foreach collection="unionIdList" open="(" close=")" separator="," item="unionId" >
                    #{unionId}
                </foreach>
            </if>
            <if test="wxidList != null  and wxidList.size() > 0">
                and t.wxid in
                <foreach collection="wxidList" open="(" close=")" separator="," item="wxid" >
                    #{wxid}
                </foreach>
            </if>
        </where>
        <if test="tagSearchIdList != null  and tagSearchIdList.size() > 0  and tagSearchType != null ">
            <if test="tagSearchType == '0'.toString()">
                GROUP BY t.`id`  HAVING COUNT(1) > 0
            </if>
            <if test="tagSearchType == '1'.toString()">
                GROUP BY t.`id`  HAVING COUNT(1) =  ${tagSearchIdList.size()}
            </if>
        </if>
        <if test="params.friendType != null and params.friendType != ''">
            group by t.union_id
        </if>

        order by t.friend_time desc, t.id desc
    </select>

    <select id="selectBizUserById" parameterType="Long" resultMap="BizUserResult">
        <include refid="selectBizUserVo"/>
        where t.id = #{id}
    </select>

    <select id="selectBizUserByUnionId" resultMap="BizUserResult">
        <include refid="selectBizUserVo"/>
        where t.union_id = #{unionId}
    </select>

    <select id="selectBizUserByWid" resultMap="BizUserResult">
        <include refid="selectBizUserVo"/>
        where t.wid = #{wid}
        limit 1
    </select>

    <select id="selectBizUserListByWxidList" resultMap="BizUserResult">
        <include refid="selectBizUserVo"/>
        where t.wxid  in
        <foreach collection="list" open="(" close=")" separator="," item="wxid" >
            #{wxid}
        </foreach>
    </select>

    <select id="selectIsSaintsById" resultType="java.lang.String">
        select is_saints from biz_user where id = #{id}
    </select>

    <insert id="insertBizUser" parameterType="com.ruoyi.biz.domain.BizUser" useGeneratedKeys="true" keyProperty="id">
        insert into biz_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="wid != null">wid,</if>
            <if test="wmNickname != null">wm_nickname,</if>
            <if test="wmPhoto != null">wm_photo,</if>
            <if test="wxid != null and wxid != ''">wxid,</if>
            <if test="wxNickname != null">wx_nickname,</if>
            <if test="wxPhoto != null">wx_photo,</if>
            <if test="unionId != null and unionId != ''">union_id,</if>
            <if test="membershipType != null and membershipType != ''">membership_type,</if>
            <if test="friendTime != null">friend_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="wid != null">#{wid},</if>
            <if test="wmNickname != null">#{wmNickname},</if>
            <if test="wmPhoto != null">#{wmPhoto},</if>
            <if test="wxid != null and wxid != ''">#{wxid},</if>
            <if test="wxNickname != null">#{wxNickname},</if>
            <if test="wxPhoto != null">#{wxPhoto},</if>
            <if test="unionId != null and unionId != ''">#{unionId},</if>
            <if test="membershipType != null and membershipType != ''">#{membershipType},</if>
            <if test="friendTime != null">#{friendTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
        ON DUPLICATE KEY UPDATE
        wid =     IFNULL(values(wid),wid),
        wm_nickname=IFNULL(values(wm_nickname),wm_nickname),
        wm_photo=IFNULL(values(wm_photo),wm_photo),
        wxid = IFNULL(values(wxid),wxid),
        wx_nickname=IFNULL(values(wx_nickname),wx_nickname),
        wx_photo=IFNULL(values(wx_photo),wx_photo)
    </insert>

    <insert id="batchAddUpdateByMembers">
        INSERT INTO biz_user(
            id,
            wid,
            wm_nickname,
            wm_photo,
            wxid,
            wx_nickname,
            wx_photo,
            union_id,
            membership_type,
            create_time)
            VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.wid},
            #{item.wmNickname},
            #{item.wmPhoto},
            #{item.wxid},
            #{item.wxNickname},
            #{item.wxPhoto},
            #{item.unionId},
            #{item.membershipType},
            #{item.createTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        wm_nickname=IFNULL(values(wm_nickname),wm_nickname),
        wm_photo=IFNULL(values(wm_photo),wm_photo),
        wx_nickname=IFNULL(values(wx_nickname),wx_nickname),
        wx_photo=IFNULL(values(wx_photo),wx_photo)

    </insert>

    <update id="updateBizUser" parameterType="com.ruoyi.biz.domain.BizUser">
        update biz_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="wid != null">wid = #{wid},</if>
            <if test="wmNickname != null">wm_nickname = #{wmNickname},</if>
            <if test="wmPhoto != null">wm_photo = #{wmPhoto},</if>
            <if test="wxid != null and wxid != ''">wxid = #{wxid},</if>
            <if test="wxNickname != null">wx_nickname = #{wxNickname},</if>
            <if test="wxPhoto != null">wx_photo = #{wxPhoto},</if>
            <if test="unionId != null and unionId != ''">union_id = #{unionId},</if>
            <if test="membershipType != null and membershipType != ''">membership_type = #{membershipType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="temp != null">temp = #{temp},</if>
            <if test="tempState != null">temp_state = #{tempState},</if>
            <if test="isSaints != null">is_saints = #{isSaints},</if>
            <if test="friendTime != null">friend_time = #{friendTime},</if>
            <if test="accountInfo != null">account_info = #{accountInfo},</if>
            <if test="dueDate != null">
                due_date = #{dueDate},
            </if>
            <if test="planId != null">
                plan_id = #{planId},
            </if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateBizUserByUnionId" parameterType="com.ruoyi.biz.domain.BizUser">
        update biz_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="wid != null">wid = #{wid},</if>
            <if test="wmNickname != null">wm_nickname = #{wmNickname},</if>
            <if test="wmPhoto != null">wm_photo = #{wmPhoto},</if>
            <if test="wxid != null and wxid != ''">wxid = #{wxid},</if>
            <if test="wxNickname != null">wx_nickname = #{wxNickname},</if>
            <if test="wxPhoto != null">wx_photo = #{wxPhoto},</if>
            <if test="unionId != null and unionId != ''">union_id = #{unionId},</if>
            <if test="membershipType != null and membershipType != ''">membership_type = #{membershipType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="temp != null">temp = #{temp},</if>
            <if test="tempState != null">temp_state = #{tempState},</if>
            <if test="isSaints != null">is_saints = #{isSaints},</if>
            <if test="friendTime != null">friend_time = #{friendTime},</if>
            <if test="accountInfo != null">account_info = #{accountInfo},</if>
            due_date = #{dueDate},
            <if test="planId != null">plan_id = #{planId},</if>
        </trim>
        where union_id = #{unionId}
    </update>

    <delete id="deleteBizUserById" parameterType="Long">
        delete from biz_user where id = #{id}
    </delete>

    <delete id="deleteBizUserByIds" parameterType="String">
        delete from biz_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectNonWidList" resultMap="BizUserResult">
        <include refid="selectBizUserVo"/>
            where t.wid is null
             and t.union_id is not null
    </select>

    <select id="selectBizUserListByUnionIdList" resultMap="BizUserResult">
        <include refid="selectBizUserVo"/>
        where t.union_id  in
        <foreach collection="list" open="(" close=")" separator="," item="unionId" >
            #{unionId}
        </foreach>
    </select>
</mapper>
