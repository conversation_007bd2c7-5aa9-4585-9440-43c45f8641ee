<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizClueMapper">

    <resultMap type="com.ruoyi.biz.domain.BizClue" id="BizClueResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="refList"    column="ref_list"    />
        <result property="source"    column="source"    />
        <result property="explain"    column="explain"    />
        <result property="genTime"    column="gen_time"    />
        <result property="userGroupId"    column="user_group_id"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.type, t.ref_list, t.source, t.explain, t.gen_time,t.user_group_id    </sql>

    <sql id="selectBizClueVo">
        select <include refid="Base_Column_List"/>  from biz_clue t
    </sql>

    <select id="selectBizClueList" parameterType="com.ruoyi.biz.domain.BizClue" resultMap="BizClueResult">
        <include refid="selectBizClueVo"/>
        <where>
            <if test="type != null  and type != ''"> and t.type = #{type}</if>
            <if test="refList != null  and refList != ''"> and t.ref_list = #{refList}</if>
            <if test="explain != null  and explain != ''"> and t.explain like concat('%', #{explain}, '%')</if>
            <if test="params.beginGenTime != null and params.beginGenTime != '' and params.endGenTime != null and params.endGenTime != ''"> and t.gen_time between #{params.beginGenTime} and #{params.endGenTime}</if>
        </where>
        order by t.id desc
    </select>

    <select id="selectBizClueById" parameterType="Long" resultMap="BizClueResult">
        <include refid="selectBizClueVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizClue" parameterType="com.ruoyi.biz.domain.BizClue" useGeneratedKeys="true" keyProperty="id">
        insert into biz_clue
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null and type != ''">type,</if>
            <if test="refList != null and refList != ''">ref_list,</if>
            <if test="source != null and source != ''">`source`,</if>
            <if test="explain != null and explain != ''">`explain`,</if>
            <if test="genTime != null and genTime != ''">gen_time,</if>
            <if test="userGroupId != null  ">user_group_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null and type != ''">#{type},</if>
            <if test="refList != null and refList != ''">#{refList},</if>
            <if test="source != null and source != ''">#{source},</if>
            <if test="explain != null and explain != ''">#{explain},</if>
            <if test="genTime != null and genTime != ''">#{genTime},</if>
            <if test="userGroupId != null  ">#{userGroupId},</if>
         </trim>
    </insert>

    <update id="updateBizClue" parameterType="com.ruoyi.biz.domain.BizClue">
        update biz_clue
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="refList != null and refList != ''">ref_list = #{refList},</if>
            <if test="source != null and source != ''">source = #{source},</if>
            <if test="explain != null and explain != ''">`explain` = #{explain},</if>
            <if test="genTime != null and genTime != ''">gen_time = #{genTime},</if>
            <if test="userGroupId != null  ">user_group_id = #{userGroupId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizClueById" parameterType="Long">
        delete from biz_clue where id = #{id}
    </delete>

    <delete id="deleteBizClueByIds" parameterType="String">
        delete from biz_clue where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
