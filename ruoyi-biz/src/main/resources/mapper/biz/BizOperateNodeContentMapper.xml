<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizOperateNodeContentMapper">

    <resultMap type="com.ruoyi.biz.domain.BizOperateNodeContent" id="BizOperateNodeContentResult">
        <result property="id"    column="id"    />
        <result property="chatId"    column="chat_id"    />
        <result property="target"    column="target"    />
        <result property="type"    column="type"    />
        <result property="md5"    column="md5"    />
        <result property="originalValue"    column="original_value"    />
        <result property="sendTime"    column="send_time"    />
    </resultMap>

    <sql id="Base_Column_List">
 t.id, t.chat_id, t.target, t.type, t.md5, t.original_value, t.send_time    </sql>

    <sql id="selectBizOperateNodeContentVo">
        select <include refid="Base_Column_List"/>  from biz_operate_node_content t
    </sql>

    <select id="selectBizOperateNodeContentList" parameterType="com.ruoyi.biz.domain.BizOperateNodeContent" resultMap="BizOperateNodeContentResult">
        <include refid="selectBizOperateNodeContentVo"/>
        <where>
            <if test="chatId != null  and chatId != ''"> and t.chat_id = #{chatId}</if>
            <if test="target != null  and target != ''"> and t.target = #{target}</if>
            <if test="type != null  and type != ''"> and t.type = #{type}</if>
            <if test="md5 != null  and md5 != ''"> and t.md5 = #{md5}</if>
            <if test="originalValue != null  and originalValue != ''"> and t.original_value = #{originalValue}</if>
            <if test="sendTime != null "> and t.send_time = #{sendTime}</if>
        </where>
        order by t.id desc
    </select>

    <select id="selectBizOperateNodeContentById" parameterType="Long" resultMap="BizOperateNodeContentResult">
        <include refid="selectBizOperateNodeContentVo"/>
        where t.id = #{id}
    </select>

    <select id="checkIsExistContent" resultType="java.lang.String" parameterType="com.ruoyi.biz.domain.BizOperateNodeContent">
        select distinct chat_id from biz_operate_node_content t
        where t.target = #{target} and t.md5 in
        <foreach collection="md5List" item="md5" open="(" close=")" separator=",">
            #{md5}
        </foreach>
        <if test="params.beginTime != null and params.beginTime != '' and params.endTime != null and params.endTime != ''">
         and t.send_time between #{params.beginTime} and #{params.endTime}</if>
    </select>

    <insert id="insertBizOperateNodeContent" parameterType="com.ruoyi.biz.domain.BizOperateNodeContent" useGeneratedKeys="true" keyProperty="id">
        insert into biz_operate_node_content
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="chatId != null and chatId != ''">chat_id,</if>
            <if test="target != null and target != ''">target,</if>
            <if test="type != null and type != ''">`type`,</if>
            <if test="md5 != null and md5 != ''">md5,</if>
            <if test="originalValue != null and originalValue != ''">original_value,</if>
            <if test="sendTime != null">send_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chatId != null and chatId != ''">#{chatId},</if>
            <if test="target != null and target != ''">#{target},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="md5 != null and md5 != ''">#{md5},</if>
            <if test="originalValue != null and originalValue != ''">#{originalValue},</if>
            <if test="sendTime != null">#{sendTime},</if>
         </trim>
    </insert>

    <insert id="batchAdd">
        INSERT INTO biz_operate_node_content(
        chat_id,
        target,
        type,
        md5,
        original_value,
        send_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.chatId},
            #{item.target},
            #{item.type},
            #{item.md5},
            #{item.originalValue},
            #{item.sendTime})
        </foreach>
    </insert>

    <update id="updateBizOperateNodeContent" parameterType="com.ruoyi.biz.domain.BizOperateNodeContent">
        update biz_operate_node_content
        <trim prefix="SET" suffixOverrides=",">
            <if test="chatId != null and chatId != ''">chat_id = #{chatId},</if>
            <if test="target != null and target != ''">target = #{target},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="md5 != null and md5 != ''">md5 = #{md5},</if>
            <if test="originalValue != null and originalValue != ''">original_value = #{originalValue},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizOperateNodeContentById" parameterType="Long">
        delete from biz_operate_node_content where id = #{id}
    </delete>

    <delete id="deleteBizOperateNodeContentByIds" parameterType="String">
        delete from biz_operate_node_content where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
