<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizVersionRecordMapper">

    <resultMap type="com.ruoyi.biz.domain.BizVersionRecord" id="BizVersionRecordResult">
        <result property="id"    column="id"    />
        <result property="refType"    column="ref_type"    />
        <result property="refId"    column="ref_id"    />
        <result property="documentType"    column="document_type"  />
        <result property="name"    column="name"    />
        <result property="event"    column="event"    />
        <result property="json"    column="json"    />
        <result property="status"    column="status"    />
        <result property="version"    column="version"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.ref_type, t.ref_id,t.document_type, t.name, t.event, t.json, t.status, t.version, t.create_time
    </sql>

    <sql id="selectBizVersionRecordVo">
        select <include refid="Base_Column_List"/>  from biz_version_record t
    </sql>

    <select id="selectBizVersionRecordList" parameterType="com.ruoyi.biz.domain.BizVersionRecord" resultMap="BizVersionRecordResult">
        <include refid="selectBizVersionRecordVo"/>
        <where>
            <if test="refType != null  and refType != ''"> and t.ref_type = #{refType}</if>
            <if test="refId != null "> and t.ref_id = #{refId}</if>
            <if test="documentType != null  and documentType != ''"> and t.document_type = #{documentType}</if>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="event != null  and event != ''"> and t.event = #{event}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="version != null "> and t.version = #{version}</if>
        </where>
        order by id desc
    </select>

    <select id="selectBizVersionRecordById" parameterType="Long" resultMap="BizVersionRecordResult">
        <include refid="selectBizVersionRecordVo"/>
        where t.id = #{id}
    </select>

    <select id="selectLockRecord" resultMap="BizVersionRecordResult">
        <include refid="selectBizVersionRecordVo"/>
        where t.ref_type = #{refType} and  t.ref_id = #{refId} and  t.status = '0'
         for update
    </select>
    <select id="selectByDocumentType" resultMap="BizVersionRecordResult">
        <include refid="selectBizVersionRecordVo"/>
        where t.ref_type = #{refType} and t.document_type = #{documentType} and t.status = '0' and t.version is null
    </select>

    <insert id="insertBizVersionRecord" parameterType="com.ruoyi.biz.domain.BizVersionRecord" useGeneratedKeys="true" keyProperty="id">
        insert into biz_version_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="refType != null and refType != ''">ref_type,</if>
            <if test="refId != null">ref_id,</if>
            <if test="documentType != null  and documentType != ''">document_type,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="event != null and event != ''">event,</if>
            <if test="json != null and json != ''">json,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="version != null">version,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="refType != null and refType != ''">#{refType},</if>
            <if test="refId != null">#{refId},</if>
            <if test="documentType != null  and documentType != ''"> #{documentType},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="event != null and event != ''">#{event},</if>
            <if test="json != null and json != ''">#{json},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="version != null">#{version},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateBizVersionRecord" parameterType="com.ruoyi.biz.domain.BizVersionRecord">
        update biz_version_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="refType != null and refType != ''">ref_type = #{refType},</if>
            <if test="refId != null">ref_id = #{refId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="event != null and event != ''">event = #{event},</if>
            <if test="json != null and json != ''">json = #{json},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="version != null">version = #{version},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>


    <update id="batchUpdateVersionStatus">
        UPDATE biz_version_record
        SET status = '1',
        version = #{versionId}
        WHERE id IN
        <foreach collection="list" item="record" index="index" open="(" separator="," close=")">
            #{record.id}
        </foreach>
    </update>

    <update id="batchUpdateVersionStatusNew">
        UPDATE biz_version_record
        SET status = '1',
            version = #{versionId}
        where status ='0'  and document_type = #{documentType}
    </update>
    <update id="batchUpdateVersionStatusByRefId">
        UPDATE biz_version_record
        SET status = '1',
            version = #{versionId}
        where status ='0'  and document_type = #{documentType} and ref_id in
        <foreach collection="refIds" item="refId" index="index" open="(" separator="," close=")">
            #{refId}
        </foreach>
    </update>

    <delete id="deleteBizVersionRecordById" parameterType="Long">
        delete from biz_version_record where id = #{id}
    </delete>

    <delete id="deleteBizVersionRecordByIds" parameterType="String">
        delete from biz_version_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
