<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizOperatePlanMapper">

    <resultMap type="com.ruoyi.biz.domain.BizOperatePlan" id="BizOperatePlanResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="refType"    column="ref_type"    />
        <result property="refId"    column="ref_id"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="coverRate"    column="cover_rate"    />
        <result property="filterCycle"    column="filter_cycle"    />
        <result property="bicheId"    column="biche_id"    />
        <result property="batchInterval"    column="batch_interval"    />
        <result property="totalBatch"    column="total_batch"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDefault"    column="is_default"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="Base_Column_List">
         t.id, t.name, t.type, t.ref_type, t.ref_id, t.content, t.status,t.is_default, t.is_delete, t.cover_rate, t.filter_cycle, t.biche_id, t.batch_interval, t.total_batch, t.create_by, t.create_time, t.update_by, t.update_time    </sql>

    <sql id="selectBizOperatePlanVo">
        select <include refid="Base_Column_List"/>  from biz_operate_plan t
    </sql>

    <select id="selectBizOperatePlanList" parameterType="com.ruoyi.biz.domain.BizOperatePlan" resultMap="BizOperatePlanResult">
        <include refid="selectBizOperatePlanVo"/>
        <if test="niche != null  and niche.name != null  and niche.name != ''">
          inner join biz_niche n on  t.biche_id = n.id
        </if>
        <where>
            <if test="id != null"> and t.id = #{id}</if>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and t.type = #{type}</if>
            <if test="refType != null  and refType != ''"> and t.ref_type = #{refType}</if>
            <if test="refId != null "> and t.ref_id = #{refId}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="isDefault != null  and isDefault != ''"> and t.is_default = #{isDefault}</if>
            <if test="bicheId != null "> and t.biche_id = #{bicheId}</if>
            <if test="niche != null  and  niche.name != null  and niche.name != ''">
                and n.name like concat('%', #{niche.name}, '%')
            </if>
            <if test="params.refTypes != null and params.refTypes.size() >0 ">
                and t.ref_type in
                <foreach collection="params.refTypes" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="statusList != null and statusList.size() >0 ">
                and t.status in
                <foreach collection="statusList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="idList != null and idList.size() >0 ">
                and t.id in
                <foreach collection="idList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            and t.is_delete = '0'
        </where>
        order by t.id desc
    </select>

    <select id="selectBizOperatePlanById" parameterType="Long" resultMap="BizOperatePlanResult">
        <include refid="selectBizOperatePlanVo"/>
        where t.id = #{id}
    </select>
    <select id="selectDefaultPlan" resultMap="BizOperatePlanResult">
        <include refid="selectBizOperatePlanVo"/>
        where t.is_default = '1' limit 1
    </select>

    <select id="selectBizOperatePlansByIds" resultMap="BizOperatePlanResult">
        <include refid="selectBizOperatePlanVo"/>
        <where>
            t.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            and t.is_delete = '0'
        </where>
    </select>

    <insert id="insertBizOperatePlan" parameterType="com.ruoyi.biz.domain.BizOperatePlan" useGeneratedKeys="true" keyProperty="id">
        insert into biz_operate_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="refType != null and refType != ''">ref_type,</if>
            <if test="refId != null">ref_id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="isDefault != null and isDefault != ''">is_default,</if>
            <if test="coverRate != null">cover_rate,</if>
            <if test="filterCycle != null">filter_cycle,</if>
            <if test="bicheId != null">biche_id,</if>
            <if test="batchInterval != null">batch_interval,</if>
            <if test="totalBatch != null">total_batch,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="refType != null and refType != ''">#{refType},</if>
            <if test="refId != null">#{refId},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="isDefault != null and isDefault != ''">#{isDefault},</if>
            <if test="coverRate != null">#{coverRate},</if>
            <if test="filterCycle != null">#{filterCycle},</if>
            <if test="bicheId != null">#{bicheId},</if>
            <if test="batchInterval != null">#{batchInterval},</if>
            <if test="totalBatch != null">#{totalBatch},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBizOperatePlan" parameterType="com.ruoyi.biz.domain.BizOperatePlan">
        update biz_operate_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="refType != null and refType != ''">ref_type = #{refType},</if>
            <if test="refId != null">ref_id = #{refId},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="coverRate != null">cover_rate = #{coverRate},</if>
            <if test="filterCycle != null">filter_cycle = #{filterCycle},</if>
            <if test="bicheId != null">biche_id = #{bicheId},</if>
            <if test="batchInterval != null">batch_interval = #{batchInterval},</if>
            <if test="totalBatch != null">total_batch = #{totalBatch},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="otherNotDefault">
        update biz_operate_plan
        set is_default ='0'
        where type ='0'
    </update>
    <update id="batchUpdateStatusToDelete">
        update biz_operate_plan set  is_delete = '1' where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteBizOperatePlanById" parameterType="Long">
        delete from biz_operate_plan where id = #{id}
    </delete>

    <delete id="deleteBizOperatePlanByIds" parameterType="String">
        delete from biz_operate_plan where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
