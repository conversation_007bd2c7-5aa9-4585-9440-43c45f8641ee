<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizChannelCodeRecordMapper">

    <resultMap type="com.ruoyi.biz.domain.BizChannelCodeRecord" id="BizChannelCodeRecordResult">
        <result property="id"    column="id"    />
        <result property="channelId"    column="channel_id"    />
        <result property="openid"    column="openid"    />
        <result property="headImgUrl"    column="head_img_url"    />
        <result property="nickname"    column="nickname"    />
        <result property="unionId"    column="union_id"    />
        <result property="isNewUser"    column="is_new_user"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.channel_id, t.openid, t.head_img_url, t.nickname, t.union_id, t.is_new_user, t.create_time    </sql>

    <sql id="selectBizChannelCodeRecordVo">
        select <include refid="Base_Column_List"/>  from biz_channel_code_record t
    </sql>

    <select id="selectBizChannelCodeRecordList" parameterType="com.ruoyi.biz.domain.BizChannelCodeRecord" resultMap="BizChannelCodeRecordResult">
        <include refid="selectBizChannelCodeRecordVo"/>
        <where>
            <if test="channelId != null "> and t.channel_id = #{channelId}</if>
            <if test="openid != null  and openid != ''"> and t.openid = #{openid}</if>
            <if test="nickname != null  and nickname != ''"> and t.nickname like concat('%', #{nickname}, '%')</if>
            <if test="unionId != null  and unionId != ''"> and t.union_id = #{unionId}</if>
            <if test="isNewUser != null  and isNewUser != ''"> and t.is_new_user = #{isNewUser}</if>
        </where>
        order by t.id desc
    </select>

    <select id="selectBizChannelCodeRecordById" parameterType="Long" resultMap="BizChannelCodeRecordResult">
        <include refid="selectBizChannelCodeRecordVo"/>
        where t.id = #{id}
    </select>

    <select id="selectLastAddFriendRecord" parameterType="String" resultMap="BizChannelCodeRecordResult">
        select <include refid="Base_Column_List"/> from biz_channel_code_record t
            inner join biz_channel_code bcc on t.channel_id = bcc.id
            where union_id = #{unionId}
              and bcc.type in ('0','1')
        order by t.id desc limit 1
    </select>

    <insert id="insertBizChannelCodeRecord" parameterType="com.ruoyi.biz.domain.BizChannelCodeRecord" useGeneratedKeys="true" keyProperty="id">
        insert into biz_channel_code_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="channelId != null">channel_id,</if>
            <if test="openid != null and openid != ''">openid,</if>
            <if test="headImgUrl != null and headImgUrl != ''">head_img_url,</if>
            <if test="nickname != null and nickname != ''">nickname,</if>
            <if test="unionId != null">union_id,</if>
            <if test="isNewUser != null and isNewUser != ''">is_new_user,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="channelId != null">#{channelId},</if>
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="headImgUrl != null and headImgUrl != ''">#{headImgUrl},</if>
            <if test="nickname != null and nickname != ''">#{nickname},</if>
            <if test="unionId != null">#{unionId},</if>
            <if test="isNewUser != null and isNewUser != ''">#{isNewUser},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateBizChannelCodeRecord" parameterType="com.ruoyi.biz.domain.BizChannelCodeRecord">
        update biz_channel_code_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="channelId != null">channel_id = #{channelId},</if>
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="headImgUrl != null and headImgUrl != ''">head_img_url = #{headImgUrl},</if>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="unionId != null">union_id = #{unionId},</if>
            <if test="isNewUser != null and isNewUser != ''">is_new_user = #{isNewUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizChannelCodeRecordById" parameterType="Long">
        delete from biz_channel_code_record where id = #{id}
    </delete>

    <delete id="deleteBizChannelCodeRecordByIds" parameterType="String">
        delete from biz_channel_code_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByUnionId" parameterType="String" resultMap="BizChannelCodeRecordResult">
        <include refid="selectBizChannelCodeRecordVo"/>
        where t.union_id = #{unionId}
        limit 1
    </select>

</mapper>
