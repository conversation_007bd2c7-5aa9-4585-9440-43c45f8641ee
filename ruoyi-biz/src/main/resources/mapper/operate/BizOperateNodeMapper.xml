<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizOperateNodeMapper">

    <resultMap type="com.ruoyi.biz.domain.BizOperateNode" id="BizOperateNodeResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="themeJson"    column="theme_json"    />
        <result property="prologue"    column="prologue"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.name,  t.type, t.theme_json, t.prologue, t.status,  t.create_by, t.create_time, t.update_by, t.update_time   </sql>

    <sql id="selectBizOperateNodeVo">
        select <include refid="Base_Column_List"/>  from biz_operate_node t
    </sql>

    <select id="selectBizOperateNodeList" parameterType="com.ruoyi.biz.domain.BizOperateNode" resultMap="BizOperateNodeResult">
        <include refid="selectBizOperateNodeVo"/>
        <where>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="type != null  and type != ''"> and t.type = #{type}</if>
            <if test="typeList != null  and typeList.size() > 0 != ''">
                and t.type in
                <foreach collection="typeList" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
        </where>
        order by t.id desc
    </select>

    <select id="selectBizOperateNodeById" parameterType="Long" resultMap="BizOperateNodeResult">
        <include refid="selectBizOperateNodeVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizOperateNode" parameterType="com.ruoyi.biz.domain.BizOperateNode" useGeneratedKeys="true" keyProperty="id">
        insert into biz_operate_node
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="type != null">`type`,</if>
            <if test="prologue != null and prologue != ''">prologue,</if>
            <if test="themeJson != null">theme_json,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="prologue != null and prologue != ''">#{prologue},</if>
            <if test="themeJson != null">#{themeJson},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBizOperateNode" parameterType="com.ruoyi.biz.domain.BizOperateNode">
        update biz_operate_node
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null and type != ''">`type` = #{type},</if>
            <if test="prologue != null and prologue != ''">prologue = #{prologue},</if>
            <if test="themeJson != null">theme_json = #{themeJson},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizOperateNodeById" parameterType="Long">
        delete from biz_operate_node where id = #{id}
    </delete>

    <delete id="deleteBizOperateNodeByIds" parameterType="String">
        delete from biz_operate_node where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
