<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizCelebrityMapper">

    <resultMap type="com.ruoyi.biz.domain.BizCelebrity" id="BizCelebrityResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="aliases"    column="aliases"    />
        <result property="baikeUrl"    column="baike_url"    />
        <result property="weiboPersonal"    column="weibo_personal"    />
        <result property="weiboOfficial"    column="weibo_official"    />
        <result property="baikePurification"    column="baike_purification"    />
        <result property="baikeContent"    column="baike_content"    />
        <result property="otherImportantContent"    column="other_important_content"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.name, t.aliases, t.baike_url, t.weibo_personal, t.weibo_official, t.baike_purification, t.baike_content, t.other_important_content, t.create_time, t.update_time
    </sql>
    <sql id="selectBizCelebrityVo">
        select
        <include refid="Base_Column_List"/>
        from biz_celebrity t
    </sql>

    <select id="selectBizCelebrityList" parameterType="com.ruoyi.biz.domain.BizCelebrity"
            resultMap="BizCelebrityResult">
        <include refid="selectBizCelebrityVo"/>
        <where>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="aliases != null">and t.aliases = #{aliases}</if>
            <if test="createTime != null "> and t.create_time = #{createTime}</if>
            <if test="updateTime != null "> and t.update_time = #{updateTime}</if>
            <if test="idList != null and  idList.length > 0 ">
                and t.id in
                <foreach item="id" collection="idList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by t.id desc
    </select>

    <select id="selectBizCelebrityById" parameterType="Long" resultMap="BizCelebrityResult">
        <include refid="selectBizCelebrityVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertBizCelebrity" parameterType="com.ruoyi.biz.domain.BizCelebrity" useGeneratedKeys="true"
            keyProperty="id">
        insert into biz_celebrity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="aliases != null">aliases,</if>
            <if test="baikeUrl != null">baike_url,</if>
            <if test="weiboPersonal != null">weibo_personal,</if>
            <if test="weiboOfficial != null">weibo_official,</if>
            <if test="baikePurification != null">baike_purification,</if>
            <if test="baikeContent != null">baike_content,</if>
            <if test="otherImportantContent != null">other_important_content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="aliases != null">#{aliases},</if>
            <if test="baikeUrl != null">#{baikeUrl},</if>
            <if test="weiboPersonal != null">#{weiboPersonal},</if>
            <if test="weiboOfficial != null">#{weiboOfficial},</if>
            <if test="baikePurification != null">#{baikePurification},</if>
            <if test="baikeContent != null">#{baikeContent},</if>
            <if test="otherImportantContent != null">#{otherImportantContent},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateBizCelebrity" parameterType="com.ruoyi.biz.domain.BizCelebrity">
        update biz_celebrity
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="aliases != null">aliases = #{aliases},</if>
            <if test="baikeUrl != null">baike_url = #{baikeUrl},</if>
            <if test="weiboPersonal != null">weibo_personal = #{weiboPersonal},</if>
            <if test="weiboOfficial != null">weibo_official = #{weiboOfficial},</if>
            <if test="baikePurification != null">baike_purification = #{baikePurification},</if>
            <if test="baikeContent != null">baike_content = #{baikeContent},</if>
            <if test="otherImportantContent != null">other_important_content = #{otherImportantContent},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizCelebrityById" parameterType="Long">
        delete
        from biz_celebrity
        where id = #{id}
    </delete>

    <delete id="deleteBizCelebrityByIds" parameterType="String">
        delete from biz_celebrity where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
