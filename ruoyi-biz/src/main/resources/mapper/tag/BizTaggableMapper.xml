<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizTaggableMapper">

    <resultMap type="com.ruoyi.biz.domain.BizTaggable" id="BizTaggableResult">
        <result property="refType"    column="ref_type"    />
        <result property="refId"    column="ref_id"    />
        <result property="tagId"    column="tag_id"    />
        <result property="tagName"    column="name"    />
        <result property="isManual"    column="is_manual"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="Base_Column_List">
       t.ref_type, t.ref_id, t.tag_id, t.is_manual, t.create_time
     </sql>

    <sql id="selectBizTaggableVo">
        select <include refid="Base_Column_List"/>  ,tag.name `name` , g.name `groupName`
            from biz_taggable t
           left join biz_tag tag on tag.id = t.tag_id
           left join biz_tag_group g on g.id = tag.group_id
    </sql>

    <select id="selectBizTaggableList" parameterType="com.ruoyi.biz.domain.BizTaggable" resultMap="BizTaggableResult">
        <include refid="selectBizTaggableVo"/>
        <where>
            <if test="refType != null  and refType != ''"> and t.ref_type = #{refType}</if>
            <if test="refId != null "> and t.ref_id = #{refId}</if>
            <if test="tagId != null "> and t.tag_id = #{tagId}</if>
            <if test="isManual != null  and isManual != ''"> and t.is_manual = #{isManual}</if>
            <if test="refIdList != null and refIdList.size() > 0 ">
                and t.ref_id in
                <foreach collection="refIdList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="refTypeList != null and refTypeList.size() > 0 ">
                and t.ref_type in
                <foreach collection="refTypeList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="tagIdList != null and tagIdList.size() > 0 ">
                and t.tag_id in
                <foreach collection="tagIdList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectBizTaggableByRefType" parameterType="String" resultMap="BizTaggableResult">
        <include refid="selectBizTaggableVo"/>
        where t.ref_type = #{refType}
    </select>

    <select id="distinctTagIdList" resultType="java.lang.Long">
        select DISTINCT t.tag_id  from  biz_taggable t
    </select>

    <insert id="insertBizTaggable" parameterType="com.ruoyi.biz.domain.BizTaggable">
        insert into biz_taggable
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="refType != null">ref_type,</if>
            <if test="refId != null">ref_id,</if>
            <if test="tagId != null">tag_id,</if>
            <if test="isManual != null and isManual != ''">is_manual,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="refType != null">#{refType},</if>
            <if test="refId != null">#{refId},</if>
            <if test="tagId != null">#{tagId},</if>
            <if test="isManual != null and isManual != ''">#{isManual},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <insert id="batchInsertBizTaggable" parameterType="com.ruoyi.biz.domain.dto.BizTaggableReq">
        insert into biz_taggable(ref_type,ref_id,tag_id)
        values
        <foreach collection="refIdList" item="refId" separator=",">
            <foreach collection="tags" item="tag" separator=",">
                (#{refType},#{refId}, #{tag})
            </foreach>
        </foreach>
        ON DUPLICATE KEY UPDATE
        tag_id=VALUES(tag_id)
    </insert>

    <insert id="insertBatchBizTaggable" parameterType="java.util.List">
        insert into biz_taggable(ref_type, ref_id, tag_id)
        values
        <foreach collection="list" item="req" separator=",">
            <foreach collection="req.refIdList" item="ref" separator=",">
                <foreach collection="req.tags" item="tag" separator=",">
                    (#{req.refType}, #{ref}, #{tag})
                </foreach>
            </foreach>
        </foreach>
        ON DUPLICATE KEY UPDATE
        tag_id = VALUES(tag_id)
    </insert>

    <update id="updateBizTaggable" parameterType="com.ruoyi.biz.domain.BizTaggable">
        update biz_taggable
        <trim prefix="SET" suffixOverrides=",">
            <if test="refId != null">ref_id = #{refId},</if>
            <if test="tagId != null">tag_id = #{tagId},</if>
            <if test="isManual != null and isManual != ''">is_manual = #{isManual},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where ref_type = #{refType}
    </update>

    <delete id="deleteBizTaggableByRefTypeAndRefIds"  >
        delete from biz_taggable where
            ref_type = #{refType}
        <if test="list != null and list.size() > 0 ">
          and ref_id in
          <foreach collection="list" open="(" close=")" separator="," item="item">
              #{item}
          </foreach>
        </if>
        <if test="delTag != null and delTag.size() > 0 ">
            and tag_id in
            <foreach collection="delTag" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </delete>

    <delete id="deleteBizTaggableByRefTypes" parameterType="String">
        delete from biz_taggable where ref_type in
        <foreach item="refType" collection="array" open="(" separator="," close=")">
            #{refType}
        </foreach>
    </delete>

    <delete id="deleteBizTaggableByTagGroupIds">
        DELETE  FROM `biz_taggable`
            WHERE tag_id IN (
            SELECT id FROM `biz_tag` t WHERE t.`group_id` IN
                <foreach item="id" collection="array" open="(" separator="," close=")">
                    #{id}
                </foreach>
            )
    </delete>

    <delete id="deleteBizTaggableByTagIds">
        delete from biz_taggable where tag_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBizTaggableByRefId" parameterType="String">
        delete from biz_taggable where ref_id = #{refId}
    </delete>
</mapper>
