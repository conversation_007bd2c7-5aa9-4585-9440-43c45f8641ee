<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizTagGroupMapper">

    <resultMap type="com.ruoyi.biz.domain.BizTagGroup" id="BizTagGroupResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="outGroupId"    column="out_group_id"    />
        <result property="specialType"    column="special_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="BizTagGroupBizTagResult" type="com.ruoyi.biz.domain.BizTagGroup" extends="BizTagGroupResult">
        <collection property="bizTagList" notNullColumn="sub_id" javaType="java.util.List" resultMap="BizTagResult" />
    </resultMap>

    <resultMap type="com.ruoyi.biz.domain.BizTag" id="BizTagResult">
        <result property="id"    column="sub_id"    />
        <result property="groupId"    column="sub_group_id"    />
        <result property="name"    column="sub_name"    />
        <result property="outTagId"    column="sub_out_tag_id"    />
        <result property="createBy"    column="sub_create_by"    />
        <result property="createTime"    column="sub_create_time"    />
        <result property="updateBy"    column="sub_update_by"    />
        <result property="updateTime"    column="sub_update_time"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.name, t.type, t.out_group_id,t.special_type, t.create_by, t.create_time, t.update_by, t.update_time
        ,b.id as sub_id, b.group_id as sub_group_id, b.name as sub_name, b.out_tag_id as sub_out_tag_id, b.create_by as sub_create_by, b.create_time as sub_create_time, b.update_by as sub_update_by, b.update_time as sub_update_time
     </sql>

    <sql id="selectBizTagGroupVo">
        select <include refid="Base_Column_List"/>
            from biz_tag_group t
        left join biz_tag b on b.group_id = t.id
    </sql>

    <select id="selectBizTagGroupList" parameterType="com.ruoyi.biz.domain.BizTagGroup" resultMap="BizTagGroupBizTagResult">
        <include refid="selectBizTagGroupVo"/>
        <where>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and t.type = #{type}</if>
            <if test="outGroupId != null  and outGroupId != ''"> and t.out_group_id = #{outGroupId}</if>
            <if test="specialType != null  and specialType != ''"> and t.special_type = #{specialType}</if>
            <if test="typeList != null  and typeList.size() > 0 ">
             and t.type  in
             <foreach collection="typeList" open="(" close=")" separator="," item="type" >
                 #{type}
             </foreach>
             </if>
        </where>
        order by t.id desc
    </select>

    <select id="selectBizTagGroupById" parameterType="Long" resultMap="BizTagGroupBizTagResult">
        select a.id, a.name, a.type, a.out_group_id,a.special_type, a.create_by, a.create_time, a.update_by, a.update_time,
        b.id as sub_id, b.group_id as sub_group_id, b.name as sub_name, b.out_tag_id as sub_out_tag_id, b.create_by as sub_create_by, b.create_time as sub_create_time, b.update_by as sub_update_by, b.update_time as sub_update_time
        from biz_tag_group a
        left join biz_tag b on b.group_id = a.id
        where a.id = #{id}
    </select>

    <select id="selectBizTagGroupByName" resultMap="BizTagGroupBizTagResult">
        select a.id, a.name, a.type, a.out_group_id,a.special_type, a.create_by, a.create_time, a.update_by, a.update_time,
               b.id as sub_id, b.group_id as sub_group_id, b.name as sub_name, b.out_tag_id as sub_out_tag_id, b.create_by as sub_create_by, b.create_time as sub_create_time, b.update_by as sub_update_by, b.update_time as sub_update_time
        from biz_tag_group a
                 left join biz_tag b on b.group_id = a.id
        where a.name = #{name}
    </select>

    <select id="selectBizTagsByTagsIds" resultMap="BizTagResult">
        select  b.id as sub_id, b.group_id as sub_group_id, b.name as sub_name,
                b.out_tag_id as sub_out_tag_id, b.create_by as sub_create_by, b.create_time as sub_create_time,
                b.update_by as sub_update_by, b.update_time as sub_update_time
        from biz_tag b where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectBizTagsByGroupId" resultMap="BizTagResult">
        select  b.id as sub_id, b.group_id as sub_group_id, b.name as sub_name,
                b.out_tag_id as sub_out_tag_id, b.create_by as sub_create_by, b.create_time as sub_create_time,
                b.update_by as sub_update_by, b.update_time as sub_update_time
        from biz_tag b where group_id = #{groupId}
    </select>

    <select id="selectBizTagGroupBaseList" resultMap="BizTagGroupResult">
        select t.* from biz_tag_group t
        <where>
            <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and t.type = #{type}</if>
            <if test="outGroupId != null  and outGroupId != ''"> and t.out_group_id = #{outGroupId}</if>
            <if test="specialType != null  and specialType != ''"> and t.special_type = #{specialType}</if>
            <if test="typeList != null  and typeList.size() > 0 ">
                and t.type  in
                <foreach collection="typeList" open="(" close=")" separator="," item="type" >
                    #{type}
                </foreach>
            </if>
        </where>
        order by t.id desc
    </select>

    <select id="selectBizTagByName" resultMap="BizTagResult">
        select * from biz_tag where name = #{name}

    </select>

    <insert id="insertBizTagGroup" parameterType="com.ruoyi.biz.domain.BizTagGroup" useGeneratedKeys="true" keyProperty="id">
        insert into biz_tag_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="outGroupId != null">out_group_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="outGroupId != null">#{outGroupId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBizTagGroup" parameterType="com.ruoyi.biz.domain.BizTagGroup">
        update biz_tag_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="outGroupId != null">out_group_id = #{outGroupId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizTagGroupById" parameterType="Long">
        delete from biz_tag_group where id = #{id}
    </delete>

    <delete id="deleteBizTagGroupByIds" parameterType="String">
        delete from biz_tag_group where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTagByTagIds" parameterType="String">
        delete from biz_tag where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBizTagByGroupIds" parameterType="String">
        delete from biz_tag where group_id in
        <foreach item="groupId" collection="array" open="(" separator="," close=")">
            #{groupId}
        </foreach>
    </delete>

    <delete id="deleteBizTagByGroupId" parameterType="Long">
        delete from biz_tag where group_id = #{groupId}
    </delete>


    <insert id="batchAddOrUpdateBizTag" useGeneratedKeys="true" keyProperty="id">
        insert into biz_tag(  id,group_id, name, out_tag_id, create_by, create_time, update_by, update_time) values
		<foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id},  #{item.groupId}, #{item.name}, #{item.outTagId}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
        on duplicate key update
        name = values(name)
    </insert>
</mapper>
