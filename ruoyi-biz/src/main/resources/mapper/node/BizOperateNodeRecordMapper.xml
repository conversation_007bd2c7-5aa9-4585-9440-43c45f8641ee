<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.BizOperateNodeRecordMapper">

    <resultMap type="com.ruoyi.biz.domain.BizOperateNodeRecord" id="BizOperateNodeRecordResult">
        <result property="id"    column="id"    />
        <result property="target"    column="target"    />
        <result property="chatIdList"    column="chat_id_list"    />
        <result property="planId"    column="plan_id"    />
        <result property="pointId"    column="point_id"    />
        <result property="nodeJson"    column="node_json"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="remark"    column="remark"    />
        <result property="sendTime"    column="send_time"    />
        <result property="speedTaskIds"    column="speed_task_ids"    />
        <result property="normalTaskIds"    column="normal_task_ids"    />
        <result property="taskExecuted" column="task_executed"    />
        <result property="dataTaskExecuted" column="data_task_executed"/>
        <result property="speedTaskInfo" column="speed_task_info"/>
        <result property="normalTaskInfo" column="normal_task_info"/>
        <result property="reachCount" column="reach_count"/>
        <result property="batchNum" column="batch_num"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.target, t.chat_id_list, t.plan_id, t.point_id, t.node_json, t.status, t.create_time, t.remark, t.send_time, t.speed_task_ids, t.normal_task_ids, t.task_executed, t.data_task_executed,t.speed_task_info, t.normal_task_info, t.reach_count, t.batch_num
    </sql>

    <sql id="selectBizOperateNodeRecordVo">
        select <include refid="Base_Column_List"/>  from biz_operate_node_record t
    </sql>

    <select id="selectBizOperateNodeRecordList" parameterType="com.ruoyi.biz.domain.BizOperateNodeRecord" resultMap="BizOperateNodeRecordResult">
        <include refid="selectBizOperateNodeRecordVo"/>
        <where>
            <if test="target != null  and target != ''"> and t.target = #{target}</if>
            <if test="chatIdList != null  and chatIdList != ''"> and t.chat_id_list = #{chatIdList}</if>
            <if test="planId != null "> and t.plan_id = #{planId}</if>
            <if test="pointId != null "> and t.point_id = #{pointId}</if>
            <if test="nodeJson != null  and nodeJson != ''"> and t.node_json = #{nodeJson}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="executeFlag != null  and executeFlag != ''"> and t.create_time &lt;= now()</if>
            <if test="taskExecuted != null"> and t.task_executed = #{taskExecuted}</if>
            <if test="dataTaskExecuted != null"> and t.data_task_executed = #{dataTaskExecuted}</if>
            <if test="batchNum != null"> and t.batch_num = #{batchNum}</if>
            <if test="params.isTask == true">and (t.speed_task_ids is not null or t.normal_task_ids is not null)</if>
            <if test="params.statusList != null and params.statusList.size() > 0">and t.status in
            <foreach item="status" collection="params.statusList" open="(" separator="," close=")">
                #{status}
            </foreach>
            </if>
        </where>
        order by t.id
        <if test="params.batchSize != null and params.batchSize > 0">
            limit #{params.batchSize, jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectBizOperateNodeRecordById" parameterType="Long" resultMap="BizOperateNodeRecordResult">
        <include refid="selectBizOperateNodeRecordVo"/>
        where t.id = #{id}
    </select>

    <select id="selectBizOperateNodeRecordByIdForUpdate" parameterType="Long" resultMap="BizOperateNodeRecordResult">
        <include refid="selectBizOperateNodeRecordVo"/>
        where t.id = #{id} for update
    </select>

    <select id="getBizOperateNodeRecordListByChatIds" resultMap="BizOperateNodeRecordResult">
        <include refid="selectBizOperateNodeRecordVo"/>
        <where>
            <if test="chatIdList != null and chatIdList.size() > 0">
                and (
                <foreach item="chatId" collection="chatIdList" open="(" separator=" or " close=")">
                    FIND_IN_SET(#{chatId}, t.chat_id_list) > 0
                </foreach>
                )
            </if>
            <if test="days != null">
                and t.create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
            </if>
        </where>
        order by t.create_time desc
    </select>

    <select id="getBizOperateNodeRecordListByChatIdsAndTime"
            resultMap="BizOperateNodeRecordResult">
        <include refid="selectBizOperateNodeRecordVo"/>
        <where>
            t.status = '0' and t.send_time between #{startTime} and #{endTime}
            <if test="chatIdList != null and chatIdList.size() > 0">
                and (
                <foreach item="chatId" collection="chatIdList" open="(" separator=" or " close=")">
                    FIND_IN_SET(#{chatId}, t.chat_id_list) > 0
                </foreach>
                )
            </if>
        </where>
    </select>

    <select id="getBizOperateNodeRecordListByChatIdsAndDueTime"
            resultMap="BizOperateNodeRecordResult">
        <include refid="selectBizOperateNodeRecordVo"/>
        left join biz_operate_plan bp on t.plan_id = bp.id
        <where>
            t.status = '0' and bp.type = '1'
            <if test="type != null and type != ''">and t.target = #{type}</if>
            <if test="chatIdList != null and chatIdList.size() > 0">
                and (
                <foreach item="chatId" collection="chatIdList" open="(" separator=" or " close=")">
                    FIND_IN_SET(#{chatId}, t.chat_id_list) > 0
                </foreach>
                )
            </if>
            <if test="dueTime != null and dueTime != ''">and t.send_time &lt;= #{dueTime}</if>
        </where>
    </select>
    <select id="selectBizOperateNodeRecordListByTime" resultMap="BizOperateNodeRecordResult">
        <include refid="selectBizOperateNodeRecordVo"/>
        <where>
            t.status = '1'
            <if test="date != null and date != ''">and t.create_time &gt;= DATE_SUB(#{date}, INTERVAL 7 DAY)</if>
        </where>
    </select>


    <select id="selectLastRecordByChatId"  resultMap="BizOperateNodeRecordResult">
        <include refid="selectBizOperateNodeRecordVo"/>
        where  FIND_IN_SET(#{chatId},t.`chat_id_list`) AND t.`status` ='1'
        AND t.`send_time` >= NOW() - INTERVAL 1 DAY
        ORDER BY t.`send_time` DESC,id ASC
    </select>

    <select id="selectBizOperateNodeRecordByCondition" resultMap="BizOperateNodeRecordResult">
        <include refid="selectBizOperateNodeRecordVo"/>
        <where>
            <if test="params.planId != null  and params.planId != ''"> and t.plan_id = #{params.planId}</if>
            <if test="params.chatId != null  and params.chatId != ''"> and t.chat_id_list like concat('%', #{params.chatId}, '%')</if>
        </where>
    </select>

    <insert id="insertBizOperateNodeRecord" parameterType="com.ruoyi.biz.domain.BizOperateNodeRecord" useGeneratedKeys="true" keyProperty="id">
        insert into biz_operate_node_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="target != null and target != ''">target,</if>
            <if test="chatIdList != null and chatIdList != ''">chat_id_list,</if>
            <if test="planId != null">plan_id,</if>
            <if test="pointId != null">point_id,</if>
            <if test="nodeJson != null and nodeJson != ''">node_json,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="speedTaskIds != null and speedTaskIds != ''">speed_task_ids,</if>
            <if test="normalTaskIds != null and normalTaskIds != ''">normal_task_ids,</if>
            <if test="taskExecuted != null">task_executed,</if>
            <if test="dataTaskExecuted != null">data_task_executed,</if>
            <if test="speedTaskInfo != null and speedTaskInfo != ''">speed_task_info,</if>
            <if test="normalTaskInfo != null and normalTaskInfo != ''">normal_task_info,</if>
            <if test="reachCount != null">reach_count,</if>
            <if test="batchNum != null">batch_num,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="target != null and target != ''">#{target},</if>
            <if test="chatIdList != null and chatIdList != ''">#{chatIdList},</if>
            <if test="planId != null">#{planId},</if>
            <if test="pointId != null">#{pointId},</if>
            <if test="nodeJson != null and nodeJson != ''">#{nodeJson},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="sendTime != null">send_time,</if>
            <if test="speedTaskIds != null and speedTaskIds != ''">#{speedTaskIds},</if>
            <if test="normalTaskIds != null and normalTaskIds != ''">#{normalTaskIds},</if>
            <if test="taskExecuted != null">#{taskExecuted},</if>
            <if test="dataTaskExecuted != null">#{dataTaskExecuted}</if>
            <if test="speedTaskInfo != null and speedTaskInfo != ''">#{speedTaskInfo},</if>
            <if test="normalTaskInfo != null and normalTaskInfo != ''">#{normalTaskInfo},</if>
            <if test="reachCount != null">#{reachCount},</if>
            <if test="batchNum != null">#{batchNum},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="batchCreate">
        insert into biz_operate_node_record (`target`,chat_id_list,plan_id,point_id,status,node_json,create_time,send_time,batch_num)
        values
            <foreach collection="nodeList" item="node" separator=",">
                (#{target},#{chatIdList},#{planId}, #{pointId}, #{status},#{node},#{executeDateTime},#{sendDateTime},#{batchNum})
            </foreach>
    </insert>

    <update id="updateBizOperateNodeRecord" parameterType="com.ruoyi.biz.domain.BizOperateNodeRecord">
        update biz_operate_node_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="target != null and target != ''">target = #{target},</if>
            <if test="chatIdList != null and chatIdList != ''">chat_id_list = #{chatIdList},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="pointId != null">point_id = #{pointId},</if>
            <if test="nodeJson != null and nodeJson != ''">node_json = #{nodeJson},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="speedTaskIds != null and speedTaskIds != ''">speed_task_ids = #{speedTaskIds},</if>
            <if test="normalTaskIds != null and normalTaskIds != ''">normal_task_ids = #{normalTaskIds},</if>
            <if test="taskExecuted != null">task_executed = #{taskExecuted},</if>
            <if test="dataTaskExecuted != null">data_task_executed = #{dataTaskExecuted},</if>
            <if test="speedTaskInfo != null and speedTaskInfo != ''">speed_task_info = #{speedTaskInfo},</if>
            <if test="normalTaskInfo != null and normalTaskInfo != ''">normal_task_info = #{normalTaskInfo},</if>
            <if test="reachCount != null">reach_count = #{reachCount},</if>
            <if test="batchNum != null">batch_num = #{batchNum},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizOperateNodeRecordById" parameterType="Long">
        delete from biz_operate_node_record where id = #{id}
    </delete>

    <delete id="deleteBizOperateNodeRecordByIds" parameterType="String">
        delete from biz_operate_node_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
