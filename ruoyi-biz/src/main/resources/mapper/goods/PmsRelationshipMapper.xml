<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.goods.mapper.PmsRelationshipMapper">

    <resultMap type="com.ruoyi.goods.domain.PmsRelationship" id="PmsRelationshipResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="relProductId"    column="rel_product_id"    />
        <result property="relProductName"    column="rel_product_name"    />
        <result property="productName"    column="product_name"    />
        <result property="relationScore"    column="relation_score"    />
        <result property="addedScore"    column="added_score"    />
        <result property="totalScore"    column="total_score"    />
        <result property="relLevel"    column="rel_level"    />
        <result property="state"    column="state"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.product_id, t.rel_product_id, t.rel_product_name, t.product_name, t.relation_score, t.added_score, t.total_score, t.rel_level, t.state, t.create_time, t.create_by, t.update_time, t.update_by    </sql>

    <sql id="selectPmsRelationshipVo">
        select <include refid="Base_Column_List"/>  from pms_relationship t
    </sql>

    <select id="selectPmsRelationshipList" parameterType="com.ruoyi.goods.domain.PmsRelationship" resultMap="PmsRelationshipResult">
        <include refid="selectPmsRelationshipVo"/>
        <where>
            <if test="productId != null "> and t.product_id = #{productId}</if>
            <if test="productName != null "> and t.product_name like concat('%', #{productName}, '%')</if>
            <if test="relProductId != null "> and t.rel_product_id = #{relProductId}</if>
            <if test="relProductName != null  and relProductName != ''"> and t.rel_product_name like concat('%', #{relProductName}, '%')</if>
            <if test="relationScore != null "> and t.relation_score = #{relationScore}</if>
            <if test="addedScore != null "> and t.added_score = #{addedScore}</if>
            <if test="totalScore != null and totalScore != 0  "> and t.total_score = #{totalScore}</if>
            <if test="relLevel != null "> and t.rel_level = #{relLevel}</if>
            <if test="state != null "> and t.state = #{state}</if>
            <if test="params.added == true "> and t.added_score > 0</if>
            <if test="params.lowScore != null  "> and t.total_score >= #{params.lowScore} </if>
        </where>
    </select>

    <select id="selectPmsRelationshipListRank" parameterType="com.ruoyi.goods.domain.PmsRelationship" resultMap="PmsRelationshipResult">
        set @row_number = 0;
        select t.id, t.product_id, t.product_name, t.rel_product_id, t.rel_product_name, t.relation_score, t.added_score, t.total_score,
        (@row_number := @row_number + 1) AS rel_level, t.state, t.create_time, t.create_by, t.update_time, t.update_by
        from pms_relationship t

        <where>
            and t.product_id = #{productId}
            and t.relation_score >= #{relationScore}
            and added_score = 0
        </where>
    </select>

    <select id="selectPmsRelationshipById" parameterType="Long" resultMap="PmsRelationshipResult">
        <include refid="selectPmsRelationshipVo"/>
        where t.id = #{id}
    </select>

    <select id="selectPmsRelationshipByProductIdAndLevel" resultMap="PmsRelationshipResult">
        <include refid="selectPmsRelationshipVo"/>
        where t.product_id = #{productId} and t.total_score >= #{lowScore} and added_score = 0 order by relation_score desc limit 1 offset #{level}
    </select>

    <insert id="insertPmsRelationship" parameterType="com.ruoyi.goods.domain.PmsRelationship" useGeneratedKeys="true" keyProperty="id">
        insert into pms_relationship
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="productName != null">product_name,</if>
            <if test="relProductId != null">rel_product_id,</if>
            <if test="relProductName != null and relProductName != ''">rel_product_name,</if>
            <if test="relationScore != null">relation_score,</if>
            <if test="addedScore != null">added_score,</if>
            <if test="totalScore != null">total_score,</if>
            <if test="relLevel != null">rel_level,</if>
            <if test="state != null">state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="productName != null">#{productName},</if>
            <if test="relProductId != null">#{relProductId},</if>
            <if test="relProductName != null and relProductName != ''">#{relProductName},</if>
            <if test="relationScore != null">#{relationScore},</if>
            <if test="addedScore != null">#{addedScore},</if>
            <if test="totalScore != null">#{totalScore},</if>
            <if test="relLevel != null">#{relLevel},</if>
            <if test="state != null">#{state},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <insert id="insertOrUpdatePmsRelationship" parameterType="com.ruoyi.goods.domain.PmsRelationship" useGeneratedKeys="true" keyProperty="id">
        insert into pms_relationship
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="productName != null">product_name,</if>
            <if test="relProductId != null">rel_product_id,</if>
            <if test="relProductName != null and relProductName != ''">rel_product_name,</if>
            <if test="relationScore != null">relation_score,</if>
            <if test="addedScore != null">added_score,</if>
            <if test="totalScore != null">total_score,</if>
            <if test="relLevel != null">rel_level,</if>
            <if test="state != null">state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="productName != null">#{productName},</if>
            <if test="relProductId != null">#{relProductId},</if>
            <if test="relProductName != null and relProductName != ''">#{relProductName},</if>
            <if test="relationScore != null">#{relationScore},</if>
            <if test="addedScore != null">#{addedScore},</if>
            <if test="totalScore != null">#{totalScore},</if>
            <if test="relLevel != null">#{relLevel},</if>
            <if test="state != null">#{state},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
        ON DUPLICATE KEY
        <trim prefix="UPDATE" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="relProductId != null">rel_product_id = #{relProductId},</if>
            <if test="relProductName != null and relProductName != ''">rel_product_name = #{relProductName},</if>
            <if test="relationScore != null">relation_score = #{relationScore},</if>
            <if test="addedScore != null">added_score = #{addedScore},</if>
            <if test="totalScore != null">total_score = #{totalScore},</if>
            rel_level = #{relLevel},
            <if test="state != null">state = #{state},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
    </insert>

    <update id="updatePmsRelationship" parameterType="com.ruoyi.goods.domain.PmsRelationship">
        update pms_relationship
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="relProductId != null">rel_product_id = #{relProductId},</if>
            <if test="relProductName != null and relProductName != ''">rel_product_name = #{relProductName},</if>
            <if test="relationScore != null">relation_score = #{relationScore},</if>
            <if test="addedScore != null">added_score = #{addedScore},</if>
            <if test="totalScore != null">total_score = #{totalScore},</if>
            <if test="relLevel != null">rel_level = #{relLevel},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePmsRelationshipById" parameterType="Long">
        delete from pms_relationship where id = #{id}
    </delete>

    <delete id="deletePmsRelationshipByIds" parameterType="String">
        delete from pms_relationship where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByProductId" parameterType="Long" resultMap="PmsRelationshipResult">
        <include refid="selectPmsRelationshipVo"/>
        where t.product_id = #{productId}
            and t.rel_product_id = #{relProductId}
    </select>
</mapper>
