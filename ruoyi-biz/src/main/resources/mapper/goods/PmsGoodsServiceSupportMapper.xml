<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.goods.mapper.PmsGoodsServiceSupportMapper">

    <resultMap type="PmsGoodsServiceSupport" id="PmsGoodsServiceSupportResult">
        <result property="id" column="id"/>
        <result property="spuId" column="spu_id"/>
        <result property="serviceSupportId" column="service_support_id"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <insert id="addSpuServicceSupportServices" parameterType="java.util.List">
        insert into pms_goods_service_support

        (spu_id, service_support_id,del_flag)
        values
        <foreach collection="list" item="spuServiceSupport" index="index" separator=",">
            (
            #{spuServiceSupport.spuId},
            #{spuServiceSupport.serviceSupportId},
            '0'
            )
        </foreach>
    </insert>

    <select id="queryBySpuId" parameterType="java.lang.Long" resultMap="PmsGoodsServiceSupportResult">
        select * from pms_goods_service_support where spu_id = #{spuId} AND  del_flag = '0'
    </select>


    <update id="deleteBySpuId" parameterType="java.lang.Long">
        update pms_goods_service_support set del_flag = '1' where spu_id = #{spuId}
    </update>

    <delete id="deleteBySpuIdPhysical" parameterType="java.lang.Long">
        delete from pms_goods_service_support  where spu_id = #{spuId}
    </delete>

    <sql id="selectPmsGoodsServiceSupportVo">
        select id, spu_id, service_support_id, del_flag from pms_goods_service_support
    </sql>

    <select id="selectPmsGoodsServiceSupportList" parameterType="PmsGoodsServiceSupport"
            resultMap="PmsGoodsServiceSupportResult">
        <include refid="selectPmsGoodsServiceSupportVo"/>
        <where>
        </where>
    </select>

    <select id="selectPmsGoodsServiceSupportById" parameterType="Long" resultMap="PmsGoodsServiceSupportResult">
        <include refid="selectPmsGoodsServiceSupportVo"/>
        where id = #{id}
    </select>

    <insert id="insertPmsGoodsServiceSupport" parameterType="PmsGoodsServiceSupport" useGeneratedKeys="true"
            keyProperty="id">
        insert into pms_goods_service_support
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="spuId != null">spu_id,</if>
            <if test="serviceSupportId != null">service_support_id,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="spuId != null">#{spuId},</if>
            <if test="serviceSupportId != null">#{serviceSupportId},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updatePmsGoodsServiceSupport" parameterType="PmsGoodsServiceSupport">
        update pms_goods_service_support
        <trim prefix="SET" suffixOverrides=",">
            <if test="spuId != null">spu_id = #{spuId},</if>
            <if test="serviceSupportId != null">service_support_id = #{serviceSupportId},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePmsGoodsServiceSupportById" parameterType="Long">
        delete from pms_goods_service_support where id = #{id}
    </delete>

    <delete id="deletePmsGoodsServiceSupportByIds" parameterType="String">
        delete from pms_goods_service_support where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>