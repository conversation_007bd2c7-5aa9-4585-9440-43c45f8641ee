<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.goods.mapper.PmsSpecValueMapper">

    <resultMap type="PmsSpecValue" id="PmsSpecValueResult">
        <result property="id" column="id"/>
        <result property="specId" column="spec_id"/>
        <result property="name" column="name"/>
        <result property="sort" column="sort"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createName" column="create_name"/>
        <result property="modifyName" column="modify_name"/>
        <result property="delName" column="del_name"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="delTime" column="del_time"/>
    </resultMap>

    <sql id="selectPmsSpecValueVo">
        select id, spec_id, name, sort, del_flag, create_name, modify_name, del_name, create_time, modify_time, del_time from pms_spec_value
    </sql>

    <select id="selectPmsSpecValueList" parameterType="PmsSpecValue" resultMap="PmsSpecValueResult">
        <include refid="selectPmsSpecValueVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
        </where>
    </select>

    <select id="selectPmsSpecValueById" parameterType="String" resultMap="PmsSpecValueResult">
        <include refid="selectPmsSpecValueVo"/>
        where id = #{id}
    </select>

    <insert id="insertPmsSpecValue" parameterType="PmsSpecValue">
        insert into pms_spec_value
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="specId != null">spec_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="sort != null">sort,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createName != null">create_name,</if>
            <if test="modifyName != null">modify_name,</if>
            <if test="delName != null">del_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="delTime != null">del_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="specId != null">#{specId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="sort != null">#{sort},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createName != null">#{createName},</if>
            <if test="modifyName != null">#{modifyName},</if>
            <if test="delName != null">#{delName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="delTime != null">#{delTime},</if>
        </trim>
    </insert>

    <update id="updatePmsSpecValue" parameterType="PmsSpecValue">
        update pms_spec_value
        <trim prefix="SET" suffixOverrides=",">
            <if test="specId != null">spec_id = #{specId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="modifyName != null">modify_name = #{modifyName},</if>
            <if test="delName != null">del_name = #{delName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="delTime != null">del_time = #{delTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePmsSpecValueById" parameterType="String">
        delete from pms_spec_value where id = #{id}
    </delete>

    <delete id="deletePmsSpecValueByIds" parameterType="String">
        delete from pms_spec_value where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="querySpecValuesBySpecId" parameterType="java.lang.Long" resultMap="PmsSpecValueResult">
        select * from pms_spec_value
        where   spec_id = #{specId} order by sort asc
    </select>

    <insert id="addSpecValues" parameterType="java.util.List">
        insert into pms_spec_value

        (id,spec_id, name, sort, del_flag,create_name,modify_name,del_name,create_time,modify_time,del_time)
        values
        <foreach collection="list" item="specvalue" index="index" separator=",">
            (
            #{specvalue.id},
            #{specvalue.specId},
            #{specvalue.name},
            #{specvalue.sort},
            #{specvalue.delFlag},
            #{specvalue.createName},
            #{specvalue.modifyName},
            #{specvalue.delName},
            #{specvalue.createTime},
            #{specvalue.modifyTime},
            #{specvalue.delTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        name = VALUES(name)

    </insert>


    <insert id="addSpecValue" parameterType="java.util.List">
        insert into pms_spec_value

        (id,spec_id, name,sort, del_flag,create_name,create_time)
        values(
            #{id},
            #{specId},
            #{name},
            #{sort},
            #{delFlag},
            #{createName},
            #{createTime}
            )
    </insert>

    <update id="deleteBySpecId" parameterType="com.ruoyi.goods.domain.PmsSpecValue">
        update pms_spec_value
        <set>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="delName != null">
                del_name = #{delName},
            </if>
            <if test="delTime != null">
                del_time = #{delTime}
            </if>
        </set>
        where spec_id = #{specId}
    </update>

    <delete id="deleteBySpecIdPhysics" parameterType="java.lang.Long">
        delete from pms_spec_value   where spec_id = #{specId}
    </delete>
</mapper>
