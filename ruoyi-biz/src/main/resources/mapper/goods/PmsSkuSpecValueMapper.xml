<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.goods.mapper.PmsSkuSpecValueMapper">

    <resultMap type="PmsSkuSpecValue" id="PmsSkuSpecValueResult">
        <result property="id" column="id"/>
        <result property="spuId" column="spu_id"/>
        <result property="skuId" column="sku_id"/>
        <result property="specId" column="spec_id"/>
        <result property="specValueId" column="spec_value_id"/>
        <result property="valueRemark" column="value_remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectPmsSkuSpecValueVo">
        select id, spu_id, sku_id, spec_id, spec_value_id, value_remark, del_flag from pms_sku_spec_value
    </sql>

    <select id="selectPmsSkuSpecValueList" parameterType="PmsSkuSpecValue" resultMap="PmsSkuSpecValueResult">
        <include refid="selectPmsSkuSpecValueVo"/>
        <where>
            <if test="spuId != null ">and spu_id = #{spuId}</if>
            <if test="skuId != null  and skuId != ''">and sku_id = #{skuId}</if>
            <if test="specId != null ">and spec_id = #{specId}</if>
            <if test="specValueId != null  and specValueId != ''">and spec_value_id = #{specValueId}</if>
            <if test="valueRemark != null  and valueRemark != ''">and value_remark = #{valueRemark}</if>
        </where>
    </select>

    <select id="selectPmsSkuSpecValueById" parameterType="Long" resultMap="PmsSkuSpecValueResult">
        <include refid="selectPmsSkuSpecValueVo"/>
        where id = #{id}
    </select>

    <insert id="insertPmsSkuSpecValue" parameterType="PmsSkuSpecValue" useGeneratedKeys="true" keyProperty="id">
        insert into pms_sku_spec_value
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="spuId != null">spu_id,</if>
            <if test="skuId != null and skuId != ''">sku_id,</if>
            <if test="specId != null">spec_id,</if>
            <if test="specValueId != null and specValueId != ''">spec_value_id,</if>
            <if test="valueRemark != null and valueRemark != ''">value_remark,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="spuId != null">#{spuId},</if>
            <if test="skuId != null and skuId != ''">#{skuId},</if>
            <if test="specId != null">#{specId},</if>
            <if test="specValueId != null and specValueId != ''">#{specValueId},</if>
            <if test="valueRemark != null and valueRemark != ''">#{valueRemark},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updatePmsSkuSpecValue" parameterType="PmsSkuSpecValue">
        update pms_sku_spec_value
        <trim prefix="SET" suffixOverrides=",">
            <if test="spuId != null">spu_id = #{spuId},</if>
            <if test="skuId != null and skuId != ''">sku_id = #{skuId},</if>
            <if test="specId != null">spec_id = #{specId},</if>
            <if test="specValueId != null and specValueId != ''">spec_value_id = #{specValueId},</if>
            <if test="valueRemark != null and valueRemark != ''">value_remark = #{valueRemark},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePmsSkuSpecValueById" parameterType="Long">
        delete from pms_sku_spec_value where id = #{id}
    </delete>

    <delete id="deletePmsSkuSpecValueByIds" parameterType="String">
        delete from pms_sku_spec_value where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="addSkuSpecValues" parameterType="java.util.List">
        insert into pms_sku_spec_value
        (spu_id,sku_id, spec_id,spec_value_id,value_remark,del_flag)
        values
        <foreach collection="list" item="skuSpecValue" index="index" separator=",">
            (
            #{skuSpecValue.spuId},
            #{skuSpecValue.skuId},
            #{skuSpecValue.specId},
            #{skuSpecValue.specValueId},
            #{skuSpecValue.valueRemark},
            '0'
            )
        </foreach>
    </insert>

    <select id="queryBySkuId" parameterType="java.lang.String" resultMap="PmsSkuSpecValueResult">
        select * from pms_sku_spec_value where sku_id = #{skuId} and del_flag = '0'
    </select>


    <update id="deleteBySpuId" parameterType="java.lang.Long">
        update pms_sku_spec_value set del_flag = '1' where spu_id = #{spuId}
    </update>

    <select id="queryBySpuId" parameterType="java.lang.Long" resultMap="PmsSkuSpecValueResult">
        select * from pms_sku_spec_value where spu_id = #{spuId} and del_flag = '0'
    </select>

    <delete id="deleteBySpuIdPhysical" parameterType="java.lang.Long">
         delete from pms_sku_spec_value  where spu_id = #{spuId}
    </delete>
    <delete id="deleteBySkuIdPhysical">
        delete from pms_sku_spec_value  where sku_id = #{skuId}
    </delete>
</mapper>
