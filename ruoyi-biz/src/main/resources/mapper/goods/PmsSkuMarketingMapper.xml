<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.goods.mapper.PmsSkuMarketingMapper">

    <resultMap type="PmsSkuMarketing" id="PmsSkuMarketingResult">
        <result property="id" column="id"/>
        <result property="skuid" column="skuid"/>
        <result property="marketingId" column="marketing_id"/>
        <result property="price" column="price"/>
    </resultMap>

    <sql id="selectPmsSkuMarketingVo">
        select id, skuid, marketing_id, price from pms_sku_marketing
    </sql>

    <select id="selectPmsSkuMarketingList" parameterType="PmsSkuMarketing" resultMap="PmsSkuMarketingResult">
        <include refid="selectPmsSkuMarketingVo"/>
        <where>
            <if test="skuid != null  and skuid != ''">and skuid = #{skuid}</if>
            <if test="marketingId != null ">and marketing_id = #{marketingId}</if>
            <if test="price != null ">and price = #{price}</if>
        </where>
    </select>

    <select id="selectPmsSkuMarketingById" parameterType="Long" resultMap="PmsSkuMarketingResult">
        <include refid="selectPmsSkuMarketingVo"/>
        where id = #{id}
    </select>

    <insert id="insertPmsSkuMarketing" parameterType="PmsSkuMarketing" useGeneratedKeys="true" keyProperty="id">
        insert into pms_sku_marketing
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="skuid != null and skuid != ''">skuid,</if>
            <if test="marketingId != null">marketing_id,</if>
            <if test="price != null">price,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="skuid != null and skuid != ''">#{skuid},</if>
            <if test="marketingId != null">#{marketingId},</if>
            <if test="price != null">#{price},</if>
        </trim>
    </insert>

    <update id="updatePmsSkuMarketing" parameterType="PmsSkuMarketing">
        update pms_sku_marketing
        <trim prefix="SET" suffixOverrides=",">
            <if test="skuid != null and skuid != ''">skuid = #{skuid},</if>
            <if test="marketingId != null">marketing_id = #{marketingId},</if>
            <if test="price != null">price = #{price},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePmsSkuMarketingById" parameterType="Long">
        delete from pms_sku_marketing where id = #{id}
    </delete>

    <delete id="deletePmsSkuMarketingByIds" parameterType="String">
        delete from pms_sku_marketing where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>