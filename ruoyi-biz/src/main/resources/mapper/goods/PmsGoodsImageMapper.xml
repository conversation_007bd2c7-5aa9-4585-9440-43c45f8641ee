<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.goods.mapper.PmsGoodsImageMapper">

    <resultMap type="PmsGoodsImage" id="PmsGoodsImageResult">
        <result property="id" column="id"/>
        <result property="spuId" column="spu_id"/>
        <result property="url" column="url"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectPmsGoodsImageVo">
        select id, spu_id, url, del_flag from pms_goods_image
    </sql>

    <select id="selectPmsGoodsImageList" parameterType="PmsGoodsImage" resultMap="PmsGoodsImageResult">
        <include refid="selectPmsGoodsImageVo"/>
        <where>
            <if test="spuId != null ">and spu_id = #{spuId}</if>
            <if test="url != null  and url != ''">and url = #{url}</if>
        </where>
    </select>

    <select id="selectPmsGoodsImageById" parameterType="Long" resultMap="PmsGoodsImageResult">
        <include refid="selectPmsGoodsImageVo"/>
        where id = #{id}
    </select>

    <insert id="insertPmsGoodsImage" parameterType="PmsGoodsImage" useGeneratedKeys="true" keyProperty="id">
        insert into pms_goods_image
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="spuId != null">spu_id,</if>
            <if test="url != null and url != ''">url,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="spuId != null">#{spuId},</if>
            <if test="url != null and url != ''">#{url},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updatePmsGoodsImage" parameterType="PmsGoodsImage">
        update pms_goods_image
        <trim prefix="SET" suffixOverrides=",">
            <if test="spuId != null">spu_id = #{spuId},</if>
            <if test="url != null and url != ''">url = #{url},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePmsGoodsImageById" parameterType="Long">
        delete from pms_goods_image where id = #{id}
    </delete>

    <delete id="deletePmsGoodsImageByIds" parameterType="String">
        delete from pms_goods_image where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <insert id="addSpuImages" parameterType="java.util.List">
        insert into pms_goods_image
        (spu_id, url,del_flag)
        values
        <foreach collection="list" item="spuImage" index="index" separator=",">
            (
            #{spuImage.spuId},
            #{spuImage.url},
            #{spuImage.delFlag}
            )
        </foreach>
    </insert>


    <update id="deleteBySpuId" parameterType="java.lang.Long">
        update pms_goods_image set del_flag = '1' where spu_id = #{spuId}
    </update>

    <select id="queryBySpuId" parameterType="java.lang.Long" resultMap="PmsGoodsImageResult">
        select * from pms_goods_image where spu_id = #{spuId}
    </select>

    <delete id="deleteBySpuIdPhysical" parameterType="java.lang.Long">
        delete from pms_goods_image where spu_id = #{spuId}
    </delete>
</mapper>