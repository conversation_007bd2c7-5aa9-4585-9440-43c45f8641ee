<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.goods.mapper.PmsSkuMemberPriceMapper">

    <resultMap type="PmsSkuMemberPrice" id="PmsSkuMemberPriceResult">
        <result property="id" column="id"/>
        <result property="spuId" column="spu_id"/>
        <result property="skuId" column="sku_id"/>
        <result property="memberLevelId" column="member_level_id"/>
        <result property="price" column="price"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectPmsSkuMemberPriceVo">
        select id, spu_id, sku_id, member_level_id, price, del_flag from pms_sku_member_price
    </sql>

    <select id="selectPmsSkuMemberPriceList" parameterType="PmsSkuMemberPrice" resultMap="PmsSkuMemberPriceResult">
        <include refid="selectPmsSkuMemberPriceVo"/>
        <where>
            <if test="spuId != null ">and spu_id = #{spuId}</if>
            <if test="skuId != null  and skuId != ''">and sku_id = #{skuId}</if>
            <if test="memberLevelId != null ">and member_level_id = #{memberLevelId}</if>
            <if test="price != null ">and price = #{price}</if>
        </where>
    </select>

    <select id="selectPmsSkuMemberPriceById" parameterType="Long" resultMap="PmsSkuMemberPriceResult">
        <include refid="selectPmsSkuMemberPriceVo"/>
        where id = #{id}
    </select>

    <insert id="insertPmsSkuMemberPrice" parameterType="PmsSkuMemberPrice" useGeneratedKeys="true" keyProperty="id">
        insert into pms_sku_member_price
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="spuId != null">spu_id,</if>
            <if test="skuId != null and skuId != ''">sku_id,</if>
            <if test="memberLevelId != null">member_level_id,</if>
            <if test="price != null">price,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="spuId != null">#{spuId},</if>
            <if test="skuId != null and skuId != ''">#{skuId},</if>
            <if test="memberLevelId != null">#{memberLevelId},</if>
            <if test="price != null">#{price},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updatePmsSkuMemberPrice" parameterType="PmsSkuMemberPrice">
        update pms_sku_member_price
        <trim prefix="SET" suffixOverrides=",">
            <if test="spuId != null">spu_id = #{spuId},</if>
            <if test="skuId != null and skuId != ''">sku_id = #{skuId},</if>
            <if test="memberLevelId != null">member_level_id = #{memberLevelId},</if>
            <if test="price != null">price = #{price},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePmsSkuMemberPriceById" parameterType="Long">
        delete from pms_sku_member_price where id = #{id}
    </delete>

    <delete id="deletePmsSkuMemberPriceByIds" parameterType="String">
        delete from pms_sku_member_price where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="addSkuMemberPrices" parameterType="java.util.List">
        insert into pms_sku_member_price
        (spu_id,sku_id, member_level_id,price,del_flag)
        values
        <foreach collection="list" item="skuMemberPrice" index="index" separator=",">
            (
            #{skuMemberPrice.spuId},
            #{skuMemberPrice.skuId},
            #{skuMemberPrice.memberLevelId},
            #{skuMemberPrice.price},
            '0'
            )
        </foreach>
    </insert>

    <select id="queryBySkuId" parameterType="java.lang.String" resultMap="PmsSkuMemberPriceResult">
        select * from pms_sku_member_price where sku_id = #{skuId}
    </select>

    <update id="deleteBySpuId" parameterType="java.lang.Long">
        update pms_sku_member_price set del_flag = '1' where spu_id = #{spuId}
    </update>

    <delete id="deleteBySpuIdPhysical" parameterType="java.lang.Long">
        delete from pms_sku_member_price where spu_id = #{spuId}
    </delete>

    <update id="deleteByLevelId" parameterType="java.lang.Long">
                update pms_sku_member_price set del_flag = '1' where member_level_id = #{customerLevelId}
    </update>

    <select id="querySkuMemberPriceCountBySkuIds" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1) from (select count(1) from pms_sku_member_price where
        sku_id IN
        <foreach collection="list" separator="," open="(" close=")" item="skuId" index="index">
            #{skuId}
        </foreach>

        GROUP BY sku_id) a
    </select>
</mapper>