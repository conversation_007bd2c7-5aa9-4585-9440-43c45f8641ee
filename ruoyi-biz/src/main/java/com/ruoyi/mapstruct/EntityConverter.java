package com.ruoyi.mapstruct;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.biz.domain.BizChatStatistics;
import com.ruoyi.biz.domain.BizSalesPitch;
import com.ruoyi.biz.domain.dto.ChatStatisticsCache;
import com.ruoyi.goods.domain.PmsRelationship;
import com.ruoyi.goods.domain.req.ProductRelationReq;
import com.ruoyi.goods.domain.req.SalesPitchVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 功能 mapStruct 接口
 *
 * <AUTHOR>
 * @date 2024/7/17 16:21
 */
@Mapper()
public interface EntityConverter {
    EntityConverter INSTANCE = Mappers.getMapper(EntityConverter.class);

    /**
     * salesPitchVo 转实体
     *
     * @param salesPitchVo salesPitchVo
     * @return 实体
     */
    BizSalesPitch toEntity(SalesPitchVo salesPitchVo);
    PmsRelationship toEntity(ProductRelationReq salesPitchVo);

    @Mapping(target = "bannedWordInfo", expression = "java(com.alibaba.fastjson2.JSON.toJSONString(chatStatisticsCache.getBannedWordInfo()))")
    BizChatStatistics toEntity(ChatStatisticsCache chatStatisticsCache);
}
