package com.ruoyi.mapstruct;

import com.ruoyi.goods.domain.PmsRelationship;
import com.ruoyi.goods.domain.vo.PmsRelationshipVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 功能 mapStruct 接口
 *
 * <AUTHOR>
 * @date 2024/7/25
 */
@Mapper()
public interface VoConverter {
    VoConverter INSTANCE = Mappers.getMapper(VoConverter.class);

    /**
     * 实体转成 VO
     *
     * @param pmsRelationship pmsRelationship
     * @return 实体
     */
    PmsRelationshipVo toVo(PmsRelationship pmsRelationship);
}
