package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.PmsRelationshipRecord;

/**
 * 商品关联关系变更记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-31
 */
public interface PmsRelationshipRecordMapper 
{
    /**
     * 查询商品关联关系变更记录
     * 
     * @param id 商品关联关系变更记录主键
     * @return 商品关联关系变更记录
     */
    public PmsRelationshipRecord selectPmsRelationshipRecordById(Long id);

    /**
     * 查询商品关联关系变更记录列表
     * 
     * @param pmsRelationshipRecord 商品关联关系变更记录
     * @return 商品关联关系变更记录集合
     */
    public List<PmsRelationshipRecord> selectPmsRelationshipRecordList(PmsRelationshipRecord pmsRelationshipRecord);

    /**
     * 新增商品关联关系变更记录
     * 
     * @param pmsRelationshipRecord 商品关联关系变更记录
     * @return 结果
     */
    public int insertPmsRelationshipRecord(PmsRelationshipRecord pmsRelationshipRecord);

    /**
     * 修改商品关联关系变更记录
     * 
     * @param pmsRelationshipRecord 商品关联关系变更记录
     * @return 结果
     */
    public int updatePmsRelationshipRecord(PmsRelationshipRecord pmsRelationshipRecord);

    /**
     * 删除商品关联关系变更记录
     * 
     * @param id 商品关联关系变更记录主键
     * @return 结果
     */
    public int deletePmsRelationshipRecordById(Long id);

    /**
     * 批量删除商品关联关系变更记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePmsRelationshipRecordByIds(Long[] ids);
}
