package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.PmsRelationshipRecord;
import com.ruoyi.system.service.IPmsRelationshipRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 商品关联关系变更记录Controller
 *
 * <AUTHOR>
 * @date 2024-10-31
 */
@RestController
@RequestMapping("/biz/pms-relation-record")
public class PmsRelationshipRecordController extends BaseController
{
    @Resource
    private IPmsRelationshipRecordService pmsRelationshipRecordService;

    /**
     * 查询商品关联关系变更记录列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(PmsRelationshipRecord pmsRelationshipRecord)
    {
        startPage();
        List<PmsRelationshipRecord> list = pmsRelationshipRecordService.selectPmsRelationshipRecordList(pmsRelationshipRecord);
        return getDataTable(list);
    }

    /**
     * 导出商品关联关系变更记录列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:record:export')")
    @Log(title = "商品关联关系变更记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PmsRelationshipRecord pmsRelationshipRecord)
    {
        List<PmsRelationshipRecord> list = pmsRelationshipRecordService.selectPmsRelationshipRecordList(pmsRelationshipRecord);
        ExcelUtil<PmsRelationshipRecord> util = new ExcelUtil<PmsRelationshipRecord>(PmsRelationshipRecord.class);
        util.exportExcel(response, list, "商品关联关系变更记录数据");
    }

    /**
     * 获取商品关联关系变更记录详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(pmsRelationshipRecordService.selectPmsRelationshipRecordById(id));
    }

    /**
     * 新增商品关联关系变更记录
     */
    //@PreAuthorize("@ss.hasPermi('biz:record:add')")
    @Log(title = "商品关联关系变更记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PmsRelationshipRecord pmsRelationshipRecord)
    {
        return toAjax(pmsRelationshipRecordService.insertPmsRelationshipRecord(pmsRelationshipRecord));
    }

    /**
     * 修改商品关联关系变更记录
     */
    //@PreAuthorize("@ss.hasPermi('biz:record:edit')")
    @Log(title = "商品关联关系变更记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PmsRelationshipRecord pmsRelationshipRecord)
    {
        return toAjax(pmsRelationshipRecordService.updatePmsRelationshipRecord(pmsRelationshipRecord));
    }

    /**
     * 删除商品关联关系变更记录
     */
    //@PreAuthorize("@ss.hasPermi('biz:record:remove')")
    @Log(title = "商品关联关系变更记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(pmsRelationshipRecordService.deletePmsRelationshipRecordByIds(ids));
    }
}
