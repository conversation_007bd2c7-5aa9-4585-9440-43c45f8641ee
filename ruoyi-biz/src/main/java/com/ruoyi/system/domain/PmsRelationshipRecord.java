package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品关联关系变更记录对象 pms_relationship_record
 *
 * <AUTHOR>
 * @date 2024-10-31
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PmsRelationshipRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 商品 id */
    @Excel(name = "商品 id")
    private Long productId;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String productName;

    /** 关联商品 id */
    @Excel(name = "关联商品 id")
    private Long relProductId;

    /** 关联商品名称 */
    @Excel(name = "关联商品名称")
    private String relProductName;

    /** 变更之前的分数, 实际值乘100 */
    @Excel(name = "变更之前的分数, 实际值乘100")
    private Integer beforeScore;

    /** 当前分数, 实际值乘100 */
    @Excel(name = "当前分数, 实际值乘100")
    private Integer currentScore;


}
