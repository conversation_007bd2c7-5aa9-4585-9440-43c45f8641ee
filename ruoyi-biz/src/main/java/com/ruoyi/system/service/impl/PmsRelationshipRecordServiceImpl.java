package com.ruoyi.system.service.impl;

import java.util.Date;
import java.util.List;

import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.utils.DateUtils;
import javax.annotation.Resource;
import javax.xml.crypto.Data;

import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.goods.domain.PmsRelationship;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.PmsRelationshipRecordMapper;
import com.ruoyi.system.domain.PmsRelationshipRecord;
import com.ruoyi.system.service.IPmsRelationshipRecordService;

/**
 * 商品关联关系变更记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-31
 */
@Service
public class PmsRelationshipRecordServiceImpl implements IPmsRelationshipRecordService
{
    @Resource
    private PmsRelationshipRecordMapper pmsRelationshipRecordMapper;

    /**
     * 查询商品关联关系变更记录
     *
     * @param id 商品关联关系变更记录主键
     * @return 商品关联关系变更记录
     */
    @Override
    public PmsRelationshipRecord selectPmsRelationshipRecordById(Long id)
    {
        return pmsRelationshipRecordMapper.selectPmsRelationshipRecordById(id);
    }

    /**
     * 查询商品关联关系变更记录列表
     *
     * @param pmsRelationshipRecord 商品关联关系变更记录
     * @return 商品关联关系变更记录
     */
    @Override
    public List<PmsRelationshipRecord> selectPmsRelationshipRecordList(PmsRelationshipRecord pmsRelationshipRecord)
    {
        return pmsRelationshipRecordMapper.selectPmsRelationshipRecordList(pmsRelationshipRecord);
    }

    /**
     * 新增商品关联关系变更记录
     *
     * @param pmsRelationshipRecord 商品关联关系变更记录
     * @return 结果
     */
    @Override
    public int insertPmsRelationshipRecord(PmsRelationshipRecord pmsRelationshipRecord)
    {
        pmsRelationshipRecord.setCreateTime(DateUtils.getNowDate());
        return pmsRelationshipRecordMapper.insertPmsRelationshipRecord(pmsRelationshipRecord);
    }

    /**
     * 修改商品关联关系变更记录
     *
     * @param pmsRelationshipRecord 商品关联关系变更记录
     * @return 结果
     */
    @Override
    public int updatePmsRelationshipRecord(PmsRelationshipRecord pmsRelationshipRecord)
    {
        pmsRelationshipRecord.setUpdateTime(DateUtils.getNowDate());
        return pmsRelationshipRecordMapper.updatePmsRelationshipRecord(pmsRelationshipRecord);
    }

    /**
     * 批量删除商品关联关系变更记录
     *
     * @param ids 需要删除的商品关联关系变更记录主键
     * @return 结果
     */
    @Override
    public int deletePmsRelationshipRecordByIds(Long[] ids)
    {
        return pmsRelationshipRecordMapper.deletePmsRelationshipRecordByIds(ids);
    }

    /**
     * 删除商品关联关系变更记录信息
     *
     * @param id 商品关联关系变更记录主键
     * @return 结果
     */
    @Override
    public int deletePmsRelationshipRecordById(Long id)
    {
        return pmsRelationshipRecordMapper.deletePmsRelationshipRecordById(id);
    }

    @Override
    public int createRecord(PmsRelationship pmsRelationship, Integer beforeScore) {
        PmsRelationshipRecord pmsRelationshipRecord = new PmsRelationshipRecord();
        pmsRelationshipRecord.setId(IdUtils.generator());
        pmsRelationshipRecord.setProductId(pmsRelationship.getProductId());
        pmsRelationshipRecord.setProductName(pmsRelationship.getProductName());
        pmsRelationshipRecord.setRelProductId(pmsRelationship.getRelProductId());
        pmsRelationshipRecord.setRelProductName(pmsRelationship.getRelProductName());
        pmsRelationshipRecord.setBeforeScore(beforeScore);
        pmsRelationshipRecord.setCurrentScore(pmsRelationship.getTotalScore());
        pmsRelationshipRecord.setCreateBy(UserConstants.SYS_USER);
        pmsRelationshipRecord.setCreateTime(new Date());

        return pmsRelationshipRecordMapper.insertPmsRelationshipRecord(pmsRelationshipRecord);
    }
}
