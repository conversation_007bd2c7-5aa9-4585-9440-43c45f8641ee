package com.ruoyi.system.service;

import java.util.List;

import com.ruoyi.goods.domain.PmsRelationship;
import com.ruoyi.system.domain.PmsRelationshipRecord;

/**
 * 商品关联关系变更记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-31
 */
public interface IPmsRelationshipRecordService 
{
    /**
     * 查询商品关联关系变更记录
     * 
     * @param id 商品关联关系变更记录主键
     * @return 商品关联关系变更记录
     */
    public PmsRelationshipRecord selectPmsRelationshipRecordById(Long id);

    /**
     * 查询商品关联关系变更记录列表
     * 
     * @param pmsRelationshipRecord 商品关联关系变更记录
     * @return 商品关联关系变更记录集合
     */
    public List<PmsRelationshipRecord> selectPmsRelationshipRecordList(PmsRelationshipRecord pmsRelationshipRecord);

    /**
     * 新增商品关联关系变更记录
     * 
     * @param pmsRelationshipRecord 商品关联关系变更记录
     * @return 结果
     */
    public int insertPmsRelationshipRecord(PmsRelationshipRecord pmsRelationshipRecord);

    /**
     * 修改商品关联关系变更记录
     * 
     * @param pmsRelationshipRecord 商品关联关系变更记录
     * @return 结果
     */
    public int updatePmsRelationshipRecord(PmsRelationshipRecord pmsRelationshipRecord);

    /**
     * 批量删除商品关联关系变更记录
     * 
     * @param ids 需要删除的商品关联关系变更记录主键集合
     * @return 结果
     */
    public int deletePmsRelationshipRecordByIds(Long[] ids);

    /**
     * 删除商品关联关系变更记录信息
     * 
     * @param id 商品关联关系变更记录主键
     * @return 结果
     */
    public int deletePmsRelationshipRecordById(Long id);


    /**
     * 新增商品关联关系变更记录
     *
     * @param pmsRelationship 商品关联关系
     * @return 结果
     */
    public int createRecord(PmsRelationship pmsRelationship, Integer beforeScore);
}
