package com.ruoyi.biz.subscriber;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.domain.BizChannelCode;
import com.ruoyi.biz.domain.BizChannelCodeConfig;
import com.ruoyi.biz.domain.BizNewContactOperatePlan;
import com.ruoyi.biz.domain.BizOperatePlan;
import com.ruoyi.biz.domain.BizTaggable;
import com.ruoyi.biz.domain.dto.BizTaggableReq;
import com.ruoyi.biz.enums.EOperatePlanStatus;
import com.ruoyi.biz.enums.EOperatePointType;
import com.ruoyi.biz.enums.ETaggableRefType;
import com.ruoyi.biz.service.IBizChannelCodeConfigService;
import com.ruoyi.biz.service.IBizChannelCodeRecordService;
import com.ruoyi.biz.service.IBizMembershipService;
import com.ruoyi.biz.service.IBizNewContactOperatePlanService;
import com.ruoyi.biz.service.IBizOperatePlanService;
import com.ruoyi.biz.service.IBizTaggableService;
import com.ruoyi.biz.service.IBizUserService;
import com.ruoyi.common.enums.ERedisChannel;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.jzbot.domain.JzContact;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 新朋友事件监听
 */
@Configuration
@Slf4j
public class NewFriendSubscriber {


    @Resource
    private IBizOperatePlanService bizOperatePlanService;

    @Resource
    private IBizNewContactOperatePlanService bizNewContactOperatePlanService;

    @Resource
    private IBizUserService bizUserService;

    @Resource
    private IBizChannelCodeRecordService bizChannelCodeRecordService;

    @Resource
    private IBizChannelCodeConfigService bizChannelCodeConfigService;

    @Resource
    private IBizTaggableService bizTaggableService;

    @Resource
    private IBizMembershipService bizMembershipService;


    public NewFriendSubscriber(RedisMessageListenerContainer container) {
        container.addMessageListener((message, pattern) -> {

            String messageBody = JSON.parse(new String(message.getBody())).toString();

            log.info("新朋友事件信息 " + messageBody);
            if (StringUtils.isNotEmpty(messageBody)) {
                try {
                    JzContact contact = JSONObject.parseObject(messageBody, JzContact.class);

                    //检查新人二维码
                    BizOperatePlan plan = checkIsChannelJoin(contact);

                    if (plan == null) {
                        //获取默认的新人计划
                        plan = bizOperatePlanService.selectDefaultPlan();
                    }

                    if (plan == null) {
                        log.info("无新人运营计划配置");
                        return;
                    }

                    if (EOperatePlanStatus.STATUS_3.getCode().equals(plan.getStatus())
                            || EOperatePlanStatus.STATUS_2.getCode().equals(plan.getStatus())) {
                        return;
                    }

                    //删除之前绑定的计划
                    bizNewContactOperatePlanService.deleteBizNewContactOperatePlanByChatId(contact.getChatId());
                    //落地关联
                    BizNewContactOperatePlan contactOperatePlan = new BizNewContactOperatePlan();
                    contactOperatePlan.setChatId(contact.getChatId());
                    contactOperatePlan.setPlanId(plan.getId());
                    contactOperatePlan.setGoodsId(plan.getGoodsId());
                    bizNewContactOperatePlanService.insertBizNewContactOperatePlan(contactOperatePlan);

//                    //执行计划
//                    bizOperatePlanService.executeNewFriendPlan(plan.getId(), plan.getGoodsId(), contact.getChatId(), EOperatePointType.TYPE_0.getCode());

                    // 新好友检查时候有会员token, 尝试开启会员
//                    bizMembershipService.tryOpenMemberByContact(contact.getChatId());


                } catch (Exception e) {
                    log.error("处理新朋友事件信息发生错误", e);
                }
            }
        }, new PatternTopic(ERedisChannel.NEW_FRIEND_CHANNEL.getCode()));
    }

    /**
     * 检查是否是 通过新人二维码加入的
     *
     * @param contact
     * @return
     */
    private BizOperatePlan checkIsChannelJoin(JzContact contact) {

        //判断是否由渠道活码加进来的。
        BizChannelCode bizChannelCode = bizChannelCodeRecordService.checkChannelCode(contact.getUnionId());

        if (bizChannelCode == null) {
            return null;
        }

        List<Long> tagList = new ArrayList<>();
        //渠道的标签Id
        Long channelId = bizChannelCode.getChannelId();
        tagList.add(channelId);

        //获取到配置的标签
        BizTaggable taggable = new BizTaggable();
        taggable.setRefType(ETaggableRefType.CHANNEL_CONFIG.getCode());
        taggable.setRefId(bizChannelCode.getConfigId().toString());
        tagList.addAll(bizTaggableService.selectBizTaggableList(taggable).stream()
                .map(BizTaggable::getTagId).collect(Collectors.toList()));

        //给用户打上标签
        BizTaggableReq taggableReq = new BizTaggableReq();
        taggableReq.setTags(tagList);
        taggableReq.setRefType(ETaggableRefType.USER.getCode());
        taggableReq.setRefIdList(Collections.singletonList(contact.getUnionId()));
        bizTaggableService.insertBizTaggable(taggableReq);

//        BizUser bizUser = bizUserService.selectBizUserByUnionId(contact.getUnionId());
//        if (bizUser != null) {
//            BizTaggableReq taggableReq = new BizTaggableReq();
//            taggableReq.setTags(tagList);
//            taggableReq.setRefType(ETaggableRefType.USER.getCode());
//            taggableReq.setRefIdList(Collections.singletonList(bizUser.getId().toString()));
//            bizTaggableService.insertBizTaggable(taggableReq);
//        }


        BizChannelCodeConfig codeConfig = bizChannelCodeConfigService.
                selectBizChannelCodeConfigById(bizChannelCode.getConfigId());
        BizOperatePlan plan = bizOperatePlanService.selectBizOperatePlanById(codeConfig.getPlanId());

        if (EOperatePlanStatus.STATUS_2.getCode().equals(plan.getStatus()) || EOperatePlanStatus.STATUS_3.getCode().equals(plan.getStatus())) {
            log.warn("该新人计划已停止{}", plan.getName());
            return null;
        }

        plan.setGoodsId(codeConfig.getGoodsId());

        return plan;
    }
}
