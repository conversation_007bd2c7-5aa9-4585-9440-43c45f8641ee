package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizOperateNodeContent;
import com.ruoyi.biz.service.IBizOperateNodeContentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 动作内容发送记录Controller
 *
 * <AUTHOR>
 * @date 2024-04-21
 */
@RestController
@RequestMapping("/biz/nodeContent")
public class BizOperateNodeContentController extends BaseController
{
    @Resource
    private IBizOperateNodeContentService bizOperateNodeContentService;

    /**
     * 查询动作内容发送记录列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:nodeContent:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizOperateNodeContent bizOperateNodeContent)
    {
        startPage();
        List<BizOperateNodeContent> list = bizOperateNodeContentService.selectBizOperateNodeContentList(bizOperateNodeContent);
        return getDataTable(list);
    }

    /**
     * 导出动作内容发送记录列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:nodeContent:export')")
    @Log(title = "动作内容发送记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizOperateNodeContent bizOperateNodeContent)
    {
        List<BizOperateNodeContent> list = bizOperateNodeContentService.selectBizOperateNodeContentList(bizOperateNodeContent);
        ExcelUtil<BizOperateNodeContent> util = new ExcelUtil<BizOperateNodeContent>(BizOperateNodeContent.class);
        util.exportExcel(response, list, "动作内容发送记录数据");
    }

    /**
     * 获取动作内容发送记录详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:nodeContent:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizOperateNodeContentService.selectBizOperateNodeContentById(id));
    }

    /**
     * 新增动作内容发送记录
     */
    //@PreAuthorize("@ss.hasPermi('biz:nodeContent:add')")
    @Log(title = "动作内容发送记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizOperateNodeContent bizOperateNodeContent)
    {
        return toAjax(bizOperateNodeContentService.insertBizOperateNodeContent(bizOperateNodeContent));
    }

    /**
     * 修改动作内容发送记录
     */
    //@PreAuthorize("@ss.hasPermi('biz:nodeContent:edit')")
    @Log(title = "动作内容发送记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizOperateNodeContent bizOperateNodeContent)
    {
        return toAjax(bizOperateNodeContentService.updateBizOperateNodeContent(bizOperateNodeContent));
    }

    /**
     * 删除动作内容发送记录
     */
    //@PreAuthorize("@ss.hasPermi('biz:nodeContent:remove')")
    @Log(title = "动作内容发送记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizOperateNodeContentService.deleteBizOperateNodeContentByIds(ids));
    }
}
