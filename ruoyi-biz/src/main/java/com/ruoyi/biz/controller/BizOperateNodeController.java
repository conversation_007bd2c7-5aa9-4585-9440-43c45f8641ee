package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.biz.domain.dto.BizOperateNodeDto;
import com.ruoyi.biz.domain.dto.paln.MessageInput;
import com.ruoyi.common.utils.bean.EntityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizOperateNode;
import com.ruoyi.biz.service.IBizOperateNodeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 运营节点Controller
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@RestController
@RequestMapping("/operate/node")
public class BizOperateNodeController extends BaseController
{
    @Resource
    private IBizOperateNodeService bizOperateNodeService;

    /**
     * 查询运营节点列表
     */
//    //@PreAuthorize("@ss.hasPermi('operate:node:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizOperateNode bizOperateNode)
    {
        startPage();
        List<BizOperateNode> list = bizOperateNodeService.selectBizOperateNodeList(bizOperateNode);
        return getDataTable(list);
    }

    /**
     * 导出运营节点列表
     */
    //@PreAuthorize("@ss.hasPermi('operate:node:export')")
    @Log(title = "运营节点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizOperateNode bizOperateNode)
    {
        List<BizOperateNode> list = bizOperateNodeService.selectBizOperateNodeList(bizOperateNode);
        ExcelUtil<BizOperateNode> util = new ExcelUtil<BizOperateNode>(BizOperateNode.class);
        util.exportExcel(response, list, "运营节点数据");
    }

    /**
     * 获取运营节点详细信息
     */
    //@PreAuthorize("@ss.hasPermi('operate:node:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizOperateNodeService.selectBizOperateNodeById(id));
    }

    /**
     * 新增运营节点
     */
    //@PreAuthorize("@ss.hasPermi('operate:node:add')")
    @Log(title = "运营节点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody @Valid BizOperateNodeDto bizOperateNodeReq)
    {
        bizOperateNodeService.checkPayload(bizOperateNodeReq);
        BizOperateNode node = EntityUtils.copyData(bizOperateNodeReq,BizOperateNode.class);
        node.setThemeJson(JSON.toJSONString(bizOperateNodeReq.getThemeJson()));
        return toAjax(bizOperateNodeService.insertBizOperateNode(node));
    }

    /**
     * 修改运营节点
     */
    //@PreAuthorize("@ss.hasPermi('operate:node:edit')")
    @Log(title = "运营节点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody  @Valid BizOperateNodeDto bizOperateNodeReq)
    {
        bizOperateNodeService.checkPayload(bizOperateNodeReq);
        BizOperateNode node = EntityUtils.copyData(bizOperateNodeReq,BizOperateNode.class);
        node.setThemeJson(JSON.toJSONString(bizOperateNodeReq.getThemeJson()));
        return toAjax(bizOperateNodeService.updateBizOperateNode(node));
    }

    /**
     * 删除运营节点
     */
    //@PreAuthorize("@ss.hasPermi('operate:node:remove')")
    @Log(title = "运营节点", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizOperateNodeService.deleteBizOperateNodeByIds(ids));
    }
}
