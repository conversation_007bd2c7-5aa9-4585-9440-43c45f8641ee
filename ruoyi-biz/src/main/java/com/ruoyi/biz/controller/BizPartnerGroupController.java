package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizPartnerGroup;
import com.ruoyi.biz.service.IBizPartnerGroupService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 合伙人群关联Controller
 *
 * <AUTHOR>
 * @date 2024-07-21
 */
@RestController
@RequestMapping("/biz/partnerGroup")
public class BizPartnerGroupController extends BaseController
{
    @Resource
    private IBizPartnerGroupService bizPartnerGroupService;

    /**
     * 查询合伙人群关联列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:partnerGroup:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizPartnerGroup bizPartnerGroup)
    {
        startPage();
        List<BizPartnerGroup> list = bizPartnerGroupService.selectBizPartnerGroupList(bizPartnerGroup);
        return getDataTable(list);
    }

    /**
     * 导出合伙人群关联列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:partnerGroup:export')")
    @Log(title = "合伙人群关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizPartnerGroup bizPartnerGroup)
    {
        List<BizPartnerGroup> list = bizPartnerGroupService.selectBizPartnerGroupList(bizPartnerGroup);
        ExcelUtil<BizPartnerGroup> util = new ExcelUtil<BizPartnerGroup>(BizPartnerGroup.class);
        util.exportExcel(response, list, "合伙人群关联数据");
    }

    /**
     * 获取合伙人群关联详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:partnerGroup:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizPartnerGroupService.selectBizPartnerGroupById(id));
    }

    /**
     * 新增合伙人群关联
     */
    //@PreAuthorize("@ss.hasPermi('biz:partnerGroup:add')")
    @Log(title = "合伙人群关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizPartnerGroup bizPartnerGroup)
    {
        return toAjax(bizPartnerGroupService.insertBizPartnerGroup(bizPartnerGroup));
    }

    /**
     * 修改合伙人群关联
     */
    //@PreAuthorize("@ss.hasPermi('biz:partnerGroup:edit')")
    @Log(title = "合伙人群关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizPartnerGroup bizPartnerGroup)
    {
        return toAjax(bizPartnerGroupService.updateBizPartnerGroup(bizPartnerGroup));
    }

    /**
     * 删除合伙人群关联
     */
    //@PreAuthorize("@ss.hasPermi('biz:partnerGroup:remove')")
    @Log(title = "合伙人群关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizPartnerGroupService.deleteBizPartnerGroupByIds(ids));
    }
}
