package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.StatUserGroup;
import com.ruoyi.biz.service.IStatUserGroupService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 用户群体Controller
 *
 * <AUTHOR>
 * @date 2024-04-21
 */
@RestController
@RequestMapping("/biz/userGroup")
public class StatUserGroupController extends BaseController
{
    @Resource
    private IStatUserGroupService statUserGroupService;

    /**
     * 查询用户群体列表
     */
//    //@PreAuthorize("@ss.hasPermi('biz:userGroup:list')")
    @GetMapping("/list")
    public TableDataInfo list(StatUserGroup statUserGroup)
    {
        startPage();
        List<StatUserGroup> list = statUserGroupService.selectStatUserGroupList(statUserGroup);
        return getDataTable(list);
    }

    /**
     * 导出用户群体列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:userGroup:export')")
    @Log(title = "用户群体", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StatUserGroup statUserGroup)
    {
        List<StatUserGroup> list = statUserGroupService.selectStatUserGroupList(statUserGroup);
        ExcelUtil<StatUserGroup> util = new ExcelUtil<StatUserGroup>(StatUserGroup.class);
        util.exportExcel(response, list, "用户群体数据");
    }

    /**
     * 获取用户群体详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:userGroup:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(statUserGroupService.selectStatUserGroupById(id));
    }

    /**
     * 新增用户群体
     */
    //@PreAuthorize("@ss.hasPermi('biz:userGroup:add')")
    @Log(title = "用户群体", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StatUserGroup statUserGroup)
    {
        return toAjax(statUserGroupService.insertStatUserGroup(statUserGroup));
    }

    /**
     * 修改用户群体
     */
    //@PreAuthorize("@ss.hasPermi('biz:userGroup:edit')")
    @Log(title = "用户群体", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StatUserGroup statUserGroup)
    {
        return toAjax(statUserGroupService.updateStatUserGroup(statUserGroup));
    }

    /**
     * 删除用户群体
     */
    //@PreAuthorize("@ss.hasPermi('biz:userGroup:remove')")
    @Log(title = "用户群体", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(statUserGroupService.deleteStatUserGroupByIds(ids));
    }
}
