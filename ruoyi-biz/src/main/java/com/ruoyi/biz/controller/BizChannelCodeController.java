package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;

import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizChannelCode;
import com.ruoyi.biz.service.IBizChannelCodeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 渠道活码Controller
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@RestController
@RequestMapping("/biz/channel")
public class BizChannelCodeController extends BaseController
{
    @Resource
    private IBizChannelCodeService bizChannelCodeService;

    /**
     * 查询渠道活码列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:channel:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizChannelCode bizChannelCode)
    {
        startPage();
        List<BizChannelCode> list = bizChannelCodeService.selectBizChannelCodeList(bizChannelCode);
        return getDataTable(list);
    }

    /**
     * 导出渠道活码列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:channel:export')")
    @Log(title = "渠道活码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizChannelCode bizChannelCode)
    {
        List<BizChannelCode> list = bizChannelCodeService.selectBizChannelCodeList(bizChannelCode);
        ExcelUtil<BizChannelCode> util = new ExcelUtil<BizChannelCode>(BizChannelCode.class);
        util.exportExcel(response, list, "渠道活码数据");
    }

    /**
     * 获取渠道活码详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:channel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizChannelCodeService.selectBizChannelCodeById(id));
    }

    /**
     * 新增渠道活码
     */
    //@PreAuthorize("@ss.hasPermi('biz:channel:add')")
    // @Log(title = "渠道活码", businessType = BusinessType.INSERT)
    // @PostMapping
    // public AjaxResult add(@RequestBody BizChannelCode bizChannelCode)
    // {
    //     return toAjax(bizChannelCodeService.insertBizChannelCode(bizChannelCode));
    // }


    /**
     * 修改渠道活码
     */
    //@PreAuthorize("@ss.hasPermi('biz:channel:edit')")
    @Log(title = "渠道活码", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizChannelCode bizChannelCode)
    {
        return toAjax(bizChannelCodeService.updateBizChannelCode(bizChannelCode));
    }

    /**
     * 删除渠道活码
     */
    //@PreAuthorize("@ss.hasPermi('biz:channel:remove')")
    @Log(title = "渠道活码", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizChannelCodeService.deleteBizChannelCodeByIds(ids));
    }


    /**
     * 获取授权链接
     *
     * @return
     */
    @Anonymous
    @GetMapping(value = "/wx/{url}")
    public AjaxResult getAuthorizeUrlInfo(@PathVariable("url") String url, @RequestParam(value = "channel", required = false) String channel) {
        return success(bizChannelCodeService.getAuthorizeUrlInfo(url, channel));
    }

}
