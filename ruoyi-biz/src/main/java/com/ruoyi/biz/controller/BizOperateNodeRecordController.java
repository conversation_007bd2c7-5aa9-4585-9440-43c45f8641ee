package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.annotation.Resource;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizOperateNodeRecord;
import com.ruoyi.biz.service.IBizOperateNodeRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 节点执行记录Controller
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@RestController
@RequestMapping("/node/record")
public class BizOperateNodeRecordController extends BaseController
{
    @Resource
    private IBizOperateNodeRecordService bizOperateNodeRecordService;

    /**
     * 查询节点执行记录列表
     */
    //@PreAuthorize("@ss.hasPermi('node:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizOperateNodeRecord bizOperateNodeRecord)
    {
        startPage();
        List<BizOperateNodeRecord> list = bizOperateNodeRecordService.selectBizOperateNodeRecordList(bizOperateNodeRecord);
        return getDataTable(list);
    }

    /**
     * 导出节点执行记录列表
     */
    //@PreAuthorize("@ss.hasPermi('node:record:export')")
    @Log(title = "节点执行记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizOperateNodeRecord bizOperateNodeRecord)
    {
        List<BizOperateNodeRecord> list = bizOperateNodeRecordService.selectBizOperateNodeRecordList(bizOperateNodeRecord);
        ExcelUtil<BizOperateNodeRecord> util = new ExcelUtil<BizOperateNodeRecord>(BizOperateNodeRecord.class);
        util.exportExcel(response, list, "节点执行记录数据");
    }

    /**
     * 获取节点执行记录详细信息
     */
    //@PreAuthorize("@ss.hasPermi('node:record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizOperateNodeRecordService.selectBizOperateNodeRecordById(id));
    }

    /**
     * 新增节点执行记录
     */
    //@PreAuthorize("@ss.hasPermi('node:record:add')")
    @Log(title = "节点执行记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizOperateNodeRecord bizOperateNodeRecord)
    {
        return toAjax(bizOperateNodeRecordService.insertBizOperateNodeRecord(bizOperateNodeRecord));
    }

    /**
     * 修改节点执行记录
     */
    //@PreAuthorize("@ss.hasPermi('node:record:edit')")
    @Log(title = "节点执行记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizOperateNodeRecord bizOperateNodeRecord)
    {
        return toAjax(bizOperateNodeRecordService.updateBizOperateNodeRecord(bizOperateNodeRecord));
    }

    /**
     * 删除节点执行记录
     */
    //@PreAuthorize("@ss.hasPermi('node:record:remove')")
    @Log(title = "节点执行记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizOperateNodeRecordService.deleteBizOperateNodeRecordByIds(ids));
    }

    /**
     * 根据用户ID 获取运营记录
     * @param id 用户ID
     * @return 运营记录
     */
    @GetMapping("/getListByUserId/{id}")
    public TableDataInfo getListByUserId(@PathVariable("id") Long id)
    {
        startPage();
        List<BizOperateNodeRecord> list = bizOperateNodeRecordService.getListByUserId(id);
        return getDataTable(list);
    }

    /**
     * 获取当前用户的运营记录
     * @param id 用户ID
     * @return 运营记录
     */
    @GetMapping("/getRecordByUserId/{id}")
    public AjaxResult getRecordByUserId(@PathVariable("id") Long id) {
        return success(bizOperateNodeRecordService.getRecordByUserId(id));
    }



    @Anonymous
    @GetMapping("/get/{chatId}")
    public AjaxResult getRecordByChatId(@PathVariable String chatId)
    {
        return success(bizOperateNodeRecordService.getRecordByChatId(chatId));
    }


}
