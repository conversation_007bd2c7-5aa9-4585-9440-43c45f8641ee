package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.biz.domain.BizOperateNode;
import com.ruoyi.biz.domain.dto.BizOperatePlanDto;
import com.ruoyi.biz.domain.dto.BizOperatePlanInsertCheckCountDto;
import com.ruoyi.common.utils.bean.EntityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizOperatePlan;
import com.ruoyi.biz.service.IBizOperatePlanService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 运营计划Controller
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@RestController
@RequestMapping("/operate/plan")
public class BizOperatePlanController extends BaseController
{
    @Resource
    private IBizOperatePlanService bizOperatePlanService;

    /**
     * 查询运营计划列表
     */
    //@PreAuthorize("@ss.hasPermi('operate:plan:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizOperatePlan bizOperatePlan)
    {
        startPage();
        List<BizOperatePlan> list = bizOperatePlanService.selectBizOperatePlanList(bizOperatePlan);
        return getDataTable(list);
    }

    /**
     * 导出运营计划列表
     */
    //@PreAuthorize("@ss.hasPermi('operate:plan:export')")
    @Log(title = "运营计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizOperatePlan bizOperatePlan)
    {
        List<BizOperatePlan> list = bizOperatePlanService.selectBizOperatePlanList(bizOperatePlan);
        ExcelUtil<BizOperatePlan> util = new ExcelUtil<BizOperatePlan>(BizOperatePlan.class);
        util.exportExcel(response, list, "运营计划数据");
    }

    /**
     * 获取运营计划详细信息
     */
    //@PreAuthorize("@ss.hasPermi('operate:plan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizOperatePlanService.selectBizOperatePlanById(id));
    }

    /**
     * 新增运营计划
     */
    //@PreAuthorize("@ss.hasPermi('operate:plan:add')")
    @Log(title = "运营计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody @Valid BizOperatePlanDto bizOperatePlanReq)
    {
        BizOperatePlan plan = EntityUtils.copyData(bizOperatePlanReq,BizOperatePlan.class);
        plan.setContent(JSON.toJSONString(bizOperatePlanReq.getContent()));

        return toAjax(bizOperatePlanService.insertBizOperatePlan(plan));
    }


    /**
     * 新增运营计划
     */
    //@PreAuthorize("@ss.hasPermi('operate:plan:add')")
    @Log(title = "运营计划预计群体数", businessType = BusinessType.OTHER)
    @PostMapping("/checkCount")
    public AjaxResult checkCount(@RequestBody @Valid BizOperatePlanInsertCheckCountDto bizOperatePlanReq)
    {
        return success(bizOperatePlanService.checkCount(bizOperatePlanReq));
    }


    /**
     * 修改运营计划
     */
    //@PreAuthorize("@ss.hasPermi('operate:plan:edit')")
    @Log(title = "运营计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody  @Valid BizOperatePlanDto bizOperatePlanReq)
    {
        BizOperatePlan plan = EntityUtils.copyData(bizOperatePlanReq,BizOperatePlan.class);
        plan.setContent(JSON.toJSONString(bizOperatePlanReq.getContent()));
        return toAjax(bizOperatePlanService.updateBizOperatePlan(plan));
    }

    /**
     * 删除运营计划
     */
    //@PreAuthorize("@ss.hasPermi('operate:plan:remove')")
    @Log(title = "运营计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizOperatePlanService.deleteBizOperatePlanByIds(ids));
    }


    /**
     * 停止运营计划
     */
    //@PreAuthorize("@ss.hasPermi('operate:plan:remove')")
    @Log(title = "停止运营计划", businessType = BusinessType.UPDATE)
    @PostMapping("/stop/{id}")
    public AjaxResult stop(@PathVariable Long id)
    {
        return toAjax(bizOperatePlanService.stopPlanByIds(id));
    }

    /**
     * 设置为默认运营计划
     */
    //@PreAuthorize("@ss.hasPermi('operate:plan:remove')")
    @Log(title = "设置为默认运营计划", businessType = BusinessType.UPDATE)
    @PostMapping("/default/{id}")
    public AjaxResult defaultPlan(@PathVariable Long id)
    {
        return toAjax(bizOperatePlanService.defaultPlanByIds(id));
    }

    /**
     * 获取运营计划实际执行情况
     */
    @GetMapping("/getExecuteInfo/{id}")
    public AjaxResult getExecuteInfo(@PathVariable("id") Long id) {
        return success(bizOperatePlanService.getExecuteInfoByPlanId(id));
    }

    /**
     * 获取多批次运营计划对应批次的执行情况
     * 根据planId以及批次数来查询
     */
    @GetMapping("/getSequentialBatchPlanExecuteInfo")
    public AjaxResult getSequentialBatchPlanExecuteInfo(@RequestParam("id") Long id, @RequestParam("num") int num) {
        return success(bizOperatePlanService.getSequentialBatchPlanExecuteInfo(id, num));
    }
}
