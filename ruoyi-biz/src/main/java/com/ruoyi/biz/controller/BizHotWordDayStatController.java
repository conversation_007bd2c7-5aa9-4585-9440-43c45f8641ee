package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.biz.domain.dto.BaseIdReq;
import com.ruoyi.biz.domain.dto.BaseNameReq;
import org.springframework.security.access.prepost.PreAuthorize;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizHotWordDayStat;
import com.ruoyi.biz.service.IBizHotWordDayStatService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 每日热词统计Controller
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
@RestController
@RequestMapping("/biz/stat")
public class BizHotWordDayStatController extends BaseController {
    @Resource
    private IBizHotWordDayStatService bizHotWordDayStatService;

    /**
     * 查询每日热词统计列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:stat:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizHotWordDayStat bizHotWordDayStat) {
        startPage();
        List<BizHotWordDayStat> list = bizHotWordDayStatService.selectBizHotWordDayStatList(bizHotWordDayStat);
        return getDataTable(list);
    }

    /**
     * 导出每日热词统计列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:stat:export')")
    @Log(title = "每日热词统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizHotWordDayStat bizHotWordDayStat) {
        List<BizHotWordDayStat> list = bizHotWordDayStatService.selectBizHotWordDayStatList(bizHotWordDayStat);
        ExcelUtil<BizHotWordDayStat> util = new ExcelUtil<BizHotWordDayStat>(BizHotWordDayStat.class);
        util.exportExcel(response, list, "每日热词统计数据");
    }

    /**
     * 获取每日热词统计详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:stat:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(bizHotWordDayStatService.selectBizHotWordDayStatById(id));
    }

    /**
     * 新增每日热词统计
     */
    //@PreAuthorize("@ss.hasPermi('biz:stat:add')")
    @Log(title = "每日热词统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizHotWordDayStat bizHotWordDayStat) {
        return toAjax(bizHotWordDayStatService.insertBizHotWordDayStat(bizHotWordDayStat));
    }

    /**
     * 修改每日热词统计
     */
    //@PreAuthorize("@ss.hasPermi('biz:stat:edit')")
    @Log(title = "每日热词统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizHotWordDayStat bizHotWordDayStat) {
        return toAjax(bizHotWordDayStatService.updateBizHotWordDayStat(bizHotWordDayStat));
    }

    /**
     * 删除每日热词统计
     */
    //@PreAuthorize("@ss.hasPermi('biz:stat:remove')")
    @Log(title = "每日热词统计", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(bizHotWordDayStatService.deleteBizHotWordDayStatByIds(ids));
    }


    /**
     * 热词AI分析
     * @return boolean
     */
    @PostMapping("/businessWordAnalysis")
    //@PreAuthorize("@ss.hasPermi('biz:stat:edit')")
    @Log(title = "热词分析", businessType = BusinessType.UPDATE)
    public AjaxResult businessWordAnalysis(@RequestBody BaseIdReq baseIdReq) {
        return success(bizHotWordDayStatService.businessWordAnalysis(baseIdReq.getId()));
    }


    /**
     * 加入热词库
     * @param baseNameReq
     * @return boolean
     */
    @PostMapping("/joinHotWordDB")
    //@PreAuthorize("@ss.hasPermi('biz:stat:edit')")
    @Log(title = "同步到热词库", businessType = BusinessType.UPDATE)
    public AjaxResult synchronizeOrNot(@RequestBody BaseNameReq baseNameReq) {
        return toAjax(bizHotWordDayStatService.joinHotWordDB(baseNameReq.getName()));
    }
}
