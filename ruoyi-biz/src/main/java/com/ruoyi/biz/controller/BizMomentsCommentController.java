package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizMomentsComment;
import com.ruoyi.biz.service.IBizMomentsCommentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 朋友圈评论点赞Controller
 *
 * <AUTHOR>
 * @date 2024-03-26
 */
@RestController
@RequestMapping("/biz/comment")
public class BizMomentsCommentController extends BaseController
{
    @Resource
    private IBizMomentsCommentService bizMomentsCommentService;

    /**
     * 查询朋友圈评论点赞列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:comment:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizMomentsComment bizMomentsComment)
    {
        startPage();
        List<BizMomentsComment> list = bizMomentsCommentService.selectBizMomentsCommentList(bizMomentsComment);
        return getDataTable(list);
    }

    /**
     * 导出朋友圈评论点赞列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:comment:export')")
    @Log(title = "朋友圈评论点赞", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizMomentsComment bizMomentsComment)
    {
        List<BizMomentsComment> list = bizMomentsCommentService.selectBizMomentsCommentList(bizMomentsComment);
        ExcelUtil<BizMomentsComment> util = new ExcelUtil<BizMomentsComment>(BizMomentsComment.class);
        util.exportExcel(response, list, "朋友圈评论点赞数据");
    }

    /**
     * 获取朋友圈评论点赞详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:comment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizMomentsCommentService.selectBizMomentsCommentById(id));
    }

    /**
     * 新增朋友圈评论点赞
     */
    //@PreAuthorize("@ss.hasPermi('biz:comment:add')")
    @Log(title = "朋友圈评论点赞", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizMomentsComment bizMomentsComment)
    {
        return toAjax(bizMomentsCommentService.insertBizMomentsComment(bizMomentsComment));
    }

    /**
     * 修改朋友圈评论点赞
     */
    //@PreAuthorize("@ss.hasPermi('biz:comment:edit')")
    @Log(title = "朋友圈评论点赞", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizMomentsComment bizMomentsComment)
    {
        return toAjax(bizMomentsCommentService.updateBizMomentsComment(bizMomentsComment));
    }

    /**
     * 删除朋友圈评论点赞
     */
    //@PreAuthorize("@ss.hasPermi('biz:comment:remove')")
    @Log(title = "朋友圈评论点赞", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizMomentsCommentService.deleteBizMomentsCommentByIds(ids));
    }
}
