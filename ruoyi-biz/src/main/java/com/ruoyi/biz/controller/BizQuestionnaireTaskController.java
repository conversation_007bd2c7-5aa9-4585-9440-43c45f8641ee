package com.ruoyi.biz.controller;

import java.io.InputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.util.List;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.StringUtils;
import org.apache.commons.io.IOUtils;
import org.hibernate.validator.internal.util.StringHelper;
import org.springframework.security.access.prepost.PreAuthorize;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizQuestionnaireTask;
import com.ruoyi.biz.service.IBizQuestionnaireTaskService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 问卷调查任务Controller
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@RestController
@RequestMapping("/biz/questionnaireTask")
public class BizQuestionnaireTaskController extends BaseController {
    @Resource
    private IBizQuestionnaireTaskService bizQuestionnaireTaskService;

    /**
     * 查询问卷调查任务列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:questionnaireTask:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizQuestionnaireTask bizQuestionnaireTask) {
        startPage();
        List<BizQuestionnaireTask> list = bizQuestionnaireTaskService.selectBizQuestionnaireTaskList(bizQuestionnaireTask);
        return getDataTable(list);
    }

    /**
     * 导出问卷调查任务列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:questionnaireTask:export')")
    @Log(title = "问卷调查任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizQuestionnaireTask bizQuestionnaireTask) {
        List<BizQuestionnaireTask> list = bizQuestionnaireTaskService.selectBizQuestionnaireTaskList(bizQuestionnaireTask);
        ExcelUtil<BizQuestionnaireTask> util = new ExcelUtil<BizQuestionnaireTask>(BizQuestionnaireTask.class);
        util.exportExcel(response, list, "问卷调查任务数据");
    }

    /**
     * 导出问卷调查任务列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:questionnaireTask:export')")
//    @Log(title = "问卷调查任务", businessType = BusinessType.EXPORT)
    @PostMapping("/down")
    public void down(HttpServletResponse response, BizQuestionnaireTask bizQuestionnaireTask) {
        List<BizQuestionnaireTask> list = bizQuestionnaireTaskService.selectBizQuestionnaireTaskList(bizQuestionnaireTask);
        if (list.isEmpty()) {
            throw new RuntimeException("任务不存在");
        }
        BizQuestionnaireTask task = list.get(0);
        if (StringUtils.isNotEmpty(task.getNewFile())) {
            try (InputStream inputStream = new URL(task.getNewFile()).openStream();
                 ServletOutputStream outputStream = response.getOutputStream()) {

                // 设置响应头信息
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(task.getNewFile().substring(task.getNewFile().lastIndexOf("/") + 1), "UTF-8"));

                // 将文件流写入响应
                IOUtils.copy(inputStream, outputStream);
                outputStream.flush();

            } catch (Exception e) {
                throw new RuntimeException("文件下载失败");
            }
        } else {
            throw new RuntimeException("文件不存在");
        }
    }

    /**
     * 获取问卷调查任务详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:questionnaireTask:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(bizQuestionnaireTaskService.selectBizQuestionnaireTaskById(id));
    }

    /**
     * 新增问卷调查任务
     */
    //@PreAuthorize("@ss.hasPermi('biz:questionnaireTask:add')")
    @Log(title = "问卷调查任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizQuestionnaireTask bizQuestionnaireTask) {
        return toAjax(bizQuestionnaireTaskService.insertBizQuestionnaireTask(bizQuestionnaireTask));
    }

    /**
     * 修改问卷调查任务
     */
    //@PreAuthorize("@ss.hasPermi('biz:questionnaireTask:edit')")
    @Log(title = "问卷调查任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizQuestionnaireTask bizQuestionnaireTask) {
        return toAjax(bizQuestionnaireTaskService.updateBizQuestionnaireTask(bizQuestionnaireTask));
    }

    /**
     * 删除问卷调查任务
     */
    //@PreAuthorize("@ss.hasPermi('biz:questionnaireTask:remove')")
    @Log(title = "问卷调查任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(bizQuestionnaireTaskService.deleteBizQuestionnaireTaskByIds(ids));
    }


}
