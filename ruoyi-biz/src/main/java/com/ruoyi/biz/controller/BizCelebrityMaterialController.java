package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizCelebrityMaterial;
import com.ruoyi.biz.service.IBizCelebrityMaterialService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 明星素材Controller
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@RestController
@RequestMapping("/biz/material")
public class BizCelebrityMaterialController extends BaseController
{
    @Resource
    private IBizCelebrityMaterialService bizCelebrityMaterialService;

    /**
     * 查询明星素材列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:material:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizCelebrityMaterial bizCelebrityMaterial)
    {
        startPage();
        List<BizCelebrityMaterial> list = bizCelebrityMaterialService.selectBizCelebrityMaterialList(bizCelebrityMaterial);
        return getDataTable(list);
    }

    /**
     * 导出明星素材列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:material:export')")
    @Log(title = "明星素材", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizCelebrityMaterial bizCelebrityMaterial)
    {
        List<BizCelebrityMaterial> list = bizCelebrityMaterialService.selectBizCelebrityMaterialList(bizCelebrityMaterial);
        ExcelUtil<BizCelebrityMaterial> util = new ExcelUtil<BizCelebrityMaterial>(BizCelebrityMaterial.class);
        util.exportExcel(response, list, "明星素材数据");
    }

    /**
     * 获取明星素材详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:material:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizCelebrityMaterialService.selectBizCelebrityMaterialById(id));
    }

    /**
     * 新增明星素材
     */
    //@PreAuthorize("@ss.hasPermi('biz:material:add')")
    @Log(title = "明星素材", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizCelebrityMaterial bizCelebrityMaterial)
    {
        return toAjax(bizCelebrityMaterialService.insertBizCelebrityMaterial(bizCelebrityMaterial));
    }

    /**
     * 修改明星素材
     */
    //@PreAuthorize("@ss.hasPermi('biz:material:edit')")
    @Log(title = "明星素材", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizCelebrityMaterial bizCelebrityMaterial)
    {
        return toAjax(bizCelebrityMaterialService.updateBizCelebrityMaterial(bizCelebrityMaterial));
    }

    /**
     * 删除明星素材
     */
    //@PreAuthorize("@ss.hasPermi('biz:material:remove')")
    @Log(title = "明星素材", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizCelebrityMaterialService.deleteBizCelebrityMaterialByIds(ids));
    }

    @Log(title = "明星素材", businessType = BusinessType.IMPORT)
    //@PreAuthorize("@ss.hasPermi('system:user:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BizCelebrityMaterial> util = new ExcelUtil<>(BizCelebrityMaterial.class);
        List<BizCelebrityMaterial> materialList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = bizCelebrityMaterialService.importMaterials(materialList, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<BizCelebrityMaterial> util = new ExcelUtil<>(BizCelebrityMaterial.class);
        util.importTemplateExcel(response, "素材数据");
    }


}
