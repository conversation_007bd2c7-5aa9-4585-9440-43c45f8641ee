package com.ruoyi.biz.controller;

import com.ruoyi.biz.domain.BizCollectProduct;
import com.ruoyi.biz.domain.req.ClipByWebReq;
import com.ruoyi.biz.service.IBizCollectProductService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.goods.domain.req.ProductDetailCleanReq;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 商品采集数据Controller
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@RestController
@RequestMapping("/biz/product_clip")
public class BizCollectProductController extends BaseController {
    @Resource
    private IBizCollectProductService bizCollectProductService;

    /**
     * 查询商品采集数据列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:product_clip:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizCollectProduct bizCollectProduct) {
        startPage();
        List<BizCollectProduct> list = bizCollectProductService.selectBizCollectProductList(bizCollectProduct);
        return getDataTable(list);
    }

    /**
     * 导出商品采集数据列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:product_clip:export')")
    @Log(title = "商品采集数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizCollectProduct bizCollectProduct) {
        List<BizCollectProduct> list = bizCollectProductService.selectBizCollectProductList(bizCollectProduct);
        ExcelUtil<BizCollectProduct> util = new ExcelUtil<BizCollectProduct>(BizCollectProduct.class);
        util.exportExcel(response, list, "商品采集数据数据");
    }

    /**
     * 获取商品采集数据详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:product_clip:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(bizCollectProductService.selectBizCollectProductById(id));
    }

    /**
     * 新增商品采集数据
     */
    //@PreAuthorize("@ss.hasPermi('biz:product_clip:add')")
    @Log(title = "商品采集数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizCollectProduct bizCollectProduct) {
        return toAjax(bizCollectProductService.insertBizCollectProduct(bizCollectProduct));
    }

    /**
     * 修改商品采集数据
     */
    //@PreAuthorize("@ss.hasPermi('biz:product_clip:edit')")
    @Log(title = "商品采集数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizCollectProduct bizCollectProduct) {
        return toAjax(bizCollectProductService.updateBizCollectProduct(bizCollectProduct));
    }

    /**
     * 删除商品采集数据
     */
    //@PreAuthorize("@ss.hasPermi('biz:product_clip:remove')")
    @Log(title = "商品采集数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(bizCollectProductService.deleteBizCollectProductByIds(ids));
    }

    /**
     * 新增商品采集数据
     */
    @PostMapping("/web")
    @Anonymous
    public AjaxResult web(@RequestBody ClipByWebReq clipByWebReq) {
        return toAjax(bizCollectProductService.clipByWeb(clipByWebReq));
    }

    /**
     * 商品数据清洗
     */
    @PostMapping("/detail/clean")
    public AjaxResult productDetailClean(@RequestBody ProductDetailCleanReq req) {
        return AjaxResult.success(bizCollectProductService.productDetailClean(req));
    }

    /**
     * 商品数据清洗
     */
    @PutMapping("isExample")
    public AjaxResult isExample(@RequestBody BizCollectProduct bizCollectProduct) {
        return toAjax(bizCollectProductService.updateIsExample(bizCollectProduct));
    }
}
