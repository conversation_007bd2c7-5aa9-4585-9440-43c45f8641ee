package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.biz.domain.req.JzMessageShareReq;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.utils.uuid.IdUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.JzMessageShare;
import com.ruoyi.biz.service.IJzMessageShareService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 会话分享记录Controller
 *
 * <AUTHOR>
 * @date 2024-10-13
 */
@RestController
@RequestMapping("/biz/share")
public class JzMessageShareController extends BaseController
{
    @Resource
    private IJzMessageShareService jzMessageShareService;

    /**
     * 查询会话分享记录列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:share:list')")
    @GetMapping("/list")
    public TableDataInfo list(JzMessageShare jzMessageShare)
    {
        startPage();
        List<JzMessageShare> list = jzMessageShareService.selectJzMessageShareList(jzMessageShare);
        return getDataTable(list);
    }

    /**
     * 导出会话分享记录列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:share:export')")
    @Log(title = "会话分享记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JzMessageShare jzMessageShare)
    {
        List<JzMessageShare> list = jzMessageShareService.selectJzMessageShareList(jzMessageShare);
        ExcelUtil<JzMessageShare> util = new ExcelUtil<JzMessageShare>(JzMessageShare.class);
        util.exportExcel(response, list, "会话分享记录数据");
    }

    /**
     * 获取会话分享记录详细信息
     */
    @Anonymous
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(jzMessageShareService.selectJzMessageShareById(id));
    }

    /**
     * 新增会话分享记录
     */
    //@PreAuthorize("@ss.hasPermi('biz:share:add')")
    @Log(title = "会话分享记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody JzMessageShareReq jzMessageShareReq)
    {

        JzMessageShare jzMessageShare = new JzMessageShare();
        jzMessageShare.setCreater(getUsername());
        jzMessageShare.setChatId(jzMessageShareReq.getChatId());
        jzMessageShare.setMessageIdList(jzMessageShareReq.getMessageIdList());
        jzMessageShare.setId(IdUtils.fastSimpleUUID());
        jzMessageShareService.insertJzMessageShare(jzMessageShare);
        return AjaxResult.success("成功",jzMessageShare.getId());
    }

    /**
     * 修改会话分享记录
     */
    //@PreAuthorize("@ss.hasPermi('biz:share:edit')")
    @Log(title = "会话分享记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JzMessageShare jzMessageShare)
    {
        return toAjax(jzMessageShareService.updateJzMessageShare(jzMessageShare));
    }

    /**
     * 删除会话分享记录
     */
    //@PreAuthorize("@ss.hasPermi('biz:share:remove')")
    @Log(title = "会话分享记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(jzMessageShareService.deleteJzMessageShareByIds(ids));
    }
}
