package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizSalesPitch;
import com.ruoyi.biz.service.IBizSalesPitchService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 话术Controller
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@RestController
@RequestMapping("/biz/salespitch")
public class BizSalesPitchController extends BaseController
{
    @Resource
    private IBizSalesPitchService bizSalesPitchService;

    /**
     * 查询话术列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:salespitch:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizSalesPitch bizSalesPitch)
    {
        startPage();
        List<BizSalesPitch> list = bizSalesPitchService.selectBizSalesPitchList(bizSalesPitch);
        return getDataTable(list);
    }

    /**
     * 导出话术列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:salespitch:export')")
    @Log(title = "话术", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizSalesPitch bizSalesPitch)
    {
        List<BizSalesPitch> list = bizSalesPitchService.selectBizSalesPitchList(bizSalesPitch);
        ExcelUtil<BizSalesPitch> util = new ExcelUtil<BizSalesPitch>(BizSalesPitch.class);
        util.exportExcel(response, list, "话术数据");
    }

    /**
     * 获取话术详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:salespitch:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizSalesPitchService.selectBizSalesPitchById(id));
    }

    /**
     * 新增话术
     */
    //@PreAuthorize("@ss.hasPermi('biz:salespitch:add')")
    @Log(title = "话术", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizSalesPitch bizSalesPitch)
    {
        return toAjax(bizSalesPitchService.insertBizSalesPitch(bizSalesPitch));
    }

    /**
     * 修改话术
     */
    //@PreAuthorize("@ss.hasPermi('biz:salespitch:edit')")
    @Log(title = "话术", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizSalesPitch bizSalesPitch)
    {
        return toAjax(bizSalesPitchService.updateBizSalesPitch(bizSalesPitch));
    }

    /**
     * 删除话术
     */
    //@PreAuthorize("@ss.hasPermi('biz:salespitch:remove')")
    @Log(title = "话术", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizSalesPitchService.deleteBizSalesPitchByIds(ids));
    }

    /**
     * 记录话术复制次数
     */
    @PostMapping("/copy")
    public AjaxResult recordCopyCount(@RequestBody BizSalesPitch bizSalesPitch)
    {
        return toAjax(bizSalesPitchService.recordCopyCount(bizSalesPitch.getId()));
    }

    /**
     * 更新点赞/点踩状态
     */
    @PostMapping("/like")
    public AjaxResult updateLikeStatus(@RequestBody BizSalesPitch bizSalesPitch)
    {
        return toAjax(bizSalesPitchService.updateLikeStatus(bizSalesPitch.getId(), bizSalesPitch.getLikeStatus()));
    }
}
