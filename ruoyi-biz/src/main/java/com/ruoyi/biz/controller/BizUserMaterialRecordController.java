package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.annotation.Resource;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizUserMaterialRecord;
import com.ruoyi.biz.service.IBizUserMaterialRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 用户素材记录Controller
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/biz/user_material")
@Anonymous
public class BizUserMaterialRecordController extends BaseController
{
    @Resource
    private IBizUserMaterialRecordService bizUserMaterialRecordService;

    /**
     * 查询用户素材记录列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:user_material:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizUserMaterialRecord bizUserMaterialRecord)
    {
        startPage();
        List<BizUserMaterialRecord> list = bizUserMaterialRecordService.selectBizUserMaterialRecordList(bizUserMaterialRecord);
        return getDataTable(list);
    }

    /**
     * 导出用户素材记录列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:user_material:export')")
    @Log(title = "用户素材记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizUserMaterialRecord bizUserMaterialRecord)
    {
        List<BizUserMaterialRecord> list = bizUserMaterialRecordService.selectBizUserMaterialRecordList(bizUserMaterialRecord);
        ExcelUtil<BizUserMaterialRecord> util = new ExcelUtil<BizUserMaterialRecord>(BizUserMaterialRecord.class);
        util.exportExcel(response, list, "用户素材记录数据");
    }

    /**
     * 获取用户素材记录详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:user_material:query')")
    @GetMapping(value = "/{code}")
    public AjaxResult getInfo(@PathVariable("code") String code)
    {
        return success(bizUserMaterialRecordService.selectBizUserMaterialRecordByCode(code));
    }

    /**
     * 新增用户素材记录
     */
    //@PreAuthorize("@ss.hasPermi('biz:user_material:add')")
    @Log(title = "用户素材记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizUserMaterialRecord bizUserMaterialRecord)
    {
        return toAjax(bizUserMaterialRecordService.insertBizUserMaterialRecord(bizUserMaterialRecord));
    }

    /**
     * 修改用户素材记录
     */
    //@PreAuthorize("@ss.hasPermi('biz:user_material:edit')")
    @Log(title = "用户素材记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizUserMaterialRecord bizUserMaterialRecord)
    {
        return toAjax(bizUserMaterialRecordService.updateBizUserMaterialRecord(bizUserMaterialRecord));
    }

    /**
     * 删除用户素材记录
     */
    //@PreAuthorize("@ss.hasPermi('biz:user_material:remove')")
    @Log(title = "用户素材记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{codes}")
    public AjaxResult remove(@PathVariable String[] codes)
    {
        return toAjax(bizUserMaterialRecordService.deleteBizUserMaterialRecordByCodes(codes));
    }
}
