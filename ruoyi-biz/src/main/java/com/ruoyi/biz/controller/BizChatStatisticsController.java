package com.ruoyi.biz.controller;

import com.ruoyi.biz.domain.BizChatStatistics;
import com.ruoyi.biz.service.IBizChatStatisticsService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 聊天统计信息Controller
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@RestController
@RequestMapping("/biz/statistics")
public class BizChatStatisticsController extends BaseController {
    @Resource
    private IBizChatStatisticsService bizChatStatisticsService;

    /**
     * 查询聊天统计信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(BizChatStatistics bizChatStatistics) {
        startPage();
        List<BizChatStatistics> list = bizChatStatisticsService.selectBizChatStatisticsList(bizChatStatistics);
        return getDataTable(list);
    }

    /**
     * 导出聊天统计信息列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:statistics:export')")
    @Log(title = "聊天统计信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizChatStatistics bizChatStatistics) {
        List<BizChatStatistics> list = bizChatStatisticsService.selectBizChatStatisticsList(bizChatStatistics);
        ExcelUtil<BizChatStatistics> util = new ExcelUtil<BizChatStatistics>(BizChatStatistics.class);
        util.exportExcel(response, list, "聊天统计信息数据");
    }

    /**
     * 获取聊天统计信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(bizChatStatisticsService.selectBizChatStatisticsById(id));
    }

    /**
     * 新增聊天统计信息
     */
    @Log(title = "聊天统计信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizChatStatistics bizChatStatistics) {
        return toAjax(bizChatStatisticsService.insertBizChatStatistics(bizChatStatistics));
    }

    /**
     * 修改聊天统计信息
     */
    @Log(title = "聊天统计信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizChatStatistics bizChatStatistics) {
        return toAjax(bizChatStatisticsService.updateBizChatStatistics(bizChatStatistics));
    }

    /**
     * 删除聊天统计信息
     */
    @Log(title = "聊天统计信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(bizChatStatisticsService.deleteBizChatStatisticsByIds(ids));
    }
}
