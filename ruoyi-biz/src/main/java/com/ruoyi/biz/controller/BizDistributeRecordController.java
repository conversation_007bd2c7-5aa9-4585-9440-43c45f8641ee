package com.ruoyi.biz.controller;

import com.ruoyi.biz.domain.BizDistributeRecord;
import com.ruoyi.biz.service.IBizDistributeRecordService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 分销邀请记录Controller
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@RestController
@RequestMapping("/biz/distribute-record")
public class BizDistributeRecordController extends BaseController {
    @Resource
    private IBizDistributeRecordService bizDistributeRecordService;

    /**
     * 查询分销邀请记录列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:distribute-record:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizDistributeRecord bizDistributeRecord) {
        startPage();
        List<BizDistributeRecord> list = bizDistributeRecordService.selectBizDistributeRecordList(bizDistributeRecord);
        return getDataTable(list);
    }

    /**
     * 导出分销邀请记录列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:distribute-record:export')")
    @Log(title = "分销邀请记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizDistributeRecord bizDistributeRecord) {
        List<BizDistributeRecord> list = bizDistributeRecordService.selectBizDistributeRecordList(bizDistributeRecord);
        ExcelUtil<BizDistributeRecord> util = new ExcelUtil<BizDistributeRecord>(BizDistributeRecord.class);
        util.exportExcel(response, list, "分销邀请记录数据");
    }

    /**
     * 获取分销邀请记录详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:distribute-record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(bizDistributeRecordService.selectBizDistributeRecordById(id));
    }

    /**
     * 新增分销邀请记录
     */
    //@PreAuthorize("@ss.hasPermi('biz:distribute-record:add')")
    @Log(title = "分销邀请记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizDistributeRecord bizDistributeRecord) {
        return toAjax(bizDistributeRecordService.insertBizDistributeRecord(bizDistributeRecord));
    }

    /**
     * 修改分销邀请记录
     */
    //@PreAuthorize("@ss.hasPermi('biz:distribute-record:edit')")
    @Log(title = "分销邀请记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizDistributeRecord bizDistributeRecord) {
        return toAjax(bizDistributeRecordService.updateBizDistributeRecord(bizDistributeRecord));
    }

    /**
     * 删除分销邀请记录
     */
    //@PreAuthorize("@ss.hasPermi('biz:distribute-record:remove')")
    @Log(title = "分销邀请记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(bizDistributeRecordService.deleteBizDistributeRecordByIds(ids));
    }
}
