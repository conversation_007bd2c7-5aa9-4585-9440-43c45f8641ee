package com.ruoyi.biz.controller;

import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;

import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizChannelCodeRecord;
import com.ruoyi.biz.service.IBizChannelCodeRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 渠道活码授权记录Controller
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@RestController
@RequestMapping("/operate/channel_record")
public class BizChannelCodeRecordController extends BaseController
{
    @Resource
    private IBizChannelCodeRecordService bizChannelCodeRecordService;


    /**
     * 渠道活码公众号回调
     */
    @Anonymous
    @GetMapping(value = "/gzh_redirect")
    public AjaxResult redirect(HttpServletResponse response, @RequestParam(name = "code", defaultValue = "") String wxCode,
                               @RequestParam(name = "state", defaultValue = "") String channelCode,
                               @RequestParam(name = "unionId", defaultValue = "") String unionId) {
        // 开始记录回调逻辑
        return bizChannelCodeRecordService.createRecord(response, wxCode, channelCode, unionId);
    }


    /**
     * 查询渠道活码授权记录列表
     */
    //@PreAuthorize("@ss.hasPermi('operate:channel:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizChannelCodeRecord bizChannelCodeRecord)
    {
        startPage();
        List<BizChannelCodeRecord> list = bizChannelCodeRecordService.selectBizChannelCodeRecordList(bizChannelCodeRecord);
        return getDataTable(list);
    }

    /**
     * 导出渠道活码授权记录列表
     */
    //@PreAuthorize("@ss.hasPermi('operate:channel:export')")
    @Log(title = "渠道活码授权记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizChannelCodeRecord bizChannelCodeRecord)
    {
        List<BizChannelCodeRecord> list = bizChannelCodeRecordService.selectBizChannelCodeRecordList(bizChannelCodeRecord);
        ExcelUtil<BizChannelCodeRecord> util = new ExcelUtil<BizChannelCodeRecord>(BizChannelCodeRecord.class);
        util.exportExcel(response, list, "渠道活码授权记录数据");
    }

    /**
     * 获取渠道活码授权记录详细信息
     */
    //@PreAuthorize("@ss.hasPermi('operate:channel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizChannelCodeRecordService.selectBizChannelCodeRecordById(id));
    }

    /**
     * 新增渠道活码授权记录
     */
    //@PreAuthorize("@ss.hasPermi('operate:channel:add')")
    @Log(title = "渠道活码授权记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizChannelCodeRecord bizChannelCodeRecord)
    {
        return toAjax(bizChannelCodeRecordService.insertBizChannelCodeRecord(bizChannelCodeRecord));
    }

    /**
     * 修改渠道活码授权记录
     */
    //@PreAuthorize("@ss.hasPermi('operate:channel:edit')")
    @Log(title = "渠道活码授权记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizChannelCodeRecord bizChannelCodeRecord)
    {
        return toAjax(bizChannelCodeRecordService.updateBizChannelCodeRecord(bizChannelCodeRecord));
    }

    /**
     * 删除渠道活码授权记录
     */
    //@PreAuthorize("@ss.hasPermi('operate:channel:remove')")
    @Log(title = "渠道活码授权记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizChannelCodeRecordService.deleteBizChannelCodeRecordByIds(ids));
    }
}
