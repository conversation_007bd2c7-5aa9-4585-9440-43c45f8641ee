package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizOperatePlanExample;
import com.ruoyi.biz.service.IBizOperatePlanExampleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 运营计划范例Controller
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@RestController
@RequestMapping("/biz/operatePlanExample")
public class BizOperatePlanExampleController extends BaseController
{
    @Resource
    private IBizOperatePlanExampleService bizOperatePlanExampleService;

    /**
     * 查询运营计划范例列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:operatePlanExample:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizOperatePlanExample bizOperatePlanExample)
    {
        startPage();
        List<BizOperatePlanExample> list = bizOperatePlanExampleService.selectBizOperatePlanExampleList(bizOperatePlanExample);
        return getDataTable(list);
    }

    /**
     * 导出运营计划范例列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:operatePlanExample:export')")
    @Log(title = "运营计划范例", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizOperatePlanExample bizOperatePlanExample)
    {
        List<BizOperatePlanExample> list = bizOperatePlanExampleService.selectBizOperatePlanExampleList(bizOperatePlanExample);
        ExcelUtil<BizOperatePlanExample> util = new ExcelUtil<BizOperatePlanExample>(BizOperatePlanExample.class);
        util.exportExcel(response, list, "运营计划范例数据");
    }

    /**
     * 获取运营计划范例详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:operatePlanExample:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizOperatePlanExampleService.selectBizOperatePlanExampleById(id));
    }

    /**
     * 新增运营计划范例
     */
    //@PreAuthorize("@ss.hasPermi('biz:operatePlanExample:add')")
    @Log(title = "运营计划范例", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizOperatePlanExample bizOperatePlanExample)
    {
        return toAjax(bizOperatePlanExampleService.insertBizOperatePlanExample(bizOperatePlanExample));
    }

    /**
     * 修改运营计划范例
     */
    //@PreAuthorize("@ss.hasPermi('biz:operatePlanExample:edit')")
    @Log(title = "运营计划范例", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizOperatePlanExample bizOperatePlanExample)
    {
        return toAjax(bizOperatePlanExampleService.updateBizOperatePlanExample(bizOperatePlanExample));
    }

    /**
     * 删除运营计划范例
     */
    //@PreAuthorize("@ss.hasPermi('biz:operatePlanExample:remove')")
    @Log(title = "运营计划范例", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizOperatePlanExampleService.deleteBizOperatePlanExampleByIds(ids));
    }
}
