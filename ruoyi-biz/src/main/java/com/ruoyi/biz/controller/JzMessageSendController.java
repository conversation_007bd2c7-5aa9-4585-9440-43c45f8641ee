package com.ruoyi.biz.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.domain.BizCelebrityMaterial;
import com.ruoyi.biz.domain.BizWorkOrder;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.biz.enums.ETaggableRefType;
import com.ruoyi.biz.service.IBizCelebrityMaterialService;
import com.ruoyi.biz.service.IBizUserMaterialRecordService;
import com.ruoyi.biz.service.IBizWorkOrderService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.EJzRecviceMessageType;
import com.ruoyi.common.enums.EJzSendMessageType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.goods.domain.PmsSku;
import com.ruoyi.goods.mapper.PmsSkuMapper;
import com.ruoyi.goods.service.IPmsGoodsService;
import com.ruoyi.jzbot.config.JuZiConfig;
import com.ruoyi.jzbot.domain.JzContact;
import com.ruoyi.jzbot.domain.JzGroupChat;
import com.ruoyi.jzbot.domain.JzRoom;
import com.ruoyi.jzbot.domain.req.JzChatMessageReq;
import com.ruoyi.jzbot.domain.JzChatMessage;
import com.ruoyi.jzbot.mapper.JzChatMessageMapper;
import com.ruoyi.jzbot.mapper.JzContactMapper;
import com.ruoyi.jzbot.mapper.JzGroupChatMapper;
import com.ruoyi.jzbot.mapper.JzRoomMapper;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> silver
 * @since : 2020-04-13 14:40
 */
@Slf4j
@RestController
@RequestMapping("/message")
@Anonymous
public class JzMessageSendController extends BaseController {

    @Value("${spring.profiles.active}")
    private String profilesActive;

    @Resource
    private JuZiConfig juZiConfig;


    @Resource
    private PmsSkuMapper pmsSkuMapper;

    @Resource
    private IPmsGoodsService pmsGoodsService;

    @Resource
    private IBizCelebrityMaterialService bizCelebrityMaterialService;

    @Resource
    private IBizWorkOrderService workOrderService;

    @Resource
    private JzChatMessageMapper jzChatMessageMapper;

    @Resource
    private JzGroupChatMapper jzGroupChatMapper;

    @Resource
    private JzRoomMapper jzRoomMapper;

    @Resource
    private JzContactMapper jzContactMapper;

    @Resource
    private RedisCache redisCache;

    @Resource
    private IBizUserMaterialRecordService bizUserMaterialRecordService;

    @Value("${py.firstAiUrl}")
    private String firstAiUrl;


    @PostMapping(value = "/send")
    @ResponseBody
    public JSONObject alexReceive(@RequestBody JSONObject params) {
        log.info("Alex消息推送 params = {}", JSON.toJSONString(params));

        JSONObject result = new JSONObject();
        result.put("code", 0);
        result.put("data", new JSONObject());

        String username;
        try {
            username = getUsername();
            //后台处理消息中，3分钟内不进行回复
//            redisCache.setCacheObject(CacheConstants.TAKE_OVER_CHAT + params.getString("chatId"), "1", 3 * 60);
        } catch (Exception e) {
            username = "AI";
        }
        String fMessageId = params.getString("messageId");
        // todo 调用python放，新增消息历史
        String chatConfKey = "chat_config:".concat(params.getString("chatId"));
        JSONObject configMap = redisCache.getCacheObject(chatConfKey);
        Map<String, Object> addMessageData = new HashMap<>();
        addMessageData.put("userId", configMap.getString("wxid"));
        addMessageData.put("content", params.getJSONObject("payload").getString("text"));
        HttpUtils.post(firstAiUrl, addMessageData);

        params.put("userName", username);
        params.put("fMessageId", fMessageId);


        sendMessage(params);
        return result;
    }

    @ApiOperation(value = "获取AI消息统计")
    @PostMapping(value = "/message/stat")
    public Map<String, Object> aiStat() {
        String time = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.addDays(new Date(), -1));

        //https://open.feishu.cn/open-apis/bot/v2/hook/2908dbfb-e42c-4dbe-8f45-9ab1b3647541
        //总群数 ,开启 AI 数, 好友数,
        JzGroupChat condition = new JzGroupChat();
        condition.setDeleted(EBoolean.NO.getCode());
        List<JzGroupChat> groupList = jzGroupChatMapper.selectJzGroupChatList(condition);

        //room数
        JzRoom condition1 = new JzRoom();
        condition1.setDeleted(EBoolean.NO.getCode());
        List<JzRoom> roomList = jzRoomMapper.selectJzRoomList(condition1);

        //好友数
        List<JzContact> contactList = jzContactMapper.selectJzContactList(new JzContact() {{
            setDeleted(EBoolean.NO.getCode());
        }});

        Object ai_day_stat_total = redisCache.getCacheObject("ai_day_stat_total:".concat(time));
        Object ai_day_stat_sales = redisCache.getCacheObject("ai_day_stat_sales:".concat(time));
        Object overtime = redisCache.getCacheObject("ai_day_message_overtime:".concat(time));

        Map<String, Object> statsMap = new LinkedHashMap<>();
        statsMap.put("群组统计", null);
        statsMap.put("总群组数", groupList.size() + "群");
        statsMap.put("AI进群数量", roomList.size() + "群");
        statsMap.put("群开启AI数", roomList.stream().filter(x -> x.getStatus().equals(EBoolean.YES.getCode())).count() + "群");

        statsMap.put("好友统计", null);
        statsMap.put("好友总数", contactList.size() + "人");
        statsMap.put("好友开启AI数", contactList.stream().filter(x -> x.getStatus().equals(EBoolean.YES.getCode())).count() + "人");

        statsMap.put("昨日统计", null);
        statsMap.put("用户提问次数", (ai_day_stat_total == null ? 0 : ai_day_stat_total) + "次");
        statsMap.put("导购意图次数", (ai_day_stat_sales == null ? 0 : ai_day_stat_sales) + "次");
        statsMap.put("对话超时数", (overtime == null ? 0 : overtime) + "次");

        return statsMap;
    }

    @Async
    protected void sendMessage(JSONObject params) {
        try {
            JSONObject aiExpand = new JSONObject();
            if (params.containsKey("aiExpand")) {
                aiExpand = params.getJSONObject("aiExpand");
                log.info("商品信息：{}", aiExpand.toJSONString());
            } else {
                params.put("aiExpand", aiExpand);
            }
            try {
                String messageId = params.getString("messageId");
                if (StringUtils.isNotBlank(messageId)) {
                    Long oldTime = redisCache.getCacheObject(CacheConstants.MSG_MONITOR + messageId);
                    if (oldTime == null) {
                        oldTime = 0L;
                    }
                    aiExpand.put("useTime", System.currentTimeMillis() - oldTime);
                    aiExpand.put("status", System.currentTimeMillis() - oldTime > 4000 * 60 ? "2" : System.currentTimeMillis() - oldTime > 1000 * 60 ? "1" : "0");
                    aiExpand.put("intentType", params.getString("intentType"));
                    redisCache.deleteObject(CacheConstants.MSG_MONITOR + messageId);
                }
            } catch (Exception e) {
                log.error("消息发送 统计用时异常");
            }

            String key = "ai_day_stat_total:";
            String str = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, new Date());
            redisCache.incr(key.concat(str), 1);

            if ("shopping_advice".equals(params.getString("intentType"))) {
                redisCache.incr("ai_day_stat_sales:".concat(str), 1);
            } else if ("writer".equals(params.getString("intentType")) || "homework".equals(params.getString("intentType"))) {
                //写作做二次渲染
                bizUserMaterialRecordService.handleMessage(params);
                return;
            }

            //发送消息
            groupMessageAddQueue(params);

            if (aiExpand != null) {
                handAiExpand(aiExpand, params);
            }

            if (params.containsKey("videoId")) {
                log.info("快手视频信息：{}", params.getString("videoId"));
                Thread.sleep(1000);
                List<Long> videoIdList = params.getJSONArray("videoId").toList(Long.class);
                if (CollectionUtils.isNotEmpty(videoIdList)) {
                    for (Long videoId : videoIdList) {
                        BizCelebrityMaterial materia = bizCelebrityMaterialService
                                .selectBizCelebrityMaterialById(videoId);
                        if (!"video".equals(materia.getType())) {
                            continue;
                        }
                        sendTextInfo(params.getString("token"), materia != null ? materia.getUrl() : null, params.getString("chatId"), params.getString("fMessageId"));
                    }
                }
            }
        } catch (Exception e) {
            log.error("Alex消息推送结束异常", e);
            throw new ServiceException("发送失败" + e.getMessage());
        }
    }


    /**
     * 处理额外的消息
     *
     * @param aiExpand
     * @param params
     */
    private void handAiExpand(JSONObject aiExpand, JSONObject params) {
        String chatId = params.getString("chatId");
        String token = params.getString("token");

        // 判断是否工单，进行处理, ai传过来一定是私聊才回答不知道
        if (aiExpand.containsKey("ai_response") && (aiExpand.get("ai_response") instanceof Map)
                && "need_artificial".equals(aiExpand.getJSONObject("ai_response").getString("type"))) {
            JSONObject aiResponse = aiExpand.getJSONObject("ai_response");

            String messageId = params.getString("messageId");

            List<JzChatMessage> currentMessage = jzChatMessageMapper.selectJzChatMessageList(new JzChatMessageReq() {{
                setMessageId(messageId);
            }});
            Date currentMessageTime = CollectionUtils.isEmpty(currentMessage) ? new Date() : currentMessage.get(0).getTime();
            Date forwardMessageTime = DateUtils.addDays(currentMessageTime, 1);    // 暂定一天内的时间

            // 聊天上下文
            PageUtils.startPage(1, 5, "time desc");
            List<JzChatMessage> chatContextList = jzChatMessageMapper.selectJzChatMessageList(new JzChatMessageReq() {{
                setChatId(chatId);
                setMessageType(EJzRecviceMessageType.TEXT.getCode());
                setParams(new HashMap<String, Object>() {{
                    put("beginTime", forwardMessageTime);
                    put("endTime", currentMessageTime);
                }});
            }});

            String msgContext = chatContextList.stream().sorted(Comparator.comparing(JzChatMessage::getTime))
                    .map(x -> {
                        JSONObject jsonObject = JSONObject.parseObject(x.getMessage());
                        return x.getContactName() + ": " + jsonObject.getString("text");
                    }).collect(Collectors.joining("\r\n"));

            // 新增工单
            workOrderService.insertBizWorkOrder(new BizWorkOrder() {{
                setMessageId(messageId);
                setChatId(chatId);
                setContactId(aiResponse.getLong("contactId"));
                setMessageText(params.getJSONObject("payload").getString("text"));
                setDescription(String.format("用户问【%s】无法回答, 消息上下文%s", aiResponse.getString("user_query"), msgContext));
                setContactName(aiResponse.getString("contactName"));
                setExpandInfo(JSON.toJSONString(new HashMap<String, Object>() {{
                    put("userQuery", aiResponse.getString("user_query"));
                    put("starId", aiResponse.getString("starId"));
                    put("starName", aiResponse.getString("starName"));
                }}));
                setCreateBy(UserConstants.SYS_USER);
            }});
            return;
        }

        JSONObject urlObj = aiExpand.getJSONObject("url");
        if (null == urlObj) {
            return;
        }

        JSONArray categoryIds = urlObj.getJSONArray("categoryIds");
        JSONArray mallUrls = urlObj.getJSONArray("mallUrls");
        JSONArray skuIds = urlObj.getJSONArray("skuIds");
        JSONArray productIds = urlObj.getJSONArray("productIds");

        if (CollectionUtils.isEmpty(categoryIds) && CollectionUtils.isEmpty(mallUrls)
                && CollectionUtils.isEmpty(skuIds) && CollectionUtils.isEmpty(productIds)) {
            return;
        }

        //判断是否 SKU 存在
        if (CollectionUtils.isNotEmpty(skuIds)) {
            if (CollectionUtils.isEmpty(productIds)) {
                productIds = new JSONArray();
            }
            List<String> list = skuIds.toList(String.class);
            Set<Long> goodsIdSet = list.stream().map(id -> pmsSkuMapper.selectPmsSkuById(id)).map(PmsSku::getSpuId).collect(Collectors.toSet());
            productIds.addAll(goodsIdSet);
        }

        //发送商品信息
        sendGoodsInfo(token, productIds, chatId, params.getString("fMessageId"));
        //发送链接
        if (CollectionUtils.isNotEmpty(mallUrls)) {
            sendTextInfo(token, StringUtils.join(mallUrls, ","), chatId, params.getString("fMessageId"));
        }
    }


    public void sendTextInfo(String token, String textInfo, String chatId, String fMessageId) {
        if (StringUtils.isNotBlank(textInfo)) {

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("fMessageId", fMessageId);
            jsonObject.put("userName", "AI");
            jsonObject.put("token", token);
            jsonObject.put("chatId", chatId);
            jsonObject.put("externalRequestId", "mallUrl_" + IdUtils.generator());
            jsonObject.put("messageType", EJzSendMessageType.Text.getCode());
            JSONObject payload = new JSONObject();
            payload.put("text", textInfo);
            jsonObject.put("payload", payload);

            log.info("开始发送文本信息{}", jsonObject.toJSONString());
            groupMessageAddQueue(jsonObject);
//            JSONObject result = HttpUtils.postToJsonObject("https://aa-api.ddregion.com/message/send", jsonObject);
//            log.info("开始发送文本信息结束 返回 = {}", result.toJSONString());
        }
    }

    public void sendGoodsInfo(String token, JSONArray productIds, String chatId, String fMessageId) {
        if (CollectionUtils.isNotEmpty(productIds)) {
            Set<Long> set = new HashSet<>();
            for (int i = 0; i < productIds.size(); i++) {
                Long goodsId = Long.valueOf(productIds.getString(i));
                if (set.contains(goodsId)) {
                    continue;
                }
                set.add(goodsId);
                try {
                    JSONObject messageInfo = new JSONObject();
                    messageInfo.put("fMessageId", "fMessageId");
                    messageInfo.put("userName", "AI");

                    messageInfo.put("token", token);
                    messageInfo.put("chatId", chatId);
                    messageInfo.put("externalRequestId", "goods_" + IdUtils.generator());

                    JSONObject message = pmsGoodsService.getJzSendMessagePayload(goodsId);
                    message.remove("type");
                    messageInfo.putAll(message);

                    log.info("开始发送小程序商品{}", messageInfo.toJSONString());
//                    JSONObject result = HttpUtils.postToJsonObject("https://aa-api.ddregion.com/message/send", messageInfo);
                    groupMessageAddQueue(messageInfo);
//                    log.info("Alex消息推送商品结束 返回 = {}", result.toJSONString());

                } catch (Exception e) {
                    log.error("推送商品消息异常", e);
                }
            }
        }
    }


    /**
     * 消息转为队列发送
     *
     * @param params
     */
    public void groupMessageAddQueue(JSONObject params) {
        Integer messageType = params.getInteger("messageType");
        JSONObject payload = params.getJSONObject("payload");
        String quoteMessageId = params.getString("quoteMessageId");

        String fMessageId = params.getString("fMessageId");
        if (StringUtils.isBlank(fMessageId) && StringUtils.isNotBlank(quoteMessageId)) {
            fMessageId = quoteMessageId;
        }

        JSONObject json = new JSONObject();

        String externalRequestId = "boss:" + IdUtils.generator();
        json.put("externalRequestId", externalRequestId);

        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("userName", params.getString("userName"));
        hashMap.put("fMessageId", fMessageId);
        if (params.containsKey("aiExpand")) {
            hashMap.put("aiExtra", params.getJSONObject("aiExpand"));
        }
        redisCache.setCacheMap(externalRequestId, hashMap);


        String chatConfKey = "chat_config:".concat(params.getString("chatId"));
        JSONObject configMap = redisCache.getCacheObject(chatConfKey);
        if (configMap == null) {
            return;
        }
        json.put("imBotId", configMap.getString("botWxid"));
        if (ETaggableRefType.ROOM.getCode().equals(configMap.getString("type"))) {
            json.put("imRoomId", configMap.getString("wxid"));
        } else {
            json.put("imContactId", configMap.getString("wxid"));
        }

        if (EJzSendMessageType.Text.getCode() == messageType) {
            if (StringUtils.isNotBlank(quoteMessageId)) {
                payload.put("quoteMessageId", quoteMessageId);
            }
            json.put("messageType", 7);
        } else if (EJzSendMessageType.IMAGE.getCode() == messageType) {
            json.put("messageType", 6);
        } else if (EJzSendMessageType.VIDEO.getCode() == messageType) {
            json.put("messageType", 13);
        } else if (EJzSendMessageType.FILE.getCode() == messageType) {
            json.put("messageType", 1);
        } else if (EJzSendMessageType.VOICE.getCode() == messageType) {
            json.put("messageType", 2);
        } else if (EJzSendMessageType.EMOTICON.getCode() == messageType) {
            json.put("messageType", 5);
            payload.put("appId", payload.getString("appid"));
        } else if (EJzSendMessageType.APPLET.getCode() == messageType) {
            json.put("messageType", 9);
        } else if (EJzSendMessageType.WEB.getCode() == messageType) {
            json.put("messageType", 12);
        } else if (EJzSendMessageType.CHANNEL.getCode() == messageType) {
            json.put("messageType", 14);
        }

        json.put("payload", payload);
        JSONObject result = HttpUtils.postToJsonObject("https://aa-hub.ddregion.com/api/v2/message/sendByQueue?token=" + juZiConfig.getToken(), json);
        log.info("消息转为队列发送返回结果：{}", result.toJSONString());
    }

}
