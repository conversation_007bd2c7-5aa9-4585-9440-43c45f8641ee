package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizPlanStatistics;
import com.ruoyi.biz.service.IBizPlanStatisticsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 运营计划数据统计Controller
 *
 * <AUTHOR>
 * @date 2024-10-29
 */
@RestController
@RequestMapping("/biz/planStatistics")
public class BizPlanStatisticsController extends BaseController
{
    @Resource
    private IBizPlanStatisticsService bizPlanStatisticsService;

    /**
     * 查询运营计划数据统计列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:planStatistics:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizPlanStatistics bizPlanStatistics)
    {
        startPage();
        List<BizPlanStatistics> list = bizPlanStatisticsService.selectBizPlanStatisticsList(bizPlanStatistics);
        return getDataTable(list);
    }

    /**
     * 导出运营计划数据统计列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:planStatistics:export')")
    @Log(title = "运营计划数据统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizPlanStatistics bizPlanStatistics)
    {
        List<BizPlanStatistics> list = bizPlanStatisticsService.selectBizPlanStatisticsList(bizPlanStatistics);
        ExcelUtil<BizPlanStatistics> util = new ExcelUtil<BizPlanStatistics>(BizPlanStatistics.class);
        util.exportExcel(response, list, "运营计划数据统计数据");
    }

    /**
     * 获取运营计划数据统计详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:planStatistics:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizPlanStatisticsService.selectBizPlanStatisticsById(id));
    }

    /**
     * 新增运营计划数据统计
     */
    //@PreAuthorize("@ss.hasPermi('biz:planStatistics:add')")
    @Log(title = "运营计划数据统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizPlanStatistics bizPlanStatistics)
    {
        return toAjax(bizPlanStatisticsService.insertBizPlanStatistics(bizPlanStatistics));
    }

    /**
     * 修改运营计划数据统计
     */
    //@PreAuthorize("@ss.hasPermi('biz:planStatistics:edit')")
    @Log(title = "运营计划数据统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizPlanStatistics bizPlanStatistics)
    {
        return toAjax(bizPlanStatisticsService.updateBizPlanStatistics(bizPlanStatistics));
    }

    /**
     * 删除运营计划数据统计
     */
    //@PreAuthorize("@ss.hasPermi('biz:planStatistics:remove')")
    @Log(title = "运营计划数据统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizPlanStatisticsService.deleteBizPlanStatisticsByIds(ids));
    }
}
