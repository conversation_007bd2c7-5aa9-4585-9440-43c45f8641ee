package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.StatChatHotWordDay;
import com.ruoyi.biz.service.IStatChatHotWordDayService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 会话每日热词统计Controller
 *
 * <AUTHOR>
 * @date 2024-04-19
 */
@RestController
@RequestMapping("/biz/chatStat")
public class StatChatHotWordDayController extends BaseController
{
    @Resource
    private IStatChatHotWordDayService statChatHotWordDayService;

    /**
     * 查询会话每日热词统计列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:chatStat:list')")
    @GetMapping("/list")
    public TableDataInfo list(StatChatHotWordDay statChatHotWordDay)
    {
        startPage();
        List<StatChatHotWordDay> list = statChatHotWordDayService.selectStatChatHotWordDayList(statChatHotWordDay);
        return getDataTable(list);
    }

    /**
     * 导出会话每日热词统计列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:chatStat:export')")
    @Log(title = "会话每日热词统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StatChatHotWordDay statChatHotWordDay)
    {
        List<StatChatHotWordDay> list = statChatHotWordDayService.selectStatChatHotWordDayList(statChatHotWordDay);
        ExcelUtil<StatChatHotWordDay> util = new ExcelUtil<StatChatHotWordDay>(StatChatHotWordDay.class);
        util.exportExcel(response, list, "会话每日热词统计数据");
    }

    /**
     * 获取会话每日热词统计详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:chatStat:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(statChatHotWordDayService.selectStatChatHotWordDayById(id));
    }

    /**
     * 新增会话每日热词统计
     */
    //@PreAuthorize("@ss.hasPermi('biz:chatStat:add')")
    @Log(title = "会话每日热词统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StatChatHotWordDay statChatHotWordDay)
    {
        return toAjax(statChatHotWordDayService.insertStatChatHotWordDay(statChatHotWordDay));
    }

    /**
     * 修改会话每日热词统计
     */
    //@PreAuthorize("@ss.hasPermi('biz:chatStat:edit')")
    @Log(title = "会话每日热词统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StatChatHotWordDay statChatHotWordDay)
    {
        return toAjax(statChatHotWordDayService.updateStatChatHotWordDay(statChatHotWordDay));
    }

    /**
     * 删除会话每日热词统计
     */
    //@PreAuthorize("@ss.hasPermi('biz:chatStat:remove')")
    @Log(title = "会话每日热词统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(statChatHotWordDayService.deleteStatChatHotWordDayByIds(ids));
    }
}
