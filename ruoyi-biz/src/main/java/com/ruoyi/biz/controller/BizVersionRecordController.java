package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.biz.enums.EBoolean;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizVersionRecord;
import com.ruoyi.biz.service.IBizVersionRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 版本详情Controller
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/biz/record")
public class BizVersionRecordController extends BaseController
{
    @Resource
    private IBizVersionRecordService bizVersionRecordService;

    /**
     * 查询版本详情列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizVersionRecord bizVersionRecord)
    {
        startPage();
        bizVersionRecord.setStatus(EBoolean.NO.getCode());
        List<BizVersionRecord> list = bizVersionRecordService.selectBizVersionRecordList(bizVersionRecord);
        return getDataTable(list);
    }

    /**
     * 导出版本详情列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:record:export')")
    @Log(title = "版本详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizVersionRecord bizVersionRecord)
    {
        List<BizVersionRecord> list = bizVersionRecordService.selectBizVersionRecordList(bizVersionRecord);
        ExcelUtil<BizVersionRecord> util = new ExcelUtil<BizVersionRecord>(BizVersionRecord.class);
        util.exportExcel(response, list, "版本详情数据");
    }

    /**
     * 获取版本详情详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizVersionRecordService.selectBizVersionRecordById(id));
    }

    /**
     * 新增版本详情
     */
    //@PreAuthorize("@ss.hasPermi('biz:record:add')")
    @Log(title = "版本详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizVersionRecord bizVersionRecord)
    {
        return toAjax(bizVersionRecordService.insertBizVersionRecord(bizVersionRecord));
    }

    /**
     * 修改版本详情
     */
    //@PreAuthorize("@ss.hasPermi('biz:record:edit')")
    @Log(title = "版本详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizVersionRecord bizVersionRecord)
    {
        return toAjax(bizVersionRecordService.updateBizVersionRecord(bizVersionRecord));
    }

    /**
     * 删除版本详情
     */
    //@PreAuthorize("@ss.hasPermi('biz:record:remove')")
    @Log(title = "版本详情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizVersionRecordService.deleteBizVersionRecordByIds(ids));
    }
}
