package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizChannelCodeConfig;
import com.ruoyi.biz.service.IBizChannelCodeConfigService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 渠道活码配置Controller
 *
 * <AUTHOR>
 * @date 2024-04-11
 */
@RestController
@RequestMapping("/biz/channelConfig")
public class BizChannelCodeConfigController extends BaseController
{
    @Resource
    private IBizChannelCodeConfigService bizChannelCodeConfigService;

    /**
     * 查询渠道活码配置列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:channelConfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizChannelCodeConfig bizChannelCodeConfig)
    {
        startPage();
        List<BizChannelCodeConfig> list = bizChannelCodeConfigService.selectBizChannelCodeConfigList(bizChannelCodeConfig);
        return getDataTable(list);
    }

    /**
     * 导出渠道活码配置列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:channelConfig:export')")
    @Log(title = "渠道活码配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizChannelCodeConfig bizChannelCodeConfig)
    {
        List<BizChannelCodeConfig> list = bizChannelCodeConfigService.selectBizChannelCodeConfigList(bizChannelCodeConfig);
        ExcelUtil<BizChannelCodeConfig> util = new ExcelUtil<BizChannelCodeConfig>(BizChannelCodeConfig.class);
        util.exportExcel(response, list, "渠道活码配置数据");
    }

    /**
     * 获取渠道活码配置详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:channelConfig:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizChannelCodeConfigService.selectBizChannelCodeConfigById(id));
    }

    /**
     * 新增渠道活码配置
     */
    //@PreAuthorize("@ss.hasPermi('biz:channelConfig:add')")
    @Log(title = "渠道活码配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizChannelCodeConfig bizChannelCodeConfig)
    {
        return toAjax(bizChannelCodeConfigService.insertBizChannelCodeConfig(bizChannelCodeConfig));
    }

    /**
     * 修改渠道活码配置
     */
    //@PreAuthorize("@ss.hasPermi('biz:channelConfig:edit')")
    @Log(title = "渠道活码配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizChannelCodeConfig bizChannelCodeConfig)
    {
        return toAjax(bizChannelCodeConfigService.updateBizChannelCodeConfig(bizChannelCodeConfig));
    }

    /**
     * 删除渠道活码配置
     */
    //@PreAuthorize("@ss.hasPermi('biz:channelConfig:remove')")
    @Log(title = "渠道活码配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizChannelCodeConfigService.deleteBizChannelCodeConfigByIds(ids));
    }
}
