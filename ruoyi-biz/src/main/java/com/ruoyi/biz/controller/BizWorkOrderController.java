package com.ruoyi.biz.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.domain.BizWorkOrder;
import com.ruoyi.biz.domain.req.BizWorkOrderReq;
import com.ruoyi.biz.enums.ETaggableRefType;
import com.ruoyi.biz.service.IBizWorkOrderService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.jzbot.domain.JzContact;
import com.ruoyi.jzbot.domain.JzRoom;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 工单，用来处理需要手工回复的消息Controller
 *
 * <AUTHOR>
 * @date 2024-08-01
 */
@RestController
@RequestMapping("/biz/workorder")
public class BizWorkOrderController extends BaseController {
    @Resource
    private IBizWorkOrderService bizWorkOrderService;

    /**
     * 查询工单，用来处理需要手工回复的消息列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:workorder:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizWorkOrder bizWorkOrder) {
        startPage();
        List<BizWorkOrder> list = bizWorkOrderService.selectBizWorkOrderList(bizWorkOrder);
        return getDataTable(list);
    }

    /**
     * 导出工单，用来处理需要手工回复的消息列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:workorder:export')")
    @Log(title = "工单，用来处理需要手工回复的消息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizWorkOrder bizWorkOrder) {
        List<BizWorkOrder> list = bizWorkOrderService.selectBizWorkOrderList(bizWorkOrder);
        ExcelUtil<BizWorkOrder> util = new ExcelUtil<BizWorkOrder>(BizWorkOrder.class);
        util.exportExcel(response, list, "工单，用来处理需要手工回复的消息数据");
    }

    /**
     * 获取工单，用来处理需要手工回复的消息详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:workorder:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(bizWorkOrderService.selectBizWorkOrderById(id));
    }

    /**
     * 新增工单，用来处理需要手工回复的消息
     */
    //@PreAuthorize("@ss.hasPermi('biz:workorder:add')")
    @Log(title = "工单，用来处理需要手工回复的消息", businessType = BusinessType.INSERT)
    @PostMapping
    @Anonymous
    public AjaxResult add(@RequestBody BizWorkOrderReq bizWorkOrderReq) {
        return toAjax(bizWorkOrderService.insertBizWorkOrderByPy(bizWorkOrderReq));
    }

    /**
     * 修改工单，用来处理需要手工回复的消息
     */
    //@PreAuthorize("@ss.hasPermi('biz:workorder:edit')")
    @Log(title = "工单，用来处理需要手工回复的消息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizWorkOrder bizWorkOrder) {
        return toAjax(bizWorkOrderService.updateBizWorkOrder(bizWorkOrder));
    }

    @PostMapping("/deal-with/{id}")
    public AjaxResult dealWith(@PathVariable("id") Long id) {
        return toAjax(bizWorkOrderService.dealWith(id));
    }

    /**
     * 删除工单，用来处理需要手工回复的消息
     */
    //@PreAuthorize("@ss.hasPermi('biz:workorder:remove')")
    @Log(title = "工单，用来处理需要手工回复的消息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(bizWorkOrderService.deleteBizWorkOrderByIds(ids));
    }
}
