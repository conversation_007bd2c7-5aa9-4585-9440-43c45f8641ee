package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizOperatePoint;
import com.ruoyi.biz.service.IBizOperatePointService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 运营节点Controller
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@RestController
@RequestMapping("/biz/point")
public class BizOperatePointController extends BaseController
{
    @Resource
    private IBizOperatePointService bizOperatePointService;

    /**
     * 查询运营节点列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:point:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizOperatePoint bizOperatePoint)
    {
        startPage();
        List<BizOperatePoint> list = bizOperatePointService.selectBizOperatePointList(bizOperatePoint);
        return getDataTable(list);
    }

    /**
     * 导出运营节点列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:point:export')")
    @Log(title = "运营节点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizOperatePoint bizOperatePoint)
    {
        List<BizOperatePoint> list = bizOperatePointService.selectBizOperatePointList(bizOperatePoint);
        ExcelUtil<BizOperatePoint> util = new ExcelUtil<BizOperatePoint>(BizOperatePoint.class);
        util.exportExcel(response, list, "运营节点数据");
    }

    /**
     * 获取运营节点详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:point:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizOperatePointService.selectBizOperatePointById(id));
    }

    /**
     * 新增运营节点
     */
    //@PreAuthorize("@ss.hasPermi('biz:point:add')")
    @Log(title = "运营节点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizOperatePoint bizOperatePoint)
    {
        return toAjax(bizOperatePointService.insertBizOperatePoint(bizOperatePoint));
    }

    /**
     * 修改运营节点
     */
    //@PreAuthorize("@ss.hasPermi('biz:point:edit')")
    @Log(title = "运营节点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizOperatePoint bizOperatePoint)
    {
        return toAjax(bizOperatePointService.updateBizOperatePoint(bizOperatePoint));
    }

    /**
     * 删除运营节点
     */
    //@PreAuthorize("@ss.hasPermi('biz:point:remove')")
    @Log(title = "运营节点", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizOperatePointService.deleteBizOperatePointByIds(ids));
    }
}
