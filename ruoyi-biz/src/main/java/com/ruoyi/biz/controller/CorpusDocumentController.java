package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.CorpusDocument;
import com.ruoyi.biz.service.ICorpusDocumentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 文档Controller
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/biz/document")
public class CorpusDocumentController extends BaseController
{
    @Resource
    private ICorpusDocumentService corpusDocumentService;

    /**
     * 查询文档列表
     */
    ////@PreAuthorize("@ss.hasPermi('biz:document:list')")
    @GetMapping("/list")
    public TableDataInfo list(CorpusDocument corpusDocument)
    {
        startPage();
        List<CorpusDocument> list = corpusDocumentService.selectCorpusDocumentList(corpusDocument);
        return getDataTable(list);
    }

    /**
     * 导出文档列表
     */
    ////@PreAuthorize("@ss.hasPermi('biz:document:export')")
    @Log(title = "文档", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CorpusDocument corpusDocument)
    {
        List<CorpusDocument> list = corpusDocumentService.selectCorpusDocumentList(corpusDocument);
        ExcelUtil<CorpusDocument> util = new ExcelUtil<CorpusDocument>(CorpusDocument.class);
        util.exportExcel(response, list, "文档数据");
    }

    /**
     * 获取文档详细信息
     */
    ////@PreAuthorize("@ss.hasPermi('biz:document:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(corpusDocumentService.selectCorpusDocumentById(id));
    }

    /**
     * 新增文档
     */
    ////@PreAuthorize("@ss.hasPermi('biz:document:add')")
    @Log(title = "文档", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CorpusDocument corpusDocument)
    {
        return toAjax(corpusDocumentService.insertCorpusDocument(corpusDocument));
    }

    @Log(title = "活动文档", businessType = BusinessType.INSERT)
    @PostMapping("/addActivityDocument")
    public AjaxResult addActivityDocument(@RequestBody CorpusDocument corpusDocument)
    {
        CorpusDocument document = corpusDocumentService.addActivityDocument(corpusDocument);
        return success(document);
    }

    /**
     * 修改文档
     */
    ////@PreAuthorize("@ss.hasPermi('biz:document:edit')")
    @Log(title = "文档", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CorpusDocument corpusDocument)
    {
        return toAjax(corpusDocumentService.updateCorpusDocument(corpusDocument));
    }

    /**
     * 删除文档
     */
    ////@PreAuthorize("@ss.hasPermi('biz:document:remove')")
    @Log(title = "文档", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(corpusDocumentService.deleteCorpusDocumentByIds(ids));
    }
}
