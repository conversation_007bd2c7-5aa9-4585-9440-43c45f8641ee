package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.BizAppTopicArticle;
import com.ruoyi.biz.service.IBizAppTopicArticleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 文章管理Controller
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@RestController
@RequestMapping("/biz/article")
public class BizAppTopicArticleController extends BaseController
{
    @Resource
    private IBizAppTopicArticleService bizAppTopicArticleService;

    /**
     * 查询文章管理列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:article:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizAppTopicArticle bizAppTopicArticle)
    {
        startPage();
        List<BizAppTopicArticle> list = bizAppTopicArticleService.selectBizAppTopicArticleList(bizAppTopicArticle);
        return getDataTable(list);
    }

    /**
     * 导出文章管理列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:article:export')")
    @Log(title = "文章管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizAppTopicArticle bizAppTopicArticle)
    {
        List<BizAppTopicArticle> list = bizAppTopicArticleService.selectBizAppTopicArticleList(bizAppTopicArticle);
        ExcelUtil<BizAppTopicArticle> util = new ExcelUtil<BizAppTopicArticle>(BizAppTopicArticle.class);
        util.exportExcel(response, list, "文章管理数据");
    }

    /**
     * 获取文章管理详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:article:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizAppTopicArticleService.selectBizAppTopicArticleById(id));
    }

    /**
     * 新增文章管理
     */
    //@PreAuthorize("@ss.hasPermi('biz:article:add')")
    @Log(title = "文章管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizAppTopicArticle bizAppTopicArticle)
    {
        return toAjax(bizAppTopicArticleService.insertBizAppTopicArticle(bizAppTopicArticle));
    }

    /**
     * 修改文章管理
     */
    //@PreAuthorize("@ss.hasPermi('biz:article:edit')")
    @Log(title = "文章管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizAppTopicArticle bizAppTopicArticle)
    {
        return toAjax(bizAppTopicArticleService.updateBizAppTopicArticle(bizAppTopicArticle));
    }

    /**
     * 删除文章管理
     */
    //@PreAuthorize("@ss.hasPermi('biz:article:remove')")
    @Log(title = "文章管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizAppTopicArticleService.deleteBizAppTopicArticleByIds(ids));
    }
}
