package com.ruoyi;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.controller.JzMessageSendController;
import com.ruoyi.biz.domain.BizHotWordDb;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.biz.service.IBizHotWordDbService;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.MsgConstants;
import com.ruoyi.common.core.domain.model.BizMessage;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.EJzRecviceMessageType;
import com.ruoyi.common.enums.WsDataType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.framework.web.domain.server.Sys;
import com.ruoyi.framework.websocket.WebSocketUsers;
import com.ruoyi.framework.websocket.WsDataDto;
import com.ruoyi.goods.service.IPmsRelationshipService;
import com.ruoyi.jzbot.domain.JzChatMessage;
import com.ruoyi.jzbot.service.IJzChatMessageService;
import com.ruoyi.jzbot.util.FsBotUtil;
import com.ruoyi.jzbot.util.JieBaUtils;
import com.ruoyi.system.service.ISysConfigService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@Order(2)
@Slf4j
@Data
public class InitRun implements CommandLineRunner {

    @Value("${spring.profiles.active}")
    private String active;

    private static final int POOL_SIZE = 3; // 线程池大小

    private final ThreadPoolExecutor ThreadPool = (ThreadPoolExecutor) Executors.newFixedThreadPool(POOL_SIZE);

    private static Long FiveTime = 30 * 1000L;

    @Resource
    private RedisCache redisCache;

    @Resource
    private IBizHotWordDbService bizHotWordDbService;

    @Resource
    private IJzChatMessageService jzChatMessageService;

    @Resource
    private IPmsRelationshipService pmsRelationshipService;


    @Resource
    private JzMessageSendController jzMessageSendController;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private RedisLock redisLock;

    @Resource
    private FsBotUtil fsBotUtil;


    public static final int MAX_WAIT_TIME = 10; // 最大等待时间（秒）
    public static final int WAIT_INTERVAL = 3; // 每次检查新消息的等待时间（秒）



    @Override
    public void run(String... args) {
        //缓存热词
        cacheHotWord();

        checkAIHandleMessageIsOverTime();

    }

    /**
     *
     */
    private void checkAIHandleMessageIsOverTime() {
        new Thread(() -> {
            while (true) {
                log.info("检查消息回复是否超时");
                try {
                    Collection<String> keys = redisCache.keys(CacheConstants.MSG_MONITOR + "*");
                    Long nowTime = System.currentTimeMillis();
                    int timeoutCount = 0;  // 超时消息计数

                    for (String key : keys) {
                        Long oldTime = redisCache.getCacheObject(key);
                        if (nowTime - oldTime >= 4*60*1000) {
                            redisCache.deleteObject(key);
                            //超时未回复
                            String messageId = key.split(":")[1];
                            JzChatMessage jzChatMessage = new JzChatMessage();
                            jzChatMessage.setMessageId(messageId);
                            jzChatMessage.setAiExtra(new JSONObject(){{
                                put("useTime",System.currentTimeMillis() - oldTime);
                                put("status","2");
                            }}.toJSONString());
                            jzChatMessageService.updateJzChatMessageByMessageId(jzChatMessage);

                            // 增加超时计数
                            timeoutCount++;

                            String overtime = "ai_day_message_overtime:";
                            String str = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, new Date());
                            redisCache.incr(overtime.concat(str), 1);
                        }
                    }

                    // 如果有超时消息，更新Redis中的计数
                    if (timeoutCount > 0) {
                        String countKey = "AI_TIMEOUT_COUNT";
                        Long count = redisCache.getCacheObject(countKey);
                        if (count == null) {
                            count = 0L;
                        }
                        count += timeoutCount;

                        // 设置计数，5分钟过期
                        redisCache.setCacheObject(countKey, count, 5, TimeUnit.MINUTES);

                        // 如果5分钟内超时消息达到10条，发送飞书通知
                        if (count >= 10) {
                            Map<String, Object> statsMap = new LinkedHashMap<>();
                            statsMap.put("AI响应异常统计", null);
                            statsMap.put("检测时间范围", "最近5分钟");
                            statsMap.put("超时消息数量", count);
                            statsMap.put("超时判定阈值", "4分钟");
                            statsMap.put("当前时间", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));

                            try {
                                fsBotUtil.sendFsStatsCardMessage("⚠️ AI响应超时告警", statsMap);
                                // 发送通知后重置计数
                                redisCache.deleteObject(countKey);
                            } catch (Exception e) {
                                log.error("发送AI超时告警通知失败", e);
                            }
                        }
                    }

                } catch (Exception e) {
                    log.error("检查AI处理消息异常", e);
                }
                try {
                    Thread.sleep(60*1000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }, "checkaihandlemessageisovertime-thread").start();
    }

//
//    //Lua 脚本拿到消息后
//    private static final String SCRIPT =
//            "local messages = redis.call('lrange', KEYS[1], 0, -1); " +
//                    "if next(messages) ~= nil then " +
//                    "  redis.call('del', KEYS[1]); " +
//                    "  redis.call('del', KEYS[2]); " +
//                    "  redis.call('del', KEYS[3]); " +
//                    "end; " +
//                    "return messages;";
//
//    /**
//     * 合并消息队列
//     *
//     * @param chatId
//     * @param contactId
//     */
//    private void combinedMessageProcessing(String chatId, String contactId) {
//        Timer timer = new Timer();
//        TimerTask task = new TimerTask() {
//            private final long startTime = System.currentTimeMillis();
//            private final String messageKey = String.format(CacheConstants.JU_ZI_BOT_CHAT_USER_MESSAGE_LIST, chatId, contactId);
//            private final String lastReceivedKey = String.format(CacheConstants.JU_ZI_BOT_CHAT_USER_MESSAGE_LAST_SEND_TIME, chatId, contactId);
//            private final String lockKey = String.format(CacheConstants.JU_ZI_BOT_CHAT_USER_MESSAGE_LOCK, chatId, contactId);
//
//            @Override
//            public void run() {
//                long lastReceivedTime = Long.parseLong(redisCache.getCacheObject(lastReceivedKey));
//                long currentTime = System.currentTimeMillis();
//
//                if ((currentTime - startTime) / 1000 >= MAX_WAIT_TIME || (currentTime - lastReceivedTime) / 1000 >= WAIT_INTERVAL) {
//                    //使用 Lua 脚本 拿到消息 并删除
//                    List<String> messages = redisCache.execute(SCRIPT, Arrays.asList(messageKey, lastReceivedKey, lockKey));
//                    if (CollectionUtils.isNotEmpty(messages)) {
//                        // 反转 List
//                        Collections.reverse(messages);
//                        log.info("开始合并推送句子消息");
//                        JSONObject jsonObject = JSONObject.parseObject(messages.get(0));
//                        StringBuilder combinedMessage = new StringBuilder();
//                        for (String message : messages) {
//                            JSONObject data = JSONObject.parseObject(message).getJSONObject("data");
//                            Object messageId = redisCache.getCacheObject("chat_recall:".concat(data.getString("messageId")));
//                            if (messageId != null) {
//                                combinedMessage.append("消息已撤回").append("\n");
//                                continue;
//                            }
//                            combinedMessage.append(data.getJSONObject("payload").getString("text")).append("\n");
//                        }
//                        //合并推送
//                        jzMessageSendController.sendTextInfo(jsonObject.getJSONObject("data").getString("token"), "测试合并:\n" + combinedMessage, chatId);
//                    }
//                    timer.cancel(); // 任务完成后取消定时器
//                }
//            }
//        };
//        timer.schedule(task, WAIT_INTERVAL * 1000, WAIT_INTERVAL * 1000); // 每隔WAIT_INTERVAL秒检查一次
//    }



    /**
     * 缓存热词到redis
     */
    private void cacheHotWord() {

        ThreadPool.submit(new Runnable() {
            @Override
            public void run() {
                int pageNum = 1;
                int pageSize = 100;
                Map<String, String> map = new HashMap<>();
                while (true) {
                    PageUtils.startPage(pageNum, pageSize, false);
                    List<BizHotWordDb> wordDbList = bizHotWordDbService.selectBizHotWordDbList(new BizHotWordDb());
                    if (CollectionUtils.isEmpty(wordDbList)) {
                        break;
                    }
                    wordDbList.forEach(bizHotWordDb -> {
                        map.put(bizHotWordDb.getHotWordName(), bizHotWordDb.getHotWordName());
                        if (StringUtils.isNotBlank(bizHotWordDb.getDerivativesJson())) {
                            for (String word : bizHotWordDb.getDerivativesJson().split(",")) {
                                if (StringUtils.isBlank(word)) {
                                    continue;
                                }
                                map.put(word, bizHotWordDb.getHotWordName());
                            }
                        }
                    });
                    if (wordDbList.size() < pageSize) {
                        break;
                    }

                    pageNum++;
                }

                if (!map.isEmpty()) {
                    redisCache.setCacheMap(CacheConstants.CACHE_HOT_WORD_MAP, map);
                }
            }
        });

    }

}


