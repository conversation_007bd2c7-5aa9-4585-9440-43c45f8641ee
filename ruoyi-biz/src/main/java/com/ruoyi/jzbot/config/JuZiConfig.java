package com.ruoyi.jzbot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取句子相关配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "juzi")
@Data
public class JuZiConfig {


    /** 企业touken */
    private String token;

    /**
     * 小组级 token
     */
    private String groupToken;



}
