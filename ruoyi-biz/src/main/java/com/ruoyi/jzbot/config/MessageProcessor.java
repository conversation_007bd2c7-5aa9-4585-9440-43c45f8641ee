package com.ruoyi.jzbot.config;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

@Component
@Slf4j
public class MessageProcessor {

    @Resource
    private  RedisCache redisCache;

    @Resource
    private RedisLock redisLock;

    private final String messageKeyTemplate = CacheConstants.JU_ZI_BOT_CHAT_USER_MESSAGE_LIST;
    private final String lastReceivedKeyTemplate = CacheConstants.JU_ZI_BOT_CHAT_USER_MESSAGE_LAST_SEND_TIME;
    private final String lockKeyTemplate = CacheConstants.JU_ZI_BOT_CHAT_USER_MESSAGE_LOCK;

    private static final int MAX_WAIT_TIME = 5; // 最大等待时间 (秒)
    private static final int WAIT_INTERVAL = 1; // 消息间隔时间 (秒)

    // 定时器线程池
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private final Map<String, ScheduledFuture<?>> taskMap = new ConcurrentHashMap<>();

    /**
     * 接收消息并处理
     */
    public void receiveMessage(String chatId, String contactId, JSONObject params, String noticeEnv) {
        String messageKey = String.format(messageKeyTemplate, chatId, contactId);
        String lastReceivedKey = String.format(lastReceivedKeyTemplate, chatId, contactId);
        String lockKey = String.format(lockKeyTemplate, chatId, contactId);

        // 将消息推入 Redis 队列
        redisCache.push(messageKey, params.toJSONString());
        redisCache.setCacheObject(lastReceivedKey, String.valueOf(System.currentTimeMillis()));

        // 启动或重置定时任务
        String taskKey = chatId + ":" + contactId;
        taskMap.compute(taskKey, (key, existingTask) -> {
            if (existingTask != null && !existingTask.isDone()) {
                existingTask.cancel(false); // 取消已有任务
            }
            return scheduler.schedule(
                () -> processMessages(chatId, contactId, noticeEnv),
                WAIT_INTERVAL,
                TimeUnit.SECONDS
            );
        });
    }

    /**
     * 合并并处理消息
     */
    private void processMessages(String chatId, String contactId, String noticeEnv) {
        String messageKey = String.format(messageKeyTemplate, chatId, contactId);
        String lastReceivedKey = String.format(lastReceivedKeyTemplate, chatId, contactId);
        String lockKey = String.format(lockKeyTemplate, chatId, contactId);
        String lockTime = String.valueOf(System.currentTimeMillis() + 30 * 1000);

        boolean lock = redisLock.lock(lockKey, lockTime); // 获取分布式锁
        if (!lock) {
            return; // 如果获取锁失败，直接返回
        }

        try {
            long lastReceivedTime = Long.parseLong(redisCache.getCacheObject(lastReceivedKey));
            long currentTime = System.currentTimeMillis();
            // 检查时间条件
            if ((currentTime - lastReceivedTime) / 1000 >= MAX_WAIT_TIME || redisCache.lSize(messageKey) > 0) {
                // 使用 Lua 脚本获取消息并清空队列
                List<String> messages = redisCache.execute(SCRIPT, Arrays.asList(messageKey, lastReceivedKey, lockKey));
                if (messages != null && !messages.isEmpty()) {
                    handleCombinedMessages(chatId, contactId, noticeEnv, messages);
                }
            }
        } finally {
            redisLock.unlock(lockKey,lockTime);
        }
    }

    /**
     * 处理合并的消息
     */
    private void handleCombinedMessages(String chatId, String contactId, String noticeEnv, List<String> messages) {
        Collections.reverse(messages); // 反转消息顺序

        StringBuilder combinedMessage = new StringBuilder();
        JSONObject jsonObject = JSONObject.parseObject(messages.get(0)); // 获取首条消息

        for (String message : messages) {
            JSONObject data = JSONObject.parseObject(message).getJSONObject("data");
            Object messageId = redisCache.getCacheObject("chat_recall:".concat(data.getString("messageId")));
            if (messageId != null) {
                continue; // 跳过被撤回的消息
            }
            combinedMessage.append(data.getJSONObject("payload").getString("text")).append("\n");
        }

        log.info("合并后的消息为：{}", combinedMessage);
        jsonObject.getJSONObject("data").getJSONObject("payload").put("text", combinedMessage);

        redisCache.push(noticeEnv, jsonObject.toJSONString());
        redisCache.setCacheObject(CacheConstants.MSG_MONITOR + jsonObject.getJSONObject("data").getString("messageId"), System.currentTimeMillis());

//        jzMessageSendController.sendTextInfo(jsonObject.getJSONObject("data").getString("token"), "测试合并:\n" + combinedMessage, chatId, "");
    }

    private static final String SCRIPT =
        "local messages = redis.call('lrange', KEYS[1], 0, -1); " +
        "if next(messages) ~= nil then " +
        "  redis.call('del', KEYS[1]); " +
        "  redis.call('del', KEYS[2]); " +
        "  redis.call('del', KEYS[3]); " +
        "end; " +
        "return messages;";
}
