package com.ruoyi.jzbot.config;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.houbb.sensitive.word.bs.SensitiveWordBs;
import com.ruoyi.biz.controller.JzMessageSendController;
import com.ruoyi.biz.domain.BizH5App;
import com.ruoyi.biz.domain.req.ChatStatisticsReq;
import com.ruoyi.biz.domain.req.TempHandlerReq;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.biz.enums.ECollectType;
import com.ruoyi.biz.enums.ETempHandlerType;
import com.ruoyi.biz.service.*;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.ConfigConstants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.EJzRecviceMessageType;
import com.ruoyi.common.enums.ERedisChannel;
import com.ruoyi.common.enums.WsDataType;
import com.ruoyi.framework.websocket.WebSocketUsers;
import com.ruoyi.framework.websocket.WsDataDto;
import com.ruoyi.jzbot.domain.JzBot;
import com.ruoyi.jzbot.domain.JzChatMessage;
import com.ruoyi.jzbot.domain.JzContact;
import com.ruoyi.jzbot.domain.JzRoom;
import com.ruoyi.jzbot.mapper.JzContactMapper;
import com.ruoyi.jzbot.mapper.JzRoomMapper;
import com.ruoyi.jzbot.service.IJzBotService;
import com.ruoyi.jzbot.service.IJzChatMessageService;
import com.ruoyi.jzbot.util.JuZiBotUtil;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Configuration
@Slf4j
public class RedisJuZiMessageListen {

    @Resource
    private IJzChatMessageService jzChatMessageService;

    @Resource
    private JzMessageSendController jzMessageSendController;

    @Resource
    private IBizUserTempService bizUserTempService;

    @Resource
    private IBizUserService bizUserService;

    @Value("${domain}")
    private String domainUrl;

    @Resource
    private RedisCache redisCache;

    @Resource
    private IJzBotService jzBotService;

    @Resource
    private JuZiBotUtil juZiBotUtil;

    @Resource
    private RedisLock redisLock;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private IBizChatStatisticsService chatStatisticsService;

    @Resource
    private SensitiveWordBs sensitiveWordBs;

    @Resource
    private JzContactMapper jzContactMapper;

    @Resource
    private JzRoomMapper jzRoomMapper;

    @Resource
    private IBizMembershipService bizMembershipService;

    @Resource
    private MessageProcessor messageProcessor;

    @Resource
    private IBizH5AppService iBizH5AppService;

    @Resource
    private IBizUserMaterialRecordService bizUserMaterialRecordService;

    @Resource
    private IDifyService iDifyService;

    public static final int MAX_WAIT_TIME = 10; // 最大等待时间（秒）
    public static final int WAIT_INTERVAL = 3; // 每次检查新消息的等待时间（秒）

    @Value("${spring.profiles.active}")
    private String active;

    public RedisJuZiMessageListen(RedisMessageListenerContainer container) {
        container.addMessageListener((message, pattern) -> {
            String osName = System.getProperty("os.name");
            if (!osName.contains("Linux")) {
                log.info("非Linux环境，不消费句子互动信息");
                return;
            }
            String messageBody = JSON.parse(new String(message.getBody())).toString();
            log.info("Redis开始消费句子互动信息 " + messageBody);
            if (StringUtils.isNotEmpty(messageBody)) {
                try {
                    JSONObject params = JSONObject.parseObject(messageBody);

                    JSONObject data = params.getJSONObject("data");

                    //发送消息给AI
                    sendMessageToAI(params);

                    JzChatMessage jzChatMessage = jzChatMessageService.handleReceiveMessage(data);

                    // 发送消息给前端, websocket
                    sendMessageToPanel(jzChatMessage);

                    // 处理用户体温，增加全局开关
                    if (sysConfigService.isOpen(ConfigConstants.IS_TEMP_CHECK_OPEN)) {
                        log.info("开始发送体温处理消息");
                        TempHandlerReq req = new TempHandlerReq();
                        req.setType(ETempHandlerType.CHAT_HANDLER);
                        req.setJzChatMessage(jzChatMessage);
                        redisCache.publish(ERedisChannel.DEAL_TEMP_CHANNEL.getCode(), req);
                    }
//
//                    // 收集聊天统计信息
                    collectChatMessage(jzChatMessage);

                } catch (Exception e) {
                    log.error("处理消息时发生错误", e);
                }
            }
        }, new PatternTopic(ERedisChannel.CHAT_CHANNEL.getCode()));
    }



    private void sendMessageToAI(JSONObject params) throws Exception {
        JSONObject data = params.getJSONObject("data");
        String chatId = data.getString("chatId");
        Integer messageType = data.getInteger("type");

        if (StringUtils.isBlank(data.getString("roomId"))) {
            JzContact contact = new JzContact();
            contact.setChatId(chatId);
            contact.setLastTime(new Date());
            if (EJzRecviceMessageType.TEXT.getCode() == messageType) {
                contact.setLastMessage(data.getJSONObject("payload").getString("text"));
            } else {
                contact.setLastMessage("["+EJzRecviceMessageType.fromValue(messageType).getDescription()+"]");
            }
            jzContactMapper.updateJzContact(contact);
        } else {
            JzRoom room = new JzRoom();
            room.setChatId(chatId);
            room.setLastTime(new Date());
            if (EJzRecviceMessageType.TEXT.getCode() == messageType) {
                room.setLastMessage(data.getJSONObject("payload").getString("text"));
            } else {
                room.setLastMessage("消息");
            }
            jzRoomMapper.updateJzRoom(room);
        }

        if (isNotAIWorkTime()) {
            log.info("当前时间非AI工作时间，跳过消息处理");
            return;
        }

//        //凌晨0点到8:30 不处理消息
//        // 获取当前时间
//        LocalTime now = LocalTime.now();
//        LocalTime start = LocalTime.of(0, 0);
//        LocalTime end = LocalTime.of(8, 30);
//        if (!now.isBefore(start) && now.isBefore(end)) {
//            // 当前时间在 0:00 到 8:30 之间，跳过消息处理
//            log.info("当前时间在 0:00 到 8:30 之间，跳过消息处理");
//            return;
//        }

        if (!"1".equals(data.getString("contactType"))) {
            return;
        }

        //撤回消息
        if (EJzRecviceMessageType.RECALLED.getCode() == messageType) {
            redisCache.setCacheObject("chat_recall:".concat(data.getJSONObject("payload").getString("content")), data.getJSONObject("payload").getString("content"), 3, TimeUnit.MINUTES);
            return;
        }
        // 如果是图片消息暂存到redis内
        if (EJzRecviceMessageType.IMAGE.getCode() == messageType && StringUtils.isBlank(data.getString("roomId"))) {
            JSONObject artworkImage = juZiBotUtil.getArtworkImage(new JSONObject() {{
                put("token", data.getString("token"));
                put("messageId", data.getString("messageId"));
                put("chatId", data.getString("chatId"));
            }});

            redisCache.setCacheObject("chat_image:".concat(data.getString("chatId")), new JSONObject(){{
                put("url",artworkImage.getString("url"));
                put("imageUrl",artworkImage.getString("url"));
            }}, 3, TimeUnit.MINUTES);
            return;
        }
        //非文本消息，不进行推送
        if (EJzRecviceMessageType.TEXT.getCode() != messageType && EJzRecviceMessageType.AUDIO.getCode() != messageType) {
            return;
        }

        String chatConfKey = "chat_config:".concat(chatId);
        JSONObject configMap = redisCache.getCacheObject(chatConfKey);

        boolean isContact = false, isOpen = configMap != null && EBoolean.YES.getCode().equals(configMap.getString("status"));
        if (StringUtils.isBlank(data.getString("roomId")) && !data.getBoolean("isSelf")) {
            isContact = true;
            //如果是语音,转为文本处理
            if (EJzRecviceMessageType.AUDIO.getCode() == messageType) {
                data.put("type", EJzRecviceMessageType.TEXT.getCode());
                JSONObject payload = data.getJSONObject("payload");
                payload.keySet().removeIf(key -> !"text".contains(key));
            }
        }

        boolean shouldPush = isContact
                || (configMap != null && EBoolean.YES.getCode().equals(configMap.getString("excludeAtReply")) && !data.getBoolean("isSelf"))
                || (data.containsKey("mentionSelf") && data.getBoolean("mentionSelf"));

        if (!shouldPush) {
            return;
        }

        JzBot bot = jzBotService.selectJzBotByBotId(data.getString("botId"));
        if (bot == null || !EBoolean.YES.getCode().equals(bot.getStatus())) {
            log.info("bot未启用或者异常, 无法发送消息{}", data.getString("botId"));
            return;
        }


        //接管中，无需进行回复
        if (redisCache.hasKey(CacheConstants.TAKE_OVER_CHAT+chatId)) {
            log.info("人工接管中:{}",data.getJSONObject("payload").getString("text").trim() );
            return;
        }

        if (isOpen) {
            //敏感词检测
            String text = data.getJSONObject("payload").getString("text").trim();
            boolean contains = sensitiveWordBs.contains(text);
            if (contains) {
                log.info("敏感词检测到内容:{}", text);
                String str = sysConfigService.selectConfigByKey("sys.sensitive_word");
                if (StringUtils.isNotBlank(str)) {
                    //发送敏感词回复
                    jzMessageSendController.sendTextInfo(data.getString("token"), str, chatId, data.getString("messageId"));
                }
                return;
            }

            //app检查
            if (isContact && appCheck( data)){
                return;
            }

            log.info("开始推送句子消息");
            String noticeEnv = CacheConstants.JU_ZI_BOT_AT_MESSAGE_LIST_V3;
            if (!"default".equals(configMap.get("noticeEnv").toString())) {
                noticeEnv = noticeEnv.concat("_").concat(configMap.get("noticeEnv").toString());
            }


            log.info(isContact ? "开始推送句子消息(好友)" : "开始推送句子消息(群聊)");

            redisCache.push(noticeEnv, JSONObject.toJSONString(params));
            //消息发送监控
            redisCache.setCacheObject(CacheConstants.MSG_MONITOR+ data.getString("messageId"), System.currentTimeMillis());

//            //合并消息
//            messageProcessor.receiveMessage(data.getString("chatId"),data.getString("contactId"),params,noticeEnv);
        }

    }

    /**
     * 检查是否需要走APP h5web
     *
     * @param data
     * @return
     */
    private boolean appCheck(JSONObject data) throws Exception {
        // 根据code 判断是否需要唤醒h5app
        BizH5App h5App = iBizH5AppService.selectBizH5AppByCode(data.getJSONObject("payload").getString("text").trim());
        if (h5App != null) {
            //先落地，防止app消息落地了，顺序不一致问题
            jzChatMessageService.handleReceiveMessage(data);
            bizUserMaterialRecordService.handleAppMessage(data,h5App);
            return true;
        }
        return false;
    }

    /**
     * 判断时间是否在指定范围内（支持跨天或非跨天）
     *
     * @return 是否在范围内
     */
    private  boolean isNotAIWorkTime() {
        String workTime = sysConfigService.selectConfigByKey("sys.ai.not_work_time");
        LocalTime now = LocalTime.now();
        String startRange = workTime.split("-")[0];
        String endRange = workTime.split("-")[1];
        LocalTime start = LocalTime.of(Integer.parseInt(startRange.split(":")[0]), Integer.parseInt(startRange.split(":")[1]));
        LocalTime end = LocalTime.of(Integer.parseInt(endRange.split(":")[0]), Integer.parseInt(endRange.split(":")[1]));

        if (start.isBefore(end)) {
            // 非跨天情况，例如 08:00 到 20:00
            return !now.isBefore(start) && now.isBefore(end);
        } else {
            // 跨天情况，例如 23:00 到次日 06:00
            return !now.isBefore(start) || now.isBefore(end);
        }
    }

    /**
     * 发送消息到后台看板
     *
     * @param jzChatMessage
     */
    private void sendMessageToPanel(JzChatMessage jzChatMessage) {
        if (StringUtils.isNotBlank(jzChatMessage.getSendName())) {
            jzChatMessage.setContactName(jzChatMessage.getSendName());
        }
        WsDataDto message = WsDataDto.message(WsDataType.ChatMessage, jzChatMessage);
        WebSocketUsers.sendMessageToUsersByText(JSON.toJSONString(message));
    }

    /**
     * 收集聊天信息
     */
    private void collectChatMessage(JzChatMessage jzChatMessage) {
        chatStatisticsService.collect(ECollectType.CHAT_MESSAGE, ChatStatisticsReq.builder()
                .chatId(jzChatMessage.getChatId())
                .messageSource(jzChatMessage.getMessageSource())
                .messageId(jzChatMessage.getMessageId())
                .message(jzChatMessage.getMessage())
                .isSelf(jzChatMessage.getIsSelf()).build());
    }


}
