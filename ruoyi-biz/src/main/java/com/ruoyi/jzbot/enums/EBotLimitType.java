package com.ruoyi.jzbot.enums;


import lombok.Getter;

@Getter
public enum EBotLimitType {
    UNKNOWN(0, "未知"),
    SEND_MESSAGE(1, "发送消息"),
    SEARCH_CONTACT(3, "搜索联系人"),
    ADD_CONTACT(4, "添加联系人"),
    ACCEPT_FRIEND_REQUEST(5, "接受好友请求"),
    INVITE_TO_GROUP(6, "拉人入群"),
    CREATE_GROUP(7, "创建群聊");

    private final int code;
    private final String description;

    // 构造方法
    EBotLimitType(int code, String description) {
        this.code = code;
        this.description = description;
    }


    // 通过 code 查找对应的枚举
    public static EBotLimitType fromCode(int code) {
        for (EBotLimitType action : EBotLimitType.values()) {
            if (action.getCode() == code) {
                return action;
            }
        }
        return UNKNOWN;  // 如果找不到匹配的，返回 UNKNOWN
    }
}
