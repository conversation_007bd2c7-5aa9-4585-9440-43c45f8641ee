package com.ruoyi.jzbot.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.jzbot.domain.BizStopWord;
import com.ruoyi.jzbot.service.IBizStopWordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * jieba过滤词典Controller
 *
 * <AUTHOR>
 * @date 2024-03-19
 */
@RestController
@RequestMapping("/biz/word")
public class BizStopWordController extends BaseController {
    @Resource
    private IBizStopWordService bizStopWordService;

    /**
     * 查询jieba过滤词典列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:word:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizStopWord bizStopWord) {
        startPage();
        List<BizStopWord> list = bizStopWordService.selectBizStopWordList(bizStopWord);
        return getDataTable(list);
    }

    /**
     * 导出jieba过滤词典列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:word:export')")
    @Log(title = "jieba过滤词典", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizStopWord bizStopWord) {
        List<BizStopWord> list = bizStopWordService.selectBizStopWordList(bizStopWord);
        ExcelUtil<BizStopWord> util = new ExcelUtil<BizStopWord>(BizStopWord.class);
        util.exportExcel(response, list, "jieba过滤词典数据");
    }

    /**
     * 获取jieba过滤词典详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:word:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(bizStopWordService.selectBizStopWordById(id));
    }

    /**
     * 新增jieba过滤词典
     */
    //@PreAuthorize("@ss.hasPermi('biz:word:add')")
    @Log(title = "jieba过滤词典", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizStopWord bizStopWord) {
        return toAjax(bizStopWordService.insertBizStopWord(bizStopWord));
    }

    /**
     * 修改jieba过滤词典
     */
    //@PreAuthorize("@ss.hasPermi('biz:word:edit')")
    @Log(title = "jieba过滤词典", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizStopWord bizStopWord) {
        return toAjax(bizStopWordService.updateBizStopWord(bizStopWord));
    }

    /**
     * 删除jieba过滤词典
     */
    //@PreAuthorize("@ss.hasPermi('biz:word:remove')")
    @Log(title = "jieba过滤词典", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(bizStopWordService.deleteBizStopWordByIds(ids));
    }

    @Log(title = "过滤词典添加", businessType = BusinessType.INSERT)
    @PostMapping("/readFileAndInsertData")
    public AjaxResult readFileAndInsertData() {
        return toAjax(bizStopWordService.readFileAndInsertData());
    }
}
