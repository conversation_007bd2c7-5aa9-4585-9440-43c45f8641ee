package com.ruoyi.jzbot.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.jzbot.domain.JzBot;
import com.ruoyi.jzbot.service.IJzBotService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * bot查看Controller
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@RestController
@RequestMapping("/bot/bot")
public class JzBotController extends BaseController
{
    @Resource
    private IJzBotService jzBotService;

    /**
     * 查询bot查看列表
     */
    //@PreAuthorize("@ss.hasPermi('bot:bot:list')")
    @GetMapping("/list")
    public TableDataInfo list(JzBot jzBot)
    {
        startPage();
        List<JzBot> list = jzBotService.selectJzBotList(jzBot);
        return getDataTable(list);
    }

    /**
     * 导出bot查看列表
     */
    //@PreAuthorize("@ss.hasPermi('bot:bot:export')")
    @Log(title = "bot查看", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JzBot jzBot)
    {
        List<JzBot> list = jzBotService.selectJzBotListSimple(jzBot);
        ExcelUtil<JzBot> util = new ExcelUtil<JzBot>(JzBot.class);
        util.exportExcel(response, list, "bot查看数据");
    }

    /**
     * 获取bot查看详细信息
     */
    //@PreAuthorize("@ss.hasPermi('bot:bot:query')")
    @GetMapping(value = "/{botId}")
    public AjaxResult getInfo(@PathVariable("botId") String botId)
    {
        return success(jzBotService.selectJzBotByBotId(botId));
    }

    /**
     * 新增bot查看
     */
    //@PreAuthorize("@ss.hasPermi('bot:bot:add')")
    @Log(title = "bot查看", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody JzBot jzBot)
    {
        return toAjax(jzBotService.insertJzBot(jzBot));
    }

    /**
     * 修改bot查看
     */
    //@PreAuthorize("@ss.hasPermi('bot:bot:edit')")
    @Log(title = "bot查看", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JzBot jzBot)
    {
        return toAjax(jzBotService.updateJzBot(jzBot));
    }

    /**
     * 删除bot查看
     */
    //@PreAuthorize("@ss.hasPermi('bot:bot:remove')")
    @Log(title = "bot查看", businessType = BusinessType.DELETE)
	@DeleteMapping("/{botIds}")
    public AjaxResult remove(@PathVariable String[] botIds)
    {
        return toAjax(jzBotService.deleteJzBotByBotIds(botIds));
    }
}
