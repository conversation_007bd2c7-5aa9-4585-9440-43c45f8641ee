package com.ruoyi.jzbot.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.jzbot.domain.JzGroupChat;
import com.ruoyi.jzbot.service.IJzGroupChatService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 群组Controller
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
@RestController
@RequestMapping("/bot/groupChat")
public class JzGroupChatController extends BaseController
{
    @Resource
    private IJzGroupChatService jzGroupChatService;

    /**
     * 查询群组列表
     */
    //@PreAuthorize("@ss.hasPermi('bot:groupChat:list')")
    @GetMapping("/list")
    public TableDataInfo list(JzGroupChat jzGroupChat)
    {
        startPage();
        List<JzGroupChat> list = jzGroupChatService.selectJzGroupChatList(jzGroupChat);
        return getDataTable(list);
    }

    /**
     * 查询客户群列表
     *
     * @param jzGroupChat
     * @return
     */
    @GetMapping("/list/customer")
    public TableDataInfo listCustomer(JzGroupChat jzGroupChat) {
        startPage();
        List<JzGroupChat> list = jzGroupChatService.selectCustomerGroupChatList(jzGroupChat);
        return getDataTable(list);
    }

    /**
     * 导出群组列表
     */
    //@PreAuthorize("@ss.hasPermi('bot:groupChat:export')")
    @Log(title = "群组", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JzGroupChat jzGroupChat)
    {
        List<JzGroupChat> list = jzGroupChatService.selectJzGroupChatList(jzGroupChat);
        ExcelUtil<JzGroupChat> util = new ExcelUtil<JzGroupChat>(JzGroupChat.class);
        util.exportExcel(response, list, "群组数据");
    }

    /**
     * 获取群组详细信息
     */
    //@PreAuthorize("@ss.hasPermi('bot:groupChat:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(jzGroupChatService.selectJzGroupChatById(id));
    }

    /**
     * 新增群组
     */
    //@PreAuthorize("@ss.hasPermi('bot:groupChat:add')")
    @Log(title = "群组", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody JzGroupChat jzGroupChat)
    {
        return toAjax(jzGroupChatService.insertJzGroupChat(jzGroupChat));
    }

    /**
     * 修改群组
     */
    //@PreAuthorize("@ss.hasPermi('bot:groupChat:edit')")
    @Log(title = "群组", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JzGroupChat jzGroupChat)
    {
        return toAjax(jzGroupChatService.updateJzGroupChat(jzGroupChat));
    }

    /**
     * 删除群组
     */
    //@PreAuthorize("@ss.hasPermi('bot:groupChat:remove')")
    @Log(title = "群组", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(jzGroupChatService.deleteJzGroupChatByIds(ids));
    }
}
