package com.ruoyi.jzbot.controller;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.jzbot.domain.BizJiebaDict;
import com.ruoyi.jzbot.domain.vo.BizJiebaDictVo;
import com.ruoyi.jzbot.service.IBizJiebaDictService;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * 分词字典Controller
 *
 * <AUTHOR>
 * @date 2024-03-18
 */
@RestController
@RequestMapping("/biz/jiebaDict")
public class BizJiebaDictController extends BaseController {
    @Resource
    private IBizJiebaDictService bizJiebaDictService;

    /**
     * 查询分词字典列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:jiebaDict:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizJiebaDict bizJiebaDict) {
        PageInfo<BizJiebaDictVo> list = bizJiebaDictService.selectBizJiebaDictList(bizJiebaDict);
        return getDataTableVo(list);
    }

    /**
     * 导出分词字典列表
     */
    //@PreAuthorize("@ss.hasPermi('biz:jiebaDict:export')")
    @Log(title = "分词字典", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizJiebaDict bizJiebaDict) {
        PageInfo<BizJiebaDictVo> list = bizJiebaDictService.selectBizJiebaDictList(bizJiebaDict);
        ExcelUtil<BizJiebaDictVo> util = new ExcelUtil<BizJiebaDictVo>(BizJiebaDictVo.class);
        util.exportExcel(response, list.getList(), "分词字典数据");
    }

    /**
     * 获取分词字典详细信息
     */
    //@PreAuthorize("@ss.hasPermi('biz:jiebaDict:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(bizJiebaDictService.selectBizJiebaDictById(id));
    }

    /**
     * 新增分词字典
     */
    //@PreAuthorize("@ss.hasPermi('biz:jiebaDict:add')")
    @Log(title = "分词字典", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizJiebaDict bizJiebaDict) {
        return toAjax(bizJiebaDictService.insertBizJiebaDict(bizJiebaDict));
    }

    /**
     * 修改分词字典
     */
    //@PreAuthorize("@ss.hasPermi('biz:jiebaDict:edit')")
    @Log(title = "分词字典", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizJiebaDict bizJiebaDict) {
        return toAjax(bizJiebaDictService.updateBizJiebaDict(bizJiebaDict));
    }

    /**
     * 删除分词字典
     */
    //@PreAuthorize("@ss.hasPermi('biz:jiebaDict:remove')")
    @Log(title = "分词字典", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(bizJiebaDictService.deleteBizJiebaDictByIds(ids));
    }


    @Log(title = "分词字典", businessType = BusinessType.INSERT)
    @PostMapping("/readFileAndInsertData")
    public AjaxResult readFileAndInsertData() {
        return toAjax(bizJiebaDictService.readFileAndInsertData());
    }
}
