package com.ruoyi.jzbot.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageInfo;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.EJzRecviceMessageType;
import com.ruoyi.common.enums.EJzSendMessageType;
import com.ruoyi.common.enums.ERedisChannel;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.jzbot.config.JuZiConfig;
import com.ruoyi.jzbot.domain.JzBot;
import com.ruoyi.jzbot.domain.JzChatMessage;
import com.ruoyi.jzbot.domain.req.JzChatMessageReq;
import com.ruoyi.jzbot.domain.vo.JzChatMessageVo;
import com.ruoyi.jzbot.enums.EBotLimitType;
import com.ruoyi.jzbot.service.IJzBotService;
import com.ruoyi.jzbot.service.IJzChatMessageService;
import com.ruoyi.jzbot.service.IJzContactService;
import com.ruoyi.jzbot.util.JuZiBotUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 会话记录Controller
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
@RestController
@RequestMapping("/bot/message")
public class JzChatMessageController extends BaseController {
    @Resource
    private IJzChatMessageService jzChatMessageService;

    @Resource
    private IJzBotService jzBotService;

    @Resource
    private IJzContactService jzContactService;
    @Resource
    private RedisCache redisCache;

    @Resource
    private JuZiConfig juZiConfig;

    @Resource
    private JuZiBotUtil juZiBotUtil;

    @Value("${spring.profiles.active}")
    private String active;


    /**
     * 查询会话记录列表
     */
    //@PreAuthorize("@ss.hasPermi('bot:message:list')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody JzChatMessageReq jzChatMessageReq) {
        PageInfo<JzChatMessageVo> list = jzChatMessageService.selectJzChatMessageList(jzChatMessageReq);
        return getDataTableVo(list);
    }


    @PostMapping("/history_list")
    public TableDataInfo historyList(@RequestBody JzChatMessageReq jzChatMessageReq) {
        PageInfo<JzChatMessageVo> list = jzChatMessageService.selectJzChatMessageHistoryList(jzChatMessageReq);
        return getDataTableVo(list);
    }


    /**
     * 导出会话记录列表
     */
    //@PreAuthorize("@ss.hasPermi('bot:message:export')")
    @Log(title = "会话记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody JzChatMessageReq jzChatMessageReq) {
        PageInfo<JzChatMessageVo> list = jzChatMessageService.selectJzChatMessageList(jzChatMessageReq);
        ExcelUtil<JzChatMessageVo> util = new ExcelUtil<JzChatMessageVo>(JzChatMessageVo.class);
        util.exportExcel(response, list.getList(), "会话记录数据");
    }

    /**
     * 获取会话记录详细信息
     */
    //@PreAuthorize("@ss.hasPermi('bot:message:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(jzChatMessageService.selectJzChatMessageById(id));
    }

    /**
     * 新增会话记录
     */
    //@PreAuthorize("@ss.hasPermi('bot:message:add')")
    @Log(title = "会话记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody JzChatMessage jzChatMessage) {
        return toAjax(jzChatMessageService.insertJzChatMessage(jzChatMessage));
    }

    /**
     * 修改会话记录
     */
    //@PreAuthorize("@ss.hasPermi('bot:message:edit')")
    @Log(title = "会话记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JzChatMessage jzChatMessage) {
        return toAjax(jzChatMessageService.updateJzChatMessage(jzChatMessage));
    }

    /**
     * 删除会话记录
     */
    //@PreAuthorize("@ss.hasPermi('bot:message:remove')")
    @Log(title = "会话记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(jzChatMessageService.deleteJzChatMessageByIds(ids));
    }

    /**
     * 对外开放接受juzi聊天记录
     */
    @Anonymous
    @PostMapping("/message")
    public AjaxResult chatRecord(@RequestBody JSONObject params) {
        logger.info("句子互动信息推送 params ={}", JSONObject.toJSONString(params));
        if (!params.isEmpty()) {
            if (params.getJSONObject("data").getInteger("type") > 100) {
                return toAjax(true);
            }
            //不通知了
            redisCache.publish(ERedisChannel.CHAT_CHANNEL.getCode(), params.toJSONString());

        } else {
            // 处理"data"不存在或为null的情况
            logger.info("句子互动信息为空");
            return toAjax(false);
        }
        return toAjax(true);
    }

    @Anonymous
    @PostMapping("/sentResult")
    public AjaxResult sentResult(@RequestBody JSONObject params) {
        logger.info("句子互动信息发送结果 params ={}", JSONObject.toJSONString(params));
        if (!params.containsKey("orgId")){
            //非队列发生消息结果,不处理
            return toAjax(true);
        }
        String requestId = params.getString("externalRequestId");
        if (StringUtils.isBlank(requestId)){
            return toAjax(true);
        }
        Map<String, Object> cacheMap = redisCache.getCacheMap(requestId);
        if (cacheMap==null || cacheMap.isEmpty()){
            return toAjax(true);
        }
        redisCache.deleteObject(requestId);

        String messageId = params.getString("messageId");
        redisCache.setCacheMap("msg_ref:"+messageId,cacheMap);

        return toAjax(true);
    }



    @Anonymous
    @PostMapping("/getArtworkImage")
    public AjaxResult getArtworkImage(@RequestBody JSONObject params) {
        params.put("token",juZiConfig.getGroupToken());

        return success(juZiBotUtil.getArtworkImage(params));
    }



    public static final int MAX_WAIT_TIME = 10; // 最大等待时间（秒）
    public static final int WAIT_INTERVAL = 3; // 每次检查新消息的等待时间（秒）

    private static long lastReceivedTime = System.currentTimeMillis();

    private void scheduleMessageProcessing(String userId) {
        Timer timer = new Timer();
        TimerTask task = new TimerTask() {
            private long startTime = System.currentTimeMillis();

            private int count = 0;

            @Override
            public void run() {
                System.out.println("第" + count + "次检查新消息" + new Date(lastReceivedTime));
                count++;

                long currentTime = System.currentTimeMillis();

                if ((currentTime - startTime) / 1000 >= MAX_WAIT_TIME || (currentTime - lastReceivedTime) / 1000 >= WAIT_INTERVAL) {
                    System.out.println("任务完成");

                    timer.cancel(); // 任务完成后取消定时器
                }
            }
        };
        timer.schedule(task, WAIT_INTERVAL * 1000, WAIT_INTERVAL * 1000); // 每隔WAIT_INTERVAL秒检查一次
    }


    /**
     * 对外开放bot增加好友回调
     */
    @Anonymous
    @PostMapping("/add/customer/callback")
    public Map<String, Integer> addCustomerCallback(@RequestBody JSONObject params) {
        logger.info("增加好友回调推送 params ={}", JSONObject.toJSONString(params));

        String wxid = params.getString("imContactId");
        String botId = params.getJSONObject("botInfo").getString("botId");
        String botWxid = params.getJSONObject("botInfo").getString("imBotId");

        JzBot bot = new JzBot();
        bot.setBotId(botId);
        bot.setWxid(botWxid);

        jzContactService.newFriendHandle(bot, wxid);


        //{
        //	"imContactId": "7881302368095873",
        //	"name": "皮蛋瘦肉周",
        //	"avatar": "http://wx.qlogo.cn/mmhead/xMlaDO55bABCU2EiaYBIzKvmaJ5wErAibPtND4sDd7erxu325xqZ51efW17ld9jYdDnZibRpkUYtmg/0",
        //	"gender": 1,
        //	"createTimestamp": 1715933751000,
        //	"imInfo": {
        //		"externalUserId": "wmdiZoLAAAFHQSI351Saw7gUsjWtL2ng",
        //		"followUser": {
        //			"wecomUserId": "alex"
        //		}
        //	},
        //	"botInfo": {
        //		"botId": "6561e7cf37a14a4356a39718",
        //		"imBotId": "1688858438490593",
        //		"name": "芙宁娜",
        //		"avatar": "https://wework.qpic.cn/wwpic/758927_QOzQT_DST028b-p_1700830911/0"
        //	}
        //}


        Map<String, Integer> map = new HashMap<>();
        map.put("errCode", 0);
        return map;
    }


    /**
     * 对外开放bot增加好友回调
     */
    @Anonymous
    @PostMapping("/bot_limit/callback")
    public Map<String, Integer> botLimitCallback(@RequestBody JSONObject params) {
        logger.info("bot账号被限制回调推送 params ={}", JSONObject.toJSONString(params));
        //{
        //    "token": "string",
        //    "botId": "string",
        //    "groupId": "string",
        //    "restrictType": 0
        //}
            //发送消息被限制
        if (params.getInteger("restrictType") == EBotLimitType.SEND_MESSAGE.getCode()) {
            String botId = params.getString("botId");
            JzBot bot = new JzBot();
            bot.setBotId(botId);
            //启用状态  1 启用中  2 手动暂停 3自动暂停
            bot.setStatus("3");
            jzBotService.updateJzBot(bot);
        }

        Map<String, Integer> map = new HashMap<>();
        map.put("errCode", 0);
        return map;
    }




    /**
     * 对外开放接受juzi聊天记录
     */
    @GetMapping("/getMiniProgramMessage")
    public TableDataInfo getMiniProgramMessage(@RequestParam(value = "type", required = true) Integer type,
                                               @RequestParam(value = "pageNum", required = true) Integer pageNum,
                                               @RequestParam(value = "pageSize", required = true) Integer pageSize) {

        JzChatMessageReq messageReq = new JzChatMessageReq();
        messageReq.setPageSize(pageSize);
        messageReq.setPageNum(pageNum);

        messageReq.setIsSelf(0);
        //todo 标签群 素材
        if (type.equals(EJzSendMessageType.APPLET.getCode())) {
            //小程序
            messageReq.setMessageType(EJzRecviceMessageType.MINI_PROGRAM.getCode());
        } else if (type.equals(EJzSendMessageType.CHANNEL.getCode())) {
            messageReq.setMessageType(EJzRecviceMessageType.CHANNEL.getCode());
        }


        return getDataTableVo(jzChatMessageService.selectJzChatMessageList(messageReq));
    }


}
