package com.ruoyi.jzbot.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.jzbot.domain.JzContact;
import com.ruoyi.jzbot.domain.dto.JzContactDto;
import com.ruoyi.jzbot.domain.req.JzAllContactReq;
import com.ruoyi.jzbot.service.IJzContactService;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 联系人Controller
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@RestController
@RequestMapping("/bot/contact")
public class JzContactController extends BaseController
{
    @Resource
    private IJzContactService jzContactService;

    /**
     * 查询控制台所有联系人列表
     */
    @GetMapping("/list_all")
    public TableDataInfo listAll(JzAllContactReq req)
    {
        startPage();
        List<JzContactDto> list = jzContactService.listAll(req);
        return getDataTable(list);
    }

    /**
     * 查询联系人列表
     */
    //@PreAuthorize("@ss.hasPermi('bot:contact:list')")
    @GetMapping("/list")
    public TableDataInfo list(JzContact jzContact)
    {
        startPage();
        List<JzContact> list = jzContactService.selectJzContactList(jzContact);
        return getDataTable(list);
    }

    /**
     * 导出联系人列表
     */
    //@PreAuthorize("@ss.hasPermi('bot:contact:export')")
    @Log(title = "联系人", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JzContact jzContact)
    {
        List<JzContact> list = jzContactService.selectJzContactList(jzContact);
        ExcelUtil<JzContact> util = new ExcelUtil<JzContact>(JzContact.class);
        util.exportExcel(response, list, "联系人数据");
    }

    /**
     * 获取联系人详细信息
     */
    //@PreAuthorize("@ss.hasPermi('bot:contact:query')")
    @GetMapping(value = "/{chatId}")
    public AjaxResult getInfo(@PathVariable("chatId") String chatId)
    {
        return success(jzContactService.selectJzContactById(chatId));
    }

    /**
     * 新增联系人
     */
    //@PreAuthorize("@ss.hasPermi('bot:contact:add')")
    @Log(title = "联系人", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody JzContact jzContact)
    {
        return toAjax(jzContactService.insertJzContact(jzContact));
    }

    /**
     * 修改联系人
     */
    //@PreAuthorize("@ss.hasPermi('bot:contact:edit')")
    @Log(title = "联系人", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JzContact jzContact)
    {
        return toAjax(jzContactService.updateJzContact(jzContact));
    }

    /**
     * 删除联系人
     */
    //@PreAuthorize("@ss.hasPermi('bot:contact:remove')")
    @Log(title = "联系人", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(jzContactService.deleteJzContactByIds(ids));
    }

}
