package com.ruoyi.jzbot.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.jzbot.domain.JzRoomMembers;
import com.ruoyi.jzbot.service.IJzRoomMembersService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 群成员Controller
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@RestController
@RequestMapping("/bot/members")
public class JzRoomMembersController extends BaseController
{
    @Resource
    private IJzRoomMembersService jzRoomMembersService;

    /**
     * 查询群成员列表
     */
    //@PreAuthorize("@ss.hasPermi('bot:members:list')")
    @GetMapping("/list")
    public TableDataInfo list(JzRoomMembers jzRoomMembers)
    {
        startPage();
        List<JzRoomMembers> list = jzRoomMembersService.selectJzRoomMembersList(jzRoomMembers);
        return getDataTable(list);
    }

    /**
     * 导出群成员列表
     */
    //@PreAuthorize("@ss.hasPermi('bot:members:export')")
    @Log(title = "群成员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JzRoomMembers jzRoomMembers)
    {
        List<JzRoomMembers> list = jzRoomMembersService.selectJzRoomMembersList(jzRoomMembers);
        ExcelUtil<JzRoomMembers> util = new ExcelUtil<JzRoomMembers>(JzRoomMembers.class);
        util.exportExcel(response, list, "群成员数据");
    }

    /**
     * 获取群成员详细信息
     */
    //@PreAuthorize("@ss.hasPermi('bot:members:query')")
    @GetMapping(value = "/{wxid}")
    public AjaxResult getInfo(@PathVariable("wxid") String wxid)
    {
        return success(jzRoomMembersService.selectJzRoomMembersByWxid(wxid));
    }

    /**
     * 新增群成员
     */
    //@PreAuthorize("@ss.hasPermi('bot:members:add')")
    @Log(title = "群成员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody JzRoomMembers jzRoomMembers)
    {
        return toAjax(jzRoomMembersService.insertJzRoomMembers(jzRoomMembers));
    }

    /**
     * 修改群成员
     */
    //@PreAuthorize("@ss.hasPermi('bot:members:edit')")
    @Log(title = "群成员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JzRoomMembers jzRoomMembers)
    {
        return toAjax(jzRoomMembersService.updateJzRoomMembers(jzRoomMembers));
    }

    /**
     * 删除群成员
     */
    //@PreAuthorize("@ss.hasPermi('bot:members:remove')")
    @Log(title = "群成员", businessType = BusinessType.DELETE)
	@DeleteMapping("/{wxids}")
    public AjaxResult remove(@PathVariable String[] wxids)
    {
        return toAjax(jzRoomMembersService.deleteJzRoomMembersByWxids(wxids));
    }
}
