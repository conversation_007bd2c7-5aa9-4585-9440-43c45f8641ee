package com.ruoyi.jzbot.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.jzbot.domain.JzRoom;
import com.ruoyi.jzbot.service.IJzRoomService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 群Controller
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@RestController
@RequestMapping("/bot/room")
public class JzRoomController extends BaseController
{
    @Resource
    private IJzRoomService jzRoomService;

    /**
     * 查询群列表
     */
//    //@PreAuthorize("@ss.hasPermi('bot:room:list')")
    @GetMapping("/list")
    public TableDataInfo list(JzRoom jzRoom)
    {
        startPage();
        List<JzRoom> list = jzRoomService.selectJzRoomList(jzRoom);
        return getDataTable(list);
    }

    /**
     * 导出群列表
     */
    //@PreAuthorize("@ss.hasPermi('bot:room:export')")
    @Log(title = "群", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JzRoom jzRoom)
    {
        List<JzRoom> list = jzRoomService.selectJzRoomList(jzRoom);
        ExcelUtil<JzRoom> util = new ExcelUtil<JzRoom>(JzRoom.class);
        util.exportExcel(response, list, "群数据");
    }

    /**
     * 获取群详细信息
     */
    //@PreAuthorize("@ss.hasPermi('bot:room:query')")
    @GetMapping(value = "/{roomId}")
    public AjaxResult getInfo(@PathVariable("roomId") String roomId)
    {
        return success(jzRoomService.selectJzRoomByChatId(roomId));
    }

    /**
     * 新增群
     */
    //@PreAuthorize("@ss.hasPermi('bot:room:add')")
    @Log(title = "群", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody JzRoom jzRoom)
    {
        return toAjax(jzRoomService.insertJzRoom(jzRoom));
    }

    /**
     * 修改群
     */
    //@PreAuthorize("@ss.hasPermi('bot:room:edit')")
    @Log(title = "群", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JzRoom jzRoom)
    {
        return toAjax(jzRoomService.updateJzRoom(jzRoom));
    }

    /**
     * 删除群
     */
    //@PreAuthorize("@ss.hasPermi('bot:room:remove')")
    @Log(title = "群", businessType = BusinessType.DELETE)
	@DeleteMapping("/{roomIds}")
    public AjaxResult remove(@PathVariable String[] roomIds)
    {
        return toAjax(jzRoomService.deleteJzRoomByRoomIds(roomIds));
    }

    /**
     * 状态修改
     */
    //@PreAuthorize("@ss.hasPermi('bot:room:edit')")
    @Log(title = "群", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody JzRoom room) {
        return toAjax(jzRoomService.updateJzRoom(room));
    }



}
