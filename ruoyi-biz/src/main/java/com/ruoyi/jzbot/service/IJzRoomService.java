package com.ruoyi.jzbot.service;

import java.util.List;
import com.ruoyi.jzbot.domain.JzRoom;

/**
 * 群Service接口
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
public interface IJzRoomService
{
    /**
     * 查询群
     *
     * @param roomId 群主键
     * @return 群
     */
    public JzRoom selectJzRoomByChatId(String roomId);

    /**
     * 查询群列表
     *
     * @param jzRoom 群
     * @return 群集合
     */
    public List<JzRoom> selectJzRoomList(JzRoom jzRoom);

    /**
     * 新增群
     *
     * @param jzRoom 群
     * @return 结果
     */
    public int insertJzRoom(JzRoom jzRoom);

    /**
     * 修改群
     *
     * @param jzRoom 群
     * @return 结果
     */
    public int updateJzRoom(JzRoom jzRoom);

    /**
     * 批量删除群
     *
     * @param roomIds 需要删除的群主键集合
     * @return 结果
     */
    public int deleteJzRoomByRoomIds(String[] roomIds);

    /**
     * 删除群信息
     *
     * @param roomId 群主键
     * @return 结果
     */
    public int deleteJzRoomByChatId(String roomId);

    /**
     * 获取所有群组
     */
    void pullJuZiRooms();

    List<JzRoom> selectJzRoomListSimple(JzRoom jzRoom);

    List<JzRoom> selectJzRoomByWxId(String wxid);

    /**
     * 根据微信号列表查询群列表
     * @param wxidList 微信号列表
     * @return 群列表
     */
    List<JzRoom> selectJzRoomListByWxIds(List<String> wxidList);

    /**
     * 根据chatId列表查询群列表
     * @param chatIds chatId列表
     * @return 群列表
     */
    List<JzRoom> selectJzRoomListByChatIds(List<String> chatIds);

    /**
     * 查询正常的群
     * @param code 群组类型编码
     * @return 群列表
     */
    List<JzRoom> selectActiveRooms(String code);
}
