package com.ruoyi.jzbot.service.impl;

import java.io.*;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;

import com.ruoyi.jzbot.domain.BizJiebaDict;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import com.ruoyi.jzbot.mapper.BizStopWordMapper;
import com.ruoyi.jzbot.domain.BizStopWord;
import com.ruoyi.jzbot.service.IBizStopWordService;

/**
 * jieba过滤词典Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-19
 */
@Service
@Slf4j
public class BizStopWordServiceImpl implements IBizStopWordService {
    @Resource
    private BizStopWordMapper bizStopWordMapper;

    /**
     * 查询jieba过滤词典
     *
     * @param id jieba过滤词典主键
     * @return jieba过滤词典
     */
    @Override
    public BizStopWord selectBizStopWordById(Long id) {
        return bizStopWordMapper.selectBizStopWordById(id);
    }

    /**
     * 查询jieba过滤词典列表
     *
     * @param bizStopWord jieba过滤词典
     * @return jieba过滤词典
     */
    @Override
    public List<BizStopWord> selectBizStopWordList(BizStopWord bizStopWord) {
        return bizStopWordMapper.selectBizStopWordList(bizStopWord);
    }

    /**
     * 新增jieba过滤词典
     *
     * @param bizStopWord jieba过滤词典
     * @return 结果
     */
    @Override
    public int insertBizStopWord(BizStopWord bizStopWord) {
        int count = bizStopWordMapper.insertBizStopWord(bizStopWord);
        if (count > 0) {
            String dictFilePath = BizJiebaDictServiceImpl.class.getResource("/file/stop_words.txt").getPath();
            try (FileWriter writer = new FileWriter(dictFilePath, true)) {
                writer.append(bizStopWord.getKeyword());
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return count;
    }

    /**
     * 修改jieba过滤词典
     *
     * @param bizStopWord jieba过滤词典
     * @return 结果
     */
    @Override
    public int updateBizStopWord(BizStopWord bizStopWord) {
        return bizStopWordMapper.updateBizStopWord(bizStopWord);
    }

    /**
     * 批量删除jieba过滤词典
     *
     * @param ids 需要删除的jieba过滤词典主键
     * @return 结果
     */
    @Override
    public int deleteBizStopWordByIds(Long[] ids) {
        int count = bizStopWordMapper.deleteBizStopWordByIds(ids);
        if (count > 0) {
            for (Long id : ids) {
                BizStopWord bizStopWord = bizStopWordMapper.selectBizStopWordById(id);
                removeBizJiebaDictFromFile(bizStopWord);
            }
        }
        return count;
    }

    public static void removeBizJiebaDictFromFile(BizStopWord bizJiebaDict) {
        String dictFilePath = BizJiebaDictServiceImpl.class.getClassLoader().getResource("file/dict.txt").getPath();

        File inputFile = new File(dictFilePath);
        File tempFile = new File(inputFile.getAbsolutePath() + ".tmp");
        try (BufferedReader reader = new BufferedReader(new FileReader(inputFile)); BufferedWriter writer = new BufferedWriter(new FileWriter(tempFile))) {
            String currentLine;
            while ((currentLine = reader.readLine()) != null) {
                String trimmedLine = currentLine.trim();
                if (trimmedLine.equals(bizJiebaDict.getKeyword())) continue;
                writer.write(currentLine + System.getProperty("line.separator"));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        if (!inputFile.delete()) {
            log.error("Could not delete original file");
            return;
        }
        if (!tempFile.renameTo(inputFile)) {
            log.error("Could not rename temp file to original file");
        }
    }

    /**
     * 删除jieba过滤词典信息
     *
     * @param id jieba过滤词典主键
     * @return 结果
     */
    @Override
    public int deleteBizStopWordById(Long id) {
        return bizStopWordMapper.deleteBizStopWordById(id);
    }

    @Override
    public boolean readFileAndInsertData() {
        String dictFilePath = BizJiebaDictServiceImpl.class.getClassLoader().getResource("file/stop_words.txt").getPath();
        try (BufferedReader reader = new BufferedReader(new FileReader(dictFilePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                BizStopWord jiebaDict = new BizStopWord();
                jiebaDict.setKeyword(line);
                jiebaDict.setCreateDatetime(new Date());
                bizStopWordMapper.insertBizStopWord(jiebaDict);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }
}
