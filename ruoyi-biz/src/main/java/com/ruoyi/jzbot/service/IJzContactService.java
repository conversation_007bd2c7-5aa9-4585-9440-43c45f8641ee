package com.ruoyi.jzbot.service;

import java.util.List;

import com.ruoyi.jzbot.domain.JzBot;
import com.ruoyi.jzbot.domain.JzContact;
import com.ruoyi.jzbot.domain.dto.JzContactDto;
import com.ruoyi.jzbot.domain.req.JzAllContactReq;

/**
 * 联系人Service接口
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
public interface IJzContactService {
    /**
     * 查询联系人
     *
     * @param chatId 联系人主键
     * @return 联系人
     */
    public JzContact selectJzContactById(String chatId);

    /**
     * 查询联系人
     *
     * @param unionId
     * @return 联系人
     */
    List<JzContact> selectJzContactByUnionId(String unionId);

    /**
     * 查询联系人列表
     *
     * @param jzContact 联系人
     * @return 联系人集合
     */
    public List<JzContact> selectJzContactList(JzContact jzContact);

    /**
     * 新增联系人
     *
     * @param jzContact 联系人
     * @return 结果
     */
    public int insertJzContact(JzContact jzContact);

    /**
     * 修改联系人
     *
     * @param jzContact 联系人
     * @return 结果
     */
    public int updateJzContact(JzContact jzContact);

    /**
     * 批量删除联系人
     *
     * @param ids 需要删除的联系人主键集合
     * @return 结果
     */
    public int deleteJzContactByIds(String[] ids);

    /**
     * 删除联系人信息
     *
     * @param id 联系人主键
     * @return 结果
     */
    public int deleteJzContactById(Long id);

    /**
     * 拉取联系人
     */
    void pullJuZiContacts();

    /**
     * 获取联系人的UnionId
     */
    void getContactsUnionId();

    void getUnionId(List<JzContact> list, boolean isHandle);

    List<JzContact> selectJzContactListSimple(JzContact conditionJz);

    /**
     * 更新联系人配置缓存
     *
     * @param chatId chatId
     * @return
     */
    void chatConfig(String chatId);

    /**
     * 新朋友通知处理
     *
     * @param botId
     * @param wxid
     */
    void newFriendHandle(JzBot botId, String wxid);

    /**
     * 获取用户信息
     *
     * @param wxid 微信id
     * @return 用户信息
     */
    List<JzContact> selectJzContactListByWxId(String wxid);


    List<JzContactDto> listAll(JzAllContactReq req);

    /**
     * 根据微信获取用户信息
     * @param externalUserIdList 微信id列表
     * @return 用户信息
     */
    List<JzContact> selectJzContactListByWeixinIds(List<String> externalUserIdList);

    /**
     * 根据微信id获取用户信息
     * @param wxidList  微信id列表
     * @return 用户信息
     */
    List<JzContact> selectJzContactListByWxIds(List<String> wxidList);

    /**
     * 根据chatId获取用户信息,批量获取
     * @param chatIds chatId列表
     * @return 用户信息
     */
    List<JzContact> selectJzConcatListByChatIds(List<String> chatIds);

    List<JzContact>  getWaitActiveUser();
}
