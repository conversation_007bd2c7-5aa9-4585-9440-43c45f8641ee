package com.ruoyi.jzbot.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.jzbot.domain.JzBot;
import com.ruoyi.jzbot.domain.JzContact;
import com.ruoyi.jzbot.domain.JzRoom;
import com.ruoyi.jzbot.enums.ERoomStatus;
import com.ruoyi.jzbot.mapper.JzBotMapper;
import com.ruoyi.jzbot.service.IJzBotService;
import com.ruoyi.jzbot.service.IJzContactService;
import com.ruoyi.jzbot.service.IJzRoomService;
import com.ruoyi.jzbot.util.FsBotUtil;
import com.ruoyi.jzbot.util.JuZiBotUtil;
import com.ruoyi.system.domain.SysConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.LinkedHashMap;

/**
 * bot查看Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@Service
public class JzBotServiceImpl implements IJzBotService {
    private static final Logger logger = LoggerFactory.getLogger(JzBotServiceImpl.class);

    @Resource
    private JzBotMapper jzBotMapper;

    @Resource
    private IJzRoomService jzRoomService;

    @Resource
    private IJzContactService jzContactService;

    @Resource
    private JuZiBotUtil juZiBotUtil;

    @Resource
    private RedisCache redisCache;

    @Resource
    private FsBotUtil fsBotUtil;

    /**
     * 查询bot查看
     *
     * @param botId bot查看主键
     * @return bot查看
     */
    @Override
    public JzBot selectJzBotByBotId(String botId) {

        JzBot bot =  redisCache.getCacheObject(CacheConstants.BOT_KEY.concat(botId));
        if (bot != null) {
            return bot;
        }

        bot = jzBotMapper.selectJzBotByBotId(botId);
        if (bot == null) {
            return null;
        }
        redisCache.setCacheObject(CacheConstants.BOT_KEY.concat(botId), bot);

        return bot;
    }

    /**
     * 查询bot查看列表
     *
     * @param jzBot bot查看
     * @return bot查看
     */
    @Override
    public List<JzBot> selectJzBotList(JzBot jzBot) {
        List<JzBot> botList = jzBotMapper.selectJzBotList(jzBot);

        for (JzBot bot : botList) {
            //获取所有群
            init(bot);
        }

        return botList;
    }

    /**
     * 拿到bot，群数量，好友数量
     *
     * @param bot
     */
    private void init(JzBot bot) {
        JzRoom room = new JzRoom();
        room.setBotId(bot.getBotId());
        List<JzRoom> roomList = jzRoomService.selectJzRoomListSimple(room);
        Map<String, List<JzRoom>> collect = roomList.stream().collect(Collectors.groupingBy(JzRoom::getStatus));

        bot.setOpenCount(0);
        bot.setTotalCount(roomList.size());
        if (collect.containsKey(ERoomStatus.STATUS_1.getCode())) {
            bot.setOpenCount(collect.get(ERoomStatus.STATUS_1.getCode()).size());
        }
        //查询bot好友数量
        JzContact condition = new JzContact();
        condition.setBotId(bot.getBotId());
        condition.setDeleted("0");
        List<JzContact> contactList = jzContactService.selectJzContactListSimple(condition);
        bot.setFriendsNumber(contactList.size());
        bot.setSurplusFriendsNumber(15000 - contactList.size());
    }


    @Override
    public List<JzBot> selectJzBotListSimple(JzBot jzBot) {
        return jzBotMapper.selectJzBotList(jzBot);
    }


    /**
     * 新增bot查看
     *
     * @param jzBot bot查看
     * @return 结果
     */
    @Override
    public int insertJzBot(JzBot jzBot) {
        return jzBotMapper.insertJzBot(jzBot);
    }

    /**
     * 修改bot查看
     *
     * @param jzBot bot查看
     * @return 结果
     */
    @Override
    public int updateJzBot(JzBot jzBot) {
        redisCache.deleteObject(CacheConstants.BOT_KEY.concat(jzBot.getBotId()));
        return jzBotMapper.updateJzBot(jzBot);
    }

    /**
     * 批量删除bot查看
     *
     * @param botIds 需要删除的bot查看主键
     * @return 结果
     */
    @Override
    public int deleteJzBotByBotIds(String[] botIds) {
        return jzBotMapper.deleteJzBotByBotIds(botIds);
    }

    /**
     * 删除bot查看信息
     *
     * @param botId bot查看主键
     * @return 结果
     */
    @Override
    public int deleteJzBotByBotId(String botId) {
        return jzBotMapper.deleteJzBotByBotId(botId);
    }
    @Override
    public void pullJuZiBots() {
        List<JzBot> botList = juZiBotUtil.getBots();

        if (CollectionUtils.isEmpty(botList)) {
            return;
        }

        // 检查每个bot的在线状态
        for (JzBot bot : botList) {
            String botId = bot.getBotId();
            String online = bot.getOnline();
            String nickname = bot.getNickname();
            String weixin = bot.getWeixin();

            // 从Redis中获取上一次的在线状态
            String lastStatus = redisCache.getCacheObject(CacheConstants.BOT_ONLINE_STATUS + botId);

            // 如果上一次是在线,这一次离线,才发送通知
            if ("0".equals(online) && !"0".equals(lastStatus)) {
                // 构建飞书通知消息
                Map<String, Object> statsMap = new LinkedHashMap<>();
                statsMap.put("Bot ID", botId);
                statsMap.put("昵称", nickname); 
                statsMap.put("微信", weixin);
                statsMap.put("当前状态", "离线");
                statsMap.put("当前时间", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
                // 发送飞书通知
                try {
                    fsBotUtil.sendFsStatsCardMessage("Bot离线提醒", statsMap);
                } catch (Exception e) {
                    logger.error("发送飞书通知失败", e);
                }
            }
            // 更新Redis中的状态
            redisCache.setCacheObject(CacheConstants.BOT_ONLINE_STATUS + botId, online);
        }

        jzBotMapper.batchAdd(botList);
    }


}
