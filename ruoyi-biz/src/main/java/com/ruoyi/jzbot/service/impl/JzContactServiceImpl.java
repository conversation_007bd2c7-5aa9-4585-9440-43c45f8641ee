package com.ruoyi.jzbot.service.impl;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.controller.JzMessageSendController;
import com.ruoyi.biz.domain.BizNewContactOperatePlan;
import com.ruoyi.biz.domain.BizUser;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.biz.enums.EOperatePointType;
import com.ruoyi.biz.enums.ETaggableRefType;
import com.ruoyi.biz.mapper.BizUserMapper;
import com.ruoyi.biz.service.*;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.ConfigConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.ERedisChannel;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.jzbot.config.JuZiConfig;
import com.ruoyi.jzbot.domain.JzBot;
import com.ruoyi.jzbot.domain.dto.JzContactDto;
import com.ruoyi.jzbot.domain.req.JzAllContactReq;
import com.ruoyi.jzbot.service.IJzBotService;
import com.ruoyi.jzbot.util.JuZiBotUtil;
import com.ruoyi.jzbot.util.QWUtil;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.traffic.component.TrafficComponent;
import com.ruoyi.traffic.enums.CreditTypeEnum;
import com.ruoyi.traffic.service.ITrafficServiceOrderService;
import com.ruoyi.weimo.service.IWeiMobService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import com.ruoyi.jzbot.mapper.JzContactMapper;
import com.ruoyi.jzbot.domain.JzContact;
import com.ruoyi.jzbot.service.IJzContactService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 联系人Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Service
public class JzContactServiceImpl implements IJzContactService {
    private static final Logger log = LoggerFactory.getLogger(JzContactServiceImpl.class);

    @Resource
    private JzContactMapper jzContactMapper;

    @Resource
    private JuZiBotUtil juZiBotUtil;

    @Resource
    private IBizUserService bizUserService;

    @Resource
    private IJzBotService jzBotService;

    @Resource
    private RedisCache redisCache;

    @Resource
    private RedisLock redisLock;

    @Resource
    private QWUtil qwUtil;

    @Resource
    private JuZiConfig juZiConfig;

    @Resource
    private IWeiMobService weiMobService;

    @Resource
    private BizUserMapper bizUserMapper;

    @Resource
    private TrafficComponent trafficComponent;

    @Resource
    private ISysConfigService sysConfigService;
    /**
     * 查询联系人
     *
     * @param chatId 联系人主键
     * @return 联系人
     */
    @Override
    public JzContact selectJzContactById(String chatId) {
        return jzContactMapper.selectJzContactById(chatId);
    }

    @Override
    public List<JzContact> selectJzContactByUnionId(String unionId) {
        return jzContactMapper.selectJzContactByUnionId(unionId);
    }

    /**
     * 查询联系人列表
     *
     * @param jzContact 联系人
     * @return 联系人
     */
    @Override
    public List<JzContact> selectJzContactList(JzContact jzContact) {
        return jzContactMapper.selectJzContactList(jzContact);
    }

    /**
     * 新增联系人
     *
     * @param jzContact 联系人
     * @return 结果
     */
    @Override
    public int insertJzContact(JzContact jzContact) {
        return jzContactMapper.insertJzContact(jzContact);
    }

    /**
     * 修改联系人
     *
     * @param jzContact 联系人
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateJzContact(JzContact jzContact) {
        int rows = jzContactMapper.updateJzContact(jzContact);
        // 处理AI服务包逻辑
        if (sysConfigService.isOpen(ConfigConstants.AI_TRAFFIC_SWITCH)) {
            handleAiPacket(jzContact);
        }
        chatConfig(jzContact.getChatId());
        return rows;
    }

    /**
     * 处理AI服务包逻辑
     * @param jzContact
     */
    private void handleAiPacket(JzContact jzContact) {
        if (jzContact.getStatus() != null) {
            boolean enableAI = EBoolean.YES.getCode().equals(jzContact.getStatus());
            // 开启时跳过服务处理，直接开启，由定时任务被动处理
            if (enableAI) return;
            trafficComponent.handleAIServicePackage(
                    jzContact.getChatId(),
                    CreditTypeEnum.USER.getCode(),
                    enableAI
            );
        }
    }

    /**
     * 批量删除联系人
     *
     * @param ids 需要删除的联系人主键
     * @return 结果
     */
    @Override
    public int deleteJzContactByIds(String[] ids) {
        return jzContactMapper.deleteJzContactByIds(ids);
    }

    /**
     * 删除联系人信息
     *
     * @param id 联系人主键
     * @return 结果
     */
    @Override
    public int deleteJzContactById(Long id) {
        return jzContactMapper.deleteJzContactById(id);
    }

    @Override
    public void pullJuZiContacts() {
        JzBot bot = new JzBot();
        List<JzBot> botList = jzBotService.selectJzBotListSimple(bot);
        Map<String, List<JzBot>> map = botList.stream().collect(Collectors.groupingBy(JzBot::getToken));

        List<String> contactList = new ArrayList<>();

        map.forEach((token, bots) -> {
            List<JzContact> list = juZiBotUtil.getContacts(token, bots, null);
            jzContactMapper.createOrUpdate(list);
            contactList.addAll(list.stream().map(JzContact::getChatId).collect(Collectors.toList()));

            //配置
            list.forEach(user -> chatConfig(user.getChatId()));
        });

        if (CollectionUtils.isEmpty(contactList)) {
            return;
        }

        jzContactMapper.checkDeleted(contactList);

    }

    @Override
    public void getContactsUnionId() {
        //拉取所有没有unionId的联系人
        List<JzContact> list = jzContactMapper.selectNonUnionIdList();
        if (CollectionUtils.isNotEmpty(list)) {
            // 处理联系人unionId, 并创建biz_user
            getUnionId(list, true);
        }

        // 拉取没有wid的biz_user
        List<BizUser> nonWidList = bizUserMapper.selectNonWidList();
        // 处理biz_user的微盟信息
        handleNonWidUser(nonWidList);

    }

    @Override
    public void getUnionId(List<JzContact> list, boolean isHandle) {
        for (JzContact contact : list) {
            try {
                String unionId = contact.getUnionId();

                if (StringUtils.isBlank(contact.getUnionId())) {
                    JzContact update = new JzContact();
                    unionId = qwUtil.getContactUnionId(contact.getWeixin());
                    update.setChatId(contact.getChatId());
                    update.setUnionId(unionId);
                    contact.setUnionId(unionId);
                    jzContactMapper.updateJzContact(update);
                }

                //绑定用户操作
                bizUserService.handleBindUser("wx", contact.getWxid(), unionId, contact.getNickname(), contact.getAvatarUrl(), contact.getFriendTime());

                //拉取不做新用户处理
                if (isHandle) {
                    handleNewFriend(contact);
                }
            } catch (Exception e) {
                log.error("获取联系人UnionId异常", e);
            }
        }
    }

    private void handleNonWidUser(List<BizUser> nonWidList) {
        for (BizUser bizUser : nonWidList) {
            try {
                // 获取当前微信用户的wid
                String wid = weiMobService.getWmIdByUnionId(bizUser.getUnionId());
                if (bizUser.getWid() == null && StringUtils.isNotBlank(wid)) {
                    bizUser.setWid(Long.valueOf(wid));
                    bizUserService.updateBizUser(bizUser);
                }
            } catch (Exception e) {
                log.error("处理biz user wid失败userid:{}: ", bizUser.getId(), e);
            }
        }
    }

    @Override
    public List<JzContact> selectJzContactListSimple(JzContact conditionJz) {
        return jzContactMapper.selectJzContactListSimple(conditionJz);
    }

    @Override
    public void newFriendHandle(JzBot bot, String wxid) {
        JzContact condition = new JzContact();
        condition.setWxid(wxid);
        condition.setBotId(bot.getBotId());
        List<JzContact> contacts = selectJzContactListSimple(condition);
        //没有拉取到
        if (CollectionUtils.isEmpty(contacts)) {
            contacts = juZiBotUtil.getContacts(juZiConfig.getGroupToken(), Collections.singletonList(bot), wxid);
        }

        if (CollectionUtils.isEmpty(contacts)) {
            return;
        }
        jzContactMapper.createOrUpdate(contacts);
        getUnionId(contacts, true);
    }

    @Override
    public List<JzContact> selectJzContactListByWxId(String wxid) {
        return jzContactMapper.selectJzContactListByWxId(wxid);
    }

    @Override
    public List<JzContactDto> listAll(JzAllContactReq req) {
        Collection<String> keys = redisCache.keys(CacheConstants.TAKE_OVER_CHAT + "*");
        List<String> chatIdList = new ArrayList<>();
        for (String key : keys) {
            String chatId = key.split(CacheConstants.TAKE_OVER_CHAT)[1];
            chatIdList.add(chatId);
        }
        req.setChatIdListStr(StringUtils.join(chatIdList, ","));
        List<JzContactDto> dtoList = jzContactMapper.listAll(req);
        if (CollectionUtils.isNotEmpty(chatIdList)) {
            for (JzContactDto jzContactDto : dtoList) {
                jzContactDto.setIsTop(req.getChatIdListStr().contains(jzContactDto.getChatId()));
            }
        }
        return dtoList;
    }

    @Override
    public List<JzContact> selectJzContactListByWeixinIds(List<String> externalUserIdList) {
        if (CollectionUtils.isEmpty(externalUserIdList)) return Collections.emptyList();
        return jzContactMapper.selectJzContactListByWeixinIds(externalUserIdList);
    }

    @Override
    public List<JzContact> selectJzContactListByWxIds(List<String> wxidList) {
        if (CollectionUtils.isEmpty(wxidList)) return Collections.emptyList();
        return jzContactMapper.selectJzContactListByWxIds(wxidList);
    }

    @Override
    public List<JzContact> selectJzConcatListByChatIds(List<String> chatIds) {
        if (CollectionUtils.isEmpty(chatIds)) return Collections.emptyList();
        return jzContactMapper.selectJzConcatListByChatIds(chatIds);
    }

    @Override
    public List<JzContact> getWaitActiveUser() {
        return jzContactMapper.getWaitActiveUser();
    }


    /**
     * 处理新好友
     *
     * @param contact
     */
    private void handleNewFriend(JzContact contact) {

        long nowTime = System.currentTimeMillis();

        if (EBoolean.YES.getCode().equals(contact.getDeleted()) || contact.getFriendTime() == null) {
            return;
        }

        if (nowTime - contact.getFriendTime().getTime() > 30 * 60 * 1000) {
            //成为好友超过了30分钟
            return;
        }

        String key = CacheConstants.CACHE_NEW_WX_FRIEND_FLAG.concat(contact.getUnionId());
        //判断是否已经推送过
        if (redisCache.hasKey(key)) {
            return;
        }

        boolean locked = redisLock.lock(key.concat("_lock"), String.valueOf(System.currentTimeMillis() + 15 * 1000));
        if (!locked) {
            return;
        }
        try {
            redisCache.setCacheObject(key, nowTime, 1, TimeUnit.HOURS);
            //发送通知
            redisCache.publish(ERedisChannel.NEW_FRIEND_CHANNEL.getCode(), JSONObject.toJSONString(contact));
        } finally {
            redisLock.unlock(key.concat("_lock"), String.valueOf(System.currentTimeMillis() + 15 * 1000));
        }
    }

    @Override
    public void chatConfig(String chatId) {
        JzContact condition = new JzContact();
        condition.setChatId(chatId);
        List<JzContact> contactList = jzContactMapper.selectJzContactList(condition);
        if (CollectionUtils.isEmpty(contactList)) {
            return;
        }

        JzContact contact = contactList.get(0);

        Map<String, String> chatConfig = new HashMap<>();
        //会话id
        chatConfig.put("chatId", contact.getChatId());
        chatConfig.put("wxid", contact.getWxid());
        chatConfig.put("botWxid", contact.getJzBot().getWxid());
        //会话类型 room 群，user 一对一
        chatConfig.put("type", ETaggableRefType.USER.getCode());
        //ai开启状态 0未开启，1已开启
        chatConfig.put("status", contact.getStatus());
        //智能体。
        chatConfig.put("agent", contact.getAgent());
        //指令列表（逗号隔开） ARTICLE_XIAOHONGSHU,TEXT2IMAGE_MIDJOURNEY
        chatConfig.put("instruction", contact.getInstruction());
        //是否无需@  ,0需要@ ，1不需要
        chatConfig.put("excludeAtReply", EBoolean.YES.getCode());
        chatConfig.put("noticeEnv", contact.getNoticeEnv());
        redisCache.setCacheObject("chat_config:" + contact.getChatId(), chatConfig);
    }


}
