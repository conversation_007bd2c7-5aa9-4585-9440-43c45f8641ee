package com.ruoyi.jzbot.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.domain.dto.AttachmentDto;
import com.ruoyi.biz.domain.dto.ImageDto;
import com.ruoyi.biz.domain.dto.paln.ImagePayload;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.jzbot.service.AttachmentStrategyService;
import com.ruoyi.jzbot.util.QWUtil;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class ImageAttachmentStrategy implements AttachmentStrategyService {


    @Override
    public List<AttachmentDto> createAttachment(JSONObject payload, QWUtil qwUtil) {
        List<AttachmentDto> attachments = new ArrayList<>();
        //{"url":"/profile/upload/2024/04/14/222.png,/profile/upload/2024/04/14/11.jpeg"}

        ImagePayload imagePayload = payload.toJavaObject(ImagePayload.class);
        imagePayload.getUrls().forEach(url -> {
                    try {
                        String fileId = qwUtil.uploadAttachment(Constants.MOMENTS_IMAGE, url);
                        ImageDto imageDto = new ImageDto();
                        imageDto.setMedia_id(fileId);
                        AttachmentDto attachment = new AttachmentDto();
                        attachment.setMsgtype(Constants.MOMENTS_IMAGE);
                        attachment.setImage(imageDto);
                        attachments.add(attachment);
                    } catch (Exception e) {
                       throw new ServiceException(e.getMessage());
                    }
                });
        return attachments;
    }
}
