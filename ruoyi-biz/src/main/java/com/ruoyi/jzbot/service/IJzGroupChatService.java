package com.ruoyi.jzbot.service;

import java.util.List;

import com.ruoyi.jzbot.domain.JzGroupChat;

/**
 * 群组Service接口
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
public interface IJzGroupChatService {
    /**
     * 查询群组
     *
     * @param id 群组主键
     * @return 群组
     */
    public JzGroupChat selectJzGroupChatById(String id);

    /**
     * 查询群组列表
     *
     * @param jzGroupChat 群组
     * @return 群组集合
     */
    public List<JzGroupChat> selectJzGroupChatList(JzGroupChat jzGroupChat);

    /**
     * 新增群组
     *
     * @param jzGroupChat 群组
     * @return 结果
     */
    public int insertJzGroupChat(JzGroupChat jzGroupChat);

    /**
     * 修改群组
     *
     * @param jzGroupChat 群组
     * @return 结果
     */
    public int updateJzGroupChat(JzGroupChat jzGroupChat);

    /**
     * 批量删除群组
     *
     * @param ids 需要删除的群组主键集合
     * @return 结果
     */
    public int deleteJzGroupChatByIds(String[] ids);

    /**
     * 删除群组信息
     *
     * @param id 群组主键
     * @return 结果
     */
    public int deleteJzGroupChatById(String id);

    /**
     * 查询用户所属的群组列表
     *
     * @param wid wid
     * @return 群组列表
     */
    List<JzGroupChat> selectJzGroupChatListByWid(String wid);


    /**
     * 更新群二维码
     */
    void updateGroupChatQR();

    /**
     * 更新群组二维码
     *
     * @param jzGroupChat
     */
    void updateGroupChatQrCode(JzGroupChat jzGroupChat);


    /**
     * 查询客户群列表
     *
     * @param jzGroupChat
     * @return
     */
    List<JzGroupChat> selectCustomerGroupChatList(JzGroupChat jzGroupChat);

    /**
     * 根据企业微信群id查询群信息
     * @param wecomChatIdList 企业微信群id列表
     * @return 群信息列表
     */
    List<JzGroupChat> selectJzGroupChatListByWecomChatIds(List<String> wecomChatIdList);
}
