package com.ruoyi.jzbot.service;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.ruoyi.jzbot.domain.BizJiebaDict;
import com.ruoyi.jzbot.domain.vo.BizJiebaDictVo;

/**
 * 分词字典Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
public interface IBizJiebaDictService 
{
    /**
     * 查询分词字典
     * 
     * @param id 分词字典主键
     * @return 分词字典
     */
    public BizJiebaDict selectBizJiebaDictById(Long id);

    /**
     * 查询分词字典列表
     * 
     * @param bizJiebaDict 分词字典
     * @return 分词字典集合
     */
    public PageInfo<BizJiebaDictVo> selectBizJiebaDictList(BizJiebaDict bizJiebaDict);

    /**
     * 新增分词字典
     * 
     * @param bizJiebaDict 分词字典
     * @return 结果
     */
    public int insertBizJiebaDict(BizJiebaDict bizJiebaDict);

    /**
     * 修改分词字典
     * 
     * @param bizJiebaDict 分词字典
     * @return 结果
     */
    public int updateBizJiebaDict(BizJiebaDict bizJiebaDict);

    /**
     * 批量删除分词字典
     * 
     * @param ids 需要删除的分词字典主键集合
     * @return 结果
     */
    public int deleteBizJiebaDictByIds(Long[] ids);

    /**
     * 删除分词字典信息
     * 
     * @param id 分词字典主键
     * @return 结果
     */
    public int deleteBizJiebaDictById(Long id);

    boolean readFileAndInsertData();
}
