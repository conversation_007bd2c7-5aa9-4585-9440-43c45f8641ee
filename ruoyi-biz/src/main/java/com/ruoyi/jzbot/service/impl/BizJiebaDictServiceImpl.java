package com.ruoyi.jzbot.service.impl;

import java.io.*;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.enums.PartOfSpeechType;
import com.ruoyi.common.utils.ObjectConversionUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.jzbot.domain.vo.BizJiebaDictVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import com.ruoyi.jzbot.mapper.BizJiebaDictMapper;
import com.ruoyi.jzbot.domain.BizJiebaDict;
import com.ruoyi.jzbot.service.IBizJiebaDictService;

import static com.ruoyi.common.utils.PageUtils.startPage;

/**
 * 分词字典Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-18
 */
@Service
@Slf4j
public class BizJiebaDictServiceImpl implements IBizJiebaDictService {
    @Resource
    private BizJiebaDictMapper bizJiebaDictMapper;

    /**
     * 查询分词字典
     *
     * @param id 分词字典主键
     * @return 分词字典
     */
    @Override
    public BizJiebaDict selectBizJiebaDictById(Long id) {
        return bizJiebaDictMapper.selectBizJiebaDictById(id);
    }

    /**
     * 查询分词字典列表
     *
     * @param bizJiebaDict 分词字典
     * @return 分词字典
     */
    @Override
    public PageInfo<BizJiebaDictVo> selectBizJiebaDictList(BizJiebaDict bizJiebaDict) {
        startPage();
        List<BizJiebaDict> bizJiebaDicts = bizJiebaDictMapper.selectBizJiebaDictList(bizJiebaDict);
        PageInfo<BizJiebaDict> pageInfo = new PageInfo<>(bizJiebaDicts);
        List<BizJiebaDictVo> bizJiebaDictVos = ObjectConversionUtils.copyList(bizJiebaDicts, BizJiebaDictVo.class);
        for (BizJiebaDictVo bizJiebaDictVo : bizJiebaDictVos) {
            if(PartOfSpeechType.fromCode(bizJiebaDictVo.getWordClass()) != null){
                String chineseDescription = PartOfSpeechType.fromCode(bizJiebaDictVo.getWordClass()).getChineseDescription();
                if(StringUtils.isNotEmpty(chineseDescription)){
                    bizJiebaDictVo.setWordClassName(chineseDescription);
                }
            }
        }
        PageInfo<BizJiebaDictVo> pageInfoVo = new PageInfo<>(bizJiebaDictVos);
        pageInfoVo.setTotal(pageInfo.getTotal());
        pageInfoVo.setPages(pageInfo.getPages());
        pageInfoVo.setPageNum(pageInfo.getPageNum());
        pageInfoVo.setPageSize(pageInfo.getPageSize());
        return pageInfoVo;
    }

    /**
     * 新增分词字典
     *
     * @param bizJiebaDict 分词字典
     * @return 结果
     */
    @Override
    public int insertBizJiebaDict(BizJiebaDict bizJiebaDict) {
        bizJiebaDict.setCreateDatetime(new Date());
        int count = bizJiebaDictMapper.insertBizJiebaDict(bizJiebaDict);
        if (count > 0) {
            String dictFilePath = BizJiebaDictServiceImpl.class.getResource("/file/dict.txt").getPath();
            try (FileWriter writer = new FileWriter(dictFilePath, true)) {
                // 构建要写入的字符串，格式为"关键字 词频 词性"
                String dataLine = String.format("%s %d %s%n", bizJiebaDict.getKeyword(), bizJiebaDict.getFrequency(), bizJiebaDict.getWordClass());
                // 将构建的字符串追加到文件的最后
                writer.append(dataLine);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return count;
    }

    /**
     * 修改分词字典
     *
     * @param bizJiebaDict 分词字典
     * @return 结果
     */
    @Override
    public int updateBizJiebaDict(BizJiebaDict bizJiebaDict) {
        return bizJiebaDictMapper.updateBizJiebaDict(bizJiebaDict);
    }

    /**
     * 批量删除分词字典
     *
     * @param ids 需要删除的分词字典主键
     * @return 结果
     */
    @Override
    public int deleteBizJiebaDictByIds(Long[] ids) {
        int count = bizJiebaDictMapper.deleteBizJiebaDictByIds(ids);
        if (count > 0) {
            for (Long id : ids) {
                BizJiebaDict bizJiebaDict = bizJiebaDictMapper.selectBizJiebaDictById(id);
                removeBizJiebaDictFromFile(bizJiebaDict);
            }
        }
        return bizJiebaDictMapper.deleteBizJiebaDictByIds(ids);
    }

    /**
     * 删除分词字典信息
     *
     * @param id 分词字典主键
     * @return 结果
     */
    @Override
    public int deleteBizJiebaDictById(Long id) {
        return bizJiebaDictMapper.deleteBizJiebaDictById(id);
    }

    public static void removeBizJiebaDictFromFile(BizJiebaDict bizJiebaDict) {
        String dictFilePath = BizJiebaDictServiceImpl.class.getClassLoader().getResource("file/dict.txt").getPath();

        File inputFile = new File(dictFilePath);
        File tempFile = new File(inputFile.getAbsolutePath() + ".tmp");
        String lineToRemove = String.format("%s %d %s", bizJiebaDict.getKeyword(), bizJiebaDict.getFrequency(), bizJiebaDict.getWordClass());

        try (BufferedReader reader = new BufferedReader(new FileReader(inputFile)); BufferedWriter writer = new BufferedWriter(new FileWriter(tempFile))) {

            String currentLine;

            while ((currentLine = reader.readLine()) != null) {
                String trimmedLine = currentLine.trim();
                if (trimmedLine.equals(lineToRemove)) continue;
                writer.write(currentLine + System.getProperty("line.separator"));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        if (!inputFile.delete()) {
            log.error("Could not delete original file");
            return;
        }
        if (!tempFile.renameTo(inputFile)) {
            log.error("Could not rename temp file to original file");
        }
    }

    @Override
    public boolean readFileAndInsertData() {
        String dictFilePath = BizJiebaDictServiceImpl.class.getClassLoader().getResource("file/dict.txt").getPath();
        List<BizJiebaDict> objects = Lists.newArrayList();
        try (BufferedReader reader = new BufferedReader(new FileReader(dictFilePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                String[] parts = line.split("\\s+"); // 假设文件中的字段是通过空格分隔的
                if (parts.length >= 3) {
                    BizJiebaDict jiebaDict = new BizJiebaDict();
                    jiebaDict.setKeyword(parts[0]);
                    jiebaDict.setFrequency(Long.parseLong(parts[1]));
                    jiebaDict.setWordClass(parts[2]);
                    jiebaDict.setCreateDatetime(new Date());
                    objects.add(jiebaDict);
                    if (objects.size() >= 10000) {
                        bizJiebaDictMapper.insertBizJiebaDictBatch(objects);
                        objects.clear(); // 执行插入后清空集合
                    }
                }
            }
            if (!objects.isEmpty()) {
                bizJiebaDictMapper.insertBizJiebaDictBatch(objects);
                objects.clear();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }
}
