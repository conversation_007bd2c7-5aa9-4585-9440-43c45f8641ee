package com.ruoyi.jzbot.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.biz.domain.BizUser;
import com.ruoyi.biz.domain.StatChatHotWordDay;
import com.ruoyi.biz.domain.dto.BizUserTempDto;
import com.ruoyi.biz.domain.req.ChatStatisticsReq;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.biz.enums.ECollectType;
import com.ruoyi.biz.enums.ETempTypeEnum;
import com.ruoyi.biz.service.*;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.ConfigConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.EJzRecviceMessageType;
import com.ruoyi.common.enums.WsDataType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.ObjectConversionUtils;
import com.ruoyi.framework.websocket.WebSocketUsers;
import com.ruoyi.framework.websocket.WsDataDto;
import com.ruoyi.jzbot.domain.JzBot;
import com.ruoyi.jzbot.domain.JzChatMessage;
import com.ruoyi.jzbot.domain.req.JzChatMessageReq;
import com.ruoyi.jzbot.domain.vo.JzChatMessageVo;
import com.ruoyi.jzbot.mapper.JzChatMessageMapper;
import com.ruoyi.jzbot.service.IJzBotService;
import com.ruoyi.jzbot.service.IJzChatMessageService;
import com.ruoyi.jzbot.util.JieBaUtils;
import com.ruoyi.jzbot.util.JuZiBotUtil;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.traffic.component.CreditComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.rmi.ServerException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.PageUtils.startPage;

/**
 * 会话记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
@Service
@Slf4j
public class JzChatMessageServiceImpl implements IJzChatMessageService {
    @Resource
    private JzChatMessageMapper jzChatMessageMapper;

    @Resource
    private IJzBotService jzBotService;


    @Resource
    private IBizHotWordDbService bizHotWordDbService;

    @Resource
    private IStatChatHotWordDayService statChatHotWordDayService;

    @Resource
    private JuZiBotUtil juZiBotUtil;

    @Resource
    private RedisCache redisCache;

    @Resource
    private IBizChatStatisticsService chatStatisticsService;

    @Resource
    private IBizUserTempService bizUserTempService;

    @Resource
    private IBizUserService bizUserService;

    @Resource
    private CreditComponent creditComponent;

    @Resource
    private ISysConfigService sysConfigService;

    /**
     * 查询会话记录
     *
     * @param id 会话记录主键
     * @return 会话记录
     */
    @Override
    public JzChatMessage selectJzChatMessageById(Long id) {
        return jzChatMessageMapper.selectJzChatMessageById(id);
    }

    /**
     * 查询会话记录列表
     *
     * @param jzChatMessageReq 会话记录
     * @return 会话记录
     */
    @Override
    public PageInfo<JzChatMessageVo> selectJzChatMessageList(JzChatMessageReq jzChatMessageReq) {
        startPage(jzChatMessageReq.getPageNum(), jzChatMessageReq.getPageSize());
        List<JzChatMessage> jzChatMessages = jzChatMessageMapper.selectJzChatMessageList(jzChatMessageReq);

        List<JzBot> botList = jzBotService.selectJzBotListSimple(new JzBot());

        // 将 List 转换为 Map，使用 botId 作为键，JzBot 作为值
        Map<String, JzBot> botMap = botList.stream()
                .collect(Collectors.toMap(JzBot::getBotId, bot -> bot));

        PageInfo<JzChatMessage> pageInfo = new PageInfo<>(jzChatMessages);
        List<JzChatMessageVo> jzChatMessageVos = ObjectConversionUtils.copyList(jzChatMessages, JzChatMessageVo.class);
        for (JzChatMessageVo vo : jzChatMessageVos) {
            // 通过 messageType 查找对应的枚举，获取中文描述
            EJzRecviceMessageType type = EJzRecviceMessageType.fromValue(vo.getMessageType());
            String messageTypeName = type.getDescription();
            // 将中文描述赋值给 messageTypeName 字段
            vo.setMessageTypeName(messageTypeName);
            vo.setJzBot(botMap.get(vo.getBotId()));
        }
        PageInfo<JzChatMessageVo> pageInfoVo = new PageInfo<>(jzChatMessageVos);
        pageInfoVo.setTotal(pageInfo.getTotal());
        pageInfoVo.setPages(pageInfo.getPages());
        pageInfoVo.setPageNum(pageInfo.getPageNum());
        pageInfoVo.setPageSize(pageInfo.getPageSize());
        return pageInfoVo;
    }

    @Override
    public PageInfo<JzChatMessageVo> selectJzChatMessageHistoryList(JzChatMessageReq jzChatMessageReq) {
        startPage(jzChatMessageReq.getPageNum(), jzChatMessageReq.getPageSize());
        List<JzChatMessage> jzChatMessages = jzChatMessageMapper.selectJzChatMessageHistoryList(jzChatMessageReq);

        List<JzBot> botList = jzBotService.selectJzBotListSimple(new JzBot());

        // 将 List 转换为 Map，使用 botId 作为键，JzBot 作为值
        Map<String, JzBot> botMap = botList.stream()
                .collect(Collectors.toMap(JzBot::getBotId, bot -> bot));

        PageInfo<JzChatMessage> pageInfo = new PageInfo<>(jzChatMessages);
        List<JzChatMessageVo> jzChatMessageVos = ObjectConversionUtils.copyList(jzChatMessages, JzChatMessageVo.class);
        for (JzChatMessageVo vo : jzChatMessageVos) {
            // 通过 messageType 查找对应的枚举，获取中文描述
            EJzRecviceMessageType type = EJzRecviceMessageType.fromValue(vo.getMessageType());
            String messageTypeName = type.getDescription();
            // 将中文描述赋值给 messageTypeName 字段
            vo.setMessageTypeName(messageTypeName);
            vo.setJzBot(botMap.get(vo.getBotId()));
        }
        PageInfo<JzChatMessageVo> pageInfoVo = new PageInfo<>(jzChatMessageVos);
        pageInfoVo.setTotal(pageInfo.getTotal());
        pageInfoVo.setPages(pageInfo.getPages());
        pageInfoVo.setPageNum(pageInfo.getPageNum());
        pageInfoVo.setPageSize(pageInfo.getPageSize());
        return pageInfoVo;
    }

    @Override
    public List<JzChatMessage> selectJzChatMessages(JzChatMessageReq jzChatMessage) {
        return jzChatMessageMapper.selectJzChatMessageList(jzChatMessage);
    }

    /**
     * 新增会话记录
     *
     * @param jzChatMessage 会话记录
     * @return 结果
     */
    @Override
    public int insertJzChatMessage(JzChatMessage jzChatMessage) {
        processMessage(jzChatMessage);
        return jzChatMessageMapper.insertJzChatMessage(jzChatMessage);
    }

    /**
     *  处理消息
     * @param jzChatMessage 会话记录
     */
    private void processMessage(JzChatMessage jzChatMessage) {
        if (jzChatMessage.getIsSelf() != null && jzChatMessage.getIsSelf() == 0 ) {
            if ("0".equals(jzChatMessage.getMessageSource())) {
                if (StringUtils.isNotBlank(jzChatMessage.getMessage())) {
                    JSONObject messageObj = JSONObject.parseObject(jzChatMessage.getMessage());
                    if (messageObj.containsKey("mention")) {
                        JSONArray jsonArray = messageObj.getJSONArray("mention");
                        for (Object o : jsonArray) {
                            // 获取被@的用户wxid
                            String mentionStr = o.toString();
                            List<JzBot> jzBots = jzBotService.selectJzBotList(new JzBot() {{
                                setWxid(mentionStr);
                            }});
                            // 只要不为空就设置 isEt 为1，并退出循环
                            if (CollectionUtils.isNotEmpty(jzBots)) {
                                jzChatMessage.setIsEt(Integer.valueOf(EBoolean.YES.getCode()));
                                break;
                            }
                        }
                    }
                }
            } else {
                // 单聊的话且是用户发的消息，默认@ bot
                jzChatMessage.setIsEt(Integer.valueOf(EBoolean.YES.getCode()));
            }
        }
    }

    /**
     * 修改会话记录
     *
     * @param jzChatMessage 会话记录
     * @return 结果
     */
    @Override
    public int updateJzChatMessage(JzChatMessage jzChatMessage) {
        return jzChatMessageMapper.updateJzChatMessage(jzChatMessage);
    }

    /**
     * 批量删除会话记录
     *
     * @param ids 需要删除的会话记录主键
     * @return 结果
     */
    @Override
    public int deleteJzChatMessageByIds(Long[] ids) {
        return jzChatMessageMapper.deleteJzChatMessageByIds(ids);
    }

    /**
     * 删除会话记录信息
     *
     * @param id 会话记录主键
     * @return 结果
     */
    @Override
    public int deleteJzChatMessageById(Long id) {
        return jzChatMessageMapper.deleteJzChatMessageById(id);
    }

    @Override
    public List<JzChatMessageVo> getYesterdayMessage(String date) {

        return jzChatMessageMapper.statistics(date, null);
    }

    /**
     * 统计每个会话的热词
     *
     * @param type
     * @param chatId
     * @param yesterday
     * @param CACHE_HOT_WORD_MAP
     */
    @Override
    public void dailyHotWordStatistics(String type, String chatId, String yesterday, Map<String, Object> CACHE_HOT_WORD_MAP) {
        List<JzChatMessageVo> messageList = jzChatMessageMapper.statistics(yesterday, chatId);
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }

        Map<String, Integer> map = new HashMap<>();

        for (JzChatMessageVo messageVo : messageList) {
            if (StringUtils.isBlank(messageVo.getSegmentedWords())) {
                continue;
            }
            List<String> words = JSON.parseArray(messageVo.getSegmentedWords(), String.class).stream()
                    .filter(t -> StringUtils.isNotBlank(t) && t.length() > 1).collect(Collectors.toList());
            words.forEach(word -> map.merge(word, 1, Integer::sum));
        }

        StatChatHotWordDay statChatHotWordDay = new StatChatHotWordDay();
        statChatHotWordDay.setDayTime(yesterday);
        statChatHotWordDay.setChatId(chatId);
        statChatHotWordDay.setType(type);
        statChatHotWordDay.setWordSort(JSON.toJSONString(map));


        Map<String, Integer> hotMap = new HashMap<>();
        Map<String, Integer> waitMap = new HashMap<>();

        map.forEach((k, c) -> {
            if (CACHE_HOT_WORD_MAP.containsKey(k)) {
                hotMap.put(k, c);
            } else {
                waitMap.put(k, c);
            }
        });

//        List<String> list = new ArrayList<>(map.keySet());
//        List<String> hotWords = bizHotWordDbService.checkIsHotWords(list);
//
//        for (String hotWord : hotWords) {
//            hotMap.put(hotWord, map.get(hotWord));
//            //删除掉热词，就是剩余待分析的
//            map.remove(hotWord);
//        }

        LinkedHashMap<String, Integer> hotWordSort = hotMap.entrySet().stream().sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldv, newv) -> oldv, LinkedHashMap::new));

        statChatHotWordDay.setBusiness(JSON.toJSONString(hotWordSort));
        statChatHotWordDay.setWaitAiWord(JSON.toJSONString(map));
        statChatHotWordDay.setCreateTime(new Date());
        statChatHotWordDayService.insertStatChatHotWordDay(statChatHotWordDay);

    }

    @Override
    public void pullHistoryMessage() {
        String datetime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, new Date());

        while (true) {
            Object cacheSeq = redisCache.getCacheObject("jz_history_message_seq");
            String seq = cacheSeq != null ? cacheSeq.toString() : "";
            JSONObject messageRes = juZiBotUtil.historyMessage(datetime, seq);
            seq = messageRes.getString("seq");
            if (StringUtils.isNotBlank(seq)) {
                redisCache.setCacheObject("jz_history_message_seq", seq);
            }
            List<JSONObject> list = messageRes.getJSONArray("data").toJavaList(JSONObject.class);

            if (CollectionUtils.isEmpty(list)) {
                break;
            }

            //buildMessage foreach
            List<JzChatMessage> messageList = list.parallelStream().map(data -> {
                try {
                    return buildMessage(data);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }).collect(Collectors.toList());

            //insert batch
            jzChatMessageMapper.insertBatch(messageList);
        }
    }

    @Override
    public JzChatMessage handleReceiveMessage(JSONObject data) throws Exception {
        JzChatMessage jzChatMessage = buildMessage(data);
        jzChatMessageMapper.insertJzChatMessage(jzChatMessage);
        return jzChatMessage;
    }

    @Override
    public void updateJzChatMessageByMessageId(JzChatMessage jzChatMessage) {
        jzChatMessageMapper.updateJzChatMessageByMessageId(jzChatMessage);
    }

    @Override
    public void updateEt() {
        // 查询所有用户发送的消息
        int pageNum = 1;
        int pageSize = 100;

        while (true) {
            Page<Object> startPage = PageHelper.startPage(pageNum, pageSize, "time desc");
            startPage.setCount(false);
            List<JzChatMessage> jzChatMessages = this.selectJzChatMessages(new JzChatMessageReq() {{
                setIsSelf(0);
                setMessageSource("1");
            }});
            if (CollectionUtils.isEmpty(jzChatMessages)) break;
            List<JzChatMessage> updateList = new ArrayList<>();
            jzChatMessages.forEach(jzChatMessage -> {
                // 群聊的话判断是否有@bot
                if ("0".equals(jzChatMessage.getMessageSource())) {
                    JSONObject messageObj = JSONObject.parseObject(jzChatMessage.getMessage());
                    if (messageObj.containsKey("mention")) {
                        JSONArray jsonArray = messageObj.getJSONArray("mention");
                        for (Object o : jsonArray) {
                            // 获取被@的用户wxid
                            String mentionStr = o.toString();
                            List<JzBot> jzBots = jzBotService.selectJzBotList(new JzBot() {{
                                setWxid(mentionStr);
                            }});
                            if (CollectionUtils.isNotEmpty(jzBots)) {
                                jzChatMessage.setIsEt(Integer.valueOf(EBoolean.YES.getCode()));
                                updateList.add(jzChatMessage);
                                break;
                            }
                        }
                    }
                } else {
                    // 单聊默认@bot
                    jzChatMessage.setIsEt(Integer.valueOf(EBoolean.YES.getCode()));
                    updateList.add(jzChatMessage);
                }
            });
            if (CollectionUtils.isNotEmpty(updateList)) {
                jzChatMessageMapper.insertBatch(updateList);
            }
            pageNum++;
        }
    }

    @Override
    public List<JzChatMessage> selectJzChatMessageByTimeAndChatIds(String time, Set<String> chatIds) {
        return jzChatMessageMapper.selectJzChatMessageByTimeAndChatIds(time, chatIds);
    }


    private JzChatMessage buildMessage(JSONObject data) throws Exception {
        JzChatMessage jzChatMessage = data.toJavaObject(JzChatMessage.class);
        jzChatMessage.setTime(new Date(data.getLong("timestamp")));
        jzChatMessage.setMessageType(data.getInteger("type"));
        jzChatMessage.setMessage(data.getString("payload"));
        jzChatMessage.setContactImageUrl(data.getString("avatar"));
        jzChatMessage.setMessageSource(StringUtils.isEmpty(data.getString("roomId")) ? Constants.PEER_TO_PEER : EBoolean.NO.getCode());

        Map<String, Object> cacheMap = null;
        //AI 账号自己发的,判断是谁发的
        if (data.getBoolean("isSelf")) {
            jzChatMessage.setSendName("手机");
            long stopTime = System.currentTimeMillis() + 3000L;
            while (System.currentTimeMillis() <= stopTime) {
                cacheMap = redisCache.getCacheMap("msg_ref:" + jzChatMessage.getMessageId());
                if (cacheMap != null && !cacheMap.isEmpty()) {
                    redisCache.expire("msg_ref:" + jzChatMessage.getMessageId(), 180);
                    break;
                }
                Thread.sleep(200);
            }
            if (cacheMap != null && !cacheMap.isEmpty()) {
                jzChatMessage.setSendName((cacheMap.get("userName") == null ? null : cacheMap.get("userName").toString()));
                jzChatMessage.setFMessageId((cacheMap.get("fMessageId") == null ? null : cacheMap.get("fMessageId").toString()));
                jzChatMessage.setAiExtra(cacheMap.containsKey("aiExtra") ? ((JSONObject) cacheMap.get("aiExtra")).toJSONString() : null);
            }
        }
        // 分词处理
        if (data.getInteger("type") == EJzRecviceMessageType.TEXT.getCode()) {
            JSONObject payload = data.getJSONObject("payload");
            String text = payload.getString("text").trim();

            // 判断是否有提及并移除所有 @符号后的字符
            if (CollectionUtils.isNotEmpty(payload.getJSONArray("mention"))) {
                // 合并正则表达式，匹配所有 @符号及后面的非空字符
                text = text.replaceAll("@\\S+\\u2005", "").replaceAll("@\\S+\\s*", "").trim();
            }

            if (StringUtils.isNotBlank(text)) {
                // 使用结巴分词
                List<String> jieba = JieBaUtils.jieba(text);
                // 将分词结果转换为JSON字符串
                jzChatMessage.setSegmentedWords(JSON.toJSONString(jieba));
            }
        }

        //判断是否@bot
        if (StringUtils.isNotBlank(data.getString("roomId")) && data.getBooleanValue("mentionSelf")) {
            jzChatMessage.setIsEt(Integer.valueOf(EBoolean.YES.getCode()));
        }

        // 积分消息缓存
        boolean open = sysConfigService.isOpen(ConfigConstants.AI_TRAFFIC_SWITCH);

        if (open && jzChatMessage.getIsSelf() != null && jzChatMessage.getIsSelf() == 1) {
            // 写入缓存中
            redisCache.leftPush(CacheConstants.CREDIT_MESSAGE_CACHE + jzChatMessage.getChatId(), jzChatMessage);
        }

        return jzChatMessage;
    }

}
