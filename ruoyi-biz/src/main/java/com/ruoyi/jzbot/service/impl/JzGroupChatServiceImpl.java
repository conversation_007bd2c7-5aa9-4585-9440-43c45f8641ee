package com.ruoyi.jzbot.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.domain.BizPartner;
import com.ruoyi.biz.domain.BizPartnerGroup;
import com.ruoyi.biz.domain.BizTaggable;
import com.ruoyi.biz.enums.ETaggableRefType;
import com.ruoyi.biz.service.IBizPartnerGroupService;
import com.ruoyi.biz.service.IBizPartnerService;
import com.ruoyi.biz.service.IBizTaggableService;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.jzbot.domain.JzGroupChat;
import com.ruoyi.jzbot.domain.JzRoom;
import com.ruoyi.jzbot.mapper.JzGroupChatMapper;
import com.ruoyi.jzbot.service.IJzGroupChatService;
import com.ruoyi.jzbot.service.IJzRoomService;
import com.ruoyi.jzbot.util.JuZiBotUtil;
import com.ruoyi.jzbot.util.QWUtil;
import com.ruoyi.system.service.ICommonService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * 群组Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
@Service
public class JzGroupChatServiceImpl implements IJzGroupChatService
{
    private static final Logger log = LoggerFactory.getLogger(JzGroupChatServiceImpl.class);
    @Resource
    private JzGroupChatMapper jzGroupChatMapper;

    @Resource
    private IJzRoomService jzRoomService;

    @Resource
    private IBizPartnerGroupService bizPartnerGroupService;

    @Resource
    private IBizPartnerService bizPartnerService;

    @Resource
    private IBizTaggableService bizTaggableService;

    @Resource
    private JuZiBotUtil juZiBotUtil;

    @Resource
    private ICommonService commonService;

    @Resource
    private QWUtil qwUtil;
    /**
     * 查询群组
     *
     * @param id 群组主键
     * @return 群组
     */
    @Override
    public JzGroupChat selectJzGroupChatById(String id)
    {
        List<JzGroupChat> chatList = selectJzGroupChatList(new JzGroupChat() {{
            setId(id);
        }});

        if (CollectionUtils.isEmpty(chatList)) {
            return null;
        }
        return chatList.get(0);
    }

    /**
     * 查询群组列表
     *
     * @param jzGroupChat 群组
     * @return 群组
     */
    @Override
    public List<JzGroupChat> selectJzGroupChatList(JzGroupChat jzGroupChat)
    {
        List<JzGroupChat> jzGroupChats = jzGroupChatMapper.selectJzGroupChatList(jzGroupChat);

        if (CollectionUtils.isEmpty(jzGroupChats)) {
            return jzGroupChats;
        }

        List<String> refIdList = jzGroupChats.stream().map(JzGroupChat::getId).collect(Collectors.toList());
        BizTaggable taggable = new BizTaggable();
        taggable.setRefType(ETaggableRefType.ROOM.getCode());
        taggable.setRefIdList(refIdList);
        List<BizTaggable> list = bizTaggableService.selectBizTaggableList(taggable);
        Map<String, List<BizTaggable>> collect = list.stream().collect(Collectors.groupingBy(BizTaggable::getRefId));
        for (JzGroupChat groupChat : jzGroupChats) {
            groupChat.setTagList(collect.get(groupChat.getId()));
        }

        //获取room 列表
        jzGroupChats.forEach(jzGroup -> jzGroup.setJzRoomList(jzRoomService.selectJzRoomByWxId(jzGroup.getId())));

        //获取分销者
        jzGroupChats.forEach(this::init);

        return jzGroupChats;
    }

    private void init(JzGroupChat chat){
        BizPartnerGroup condition = new BizPartnerGroup();
        condition.setRoomId(chat.getId());
        //判断是否已经有合伙人了
        List<BizPartnerGroup> partnerGroups = bizPartnerGroupService.selectBizPartnerGroupList(condition);
        if (CollectionUtils.isNotEmpty(partnerGroups)){
            chat.setPartnerName(bizPartnerService.selectBizPartnerById(partnerGroups.get(0).getPartnerId()).getName());
            chat.setPartnerId(partnerGroups.get(0).getPartnerId());
        }
    }

    /**
     * 新增群组
     *
     * @param jzGroupChat 群组
     * @return 结果
     */
    @Override
    public int insertJzGroupChat(JzGroupChat jzGroupChat)
    {
        return jzGroupChatMapper.insertJzGroupChat(jzGroupChat);
    }

    /**
     * 修改群组
     *
     * @param jzGroupChat 群组
     * @return 结果
     */
    @Override
    public int updateJzGroupChat(JzGroupChat jzGroupChat)
    {
        return jzGroupChatMapper.updateJzGroupChat(jzGroupChat);
    }

    /**
     * 批量删除群组
     *
     * @param ids 需要删除的群组主键
     * @return 结果
     */
    @Override
    public int deleteJzGroupChatByIds(String[] ids)
    {
        return jzGroupChatMapper.deleteJzGroupChatByIds(ids);
    }

    /**
     * 删除群组信息
     *
     * @param id 群组主键
     * @return 结果
     */
    @Override
    public int deleteJzGroupChatById(String id)
    {
        return jzGroupChatMapper.deleteJzGroupChatById(id);
    }

    /**
     * 查询群组列表
     * @param wid wid
     * @return 群组
     */
    @Override
    public List<JzGroupChat> selectJzGroupChatListByWid(String wid) {
        // 根据用户wid查询合伙人群关联表获取room_id
        BizPartner bizPartner = bizPartnerService.selectBizPartnerByWid(wid);
        if (bizPartner == null) {
            return Collections.emptyList();
        }

        // 查询合伙人群关联表获取room_id
        BizPartnerGroup bizPartnerGroup = new BizPartnerGroup();
        bizPartnerGroup.setPartnerId(bizPartner.getId());
        List<BizPartnerGroup> bizPartnerGroups = bizPartnerGroupService.selectBizPartnerGroupList(bizPartnerGroup);
        if (CollectionUtils.isEmpty(bizPartnerGroups)) {
            return Collections.emptyList();
        }

        Set<String> roomIds = bizPartnerGroups.stream()
                .map(BizPartnerGroup::getRoomId)
                .collect(Collectors.toSet());

        // 查询群组表
        List<JzGroupChat> jzGroupChats = jzGroupChatMapper.selectJzGroupChatListByIds(roomIds);

        // 使用并发集合
        ConcurrentMap<String, List<JzRoom>> jzRoomMap = new ConcurrentHashMap<>();

        jzGroupChats.parallelStream().forEach(jzGroup -> {
            // 确保线程安全地获取JzRoom列表
            List<JzRoom> jzRooms = jzRoomService.selectJzRoomByWxId(jzGroup.getId());
            jzRoomMap.put(jzGroup.getId(), jzRooms);
        });

        jzGroupChats.forEach(jzGroup -> jzGroup.setJzRoomList(jzRoomMap.get(jzGroup.getId())));

        return jzGroupChats;
    }

    @Override
    public void updateGroupChatQR() {

        int pageNum = 1;
        int pageSize = 5;

        while (true) {
            PageUtils.startPage(pageNum, pageSize, false);
            List<JzGroupChat> jzGroupChats = jzGroupChatMapper.selectJzGroupChatList(new JzGroupChat(){{
                setDeleted("0");
            }});
            if (StringUtils.isEmpty(jzGroupChats)) {
                break;
            }

            for (JzGroupChat jzGroupChat : jzGroupChats) {
                updateGroupChatQrCode(jzGroupChat);
            }

            pageNum++;
        }
    }

    public void updateGroupChatQrCode(JzGroupChat jzGroupChat) {
        List<JzRoom> jzRooms = jzRoomService.selectJzRoomByWxId(jzGroupChat.getId());

        String groupQRCodeUrl = juZiBotUtil.getGroupQRCode(jzGroupChat.getId(), jzRooms.get(0).getJzBot().getWxid());
        if (StringUtils.isEmpty(groupQRCodeUrl)) {
            return;
        }

        try {
            String qrImgUrl = commonService.uploadQrCodeByUrl(groupQRCodeUrl);
            jzGroupChat.setInviteQrUrl(qrImgUrl);
        } catch (IOException e) {
            log.error("生成群二维码失败, group chat: {}, error: ", jzGroupChat.getId(), e);
        }

        jzGroupChatMapper.updateJzGroupChat(jzGroupChat);
    }

    @Override
    public List<JzGroupChat> selectCustomerGroupChatList(JzGroupChat jzGroupChat) {
        JSONArray customerGroupChatList = qwUtil.groupChatList();
        List<String> filterGroupChatIdList = customerGroupChatList.stream()
                .map(x -> (JSONObject) x)
                .filter(x -> x.getInteger("status") == 0)
                .map(x -> x.getString("chat_id"))
                .collect(Collectors.toList());

        jzGroupChat.setWecomChatIdList(filterGroupChatIdList);
        return jzGroupChatMapper.selectJzGroupChatList(jzGroupChat);
    }

    @Override
    public List<JzGroupChat> selectJzGroupChatListByWecomChatIds(List<String> wecomChatIdList) {
        if (CollectionUtils.isEmpty(wecomChatIdList)) {
            return Collections.emptyList();
        }

        return jzGroupChatMapper.selectJzGroupChatListByWecomChatIds(wecomChatIdList);
    }
}
