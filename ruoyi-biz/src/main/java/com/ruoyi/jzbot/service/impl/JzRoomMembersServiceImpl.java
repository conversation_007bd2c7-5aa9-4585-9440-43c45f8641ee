package com.ruoyi.jzbot.service.impl;

import com.ruoyi.biz.domain.req.ChatStatisticsReq;
import com.ruoyi.biz.enums.ECollectType;
import com.ruoyi.biz.service.IBizChatStatisticsService;
import com.ruoyi.biz.service.IBizUserService;
import com.ruoyi.jzbot.domain.JzRoom;
import com.ruoyi.jzbot.domain.JzRoomMembers;
import com.ruoyi.jzbot.mapper.JzRoomMembersMapper;
import com.ruoyi.jzbot.service.IJzRoomMembersService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 群成员Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@Service
@Slf4j
public class JzRoomMembersServiceImpl implements IJzRoomMembersService
{
    @Resource
    private JzRoomMembersMapper jzRoomMembersMapper;

    @Resource
    private IBizUserService bizUserService;

    @Resource
    private IBizChatStatisticsService bizChatStatisticsService;


    /**
     * 查询群成员
     *
     * @param wxid 群成员主键
     * @return 群成员
     */
    @Override
    public List<JzRoomMembers> selectJzRoomMembersByWxid(String wxid)
    {
        return jzRoomMembersMapper.selectJzRoomMembersByWxid(wxid);
    }

    /**
     * 查询群成员列表
     *
     * @param jzRoomMembers 群成员
     * @return 群成员
     */
    @Override
    public List<JzRoomMembers> selectJzRoomMembersList(JzRoomMembers jzRoomMembers)
    {
        return jzRoomMembersMapper.selectJzRoomMembersList(jzRoomMembers);
    }

    /**
     * 新增群成员
     *
     * @param jzRoomMembers 群成员
     * @return 结果
     */
    @Override
    public int insertJzRoomMembers(JzRoomMembers jzRoomMembers)
    {
        return jzRoomMembersMapper.insertJzRoomMembers(jzRoomMembers);
    }

    /**
     * 修改群成员
     *
     * @param jzRoomMembers 群成员
     * @return 结果
     */
    @Override
    public int updateJzRoomMembers(JzRoomMembers jzRoomMembers)
    {
        return jzRoomMembersMapper.updateJzRoomMembers(jzRoomMembers);
    }

    /**
     * 批量删除群成员
     *
     * @param wxids 需要删除的群成员主键
     * @return 结果
     */
    @Override
    public int deleteJzRoomMembersByWxids(String[] wxids)
    {
        return jzRoomMembersMapper.deleteJzRoomMembersByWxids(wxids);
    }

    /**
     * 删除群成员信息
     *
     * @param wxid 群成员主键
     * @return 结果
     */
    @Override
    public int deleteJzRoomMembersByWxid(String wxid)
    {
        return jzRoomMembersMapper.deleteJzRoomMembersByWxid(wxid);
    }


    @Override
    public void handelMembers(List<JzRoom> roomList) {

        Map<String,String> cacheRoom = new HashMap<>();

        for (JzRoom room : roomList) {
            if (cacheRoom.containsKey(room.getWxid())){
                continue;
            }

            //该群的所有成员
            List<JzRoomMembers> nowMembersList = room.getMembersList();

            if (CollectionUtils.isEmpty(nowMembersList)){
                continue;
            }

            //先查询该群现在的群员
            JzRoomMembers condition = new JzRoomMembers();
            condition.setRoomId(room.getWxid());
            List<JzRoomMembers> oldMembers = jzRoomMembersMapper.selectJzRoomMembersList(condition);
            Map<String, JzRoomMembers> oldCollect = oldMembers.stream().filter(jzRoomMembers -> "1".equals(jzRoomMembers.getStatus())).collect(Collectors.toMap(JzRoomMembers::getWxid,
                    object -> object));

            Map<String, JzRoomMembers> nowCollect = nowMembersList.stream().collect(Collectors.toMap(JzRoomMembers::getWxid,
                    object -> object));

            //判断新增，
            Collection<String> add = CollectionUtils.subtract(nowCollect.keySet(), oldCollect.keySet());
            //判断退出的
            Collection<String> quit = CollectionUtils.subtract(oldCollect.keySet(), nowCollect.keySet());

            // 记录当天用户变更情况并检测预警
            if (CollectionUtils.isNotEmpty(add) || CollectionUtils.isNotEmpty(quit)){
                bizChatStatisticsService.collect(ECollectType.PERSON_CHANGE, ChatStatisticsReq.builder()
                        .chatId(room.getChatId())
                        .enterNum(add.size())
                        .exitNum(quit.size())
                        .build());
            }

            //更新成员信息
            for (String wxid : quit) {
                JzRoomMembers jzRoomMembers = oldCollect.get(wxid);
                //退群
                jzRoomMembers.setStatus("0");
                jzRoomMembers.setQuitTime(new Date());
                nowMembersList.add(jzRoomMembers);
            }

            //开始批量更新
            jzRoomMembersMapper.batchAddUpdate(nowMembersList);



            //开始统计系统用户
//            bizUserService.batchAddUpdateByMembers(nowMembersList);


            cacheRoom.put(room.getWxid(),"ok");
        }


    }
}
