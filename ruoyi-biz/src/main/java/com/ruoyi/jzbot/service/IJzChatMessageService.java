package com.ruoyi.jzbot.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageInfo;
import com.ruoyi.jzbot.domain.JzChatMessage;
import com.ruoyi.jzbot.domain.req.JzChatMessageReq;
import com.ruoyi.jzbot.domain.vo.JzChatMessageVo;

/**
 * 会话记录Service接口
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
public interface IJzChatMessageService {
    /**
     * 查询会话记录
     *
     * @param id 会话记录主键
     * @return 会话记录
     */
    public JzChatMessage selectJzChatMessageById(Long id);

    /**
     * 查询会话记录列表
     *
     * @param jzChatMessage 会话记录
     * @return 会话记录集合
     */
    public PageInfo<JzChatMessageVo> selectJzChatMessageList(JzChatMessageReq jzChatMessage);


    PageInfo<JzChatMessageVo> selectJzChatMessageHistoryList(JzChatMessageReq jzChatMessageReq);

    /**
     * 查询会话记录列表
     * @param jzChatMessage 会话记录
     * @return 会话记录集合
     */
    public List<JzChatMessage> selectJzChatMessages(JzChatMessageReq jzChatMessage);

    /**
     * 新增会话记录
     *
     * @param jzChatMessage 会话记录
     * @return 结果
     */
    public int insertJzChatMessage(JzChatMessage jzChatMessage);

    /**
     * 修改会话记录
     *
     * @param jzChatMessage 会话记录
     * @return 结果
     */
    public int updateJzChatMessage(JzChatMessage jzChatMessage);

    /**
     * 批量删除会话记录
     *
     * @param ids 需要删除的会话记录主键集合
     * @return 结果
     */
    public int deleteJzChatMessageByIds(Long[] ids);

    /**
     * 删除会话记录信息
     *
     * @param id 会话记录主键
     * @return 结果
     */
    public int deleteJzChatMessageById(Long id);


    /**
     * 统计
     *
     * @return
     */
    List<JzChatMessageVo> getYesterdayMessage(String date);

    /**
     * 统计会话热词
     *
     * @param code
     * @param chatId
     * @param yesterday
     * @param CACHE_HOT_WORD_MAP
     */
    void dailyHotWordStatistics(String code, String chatId, String yesterday, Map<String, Object> CACHE_HOT_WORD_MAP);

    /**
     * 拉取历史消息
     */
    void pullHistoryMessage();

    JzChatMessage handleReceiveMessage(JSONObject data) throws Exception;

    void updateJzChatMessageByMessageId(JzChatMessage jzChatMessage);

    /**
     * 更新是否@bot状态
     */
    void updateEt();

    /**
     * 根据时间和聊天id查询消息
     * @param time 时间
     * @param chatIds 聊天id
     * @return
     */
    List<JzChatMessage> selectJzChatMessageByTimeAndChatIds(String time, Set<String> chatIds);
}
