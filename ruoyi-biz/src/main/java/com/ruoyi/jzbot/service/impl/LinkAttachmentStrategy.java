package com.ruoyi.jzbot.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.domain.dto.AttachmentDto;
import com.ruoyi.biz.domain.dto.LinkDto;
import com.ruoyi.biz.domain.dto.paln.WebPagePayload;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.jzbot.service.AttachmentStrategyService;
import com.ruoyi.jzbot.util.QWUtil;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class LinkAttachmentStrategy implements AttachmentStrategyService {


    @Override
    public List<AttachmentDto> createAttachment(JSONObject payload, QWUtil qwUtil) {
        List<AttachmentDto> attachments = new ArrayList<>();

        //{"sourceUrl":"http://baidu.com","title":"百度","summary":"百度"
        // ,"imageUrl":"/profile/upload/2024/04/14/0_20240414181425A005.png"}

        WebPagePayload webPagePayload = payload.toJavaObject(WebPagePayload.class);

        try {
            String fileId = qwUtil.uploadAttachment(Constants.MOMENTS_IMAGE, webPagePayload.getImageUrl());
            LinkDto linkDto = new LinkDto();
            linkDto.setMedia_id(fileId);
            linkDto.setUrl(webPagePayload.getSourceUrl());
            linkDto.setTitle(webPagePayload.getTitle());
            AttachmentDto attachment = new AttachmentDto();
            attachment.setMsgtype(Constants.MOMENTS_LINK);
            attachment.setLink(linkDto);
            attachments.add(attachment);
        }catch (Exception e ){
            throw new ServiceException(e.getMessage());
        }

        return attachments;
    }
}
