package com.ruoyi.jzbot.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.biz.domain.BizTaggable;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.biz.enums.ETaggableRefType;
import com.ruoyi.biz.service.IBizTaggableService;
import com.ruoyi.common.constant.ConfigConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.jzbot.domain.JzBot;
import com.ruoyi.jzbot.domain.JzContact;
import com.ruoyi.jzbot.domain.JzGroupChat;
import com.ruoyi.jzbot.mapper.JzGroupChatMapper;
import com.ruoyi.jzbot.service.IJzBotService;
import com.ruoyi.jzbot.service.IJzRoomMembersService;
import com.ruoyi.jzbot.util.JuZiBotUtil;

import javax.annotation.Resource;

import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.traffic.component.TrafficComponent;
import com.ruoyi.traffic.enums.CreditTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.jzbot.mapper.JzRoomMapper;
import com.ruoyi.jzbot.domain.JzRoom;
import com.ruoyi.jzbot.service.IJzRoomService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 群Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@Service
public class JzRoomServiceImpl implements IJzRoomService {
    @Resource
    private JzRoomMapper jzRoomMapper;

    @Resource
    private JzGroupChatMapper jzGroupChatMapper;

    @Resource
    private JuZiBotUtil juZiBotUtil;

    @Resource
    private IJzBotService jzBotService;

    @Resource
    private IBizTaggableService bizTaggableService;

    @Resource
    private IJzRoomMembersService jzRoomMembersService;

    @Resource
    private RedisCache redisCache;

    @Resource
    private TrafficComponent trafficComponent;

    @Resource
    private ISysConfigService sysConfigService;

    /**
     * 查询群
     *
     * @param roomId 群主键
     * @return 群
     */
    @Override
    public JzRoom selectJzRoomByChatId(String roomId) {
        return jzRoomMapper.selectJzRoomByChatId(roomId);
    }

    /**
     * 查询群列表
     *
     * @param jzRoom 群
     * @return 群
     */
    @Override
    public List<JzRoom> selectJzRoomList(JzRoom jzRoom) {
        List<JzRoom> rooms = jzRoomMapper.selectJzRoomList(jzRoom);

        if (CollectionUtils.isEmpty(rooms)) {
            return rooms;
        }
        List<String> refIdList = rooms.stream().map(JzRoom::getChatId).collect(Collectors.toList());
        BizTaggable taggable = new BizTaggable();
        taggable.setRefType(ETaggableRefType.ROOM.getCode());
        taggable.setRefIdList(refIdList);
        List<BizTaggable> list = bizTaggableService.selectBizTaggableList(taggable);

        Map<String, List<BizTaggable>> collect = list.stream().collect(Collectors.groupingBy(BizTaggable::getRefId));

        for (JzRoom room : rooms) {
            room.setTagList(collect.get(room.getChatId()));
        }

        return rooms;
    }

    /**
     * 新增群
     *
     * @param jzRoom 群
     * @return 结果
     */
    @Override
    public int insertJzRoom(JzRoom jzRoom) {
        return jzRoomMapper.insertJzRoom(jzRoom);
    }

    /**
     * 修改群
     *
     * @param jzRoom 群
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateJzRoom(JzRoom jzRoom) {
        int row = jzRoomMapper.updateJzRoom(jzRoom);
        // 处理AI服务包逻辑
        if (sysConfigService.isOpen(ConfigConstants.AI_TRAFFIC_SWITCH)) {
            handleAiPacket(jzRoom);
        }
        chatConfig(jzRoom.getChatId());

        return row;
    }

    /**
     * 处理AI服务包逻辑
     * @param jzRoom
     */
    private void handleAiPacket(JzRoom jzRoom) {
        if (jzRoom.getStatus() != null) {
            boolean enableAI = EBoolean.YES.getCode().equals(jzRoom.getStatus());
            // 开启时跳过服务处理，直接开启，由定时任务被动处理
            if (enableAI) {
                return;
            }
            trafficComponent.handleAIServicePackage(
                    jzRoom.getChatId(),
                    CreditTypeEnum.GROUP.getCode(),
                    enableAI
            );
        }
    }

    /**
     * 批量删除群
     *
     * @param roomIds 需要删除的群主键
     * @return 结果
     */
    @Override
    public int deleteJzRoomByRoomIds(String[] roomIds) {
        return jzRoomMapper.deleteJzRoomByChatIds(roomIds);
    }

    /**
     * 删除群信息
     *
     * @param chatId 群主键
     * @return 结果
     */
    @Override
    public int deleteJzRoomByChatId(String chatId) {
        return jzRoomMapper.deleteJzRoomByChatId(chatId);
    }

    @Override
    public void pullJuZiRooms() {

        //所有群
        List<String> idList = new ArrayList<>();
        int currentX = 0;
        while (true) {
            List<JzGroupChat> groupChatList = juZiBotUtil.getGroupChats(currentX);
            if (CollectionUtils.isEmpty(groupChatList)) {
                break;
            }
            jzGroupChatMapper.createOrUpdateGroupChat(groupChatList);
            idList.addAll(groupChatList.stream().map(JzGroupChat::getId).collect(Collectors.toList()));
            currentX++;
        }
        //设置为删除
        if (CollectionUtils.isNotEmpty(idList)) {
            jzGroupChatMapper.checkDeleted(idList);
        }

        JzBot bot = new JzBot();
        List<JzBot> botList = jzBotService.selectJzBotListSimple(bot);
        Map<String, List<JzBot>> map = botList.stream().collect(Collectors.groupingBy(JzBot::getToken));

        idList = new ArrayList<>();

        List<String> finalIdList = idList;
        map.forEach((token, bots) -> {
            int current = 0;
            while (true) {
                List<JzRoom> roomList = juZiBotUtil.getRooms(token, bots, current);
                if (CollectionUtils.isEmpty(roomList)) {
                    break;
                }
                jzRoomMapper.createOrUpdate(roomList);

                finalIdList.addAll(roomList.stream().map(JzRoom::getChatId).collect(Collectors.toList()));

                //开始新增群成员
                jzRoomMembersService.handelMembers(roomList);
                //群配置
                roomList.forEach(room -> chatConfig(room.getChatId()));

                current++;
            }
        });

        //设置为删除
        if (CollectionUtils.isNotEmpty(finalIdList)) {
            jzRoomMapper.checkDeleted(finalIdList);
        }

    }

    @Override
    public List<JzRoom> selectJzRoomListSimple(JzRoom jzRoom) {
        return jzRoomMapper.selectJzRoomListSimple(jzRoom);
    }

    @Override
    public List<JzRoom> selectJzRoomByWxId(String wxid) {
        JzRoom condition = new JzRoom();
        condition.setWxid(wxid);
        return jzRoomMapper.selectJzRoomList(condition);
    }

    @Override
    public List<JzRoom> selectJzRoomListByWxIds(List<String> wxidList) {
        if (CollectionUtils.isEmpty(wxidList)) return Collections.emptyList();
        return jzRoomMapper.selectJzRoomListByWxIds(wxidList);
    }

    @Override
    public List<JzRoom> selectJzRoomListByChatIds(List<String> chatIds) {
        if (CollectionUtils.isEmpty(chatIds)) return Collections.emptyList();
        return jzRoomMapper.selectJzRoomListByChatIds(chatIds);
    }

    @Override
    public List<JzRoom> selectActiveRooms(String code) {
        return jzRoomMapper.selectJzRoomList(new JzRoom(){{
            setStatus(EBoolean.YES.getCode());
            setDeleted(EBoolean.NO.getCode());
        }});
    }


    private void chatConfig(String chatId) {
        JzRoom condition = new JzRoom();
        condition.setChatId(chatId);
        List<JzRoom> roomList = jzRoomMapper.selectJzRoomList(condition);

        if (CollectionUtils.isEmpty(roomList)) {
            return;
        }
        JzRoom room = roomList.get(0);
        Map<String, String> chatConfig = new HashMap<>();
        //会话id
        chatConfig.put("chatId", room.getChatId());
        chatConfig.put("wxid", room.getWxid());
        chatConfig.put("botWxid", room.getJzBot().getWxid());
        //会话类型 room 群，user 一对一
        chatConfig.put("type", ETaggableRefType.ROOM.getCode());
        //ai开启状态 0未开启，1已开启
        chatConfig.put("status", room.getStatus());
        //智能体。
        chatConfig.put("agent", room.getAgent());
        //指令列表（逗号隔开） ARTICLE_XIAOHONGSHU,TEXT2IMAGE_MIDJOURNEY
        chatConfig.put("instruction", room.getInstruction());
        //是否无需@  ,0需要@ ，1不需要
        chatConfig.put("excludeAtReply", room.getExcludeAtReply());
        chatConfig.put("noticeEnv", room.getNoticeEnv());
        redisCache.setCacheObject("chat_config:" + room.getChatId(), chatConfig);
    }

}
