package com.ruoyi.jzbot.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.domain.dto.AttachmentDto;
import com.ruoyi.biz.domain.dto.VideoDto;
import com.ruoyi.biz.domain.dto.paln.VideoPayload;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.jzbot.service.AttachmentStrategyService;
import com.ruoyi.jzbot.util.QWUtil;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class VideoAttachmentStrategy implements AttachmentStrategyService {


    @Override
    public List<AttachmentDto> createAttachment(JSONObject payload, QWUtil qwUtil) {
        List<AttachmentDto> attachments = new ArrayList<>();
        //{"url":"/profile/upload/2024/04/14/222.png"}
        VideoPayload videoPayload = payload.toJavaObject(VideoPayload.class);

        String fileId = null;
        try {
            fileId = qwUtil.uploadAttachment(Constants.MOMENTS_VIDEO, videoPayload.getUrl());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        VideoDto videoDto = new VideoDto();
        videoDto.setMedia_id(fileId);
        AttachmentDto attachment = new AttachmentDto();
        attachment.setMsgtype(Constants.MOMENTS_VIDEO);
        attachment.setVideo(videoDto);
        attachments.add(attachment);
        return attachments;
    }
}
