package com.ruoyi.jzbot.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.biz.domain.BizMoments;
import com.ruoyi.biz.domain.dto.*;
import com.ruoyi.biz.service.IBizMomentsService;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.jzbot.config.QiWeiConfig;
import com.ruoyi.jzbot.service.AttachmentStrategyService;
import com.ruoyi.jzbot.service.impl.ImageAttachmentStrategy;
import com.ruoyi.jzbot.service.impl.LinkAttachmentStrategy;
import com.ruoyi.jzbot.service.impl.VideoAttachmentStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 企业微信 API
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class QWUtil {

    @Resource
    private QiWeiConfig qiWeiConfig;

    @Resource
    private RedisCache redisCache;
    @Resource
    private IBizMomentsService iBizMomentsService;

    private AttachmentStrategyService getStrategy(String msgType) {
        switch (msgType) {
            case Constants.MOMENTS_IMAGE:
                return new ImageAttachmentStrategy();
            case Constants.MOMENTS_VIDEO:
                return new VideoAttachmentStrategy();
            case Constants.MOMENTS_LINK:
                return new LinkAttachmentStrategy();
            default:
                throw new IllegalArgumentException("Unsupported message type: " + msgType);
        }
    }

    private String createPayload(BizMoments bizMoments) throws JsonProcessingException {
        MomentsMessageDto momentsMessageDto = new MomentsMessageDto();
        ObjectMapper mapper = new ObjectMapper();

        BizOperateNodeDto nodeDto = JSON.parseObject(bizMoments.getNodeInfo(), BizOperateNodeDto.class);
        JSONObject payload = nodeDto.getThemeJson().get(0).getPayload();

        TextDto textDto = new TextDto();
        textDto.setContent(nodeDto.getPrologue());
        momentsMessageDto.setText(textDto);

        AttachmentStrategyService strategy = getStrategy(bizMoments.getMsgtype());
        List<AttachmentDto> attachment = strategy.createAttachment(payload, this);

//        String name = DateUtils.datePath() + "朋友圈标签" + bizMoments.getId();
//        //处理标签
//        Map<String, String> map = addCorpTag("朋友圈临时标签组", Collections.singletonList(name));
//        String tagId = map.get(name);
//        //开始给用户打标签了
//        JSONObject jsonObject = JSONObject.parseObject(bizMoments.getWeixinList());
//        for (String botWeixin : jsonObject.keySet()) {
//            JSONArray userWeixinList = jsonObject.getJSONArray(botWeixin);
//            for (Object user : userWeixinList) {
//                markTag(botWeixin, user.toString(), Collections.singletonList(tagId), null);
//            }
//        }

        //标签
        VisibleRangeDto visibleRangeDto = new VisibleRangeDto();

        //能见范围
//        ExternalContactListDto externalContactListDto = new ExternalContactListDto();
//        externalContactListDto.setTag_list(Collections.singletonList(tagId));
//        visibleRangeDto.setExternal_contact_list(externalContactListDto);

        //发送人
        SenderListDto senderListDto = new SenderListDto();
        senderListDto.setUser_list(Arrays.asList(bizMoments.getSendBotWeixin().split(",")));
        visibleRangeDto.setSender_list(senderListDto);

        //组装
        momentsMessageDto.setVisible_range(visibleRangeDto);
        momentsMessageDto.setAttachments(attachment);
        return mapper.writeValueAsString(momentsMessageDto);
    }

    /**
     * 发送朋友圈
     *
     * @param bizMoments
     * @throws Exception
     */
    public void sendMoment(BizMoments bizMoments) throws Exception {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/add_moment_task?access_token=" + getAccessToken();
        String payload = createPayload(bizMoments);

        log.info("发表朋友圈参数:{}", payload);

        HttpClient client = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(new StringEntity(payload, "UTF-8"));
        httpPost.setHeader("Content-Type", "application/json;charset=utf-8");
        // 发送请求并接收响应
        HttpResponse response = client.execute(httpPost);
        // 响应转字符串
        String responseString = EntityUtils.toString(response.getEntity(), "UTF-8");
        JSONObject jsonObject = JSON.parseObject(responseString);


        log.info("发表朋友圈返回参数:{}", jsonObject);

        if (!"ok".equals(jsonObject.getString("errmsg"))) {
            throw new Exception("Failed to send moment: " + jsonObject.getString("errmsg"));
        } else {
            bizMoments.setStatus("1");
            bizMoments.setJobId(jsonObject.getString("jobid"));
            iBizMomentsService.updateBizMoments(bizMoments);
        }
    }


    public String uploadAttachment(String mediaType, String fileUrl) throws Exception {
        File file = url2File(fileUrl, mediaType);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/media/upload_attachment?access_token=" + getAccessToken() + "&media_type=" + mediaType + "&attachment_type=1";
        HttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost(url);
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        builder.addBinaryBody("media", file, ContentType.APPLICATION_OCTET_STREAM, file.getName());
        HttpEntity multipart = builder.build();
        post.setEntity(multipart);
        org.apache.http.HttpResponse response = client.execute(post);
        String responseString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        JSONObject jsonObject = JSON.parseObject(responseString);
        file.deleteOnExit();
        if (!jsonObject.containsKey("media_id")) {
            throw new ServiceException("上传文件异常" + jsonObject.toJSONString());
        }
        return jsonObject.getString("media_id");
    }

    private  File url2File(String fileUrl, String mediaType) throws IOException {
        String fileExtension = "";
        if (mediaType.equals(Constants.MOMENTS_IMAGE)) {
            fileUrl = fileUrl + "?x-oss-process=image/resize,s_1080,l_10800";
            fileExtension = ".jpg";  // 对图片使用 .jpg 作为扩展名
        } else if (mediaType.equals(Constants.MOMENTS_VIDEO)) {
            fileExtension = ".mp4";  // 对视频使用 .mp4 作为扩展名
        }
        // URL to image file
        URL url = new URL(fileUrl);
        // Open a connection to the URL
        URLConnection conn = url.openConnection();
        // Get the input stream from the connection
        InputStream in = conn.getInputStream();
        // Create a temporary file to store the downloaded image
        File tempFile = File.createTempFile("media", fileExtension);
        // Create an output stream to write the image to the temporary file
        FileOutputStream out = new FileOutputStream(tempFile);
        // Read the image from the input stream and write it to the output stream
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = in.read(buffer)) != -1) {
            out.write(buffer, 0, bytesRead);
        }
        // Close the input and output streams
        in.close();
        out.close();
        return tempFile;
    }

    /**
     * 根据job获取微信id
     *
     * @param jobId
     * @return
     */
    public String getMomentId(String jobId) {
        String str = HttpUtils.sendGet("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_moment_task_result?access_token=" + getAccessToken() + "&jobid=" + jobId);
        JSONObject parseObject = JSON.parseObject(str).getJSONObject("result");
        return parseObject.getString("moment_id");
    }


    /**
     * 获取该朋友圈已确认发送的bot
     *
     * @param momentId
     * @return
     */
    public JSONArray getMomentTask(String momentId) {
        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("moment_id", momentId);
        objectObjectHashMap.put("limit", 1000);
        JSONObject jsonObject = HttpUtils.postToJsonObject("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_moment_task?access_token=" + getAccessToken(), objectObjectHashMap);
        if ("ok".equals(jsonObject.getString("errmsg"))) {
            return jsonObject.getJSONArray("task_list");
        } else {
            throw new ServiceException("未知错误");
        }

        //{
        //	"errcode":0,
        //	"errmsg":"ok",
        //	"next_cursor":"CURSOR",
        //	"task_list":[
        //		{
        //			"userid":"zhangsan",
        //			"publish_status":1
        //		}
        //	]
        //}
    }

    public JSONObject getMomentsComment(String momentId, String userId) {
        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("moment_id", momentId);
        objectObjectHashMap.put("userid", userId);
        return HttpUtils.postToJsonObject("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_moment_comments?access_token=" + getAccessToken(), objectObjectHashMap);
    }


    /**
     * 获取 企微联系人详情
     * {
     * "errcode": 0,
     * "errmsg": "ok",
     * "external_contact": {
     * "external_userid": "wmdiZoLAAA9olX_TP2PJAuU467MwSvQA",
     * "name": "杰尼杰尼",
     * "type": 1,
     * "avatar": "http://wx.qlogo.cn/mmhead/icZR3enfMDibtmHVVcg7RZu1ZagWrxlg4p5IbAT672O8x2NcazzKWx3R2tJHZTIjA3XSCEHKNJfKI/0",
     * "gender": 0,
     * "unionid": "oAeM56mhjfBG22N90_SjwmbI18aM"
     * },
     * "follow_user": [
     * {
     * "userid": "alex",
     * "remark": "周栋",
     * "description": "",
     * "createtime": 1703749987,
     * "tags": [],
     * "remark_mobiles": [
     * "13004724825"
     * ],
     * "add_way": 2,
     * "oper_userid": "alex"
     * }
     * ]
     * }
     *
     * @return
     */
    public String getContactUnionId(String weixin) {
        String str = HttpUtils.sendGet("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get?access_token=" + getAccessToken() + "&external_userid=" + weixin);
        JSONObject parseObject = JSON.parseObject(str).getJSONObject("external_contact");
        return parseObject.getString("unionid");
    }


    /**
     * 获取群信息
     *
     * @param
     * @return
     */
    public JSONArray groupChatList() {
        String api = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/list?access_token=";
        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
//        objectObjectHashMap.put("userid_list", useridList);
        objectObjectHashMap.put("limit", 100);

        JSONObject jsonObject = HttpUtils.postToJsonObject(api + getAccessToken(), objectObjectHashMap);
        isSuccess(jsonObject, api);
        return jsonObject.getJSONArray("group_chat_list");
    }


    /**
     * 获取群信息
     *
     * @param chatId
     * @return
     */
    public JSONObject groupChat(String chatId) {
        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("chat_id", chatId);
        objectObjectHashMap.put("need_name", "1");

        JSONObject jsonObject = HttpUtils.postToJsonObject("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/get?access_token=" + getAccessToken(), objectObjectHashMap);
        return jsonObject;
    }


    /**
     * 获取群聊会话
     *
     * @param chatId
     * @return
     */
    public String appChatGet(String chatId) {
        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("chat_id", chatId);
        objectObjectHashMap.put("need_name", "1");
        String format = String.format("https://qyapi.weixin.qq.com/cgi-bin/appchat/get?access_token=%s&chatid=%s", getAccessToken(), chatId);
        String string = HttpUtils.sendGet(format);

        log.info(string);
        return string;
    }


    /**
     * 获取企业标签库
     * {
     * "errcode": 0,
     * "errmsg": "ok",
     * "tag_group": [{
     * "group_id": "TAG_GROUPID1",
     * "group_name": "GOURP_NAME",
     * "create_time": 1557838797,
     * "order": 1,
     * "deleted": false,
     * "tag": [{
     * "id": "TAG_ID1",
     * "name": "NAME1",
     * "create_time": 1557838797,
     * "order": 1,
     * "deleted": false
     * },
     * {
     * "id": "TAG_ID2",
     * "name": "NAME2",
     * "create_time": 1557838797,
     * "order": 2,
     * "deleted": true
     * }
     * ]
     * }]
     * }
     *
     * @param tagId   可筛选的tagId
     * @param groupId 可筛选的groupId
     * @return
     */
    public JSONArray getCorpTagList(String tagId, String groupId) {
        HashMap<String, Object> map = new HashMap<>();
        if (StringUtils.isNotBlank(tagId)) {
            map.put("tag_id", Collections.singletonList(tagId));
        }
        if (StringUtils.isNotBlank(groupId)) {
            map.put("group_id", Collections.singletonList(groupId));
        }
        JSONObject parseObject = HttpUtils.postToJsonObject("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_corp_tag_list?access_token=ACCESS_TOKEN".replace("ACCESS_TOKEN", getAccessToken()), map);

        if ("ok".equals(parseObject.getString("errmsg"))) {
            return parseObject.getJSONArray("tag_group");
        }
        return null;
    }


    /**
     * 新增标签
     * 如果要向指定的标签组下添加标签，需要填写group_id参数；如果要创建一个全新的标签组以及标签，则需要通过group_name参数指定新标签组名称，如果填写的groupname已经存在，则会在此标签组下新建标签。
     * 如果填写了group_id参数，则group_name和标签组的order参数会被忽略。
     * 不支持创建空标签组。
     * 标签组内的标签不可同名，如果传入多个同名标签，则只会创建一个
     * <p>
     * {
     * "errcode": 0,
     * "errmsg": "ok",
     * "tag_group": {
     * "group_id": "TAG_GROUPID1",
     * "group_name": "GOURP_NAME",
     * "create_time": 1557838797,
     * "order": 1,
     * "tag": [{
     * "id": "TAG_ID1",
     * "name": "NAME1",
     * "create_time": 1557838797,
     * "order": 1
     * },
     * {
     * "id": "TAG_ID2",
     * "name": "NAME2",
     * "create_time": 1557838797,
     * "order": 2
     * }
     * ]
     * }
     * }
     *
     * @param groupName
     * @param tagList
     * @return
     */
    public Map<String, String> addCorpTag(String groupName, List<String> tagList) {

        if (CollectionUtils.isEmpty(tagList)) {
            return null;
        }

        List<JSONObject> list = tagList.stream().map((name -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", name);
            return jsonObject;
        })).collect(Collectors.toList());

        /*{
        //	"group_id": "GROUP_ID",
        //	"group_name": "GROUP_NAME",
        //	"order": 1,
        //	"tag": [{
        //			"name": "TAG_NAME_1",
        //			"order": 1
        //		},
        //		{
        //			"name": "TAG_NAME_2",
        //			"order": 2
        //		}
        //	],
        //	 "agentid" : 1000014
        }*/

        HashMap<String, Object> map = new HashMap<>();
        map.put("group_name", groupName);
        //
        map.put("tag", list);

        JSONObject parseObject = HttpUtils.postToJsonObject("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/add_corp_tag?access_token=ACCESS_TOKEN".replace("ACCESS_TOKEN", getAccessToken()), map);

        if ("ok".equals(parseObject.getString("errmsg"))) {
            JSONObject jsonObject = parseObject.getJSONObject("tag_group");

            String groupId = jsonObject.getString("group_id");
            JSONArray tagArray = jsonObject.getJSONArray("tag");

            Map<String, String> tagMap = tagArray.stream().filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            object -> {
                                JSONObject item = (JSONObject) object;
                                return item.getString("name");
                            },
                            object -> {
                                JSONObject item = (JSONObject) object;
                                return item.getString("id");
                            }
                    ));

            //设置标签组Id，默认group_id
            tagMap.put("group_id", groupId);
            return tagMap;
        }

        log.error("创建签失败:{}", parseObject.getString("errmsg"));
        throw new ServiceException("新建标签失败");
    }


    /**
     * 修改标签
     *
     * @param id    标签或标签组的id
     * @param name  新的标签或标签组名称，最长为30个字符
     * @param order 标签/标签组的次序值。order值大的排序靠前。有效的值范围是[0, 2^32)
     * @return
     */
    public boolean editCorpTag(String id, String name, Integer order) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("name", name);
        map.put("order", order);
        JSONObject parseObject = HttpUtils.postToJsonObject("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/edit_corp_tag?access_token=ACCESS_TOKEN".replace("ACCESS_TOKEN", getAccessToken()), map);
        return "ok".equals(parseObject.getString("errmsg"));
    }


    /**
     * 删除标签
     *
     * @param tagIdList tag_id和group_id不可同时为空。
     * @param groupList
     * @return
     */
    public boolean delCorpTag(List<String> tagIdList, List<String> groupList) {
        HashMap<String, Object> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(tagIdList)) {
            map.put("tag_id", tagIdList);
        }
        if (CollectionUtils.isNotEmpty(groupList)) {
            map.put("group_id", groupList);
        }
        JSONObject parseObject = HttpUtils.postToJsonObject("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/del_corp_tag?access_token=ACCESS_TOKEN".replace("ACCESS_TOKEN", getAccessToken()), map);
        return "ok".equals(parseObject.getString("errmsg"));
    }


    /**
     * 给客户打上标签
     *
     * @param botWeixin
     * @param userWeixin
     * @param addTagIdList
     * @param delTagIdList
     * @return
     */
    public boolean markTag(String botWeixin, String userWeixin, List<String> addTagIdList, List<String> delTagIdList) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("userid", botWeixin);
        map.put("external_userid", userWeixin);
        if (CollectionUtils.isNotEmpty(addTagIdList)) {
            map.put("add_tag", addTagIdList);
        }
        if (CollectionUtils.isNotEmpty(delTagIdList)) {
            map.put("remove_tag", delTagIdList);
        }

        JSONObject parseObject = HttpUtils.postToJsonObject("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/mark_tag?access_token=ACCESS_TOKEN".replace("ACCESS_TOKEN", getAccessToken()), map);
        return "ok".equals(parseObject.getString("errmsg"));
    }


    /**
     * 创建群进群配置
     * https://developer.work.weixin.qq.com/document/path/92229
     *
     * @param scene          场景 1 - 群的小程序插件 2 - 群的二维码插件
     * @param remark
     * @param autoCreateRoom
     * @param roomBaseName
     * @param roomBaseId
     * @param chatIdList     room_base_name 和 room_base_id 两个参数配合，用于指定自动新建群的群名
     *                       例如，假如 room_base_name = "销售客服群", room_base_id = 10
     *                       那么，自动创建的第一个群，群名为“销售客服群10”；自动创建的第二个群，群名为“销售客服群11”，依次类推
     * @return
     */
    public String addJoinWay(List<String> chatIdList, Integer scene, String remark, Integer autoCreateRoom, String roomBaseName, Integer roomBaseId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("scene", scene); //1小程序插件码，2群扫描二维码
        map.put("remark", remark);
        map.put("auto_create_room", autoCreateRoom); //满人自动创建群, 0-否；1-是。 默认为1
        map.put("room_base_name", roomBaseName); //群名称, 自动建群的群名前缀
        map.put("room_base_id", roomBaseId); //群序号
        map.put("chat_id_list", chatIdList); //需要群列表
//        map.put("state", state);
        JSONObject jsonObject = HttpUtils.postToJsonObject("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/add_join_way?debug=1&access_token=ACCESS_TOKEN".replace("ACCESS_TOKEN", getAccessToken()), map);
        if ("ok".equals(jsonObject.getString("errmsg"))) {
            return jsonObject.getString("config_id");
        }
        throw new ServiceException("创建群进群配置失败");
    }

    /**
     * 更新企微加入群聊
     */
    public void updateJoinWay(String configId, List<String> chatIdList, Integer scene, String remark, Integer autoCreateRoom, String roomBaseName, Integer roomBaseId) {
        String api = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/update_join_way?access_token=ACCESS_TOKEN";
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("config_id", configId); //1小程序插件码，2群扫描二维码
        map.put("scene", scene); //1小程序插件码，2群扫描二维码
        map.put("remark", remark);
        map.put("auto_create_room", autoCreateRoom); //满人自动创建群, 0-否；1-是。 默认为1
        map.put("room_base_name", roomBaseName); //群名称, 自动建群的群名前缀
        map.put("room_base_id", roomBaseId); //群序号
        map.put("chat_id_list", chatIdList); //需要群列表
//        map.put("state", state);
        JSONObject jsonObject = HttpUtils.postToJsonObject(api.replace("ACCESS_TOKEN", getAccessToken()), map);
        isSuccess(jsonObject, api);
    }

    public boolean deleteJoinWay(String configId) {
        String api = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/del_join_way?access_token=ACCESS_TOKEN";
        HashMap<String, Object> map = new HashMap<>();
        map.put("config_id", configId); //1小程序插件码，2群扫描二维码
        JSONObject jsonObject = HttpUtils.postToJsonObject(api.replace("ACCESS_TOKEN", getAccessToken()), map);
        return "ok".equals(jsonObject.getString("errmsg"));
    }

    /**
     * 获取二维码
     *
     * @param configId
     * @return
     */
    public String getJoinQrUrl(String configId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("config_id", configId);
        JSONObject jsonObject = HttpUtils.postToJsonObject("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/get_join_way?debug=1&access_token=ACCESS_TOKEN&".replace("ACCESS_TOKEN", getAccessToken()), map);
        if ("ok".equals(jsonObject.getString("errmsg"))) {
            return jsonObject.getJSONObject("join_way").getString("qr_code");
        }
        throw new ServiceException("获取二维码失败");
    }

    /**
     * 删除进群的配置
     *
     * @param configId
     * @return
     */
    public boolean delJoinWay(String configId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("config_id", configId);
        JSONObject jsonObject = HttpUtils.postToJsonObject("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/del_join_way?access_token=ACCESS_TOKEN".replace("ACCESS_TOKEN", getAccessToken()), map);
        return "ok".equals(jsonObject.getString("errmsg"));
    }


    /**
     * 获取access_token
     *
     * @return
     */
    protected String getAccessToken() {
        String accessTokenKey = CacheConstants.ACCESS_TOKEN_KEY + "QW";
        String accessToken;
        if (redisCache.hasKey(accessTokenKey)) {
            accessToken = redisCache.getCacheObject(accessTokenKey);
        } else {
            String str = HttpUtils.sendGet("https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=" + qiWeiConfig.getCorpid() + "&corpsecret=" + qiWeiConfig.getCorpsecret());
            JSONObject parseObject = JSON.parseObject(str);
            accessToken = parseObject.getString("access_token");
            if (StringUtils.isNotBlank(accessToken)) {
                redisCache.setCacheObject(accessTokenKey, accessToken, 7000, TimeUnit.SECONDS);
            }
        }
        return accessToken;
    }

    public JSONObject createCustomerAcquisition(String linkName, List<String> userList) {
        String api = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/customer_acquisition/create_link?access_token=ACCESS_TOKEN";
        HashMap<String, Object> map = new HashMap<>();
        map.put("link_name", linkName);
        map.put("range", new HashMap<String, Object>() {{
            put("user_list", userList);
        }});
        map.put("department_list", new ArrayList<>());

        JSONObject jsonObject = HttpUtils.postToJsonObject(api.replace("ACCESS_TOKEN", getAccessToken()), map);
        isSuccess(jsonObject, api);
        return jsonObject;
    }

    public JSONObject updateCustomerAcquisition(String linkId, String linkName, List<String> userList) {
        String api = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/customer_acquisition/update_link?access_token=ACCESS_TOKEN";
        HashMap<String, Object> map = new HashMap<>();
        map.put("link_id", linkId);
        map.put("link_name", linkName);
        map.put("range", new HashMap<String, Object>() {{
            put("user_list", userList);
        }});
        map.put("department_list", new ArrayList<>());

        JSONObject jsonObject = HttpUtils.postToJsonObject(api.replace("ACCESS_TOKEN", getAccessToken()), map);
        isSuccess(jsonObject, api);
        return jsonObject;
    }

    /**
     * 获客助手链接列表
     */
    public JSONObject customerAcquisitionList() {
        String api = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/customer_acquisition/list_link?access_token=ACCESS_TOKEN";
        HashMap<String, Object> map = new HashMap<>();
        map.put("limit", 100);
        map.put("cursor", null);
        JSONObject jsonObject = HttpUtils.postToJsonObject(api.replace("ACCESS_TOKEN", getAccessToken()), map);
        isSuccess(jsonObject, api);
        return jsonObject;
    }

    /**
     * 删除获客助手链接
     *
     * @param linkId linkId
     */
    public boolean deleteCustomerAcquisition(String linkId) {
        String api = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/customer_acquisition/delete_link?access_token=ACCESS_TOKEN";
        HashMap<String, Object> map = new HashMap<>();
        map.put("link_id", linkId);
        JSONObject jsonObject = HttpUtils.postToJsonObject(api.replace("ACCESS_TOKEN", getAccessToken()), map);
        return "ok".equals(jsonObject.getString("errmsg"));
    }

    /**
     * 增加联系我
     *
     * @param remark   remark
     * @param userList userList
     */
    public JSONObject addContactWay(String remark, List<String> userList) {
        String api = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/add_contact_way?access_token=ACCESS_TOKEN";
        HashMap<String, Object> map = new HashMap<>();
        map.put("type", 2);
        map.put("scene", 2);
        map.put("user", userList);
        map.put("is_exclusive", false);
        map.put("remark", remark);
        JSONObject jsonObject = HttpUtils.postToJsonObject(api.replace("ACCESS_TOKEN", getAccessToken()), map);
        isSuccess(jsonObject, api);
        return jsonObject;
    }

    public JSONObject updateContactWay(String configId, String remark, List<String> userList) {
        String api = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/update_contact_way?access_token=ACCESS_TOKEN";
        HashMap<String, Object> map = new HashMap<>();
        map.put("config_id", configId);
        map.put("type", 2);
        map.put("scene", 2);
        map.put("user", userList);
        map.put("is_exclusive", false);
        map.put("remark", remark);
        JSONObject jsonObject = HttpUtils.postToJsonObject(api.replace("ACCESS_TOKEN", getAccessToken()), map);
        isSuccess(jsonObject, api);
        return jsonObject;
    }

    public boolean deleteContactWay(String configId) {
        if (StringUtils.isBlank(configId)) {
            log.info("configId is null, not process");
            return false;
        }
        String api = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/del_contact_way?access_token=ACCESS_TOKEN";
        HashMap<String, Object> map = new HashMap<>();
        map.put("config_id", configId);
        JSONObject jsonObject = HttpUtils.postToJsonObject(api.replace("ACCESS_TOKEN", getAccessToken()), map);
        return "ok".equals(jsonObject.getString("errmsg"));
    }

    private void isSuccess(JSONObject response, String api) {
        if (!"ok".equals(response.getString("errmsg"))) {
            log.error("调用企业微信接口失败, api: {}, ex: {}", api, response);
            throw new ServiceException("调用企业微信接口失败");
        }
    }

}
