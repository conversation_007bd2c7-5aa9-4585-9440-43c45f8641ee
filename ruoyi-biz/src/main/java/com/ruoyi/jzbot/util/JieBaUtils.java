package com.ruoyi.jzbot.util;

import com.huaban.analysis.jieba.JiebaSegmenter;
import com.huaban.analysis.jieba.WordDictionary;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

import java.io.File;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

public class JieBaUtils {
    /**
     * jieba 分词
     *
     * @param content
     * @return
     * @throws IOException
     */
    public static List<String> jieba(String content) throws IOException {
        JiebaSegmenter jiebaSegmenter = new JiebaSegmenter();
        List<String> stop_words;
        //加载自定义字典
        String dictFilePath = JieBaUtils.class.getResource("/file/dict.txt").getPath();
        Path path = Paths.get(dictFilePath);
        WordDictionary.getInstance().loadUserDict(path);
        //加载过滤文件
        try (InputStream inputStream = JieBaUtils.class.getClassLoader().getResourceAsStream("file/stop_words.txt")) {
            if (inputStream == null) {
                throw new IOException("stop_words.txt file not found in classpath");
            }
            stop_words = IOUtils.readLines(inputStream, StandardCharsets.UTF_8);
        }
        List<String> result = jiebaSegmenter.sentenceProcess(content);
        //过滤
        result = result.stream().map(String::trim).filter(o -> !stop_words.contains(o)).collect(Collectors.toList());
        return result;
    }
}
