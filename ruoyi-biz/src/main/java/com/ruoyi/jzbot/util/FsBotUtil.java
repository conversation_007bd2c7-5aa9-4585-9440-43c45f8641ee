package com.ruoyi.jzbot.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.biz.domain.BizMoments;
import com.ruoyi.biz.domain.dto.*;
import com.ruoyi.biz.service.IBizMomentsService;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.jzbot.config.QiWeiConfig;
import com.ruoyi.jzbot.service.AttachmentStrategyService;
import com.ruoyi.jzbot.service.impl.ImageAttachmentStrategy;
import com.ruoyi.jzbot.service.impl.LinkAttachmentStrategy;
import com.ruoyi.jzbot.service.impl.VideoAttachmentStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 企业微信 API
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class FsBotUtil {


    @Value("${bot.fs-token}")
    private String token;


    public void sendFsMessage(String dataMessage) {
        String fsUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/" + token;
        Map<String, Object> map = new HashMap<>();
        Map<String, String> contentMap = new HashMap<>();
        contentMap.put("text", dataMessage);

        map.put("msg_type", "text");
        map.put("content", contentMap);
        HttpUtils.post(fsUrl, map);
    }


    /**
     * 发送飞书数据统计卡片
     * @param title 卡片标题
     * @param statsMap 统计数据Map，key为标题，value为统计值
     */
    public void sendFsStatsCardMessage(String title, Map<String, Object> statsMap) {
        String fsUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/" + token;
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> cardMap = new HashMap<>();
        Map<String, Object> headerMap = new HashMap<>();
        Map<String, Object> titleMap = new HashMap<>();

        // 设置标题
        titleMap.put("content", title);
        titleMap.put("tag", "plain_text");
        headerMap.put("title", titleMap);
        headerMap.put("template", "red");  // 设置标题背景色为红色

        // 设置多个内容区域
        List<Map<String, Object>> elements = new ArrayList<>();

        // 添加@所有人
        Map<String, Object> atDiv = new HashMap<>();
        Map<String, Object> atText = new HashMap<>();
        atText.put("content", "@所有人");
        atText.put("tag", "lark_md");
        atDiv.put("tag", "div");
        atDiv.put("text", atText);
        elements.add(atDiv);

        // 遍历统计数据
        boolean isLastGroup = false;  // 标记是否是最后一个分组
        for (Map.Entry<String, Object> entry : statsMap.entrySet()) {
            if (entry.getValue() == null) {
                // 这是分组标题，在添加新分组标题前，先添加分割线（除了第一个分组）
                if (!elements.isEmpty() && !elements.get(elements.size() - 1).get("tag").equals("hr")) {
                    Map<String, Object> hrMap = new HashMap<>();
                    hrMap.put("tag", "hr");
                    elements.add(hrMap);
                }

                // 添加分组标题
                Map<String, Object> divMap = new HashMap<>();
                Map<String, Object> textMap = new HashMap<>();
                textMap.put("content", "**" + entry.getKey() + "**");  // 加粗显示分组标题
                textMap.put("tag", "lark_md");
                divMap.put("tag", "div");
                divMap.put("text", textMap);
                elements.add(divMap);
            } else {
                // 这是普通数据项
                Map<String, Object> divMap = new HashMap<>();
                Map<String, Object> textMap = new HashMap<>();
                textMap.put("content", entry.getKey() + "：" + entry.getValue());  // 使用冒号连接键值
                textMap.put("tag", "lark_md");
                divMap.put("tag", "div");
                divMap.put("text", textMap);
                elements.add(divMap);
            }
        }

        cardMap.put("header", headerMap);
        cardMap.put("elements", elements);

        map.put("msg_type", "interactive");
        map.put("card", cardMap);

        HttpUtils.post(fsUrl, map);
    }

    // 使用示例
    public void sendStatsOverview(String date) {
        Map<String, Object> statsMap = new LinkedHashMap<>();  // 使用LinkedHashMap保持顺序
        statsMap.put("好友群聊统计", null);  // 作为分组标题
        statsMap.put("新增人数", 0);
        statsMap.put("流失人数", 0);
        statsMap.put("活跃人数", 41);
        statsMap.put("活跃群（发言人数/总人数>5%）", 0);

        statsMap.put("客户在线统计", null);  // 作为分组标题
        statsMap.put("今日客服在线数", 0);

        statsMap.put("消息统计", null);  // 作为分组标题
        statsMap.put("消息总数", 835);
        statsMap.put("客服发送消息数", 0);

        sendFsStatsCardMessage(date + " 数据概览", statsMap);
    }

    public static void main(String[] args) {
        FsBotUtil fsBotUtil = new FsBotUtil();
        fsBotUtil.token="61ea55ef-e3b1-462f-a8f0-89a1e375b451";
        fsBotUtil.sendStatsOverview("2025-02-07");
    }
}
