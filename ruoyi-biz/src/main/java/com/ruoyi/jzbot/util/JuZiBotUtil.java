package com.ruoyi.jzbot.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.domain.BizMoments;
import com.ruoyi.biz.domain.BizMomentsComment;
import com.ruoyi.biz.domain.dto.BizOperateNodeDto;
import com.ruoyi.biz.domain.dto.paln.ChannelPayload;
import com.ruoyi.biz.domain.dto.paln.ImagePayload;
import com.ruoyi.biz.domain.dto.paln.VideoPayload;
import com.ruoyi.biz.domain.dto.paln.WebPagePayload;
import com.ruoyi.biz.domain.req.ImageCreateReq;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.biz.service.IBizMomentsService;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.EJzSendMessageType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.jzbot.config.JuZiConfig;
import com.ruoyi.jzbot.domain.JzBot;
import com.ruoyi.jzbot.domain.JzContact;
import com.ruoyi.jzbot.domain.JzGroupChat;
import com.ruoyi.jzbot.domain.JzRoom;
import com.ruoyi.jzbot.domain.JzRoomMembers;
import com.ruoyi.jzbot.domain.dto.Payload;
import com.ruoyi.jzbot.service.IJzContactService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class JuZiBotUtil {

    @Resource
    private JuZiConfig juZiConfig;

    @Resource
    private RedisCache redisCache;


    @Resource
    private IBizMomentsService iBizMomentsService;

    @Resource
    private IJzContactService jzContactService;
    /**
     * 获取小组 api token
     *
     * @return
     */
    private List<String> getJuZiGroupAPI() {
        String str = HttpUtils.sendGet("https://aa-hub.ddregion.com/api/v1/instantReply/mhAPI/get?token=" + juZiConfig.getToken());
        JSONObject parseObject = JSON.parseObject(str);
        JSONArray data = parseObject.getJSONArray("data");
        List<String> tokenList = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            tokenList.add(data.getJSONObject(i).getString("token"));
        }
        return tokenList;
    }

    /**
     * 获取所有机器人BOT
     *
     * @return
     */
    public List<JzBot> getBots() {
//        List<String> tokenList = getJuZiGroupAPI();
//        if (CollectionUtils.isEmpty(tokenList)) {
//            return new ArrayList<>();
//        }
        List<JzBot> botList = new ArrayList<>();

        String str = HttpUtils.sendGet("https://aa-api.ddregion.com/bot/list?token=" + juZiConfig.getGroupToken());

        JSONObject parseObject = JSON.parseObject(str);
        JSONArray data = parseObject.getJSONArray("data");
        for (int i = 0; i < data.size(); i++) {
            JSONObject jsonObject = data.getJSONObject(i);
            JzBot bot = new JzBot();
            bot.setBotId(jsonObject.getString("id"));
            bot.setWxid(jsonObject.getString("wxid"));
            bot.setWeixin(jsonObject.getString("weixin"));
            bot.setNickname(jsonObject.getString("nickName"));
            bot.setAvatar(jsonObject.getString("avatar"));
            bot.setToken(juZiConfig.getGroupToken());
            bot.setOnline(jsonObject.getBoolean("online") ? EBoolean.YES.getCode() : EBoolean.NO.getCode());
            bot.setJoinTime(new Date());
            botList.add(bot);
        }


//        for (String token : tokenList) {
//            String str = HttpUtils.sendGet("https://aa-api.ddregion.com/bot/list?token=" + token);
//
//            JSONObject parseObject = JSON.parseObject(str);
//            JSONArray data = parseObject.getJSONArray("data");
//        for (int i = 0; i < data.size(); i++) {
//            JSONObject jsonObject = data.getJSONObject(i);
//            JzBot bot = new JzBot();
//            bot.setBotId(jsonObject.getString("id"));
//            bot.setWxid(jsonObject.getString("wxid"));
//            bot.setWeixin(jsonObject.getString("weixin"));
//            bot.setNickname(jsonObject.getString("nickName"));
//            bot.setAvatar(jsonObject.getString("avatar"));
//            bot.setToken(juZiConfig.getGroupToken());
//            bot.setOnline(jsonObject.getBoolean("online") ? EBoolean.YES.getCode() : EBoolean.NO.getCode());
//            bot.setJoinTime(new Date());
//            botList.add(bot);
//        }
//        }
        return botList;
    }


    public List<JzGroupChat> getGroupChats(int current) {
        int pageSize = 30;
        String str = HttpUtils.sendGet("https://aa-hub.ddregion.com/api/v2/groupChat/list?token=" + juZiConfig.getToken() + "&current=" + current + "&pageSize=" + pageSize);
        JSONObject parseObject = JSON.parseObject(str);

        JSONArray jsonArray = parseObject.getJSONArray("data");

        if (CollectionUtils.isEmpty(jsonArray)) {
            return null;
        }

        List<JSONObject> data = jsonArray.toJavaList(JSONObject.class);

        List<JzGroupChat> groupChatList = new ArrayList<>();

        for (JSONObject group : data) {
            if (StringUtils.isBlank(group.getString("name"))) {
                continue;
            }
            JzGroupChat groupChat = new JzGroupChat();
            groupChat.setId(group.getString("imRoomId"));
            groupChat.setTopic(group.getString("name"));
            groupChat.setAvatarUrl(group.getString("chatAvatar"));
            groupChat.setOwnerId(group.getString("owner"));
            if (group.containsKey("memberList")) {
                groupChat.setMemberCount(group.getJSONArray("memberList").size());
            } else {
                groupChat.setMemberCount(1);
            }
            if (group.containsKey("createTime")) {
                groupChat.setCreateTime(new Date(group.getLong("createTime")));
            } else {
                groupChat.setCreateTime(new Date());
            }
            groupChat.setWecomChatId(group.getString("wecomChatId"));

            if (StringUtils.isBlank(groupChat.getWecomChatId())) {
                str = HttpUtils.sendGet("https://aa-hub.ddregion.com/api/v1/groupChat/wxid_to_wecomChatId?token=" + juZiConfig.getToken() + "&wxid=" + groupChat.getId());
                parseObject = JSON.parseObject(str);
                if (parseObject.containsKey("wecomChatId")) {
                    groupChat.setWecomChatId(parseObject.getString("wecomChatId"));
                } else {
                    groupChat.setWecomChatId("无");
                }
            }
            groupChatList.add(groupChat);
        }

        return groupChatList;
    }

    /**
     * 获取所有机器人BOT
     *
     * @return
     */
    public List<JzRoom> getRooms(String token, List<JzBot> bots, int current) {

        Map<String, List<JzBot>> botwxMap = bots.stream().collect(Collectors.groupingBy(JzBot::getWxid));

        List<JzRoom> roomList = new ArrayList<>();
        String str = HttpUtils.sendGet("https://aa-api.ddregion.com/room/list?token=" + token + "&current=" + current);
        JSONObject parseObject = JSON.parseObject(str);
        JSONArray data = parseObject.getJSONArray("data");
        if (data == null || data.isEmpty()) {
            return null;
        }
        for (int i = 0; i < data.size(); i++) {
            JzRoom chat = new JzRoom();
            chat.setChatId(data.getJSONObject(i).getString("chatId"));
            String botWxid = data.getJSONObject(i).getJSONObject("botInfo").getString("wxid");
            chat.setBotId(botwxMap.get(botWxid).get(0).getBotId());
            chat.setWxid(data.getJSONObject(i).getString("wxid"));
            chat.setTopic(data.getJSONObject(i).getString("topic"));
            chat.setAvatarUrl(data.getJSONObject(i).getString("avatarUrl"));
            chat.setOwnerId(data.getJSONObject(i).getString("ownerId"));
            chat.setMemberCount(data.getJSONObject(i).getLong("memberCount"));
            chat.setDeleted(data.getJSONObject(i).getBoolean("deleted") ? "1" : "0");
            //群员列表
            JSONArray members = data.getJSONObject(i).getJSONArray("members");

            List<JzRoomMembers> roomMembers = new ArrayList<>();
            int friendCount = 0;
            for (int xx = 0; xx < members.size(); xx++) {
                JSONObject membersJSONObject = members.getJSONObject(xx);
                Boolean isFriend = membersJSONObject.getBoolean("isFriend");
                if (isFriend) {
                    friendCount++;
                }

                JzRoomMembers member = new JzRoomMembers();
                member.setWxid(membersJSONObject.getString("wxid"));
                member.setRoomId(chat.getWxid());
                member.setNickname(membersJSONObject.getString("nickName"));
                member.setPhoto(membersJSONObject.getString("avatarUrl"));
                member.setIsFriend(isFriend ? 1 : 0);
                member.setRoomAlias(membersJSONObject.getString("roomAlias"));
                member.setStatus("1");
                member.setJoinScene(membersJSONObject.getString("joinScene"));
                member.setJoinTime(new Date(membersJSONObject.getLong("joinTime") * 1000));
                if (member.getWxid().equals(botWxid)) {
                    chat.setJoinTime(member.getJoinTime());
                }
                roomMembers.add(member);
            }
            chat.setFriendCount((long) friendCount);
            chat.setMembersList(roomMembers);
            roomList.add(chat);
        }
        return roomList;
    }


    /**
     * 创建群聊
     *
     * @param botUserId
     * @param greeting
     * @param name
     * @param wxids
     * @return
     */
    public String createRoom(String botUserId, String greeting, String name, List<String> wxids) {
        if (CollectionUtils.isEmpty(wxids)) {
            throw new ServiceException("群成员不能为空");
        }
        if (wxids.size() < 2 || wxids.size() > 40) {
            throw new ServiceException("wxids至少需要两个最多支持40个");
        }

        JSONObject json = new JSONObject();
        json.put("botUserId", botUserId);
        json.put("greeting", greeting);
        json.put("name", name);
        json.put("wxids", wxids);
        json.put("token", juZiConfig.getGroupToken());
        try {
            JSONObject jsonObject = HttpUtils.postToJsonObject("https://aa-api.ddregion.com/room/create", json);
            log.info("创建群返回:{}", jsonObject.toJSONString());
            if (jsonObject.getInteger("code") == 0) {
                return jsonObject.getJSONObject("data").getString("roomWxid");
            }
            throw new ServiceException("创建群聊失败");
        } catch (Exception e) {
            log.error("createRoom", e);
            throw new ServiceException("创建群聊失败");
        }
    }

    //2转换 wxid 为 企微群🆔
    //https://aa-hub.ddregion.com/api/v1/groupChat/wxid_to_wecomChatId?token=b0e88a6243df4f7a9e9dd9c53a88df87&wxid=R:10730805667890415
    //res:
    //{"errcode":0,"errmsg":"ok","wecomChatId":"wrdiZoLAAAcbvUivzsjmdrJNVyPctrBQ"}

    /**
     * 句子的群wxid 转换为企微群🆔
     *
     * @param wxid
     * @return
     */
    public String wxidToWecomChatId(String wxid) {
        String str = HttpUtils.sendGet("https://aa-hub.ddregion.com/api/v1/groupChat/wxid_to_wecomChatId?token=" + juZiConfig.getToken() + "&wxid=" + wxid);
        JSONObject parseObject = JSON.parseObject(str);
        return parseObject.getString("wecomChatId");
    }


    /**
     * 获取所有机器人BOT
     *
     * @return
     */
    public List<JzContact> getContacts(String token, List<JzBot> bots, String wxid) {

        Map<String, List<JzBot>> botwxMap = bots.stream().collect(Collectors.groupingBy(JzBot::getWxid));

        List<JzContact> roomList = new ArrayList<>();

        String wxidStr = StringUtils.isBlank(wxid) ? "" : "&wxid=" + wxid;

        int current = 0;
        while (true) {
            String str = HttpUtils.sendGet("https://aa-api.ddregion.com/contact/list?token=" + token + "&current=" + current + wxidStr);
            JSONObject parseObject = JSON.parseObject(str);
            JSONArray data = parseObject.getJSONArray("data");
            if (data == null || data.isEmpty()) {
                break;
            }
            for (int i = 0; i < data.size(); i++) {
                //联系人类型，0 - 未知，1 - 微信，2 - 公众号，3 - 企业微信
                if (!"1".equals(data.getJSONObject(i).getString("contactType"))) {
                    continue;
                }
                JzContact contact = new JzContact();
                contact.setChatId(data.getJSONObject(i).getString("chatId"));
                String botWxid = data.getJSONObject(i).getJSONObject("botInfo").getString("wxid");
                contact.setBotId(botwxMap.get(botWxid).get(0).getBotId());
                contact.setWxid(data.getJSONObject(i).getString("wxid"));
                contact.setWeixin(data.getJSONObject(i).getString("weixin"));
                contact.setNickname(data.getJSONObject(i).getString("nickName"));
                contact.setAlias(data.getJSONObject(i).getString("alias"));
                contact.setAvatarUrl(data.getJSONObject(i).getString("avatarUrl"));
                contact.setGender(data.getJSONObject(i).getString("gender"));
                contact.setUnionId(data.getJSONObject(i).getString("unionId"));
                contact.setDeleted(data.getJSONObject(i).getBoolean("deleted") ? "1" : "0");
                if (data.getJSONObject(i).getLong("newFriendTimestamp") != null) {
                    contact.setFriendTime(new Date(data.getJSONObject(i).getLong("newFriendTimestamp")));
                }
                roomList.add(contact);
            }

            if (data.size() < 10) {
                break;
            }

            current++;
        }
        return roomList;
    }


    /**
     * 发送消息到 会话
     *
     * @param chatId
     * @return
     */
    public boolean sendMessage(String chatId, EJzSendMessageType messageType, Payload payload) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("token", juZiConfig.getGroupToken());
        map.put("chatId", chatId);
        map.put("externalRequestId", chatId.concat(IdUtils.fastSimpleUUID()));
        map.put("messageType", messageType.getCode());
        map.put("payload", payload);

        try {
            log.info("句子互动发送消息, body: {}", map);
            JSONObject jsonObject = HttpUtils.postToJsonObject("https://aa-api.ddregion.com/message/send", map);
            log.info("句子互动发送消息, result: {}", jsonObject);

            if (jsonObject.getInteger("code") == 0) {
                return true;
            }
        } catch (Exception e) {
            log.error("sendMessage error", e);
            return false;
        }
        return false;
    }


    public boolean sendMessage(String chatId, EJzSendMessageType messageType, JSONObject payload) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("token", juZiConfig.getGroupToken());
        map.put("chatId", chatId);
        map.put("externalRequestId", chatId.concat(IdUtils.fastSimpleUUID()));
        map.put("messageType", messageType.getCode());
        map.put("payload", payload);

        try {
            log.info("句子互动发送消息, body: {}", map);
            JSONObject jsonObject = HttpUtils.postToJsonObject("https://aa-api.ddregion.com/message/send", map);
            log.info("句子互动发送消息, result: {}", jsonObject);

            if (jsonObject.getInteger("code") == 0) {
                return true;
            }
        } catch (Exception e) {
            log.error("sendMessage error", e);
            return false;
        }
        return false;
    }

    /**
     * 群发消息
     *
     * @param botChatMap
     * @param type
     * @param messages
     * @return
     */
    public String sendBroadcast(Map<JzBot, Set<String>> botChatMap, Integer type, JSONArray messages) {
        final int MAX_WXID_COUNT = 1000;  // 每次最多处理 1000 个 wxid
        String broadcastId = null;        // 群发ID

        List<JSONObject> memberList = new ArrayList<>();

        // 构建 members 列表，包含每个 bot 的 botUserId 和 wxids
        for (Map.Entry<JzBot, Set<String>> entry : botChatMap.entrySet()) {
            JzBot bot = entry.getKey();
            Set<String> wxids = entry.getValue();

            JSONObject member = new JSONObject();
            member.put("botUserId", bot.getWeixin());
            member.put("wxids", new ArrayList<>(wxids));  // 将 wxids 转为 List
            member.put("token", bot.getToken());  // 每个 bot 记录其 token
            memberList.add(member);
        }

        // 对 wxids 进行分页，每次发送最多 1000 个
        List<String> allWxids = memberList.stream()
                .flatMap(member -> ((List<String>) member.get("wxids")).stream())
                .collect(Collectors.toList());

        int totalWxids = allWxids.size();
        int pageCount = (int) Math.ceil((double) totalWxids / MAX_WXID_COUNT);

        for (int page = 0; page < pageCount; page++) {
            // 计算分页的范围
            int start = page * MAX_WXID_COUNT;
            int end = Math.min(start + MAX_WXID_COUNT, totalWxids);

            List<String> pagedWxids = allWxids.subList(start, end);
            JSONArray membersForCurrentPage = new JSONArray();

            // 为当前页构建 members 列表
            for (JSONObject member : memberList) {
                List<String> memberWxids = (List<String>) member.get("wxids");

                // 过滤当前页的 wxids
                List<String> filteredWxids = memberWxids.stream()
                        .filter(pagedWxids::contains)
                        .collect(Collectors.toList());

                if (!filteredWxids.isEmpty()) {
                    JSONObject newMember = new JSONObject();
                    newMember.put("botUserId", member.get("botUserId"));
                    newMember.put("wxids", filteredWxids);
                    membersForCurrentPage.add(newMember);
                }
            }

            // 组装 payload
            JSONObject json = new JSONObject();
            json.put("token", memberList.get(0).get("token"));  // 任意一个 bot 的 token
            json.put("type", type);
            json.put("hasMore", page < pageCount - 1);  // 如果还有下一页，设置为 true
            json.put("messages", messages);
            json.put("members", membersForCurrentPage);

            if (broadcastId != null) {
                json.put("id", broadcastId);  // 如果有群发ID，传入ID继续分页
            }

            try {
                JSONObject jsonObject = HttpUtils.postToJsonObject("https://aa-api.ddregion.com/broadcast/create", json);
                log.info("群发消息返回:{}", jsonObject.toJSONString());
                if (jsonObject.getInteger("code") == 0) {
                    broadcastId = jsonObject.getJSONObject("data").getString("id");  // 获取群发ID
                } else {
                    broadcastId = jsonObject.getString("msg");
                }
            } catch (Exception e) {
                log.error("sendMessage", e);
                throw new ServiceException("群发失败");
            }
        }
        // 返回最终的群发ID
        return broadcastId;
    }


    /**
     * 极速群发 请求
     *
     * @param name       群发名称
     * @param type       群发类型（私聊或群聊）  1-私聊  2-群聊
     * @param messages   要发送的消息内容
     * @param botChatMap 每个 bot 的 externalUserId 列表（成员数据）
     * @return 群发任务ID
     */
    public String sendWecomBatchMessage(String name, Map<JzBot, Set<String>> botChatMap, int type, JSONArray messages) {
        final int MAX_MEMBERS_PER_REQUEST = 1000;
        String wecomFlag;
        if (type == 2) {
            wecomFlag = "wecomChatId";
        } else {
            wecomFlag = "externalUserId";
        }

//        String broadcastId = null;

        List<String> idList = new ArrayList<>();

        // 构建 members 列表
        List<JSONObject> memberList = new ArrayList<>();
        for (JzBot bot : botChatMap.keySet()) {
            Set<String> externalUserIds = botChatMap.get(bot);
            JSONObject member = new JSONObject();
            member.put("botUserId", bot.getWeixin());
            member.put(wecomFlag, new ArrayList<>(externalUserIds));  // 每个 bot 的 externalUserId 列表
            memberList.add(member);
        }

        // 计算总的 externalUserId 数量
        List<String> allExternalUserIds = memberList.stream()
                .flatMap(member -> ((List<String>) member.get(wecomFlag)).stream())
                .collect(Collectors.toList());

        // 按照最大 externalUserId 数量分页处理
        int totalExternalUserIds = allExternalUserIds.size();
        int pageCount = (int) Math.ceil((double) totalExternalUserIds / MAX_MEMBERS_PER_REQUEST);

        for (int page = 0; page < pageCount; page++) {
            // 计算分页的范围
            int start = page * MAX_MEMBERS_PER_REQUEST;
            int end = Math.min(start + MAX_MEMBERS_PER_REQUEST, totalExternalUserIds);
            List<String> pagedExternalUserIds = allExternalUserIds.subList(start, end);
            JSONArray membersForCurrentPage = new JSONArray();

            // 为当前页构建 members 列表
            for (JSONObject member : memberList) {
                List<String> memberExternalUserIds = (List<String>) member.get(wecomFlag);

                // 过滤当前页的 externalUserId
                List<String> filteredExternalUserIds = memberExternalUserIds.stream()
                        .filter(pagedExternalUserIds::contains)
                        .collect(Collectors.toList());

                if (!filteredExternalUserIds.isEmpty()) {
                    JSONObject newMember = new JSONObject();
                    newMember.put("botUserId", member.get("botUserId"));
                    newMember.put(wecomFlag, filteredExternalUserIds);
                    membersForCurrentPage.add(newMember);
                }
            }

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("name", name);  // 群发名称
            requestBody.put("token", juZiConfig.getToken());  // API 调用的凭证
            requestBody.put("type", type);  // 群发类型
            requestBody.put("messages", messages);  // 消息内容
            requestBody.put("members", membersForCurrentPage);  // 分页后的成员

//            requestBody.put("hasMore", page < pageCount - 1);  // 是否还有下一页
//            if (broadcastId != null) {
//                requestBody.put("id", broadcastId);  // 如果有群发ID，传入ID继续分页
//            }

            // 发送请求
            try {
                log.info("极速群发消息请求:{}", requestBody.toJSONString());
                JSONObject jsonObject = HttpUtils.postToJsonObject("https://aa-hub.ddregion.com/api/v2/wecomMessage/batchSend?token=" + juZiConfig.getToken(), requestBody);
                log.info("极速群发消息返回:{}", jsonObject.toJSONString());
                if (jsonObject.getInteger("code") == 0) {
//                    broadcastId = jsonObject.getJSONObject("data").getString("id");  // 获取群发ID
                    idList.add(jsonObject.getJSONObject("data").getString("id"));
                } else {
                    log.error("极速群发失败:{}", jsonObject.getString("msg"));
                    idList.add("极速失败");
//                    broadcastId = jsonObject.getString("msg");
                }
            } catch (Exception e) {
                log.error("sendMessage", e);
                throw new ServiceException("极速群发失败");
            }
        }
        return StringUtils.join(idList,",");
    }


    /**
     * @param roomId  群的Id
     * @param botWxId bot的wxid
     * @param name    新的群名称
     * @return
     */
    public boolean changeRoomName(String roomId, String botWxId, String name) {
        JSONObject json = new JSONObject();
        json.put("imRoomId", roomId);
        json.put("imBotId", botWxId);
        json.put("name", name);

        try {
            JSONObject jsonObject = HttpUtils.postToJsonObject("https://aa-hub.ddregion.com/api/v1/instantReply/changeRoomName?token=" + juZiConfig.getToken(), json);
            log.info("修改群名称返回:{}", jsonObject.toJSONString());
            if (jsonObject.getInteger("errcode") != 0) {
                throw new ServiceException("修改群名称失败");
            }
        } catch (Exception e) {
            log.error("修改群名称异常", e);
            throw new ServiceException("修改群名称失败");
        }
        return true;
    }

    /**
     * 获取群二维码
     *
     * @param imRoomId roomId
     * @param imBotId  bot wxid
     * @return
     */
    public String getGroupQRCode(String imRoomId, String imBotId) {
        String formatUrl = String.format("https://aa-hub.ddregion.com/api/v2/groupChat/getQRcode?token=%s&imRoomId=%s&imBotId=%s", juZiConfig.getToken(), imRoomId, imBotId);
        log.info("获取群二维码, imRoomId: {}, imBotId: {}", imRoomId, imBotId);
        try {
            String response = HttpUtils.sendGet(formatUrl);
            JSONObject jsonObject = JSONObject.parseObject(response);
            if (jsonObject.getInteger("errcode") != 0) {
                log.error("获取群二维码失败");
                return null;
            }

            return jsonObject.getString("url");
        } catch (Exception e) {
            log.error("获取群二维码异常", e);
            throw new ServiceException("获取群二维码异常");
        }
    }

    public JSONObject historyMessage(String dayTime, String seq) {
        String str = null;
        String url = "https://aa-api.ddregion.com/message/history?token="
                + juZiConfig.getGroupToken() + "&snapshotDay=" + dayTime + "&pageSize=100" + (StringUtils.isNotBlank(seq) ? "&seq=" + seq : "");
        try {
            str = HttpUtils.sendGet(url);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return JSONObject.parseObject(str);
    }


    /**
     * 最终发送图片
     *
     * @param object 原始消息
     * @param data   图片信息
     */
    public void sendImage(JSONObject object, JSONObject data) {
        JSONObject result;
        JSONObject obj = new JSONObject();
        obj.put("token", object.getString("token"));
        obj.put("chatId", object.getString("chatId"));
        obj.put("messageType", EJzSendMessageType.IMAGE.getCode());
        JSONObject payload = new JSONObject();
        String imageUrl = data.getString("imageUrl").replace("https://cdn.discordapp.com/", "https://cdnmj.zyai.online/");
        payload.put("url", imageUrl);
        obj.put("payload", payload);
        // 发送图片
        result = HttpUtils.postToJsonObject("https://aa-api.ddregion.com/message/send", obj);
        log.info("图片生成 开始发送图片结束 返回 = {}", result.toJSONString());
    }


    public JSONObject getArtworkImage(JSONObject params){
        log.info("获取图片原图 params ={}", JSONObject.toJSONString(params));
        params.put("token",juZiConfig.getGroupToken());
        String str = HttpUtils.post("https://aa-api.ddregion.com/message/getArtworkImage", params);
        JSONObject jsonObject = JSON.parseObject(str);
        return jsonObject.getJSONObject("data");
    }

    /**
     * 图片生成过程中，需要发送的文本消息
     *
     * @param params 消息
     * @param text   文本
     */
    public void sendImageText(ImageCreateReq params, String text) {
        JSONObject result;
        JSONObject obj = new JSONObject();
        obj.put("token", params.getToken());
        obj.put("chatId", params.getChatId());
        obj.put("messageType", EJzSendMessageType.Text.getCode());
        JSONObject payload = new JSONObject();
        payload.put("text", text);

        String chatConfKey = "chat_config:".concat(params.getChatId());
        JSONObject configMap = redisCache.getCacheObject(chatConfKey);
        //v2 太阳神直接调用这边服务
        String type = configMap == null ? "" : configMap.getString("type");
        if (com.ruoyi.common.utils.StringUtils.isNotEmpty(params.getUserId()) && !"user".equals(type)) {
            payload.put("mention", new JSONArray() {{
                add(params.getUserId());
            }});
        }
        obj.put("payload", payload);
        // 发送消息
        result = HttpUtils.postToJsonObject("https://aa-api.ddregion.com/message/send", obj);
        log.info("图片生成 开始发送文本信息结束 返回 = {}", result.toJSONString());
    }

    /**
     * 获取普通群发任务的进度
     * @param id 群发
     */
    public JSONObject getNormalTaskInfo(String id) {
        String formatUrl = String.format("https://aa-api.ddregion.com/broadcast/get_job_progress?id=%s&token=%s", id, juZiConfig.getGroupToken());
        log.info("获取普通群发, url: {}", formatUrl);
        try {
            String response = HttpUtils.sendGet(formatUrl);
            log.info("获取普通群发任务返回:{}", response);

            JSONObject jsonObject = JSONObject.parseObject(response);
            if (jsonObject.getInteger("code") != 0) {
                log.error("获取普通群发任务失败");
                return null;
            }

            return jsonObject;
        } catch (Exception e) {
            log.error("获取普通群发任务异常", e);
            throw new ServiceException("获取普通群发任务异常");
        }
    }

    /**
     * 获取极速群发任务的进度
     * @param id 群发
     */
    public JSONObject getSpeedTaskInfo(String id) {
        String formatUrl = String.format("https://aa-hub.ddregion.com/api/v2/wecomMessage/failedList?id=%s&token=%s&current=0&pageSize=1000", id, juZiConfig.getToken());
        log.info("获取极速群发任务, url: {}", formatUrl);
        try {
            String response = HttpUtils.sendGet(formatUrl);
            log.info("获取极速群发任务返回:{}", response);

            JSONObject jsonObject = JSONObject.parseObject(response);
            if (jsonObject.getInteger("errcode") != 0) {
                log.error("获取极速群发任务失败");
                return null;
            }

            return jsonObject;
        } catch (Exception e) {
            log.error("获取极速群发任务异常", e);
            throw new ServiceException("获取极速群发任务异常");
        }
    }

    /**
     * 获取普通群发任务的失败列表
     * @param id 任务ID
     * @return
     */
    public JSONObject getNormalTaskFailedList(String id, int current, int pageSize) {
        String formatUrl = String.format("https://aa-api.ddregion.com/broadcast/failedList?id=%s&token=%s&current=%s&pageSize=%s", id, juZiConfig.getGroupToken(), current, pageSize);
        log.info("获取普通群发任务的失败列表, url: {}", formatUrl);
        try {
            String response = HttpUtils.sendGet(formatUrl);
            log.info("获取普通群发任务的失败列表:{}", response);

            JSONObject jsonObject = JSONObject.parseObject(response);
            if (jsonObject.getInteger("code") != 0) {
                log.error("获取普通群发任务的失败列表失败");
                return null;
            }

            return jsonObject;
        } catch (Exception e) {
            log.error("获取普通群发任务的失败列表异常", e);
            throw new ServiceException("获取普通群发任务的失败列表异常");
        }
    }

    /**
     * 获取普通群发任务的未发送列表
     * @param id 任务ID
     * @return
     */
    public JSONObject getNormalTaskUnSendList(String id, int current, int pageSize) {
        String formatUrl = String.format("https://aa-api.ddregion.com/broadcast/list_unsent_tasks?id=%s&token=%s&current=%s&pageSize=%s", id, juZiConfig.getGroupToken(), current, pageSize);
        log.info("获取普通群发任务的未发送列表, url: {}", formatUrl);
        try {
            String response = HttpUtils.sendGet(formatUrl);
            log.info("获取普通群发任务的未发送列表:{}", response);

            JSONObject jsonObject = JSONObject.parseObject(response);
            if (jsonObject.getInteger("code") != 0) {
                log.error("获取普通群发任务的未发送列表失败");
                return null;
            }

            return jsonObject;
        } catch (Exception e) {
            log.error("获取普通群发任务的未发送列表异常", e);
            throw new ServiceException("获取普通群发任务的未发送列表异常");
        }
    }




    public void sendMoment(BizMoments moments) {
        String formatUrl = "https://aa-hub.ddregion.com/api/v2/wecom/moment/create?token="+juZiConfig.getToken();

        JSONObject obj = new JSONObject();

        //朋友圈可见类型
        obj.put("scope", 1);
        obj.put("title", DateUtils.dateTime()+"朋友圈");

        BizOperateNodeDto nodeDto = JSON.parseObject(moments.getNodeInfo(), BizOperateNodeDto.class);
        JSONObject payload = nodeDto.getThemeJson().get(0).getPayload();

        int type = 1;
        if (moments.getMsgtype().equals("text")){
            type = 1;
            obj.put("text",new JSONObject(){{put("text",nodeDto.getPrologue());}});

        }else if (moments.getMsgtype().equals("image")){
            type = 2;
            ImagePayload imagePayload = payload.toJavaObject(ImagePayload.class);
            obj.put("images", new JSONObject(){{
                put("text",nodeDto.getPrologue());
                put("urls",imagePayload.getUrls());
            }});
        }else if (moments.getMsgtype().equals("link")){
            type = 3;
            WebPagePayload wbPagePayload = payload.toJavaObject(WebPagePayload.class);
            obj.put("link", new JSONObject(){{
                put("text",nodeDto.getPrologue());
                put("source_url",wbPagePayload.getSourceUrl());
                put("title",wbPagePayload.getTitle());
                put("thumbnail_url",wbPagePayload.getImageUrl());
                put("description",wbPagePayload.getSummary());
            }});
        }else if (moments.getMsgtype().equals("video")){
            type = 5;
            VideoPayload videoPayload = payload.toJavaObject(VideoPayload.class);
            obj.put("video", new JSONObject(){{
                put("text",nodeDto.getPrologue());
                put("url",videoPayload.getUrl());
            }});
        }else if (moments.getMsgtype().equals("channel")){
            type = 4;
            ChannelPayload channelPayload = payload.toJavaObject(ChannelPayload.class);
            obj.put("channel", new JSONObject(){{
                put("text",nodeDto.getPrologue());
                put("avatar",channelPayload.getAvatarUrl());
                put("cover_url",channelPayload.getCoverUrl());
                put("desc",channelPayload.getDescription());
                put("extras",channelPayload.getExtras());
                put("feed_type",channelPayload.getFeedType());
                put("nickname",channelPayload.getNickname());
                put("thumb_url",channelPayload.getThumbUrl());
                put("url",channelPayload.getUrl());
            }});
        }
        obj.put("type", type);

        JSONObject wxList = JSON.parseObject(moments.getWeixinList());

        JSONArray sendInfo = new JSONArray();
        JSONArray momentIds = new JSONArray();

        wxList.forEach((bot,user)->{
            obj.put("wecomUserId", bot);
            //限制用户可见
//            obj.put("scope", 2);
//            obj.put("externalUserId", user);
            //开始创建朋友圈
            JSONObject result = HttpUtils.postToJsonObject(formatUrl, obj);
            if (result.getInteger("errcode") == 0){
                sendInfo.add(new JSONObject(){{
                    put("userid",bot);
                    put("publish_status",1);
                    put("momentId",result.getString("momentId"));
                }});
                momentIds.add(result.getString("momentId"));
            }else {
                sendInfo.add(new JSONObject(){{
                    put("userid",bot);
                    put("publish_status",0);
                }});
            }
        });

        moments.setTaskInfo(sendInfo.toJSONString());
        //发送完成
        moments.setStatus("3");
        moments.setMomentsId(StringUtils.join(momentIds,","));
        iBizMomentsService.updateBizMoments(moments);
    }

    public BizMomentsComment getMomentsComment(String momentsId, String taskInfo) {
        String formatUrl = "https://aa-hub.ddregion.com/api/v2/wecom/moment/detail?token="+juZiConfig.getToken()+"&id="+momentsId;
        String response = HttpUtils.sendGet(formatUrl);
        JSONObject result = JSON.parseObject(response);
        if (result.getInteger("errcode") == 0){
            JSONObject data = result.getJSONObject("data");
            JSONArray likeWxids = data.getJSONArray("likeWxids");
            List<String> wxids = likeWxids.toJavaList(String.class);
            List<JzContact> contactList = jzContactService.selectJzContactListByWxIds(wxids);
            JSONArray like = new JSONArray();
            Set<String> setList = new HashSet<>();
            for (JzContact contact : contactList) {
                if (setList.contains(contact.getUnionId())){
                    continue;
                }
                like.add(new JSONObject(){{
                    put("external_userid",contact.getWeixin());
                }});
                setList.add(contact.getUnionId());
            }
            // 使用流提取 wxid 列表
            Set<String> wxidset = data.getJSONArray("comments").stream()
                    .map(comment -> ((JSONObject) comment).getString("wxid"))
                    .collect(Collectors.toSet());

            List<JzContact> commentsJzContact = jzContactService.selectJzContactListByWxIds(new ArrayList<>(wxidset));
            JSONArray comments = new JSONArray();
            setList = new HashSet<>();
            for (JzContact contact : commentsJzContact) {
                if (setList.contains(contact.getUnionId())){
                    continue;
                }
                comments.add(new JSONObject(){{
                    put("external_userid",contact.getWeixin());
                }});
                setList.add(contact.getUnionId());
            }

            // 使用流将 JSONArray 转为 Map
            Map<String, String> idToUserIdMap = JSONArray.parseArray(taskInfo).stream()
                    .map(obj -> (JSONObject) obj) // 将每个元素转换为 JSONObject
                    .collect(Collectors.toMap(
                            obj -> obj.getString("momentId"),
                            obj -> obj.getString("userid")
                    ));

            BizMomentsComment bizMomentsComment = new BizMomentsComment();
            bizMomentsComment.setBotId(idToUserIdMap.get(momentsId));
            bizMomentsComment.setMomentsId(momentsId);
            bizMomentsComment.setLikeList(like.toJSONString());
            bizMomentsComment.setCommentList(comments.toJSONString());

            return bizMomentsComment;

        }

        return null;

    }
}
