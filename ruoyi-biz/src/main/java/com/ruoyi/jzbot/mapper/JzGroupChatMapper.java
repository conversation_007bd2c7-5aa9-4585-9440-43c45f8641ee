package com.ruoyi.jzbot.mapper;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import com.ruoyi.biz.domain.BizPartner;
import com.ruoyi.jzbot.domain.JzGroupChat;
import com.ruoyi.jzbot.domain.JzRoom;
import org.apache.ibatis.annotations.Param;

/**
 * 群组Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
public interface JzGroupChatMapper
{
    /**
     * 查询群组
     *
     * @param id 群组主键
     * @return 群组
     */
    public JzGroupChat selectJzGroupChatById(String id);

    /**
     * 查询群组列表
     *
     * @param jzGroupChat 群组
     * @return 群组集合
     */
    public List<JzGroupChat> selectJzGroupChatList(JzGroupChat jzGroupChat);

    /**
     * 新增群组
     *
     * @param jzGroupChat 群组
     * @return 结果
     */
    public int insertJzGroupChat(JzGroupChat jzGroupChat);

    /**
     * 修改群组
     *
     * @param jzGroupChat 群组
     * @return 结果
     */
    public int updateJzGroupChat(JzGroupChat jzGroupChat);

    /**
     * 删除群组
     *
     * @param id 群组主键
     * @return 结果
     */
    public int deleteJzGroupChatById(String id);

    /**
     * 批量删除群组
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJzGroupChatByIds(String[] ids);

    void createOrUpdateGroupChat(List<JzGroupChat> list);

    /**
     * 根据群组id查询群组
     * @param roomIds 群组id集合
     * @return 群组集合
     */
    List<JzGroupChat> selectJzGroupChatListByIds(@Param("roomIds") Collection<String> roomIds);

    List<JzGroupChat> selectGroupChatListByPartner(BizPartner bizPartner);

    void checkDeleted(List<String> list);

    /**
     * 根据微信群id查询群组
     * @param ids 微信群id集合
     * @return 群组集合
     */
    List<JzGroupChat> selectJzGroupChatListByWecomChatIds(@Param("ids") List<String> ids);
}
