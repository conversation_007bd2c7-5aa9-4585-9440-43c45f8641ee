package com.ruoyi.jzbot.mapper;

import java.util.List;

import com.ruoyi.jzbot.domain.BizJiebaDict;

/**
 * 分词字典Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-18
 */
public interface BizJiebaDictMapper {
    /**
     * 查询分词字典
     *
     * @param id 分词字典主键
     * @return 分词字典
     */
    public BizJiebaDict selectBizJiebaDictById(Long id);

    /**
     * 查询分词字典列表
     *
     * @param bizJiebaDict 分词字典
     * @return 分词字典集合
     */
    public List<BizJiebaDict> selectBizJiebaDictList(BizJiebaDict bizJiebaDict);

    /**
     * 新增分词字典
     *
     * @param bizJiebaDict 分词字典
     * @return 结果
     */
    public int insertBizJiebaDict(BizJiebaDict bizJiebaDict);


    /**
     * 批量插入分词字典
     *
     * @param bizJiebaDict 分词字典
     * @return 结果
     */
    int insertBizJiebaDictBatch(List<BizJiebaDict> bizJiebaDict);

    /**
     * 修改分词字典
     *
     * @param bizJiebaDict 分词字典
     * @return 结果
     */
    public int updateBizJiebaDict(BizJiebaDict bizJiebaDict);

    /**
     * 删除分词字典
     *
     * @param id 分词字典主键
     * @return 结果
     */
    public int deleteBizJiebaDictById(Long id);

    /**
     * 批量删除分词字典
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBizJiebaDictByIds(Long[] ids);
}
