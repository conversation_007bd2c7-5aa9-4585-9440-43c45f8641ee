package com.ruoyi.jzbot.mapper;

import java.util.List;
import com.ruoyi.jzbot.domain.JzBot;

/**
 * bot查看Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
public interface JzBotMapper
{
    /**
     * 查询bot查看
     *
     * @param botId bot查看主键
     * @return bot查看
     */
    public JzBot selectJzBotByBotId(String botId);

    /**
     * 查询bot查看
     *
     * @param botIds bot查看主键列表
     * @return bot查看
     */
    List<JzBot> selectJzBotByBotIds(String[] botIds);


    /**
     * 查询bot查看列表
     *
     * @param jzBot bot查看
     * @return bot查看集合
     */
    public List<JzBot> selectJzBotList(JzBot jzBot);

    /**
     * 新增bot查看
     *
     * @param jzBot bot查看
     * @return 结果
     */
    public int insertJzBot(JzBot jzBot);

    /**
     * 修改bot查看
     *
     * @param jzBot bot查看
     * @return 结果
     */
    public int updateJzBot(JzBot jzBot);

    /**
     * 删除bot查看
     *
     * @param botId bot查看主键
     * @return 结果
     */
    public int deleteJzBotByBotId(String botId);

    /**
     * 批量删除bot查看
     *
     * @param botIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJzBotByBotIds(String[] botIds);

    /**
     * 批量新增更新
     * @param list
     */
    void batchAdd(List<JzBot> list);



    String selectLeastContactBot();
}
