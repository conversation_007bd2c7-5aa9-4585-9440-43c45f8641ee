package com.ruoyi.jzbot.mapper;

import java.util.List;
import com.ruoyi.jzbot.domain.JzRoomMembers;

/**
 * 群成员Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
public interface JzRoomMembersMapper
{
    /**
     * 查询群成员
     *
     * @param wxid 群成员主键
     * @return 群成员
     */
    public List<JzRoomMembers> selectJzRoomMembersByWxid(String wxid);

    /**
     * 查询群成员列表
     *
     * @param jzRoomMembers 群成员
     * @return 群成员集合
     */
    public List<JzRoomMembers> selectJzRoomMembersList(JzRoomMembers jzRoomMembers);

    /**
     * 新增群成员
     *
     * @param jzRoomMembers 群成员
     * @return 结果
     */
    public int insertJzRoomMembers(JzRoomMembers jzRoomMembers);

    /**
     * 修改群成员
     *
     * @param jzRoomMembers 群成员
     * @return 结果
     */
    public int updateJzRoomMembers(JzRoomMembers jzRoomMembers);

    /**
     * 删除群成员
     *
     * @param wxid 群成员主键
     * @return 结果
     */
    public int deleteJzRoomMembersByWxid(String wxid);

    /**
     * 批量删除群成员
     *
     * @param wxids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJzRoomMembersByWxids(String[] wxids);

    void batchAddUpdate(List<JzRoomMembers> list);
}
