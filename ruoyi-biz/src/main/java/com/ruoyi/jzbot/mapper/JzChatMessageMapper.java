package com.ruoyi.jzbot.mapper;

import java.util.List;
import java.util.Set;

import com.ruoyi.jzbot.domain.JzChatMessage;
import com.ruoyi.jzbot.domain.req.JzChatMessageReq;
import com.ruoyi.jzbot.domain.vo.JzChatMessageVo;
import org.apache.ibatis.annotations.Param;

/**
 * 会话记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
public interface JzChatMessageMapper
{
    /**
     * 查询会话记录
     *
     * @param id 会话记录主键
     * @return 会话记录
     */
    public JzChatMessage selectJzChatMessageById(Long id);

    /**
     * 查询会话记录列表
     *
     * @param jzChatMessageReq 会话记录
     * @return 会话记录集合
     */
    public List<JzChatMessage> selectJzChatMessageList(JzChatMessageReq jzChatMessageReq);

    /**
     * 新增会话记录
     *
     * @param jzChatMessage 会话记录
     * @return 结果
     */
    public int insertJzChatMessage(JzChatMessage jzChatMessage);

    /**
     * 修改会话记录
     *
     * @param jzChatMessage 会话记录
     * @return 结果
     */
    public int updateJzChatMessage(JzChatMessage jzChatMessage);

    /**
     * 删除会话记录
     *
     * @param id 会话记录主键
     * @return 结果
     */
    public int deleteJzChatMessageById(Long id);

    /**
     * 批量删除会话记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJzChatMessageByIds(Long[] ids);

    /**
     * 获取当前所有数据
     */
    List<JzChatMessageVo> statistics(@Param(value = "yesterday") String yesterday,@Param(value = "chatId") String chatId);

    void insertBatch(List<JzChatMessage> list);

    void updateJzChatMessageByMessageId(JzChatMessage jzChatMessage);


    List<JzChatMessage> selectJzChatMessageHistoryList(JzChatMessageReq jzChatMessageReq);

    /**
     *
     * @param time
     * @param chatIds
     * @return
     */
    List<JzChatMessage> selectJzChatMessageByTimeAndChatIds(@Param("time") String time, @Param("chatIds") Set<String> chatIds);
}
