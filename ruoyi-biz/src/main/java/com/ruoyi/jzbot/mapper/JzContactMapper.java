package com.ruoyi.jzbot.mapper;

import java.util.List;
import com.ruoyi.jzbot.domain.JzContact;
import com.ruoyi.jzbot.domain.dto.JzContactDto;
import com.ruoyi.jzbot.domain.req.JzAllContactReq;
import org.apache.ibatis.annotations.Param;

/**
 * 联系人Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
public interface JzContactMapper
{
    /**
     * 查询联系人
     *
     * @param chatId 联系人主键
     * @return 联系人
     */
    public JzContact selectJzContactById(String chatId);

    /**
     * 查询联系人列表
     *
     * @param jzContact 联系人
     * @return 联系人集合
     */
    public List<JzContact> selectJzContactList(JzContact jzContact);

    /**
     * 新增联系人
     *
     * @param jzContact 联系人
     * @return 结果
     */
    public int insertJzContact(JzContact jzContact);

    /**
     * 修改联系人
     *
     * @param jzContact 联系人
     * @return 结果
     */
    public int updateJzContact(JzContact jzContact);

    /**
     * 删除联系人
     *
     * @param id 联系人主键
     * @return 结果
     */
    public int deleteJzContactById(Long id);

    /**
     * 批量删除联系人
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJzContactByIds(String[] ids);

    /**
     * 新增或者更新
     * @param list
     */
    void createOrUpdate(List<JzContact> list);

    /**
     * 获取所有没有UnionId的人
     * @return
     */
    List<JzContact> selectNonUnionIdList();

    List<JzContact> selectJzContactByUnionId(String unionId);

    List<JzContact> selectJzContactListSimple(JzContact conditionJz);

    List<JzContact> selectJzContactListByWxId(String wxid);

    void checkDeleted(List<String> list);

    List<JzContactDto> listAll(JzAllContactReq req);

    /**
     * 根据外部联系人id查询联系人列表
     * @param ids 外部联系人id列表
     * @return
     */
    List<JzContact> selectJzContactListByWeixinIds(@Param("ids") List<String> ids);

    /**
     * 根据外部联系人wxid查询联系人列表
     * @param ids 外部联系人wxid列表
     * @return 联系人列表
     */
    List<JzContact> selectJzContactListByWxIds(@Param("ids") List<String> ids);


    /**
     * 批量获取-根据chatId获取联系人列表
     * @param chatIds chatId列表
     * @return 联系人列表
     */
    List<JzContact> selectJzConcatListByChatIds(@Param("chatIds") List<String> chatIds);

    List<JzContact> getWaitActiveUser();
}
