package com.ruoyi.jzbot.mapper;

import java.util.List;
import com.ruoyi.jzbot.domain.BizStopWord;

/**
 * jieba过滤词典Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
public interface BizStopWordMapper 
{
    /**
     * 查询jieba过滤词典
     * 
     * @param id jieba过滤词典主键
     * @return jieba过滤词典
     */
    public BizStopWord selectBizStopWordById(Long id);

    /**
     * 查询jieba过滤词典列表
     * 
     * @param bizStopWord jieba过滤词典
     * @return jieba过滤词典集合
     */
    public List<BizStopWord> selectBizStopWordList(BizStopWord bizStopWord);

    /**
     * 新增jieba过滤词典
     * 
     * @param bizStopWord jieba过滤词典
     * @return 结果
     */
    public int insertBizStopWord(BizStopWord bizStopWord);

    /**
     * 修改jieba过滤词典
     * 
     * @param bizStopWord jieba过滤词典
     * @return 结果
     */
    public int updateBizStopWord(BizStopWord bizStopWord);

    /**
     * 删除jieba过滤词典
     * 
     * @param id jieba过滤词典主键
     * @return 结果
     */
    public int deleteBizStopWordById(Long id);

    /**
     * 批量删除jieba过滤词典
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBizStopWordByIds(Long[] ids);
}
