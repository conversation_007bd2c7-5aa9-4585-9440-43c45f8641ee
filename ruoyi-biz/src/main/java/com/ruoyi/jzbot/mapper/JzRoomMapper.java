package com.ruoyi.jzbot.mapper;

import java.util.List;

import com.ruoyi.biz.domain.BizPartner;
import com.ruoyi.jzbot.domain.JzRoom;
import org.apache.ibatis.annotations.Param;

/**
 * 群Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
public interface JzRoomMapper
{
    /**
     * 查询群
     *
     * @param chatId 群主键
     * @return 群
     */
    public JzRoom selectJzRoomByChatId(String chatId);

    /**
     * 查询群列表
     *
     * @param jzRoom 群
     * @return 群集合
     */
    public List<JzRoom> selectJzRoomList(JzRoom jzRoom);

    /**
     * 查询群名称
     *
     * @param chatId 群
     * @return 群名称
     */
    String selectTopicByChatId(String chatId);

    /**
     * 新增群
     *
     * @param jzRoom 群
     * @return 结果
     */
    public int insertJzRoom(JzRoom jzRoom);

    /**
     * 修改群
     *
     * @param jzRoom 群
     * @return 结果
     */
    public int updateJzRoom(JzRoom jzRoom);

    /**
     * 删除群
     *
     * @param chatId 群主键
     * @return 结果
     */
    public int deleteJzRoomByChatId(String chatId);

    /**
     * 批量删除群
     *
     * @param chatIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJzRoomByChatIds(String[] chatIds);

    /**
     * 创建以及更新
     * @param list
     */
    void createOrUpdate(List<JzRoom> list);

    List<JzRoom> selectJzRoomListSimple(JzRoom jzRoom);

    void checkDeleted(List<String> list);

    /**
     * 根据微信号查询群信息
     * @param ids 微信号
     * @return 群信息
     */
    List<JzRoom> selectJzRoomListByWxIds(@Param("ids") List<String> ids);

    /**
     * 根据chatId查询群信息
     * @param chatIds 群id
     * @return 群信息
     */
    List<JzRoom> selectJzRoomListByChatIds(@Param("chatIds") List<String> chatIds);
}
