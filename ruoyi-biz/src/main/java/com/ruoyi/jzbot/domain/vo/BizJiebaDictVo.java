package com.ruoyi.jzbot.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class BizJiebaDictVo extends BaseEntity {
    /**
     * 主键ID，自增
     */
    private Long id;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 词频
     */
    private Long frequency;

    /**
     * 词性
     */
    private String wordClass;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDatetime;

    private String wordClassName;
}
