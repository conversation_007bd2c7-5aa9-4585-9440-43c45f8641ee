package com.ruoyi.jzbot.domain;

import com.ruoyi.biz.domain.BizTaggable;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 群对象 jz_room
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class JzRoom extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 群会话id */
    private String chatId;

    /** 所属bot */
    @Excel(name = "所属bot")
    private String botId;

    @Excel(name = "AI智能体")
    private String agent;

    @Excel(name = "指令列表")
    private String instruction;

    //是否无需@
    private String excludeAtReply;
    //消息通知队列
    private String noticeEnv;

    /** 微信 */
    @Excel(name = "微信")
    private String wxid;

    /** 群名称 */
    @Excel(name = "群名称")
    private String topic;

    /** 群头像 */
    @Excel(name = "群头像")
    private String avatarUrl;

    /** 群主wxid */
    private String ownerId;

    /** 群人数 */
    @Excel(name = "群人数")
    private Long memberCount;

    @Excel(name = "好友人数")
    private Long friendCount;


    /** 是否删除 */
    @Excel(name = "是否删除")
    private String deleted;
    private Date lastTime;

    private String lastMessage;


    /**
     * 状态 0待启用 1已启用 2已停用
     */
    @Excel(name = "状态")
    private String status;

    private Date joinTime;

    private JzBot jzBot;

    private List<BizTaggable> tagList;

    private List<String> chatIdList;

    private List<String> tagSearchIdList;

    /**
     * tag 搜索类型 0  满足任意一个标签 ， 1 同时满足所有标签
     */
    private String tagSearchType;

    private List<JzRoomMembers> membersList;

    private  List<String> wxidList;

}
