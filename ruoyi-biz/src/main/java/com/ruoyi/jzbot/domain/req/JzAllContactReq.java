package com.ruoyi.jzbot.domain.req;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 句子聊天记录请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class JzAllContactReq extends BaseEntity {


    /**
     * 机器人ID，标识哪个bot参与了会话
     */
    private String botId;

    /**
     * 类型 1 私聊  2 群聊  不传全部
     */
    private String contactType;

    /**
     * 名称搜索
     */
    private String keywords;

    private String chatIdListStr;


}
