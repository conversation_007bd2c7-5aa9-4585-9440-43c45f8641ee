package com.ruoyi.jzbot.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.CustomerStrToJsonObjectSerialize;
import com.ruoyi.jzbot.domain.JzBot;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class JzChatMessageVo extends BaseEntity {
    /**
     * 唯一标识符，自增主键
     */
    private Long id;

    /**
     * 聊天ID，标识一个bot与一个客户的会话
     */

    private String chatId;

    /**
     * 机器人ID，标识哪个bot参与了会话
     */
    private String botId;

    /**
     * 消息ID，每条消息的唯一标识
     */
    private String messageId;

    /**
     * 消息类型（如文本、图片等的整数标识）
     */
    private Integer messageType;

    /**
     * 消息内容
     */
    @JsonSerialize(using = CustomerStrToJsonObjectSerialize.class)
    private String message;

    /**
     * 联系人ID，可能用于标识消息发起者
     */
    private String contactId;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 联系人头像URL
     */
    private String contactImageUrl;

    /**
     * 消息是否由托管账号自己发送
     */
    private Integer isSelf;

    /**
     * 是否@bot
     */
    private Integer isEt;

    /**
     * 消息分词结果
     */
    private String segmentedWords;

    /**
     * 费率，可能用于某些计费功能
     */
    private BigDecimal rate;


    /**
     * 类型名称
     */
    private String messageTypeName;

    private String messageSource;

    /**
     * 父消息id
     */
    private String fMessageId;

    private String sendName;

    @JsonSerialize(using = CustomerStrToJsonObjectSerialize.class)
    private String aiExtra;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "记录创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date time;

    private JzBot jzBot;


    private Long rowNum;

    private Long page;

    public String getContactName() {
        if (StringUtils.isNotBlank(sendName)){
            return sendName;
        }
        return contactName;
    }
}
