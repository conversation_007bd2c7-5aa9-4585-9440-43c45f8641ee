package com.ruoyi.jzbot.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 群成员对象 jz_room_members
 * 
 * <AUTHOR>
 * @date 2024-04-08
 */
public class JzRoomMembers extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 群员wxid */
    @Excel(name = "群员wxid")
    private String wxid;

    /** 所在群id */
    @Excel(name = "所在群id")
    private String roomId;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nickname;

    /** 头像 */
    @Excel(name = "头像")
    private String photo;

    /** 是否是朋友 */
    @Excel(name = "是否是朋友")
    private Integer isFriend;

    /** 群内备注 */
    @Excel(name = "群内备注")
    private String roomAlias;

    /** 状态 1正常,0已退群 */
    @Excel(name = "状态 1正常,0已退群")
    private String status;

    /** 加入方式 */
    @Excel(name = "加入方式")
    private String joinScene;

    /** 加入时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "加入时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date joinTime;

    /** 退群时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "退群时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date quitTime;

    public void setWxid(String wxid) 
    {
        this.wxid = wxid;
    }

    public String getWxid() 
    {
        return wxid;
    }
    public void setRoomId(String roomId) 
    {
        this.roomId = roomId;
    }

    public String getRoomId() 
    {
        return roomId;
    }
    public void setNickname(String nickname) 
    {
        this.nickname = nickname;
    }

    public String getNickname() 
    {
        return nickname;
    }
    public void setPhoto(String photo) 
    {
        this.photo = photo;
    }

    public String getPhoto() 
    {
        return photo;
    }
    public void setIsFriend(Integer isFriend) 
    {
        this.isFriend = isFriend;
    }

    public Integer getIsFriend() 
    {
        return isFriend;
    }
    public void setRoomAlias(String roomAlias) 
    {
        this.roomAlias = roomAlias;
    }

    public String getRoomAlias() 
    {
        return roomAlias;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setJoinScene(String joinScene) 
    {
        this.joinScene = joinScene;
    }

    public String getJoinScene() 
    {
        return joinScene;
    }
    public void setJoinTime(Date joinTime) 
    {
        this.joinTime = joinTime;
    }

    public Date getJoinTime() 
    {
        return joinTime;
    }
    public void setQuitTime(Date quitTime) 
    {
        this.quitTime = quitTime;
    }

    public Date getQuitTime() 
    {
        return quitTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("wxid", getWxid())
            .append("roomId", getRoomId())
            .append("nickname", getNickname())
            .append("photo", getPhoto())
            .append("isFriend", getIsFriend())
            .append("roomAlias", getRoomAlias())
            .append("status", getStatus())
            .append("joinScene", getJoinScene())
            .append("joinTime", getJoinTime())
            .append("quitTime", getQuitTime())
            .toString();
    }
}
