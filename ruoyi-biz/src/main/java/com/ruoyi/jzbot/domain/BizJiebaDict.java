package com.ruoyi.jzbot.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 分词字典对象 biz_jieba_dict
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
public class BizJiebaDict extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID，自增 */
    @Excel(name = "主键ID，自增")
    private Long id;

    /** 关键字 */
    @Excel(name = "关键字")
    private String keyword;

    /** 词频 */
    @Excel(name = "词频")
    private Long frequency;

    /** 词性 */
    @Excel(name = "词性")
    private String wordClass;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDatetime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setKeyword(String keyword) 
    {
        this.keyword = keyword;
    }

    public String getKeyword() 
    {
        return keyword;
    }
    public void setFrequency(Long frequency) 
    {
        this.frequency = frequency;
    }

    public Long getFrequency() 
    {
        return frequency;
    }
    public void setWordClass(String wordClass) 
    {
        this.wordClass = wordClass;
    }

    public String getWordClass() 
    {
        return wordClass;
    }
    public void setCreateDatetime(Date createDatetime) 
    {
        this.createDatetime = createDatetime;
    }

    public Date getCreateDatetime() 
    {
        return createDatetime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("keyword", getKeyword())
            .append("frequency", getFrequency())
            .append("wordClass", getWordClass())
            .append("createDatetime", getCreateDatetime())
            .toString();
    }
}
