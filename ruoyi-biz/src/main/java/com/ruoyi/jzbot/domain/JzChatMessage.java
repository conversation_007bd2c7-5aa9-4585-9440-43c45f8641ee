package com.ruoyi.jzbot.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.CustomerStrToJsonObjectSerialize;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 会话记录对象 jz_chat_message
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class JzChatMessage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识符，自增主键
     */
    private Long id;

    /**
     * 聊天ID，标识一个bot与一个客户的会话
     */
    @Excel(name = "聊天ID，标识一个bot与一个客户的会话")
    private String chatId;

    /**
     * 信息来源 0群 1私聊
     */
    private String messageSource;

    /**
     * 机器人ID，标识哪个bot参与了会话
     */
    @Excel(name = "机器人ID，标识哪个bot参与了会话")
    private String botId;

    /**
     * 消息ID，每条消息的唯一标识
     */
    @Excel(name = "消息ID，每条消息的唯一标识")
    private String messageId;

    /**
     * 消息类型（如文本、图片等的整数标识）
     */
    @Excel(name = "消息类型", readConverterExp = "如=文本、图片等的整数标识")
    private Integer messageType;

    /**
     * 消息内容
     */
    @Excel(name = "消息内容")
    @JsonSerialize(using = CustomerStrToJsonObjectSerialize.class)
    private String message;

    /**
     * 联系人ID，可能用于标识消息发起者
     */
    @Excel(name = "联系人ID，可能用于标识消息发起者")
    private String contactId;

    /**
     * 联系人名称
     */
    @Excel(name = "联系人名称")
    private String contactName;

    /**
     * 联系人头像URL
     */
    @Excel(name = "联系人头像URL")
    private String contactImageUrl;

    /**
     * 消息是否由托管账号自己发送, 0-否, 1-是
     */
    @Excel(name = "消息是否由托管账号自己发送")
    private Integer isSelf;

    /**
     * 消息分词结果
     */
    @Excel(name = "消息分词结果")
    private String segmentedWords;

    /**
     * 费率，可能用于某些计费功能
     */
    @Excel(name = "费率，可能用于某些计费功能")
    private BigDecimal rate;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "记录创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date time;

    @JsonProperty("fmessageId")
    private String fMessageId;

    private String sendName;

    @JsonSerialize(using = CustomerStrToJsonObjectSerialize.class)
    private String aiExtra;

    /**
     * 是否@bot
     */
    @Excel(name = "是否@bot")
    public Integer isEt;

    private Long rowNum;

    private Long page;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId()).append("chatId", getChatId()).append("botId", getBotId()).append("messageId", getMessageId()).append("messageType", getMessageType()).append("message", getMessage()).append("contactId", getContactId()).append("contactName", getContactName()).append("contactImageUrl", getContactImageUrl()).append("isSelf", getIsSelf()).append("segmentedWords", getSegmentedWords()).append("rate", getRate()).append("time", getTime()).toString();
    }
}
