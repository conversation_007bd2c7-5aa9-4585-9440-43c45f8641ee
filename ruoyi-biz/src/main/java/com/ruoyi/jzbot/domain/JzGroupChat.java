package com.ruoyi.jzbot.domain;

import com.ruoyi.biz.domain.BizTaggable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 群组对象 jz_group_chat
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class JzGroupChat extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 群id */
    private String id;

    /** 群名称 */
    @Excel(name = "群名称")
    private String topic;

    /** 群头像 */
    @Excel(name = "群头像")
    private String avatarUrl;

    /** 群主wxid */
    @Excel(name = "群主wxid")
    private String ownerId;

    /** 群人数 */
    @Excel(name = "群人数")
    private Integer memberCount;

    /** 是否删除0正常,1删除 */
    @Excel(name = "是否删除0正常,1删除")
    private String deleted;

    /** 企微群id */
    @Excel(name = "企微群id")
    private String wecomChatId;

    /**
     * 邀请入群二维码
     */
    private String inviteQrUrl;

    private List<JzRoom> jzRoomList;

    private Long partnerId;
    private String partnerName;

    private List<BizTaggable> tagList;

    private List<String> tagSearchIdList;
    /**
     * tag 搜索类型 0  满足任意一个标签 ， 1 同时满足所有标签
     */
    private String tagSearchType;

    /**
     * 企微客户群id列表
     */
    private List<String> wecomChatIdList;

}
