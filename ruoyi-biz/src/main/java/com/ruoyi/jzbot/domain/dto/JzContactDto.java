package com.ruoyi.jzbot.domain.dto;

import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * Description: new java files header..
 *
 * <AUTHOR>
 * @date 2024/8/29 09:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class JzContactDto extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 1私聊  2群聊
     */
    private String contactType;
    /**
     * 用户信息
     */
    private String userId;

    private String chatId;

    /** 头像 */
    private String avatarUrl;

    private String nickname;

    private String botId;

    private String botName;

    private String token;

    /**
     * 最后活动时间
     */
    private Date lastTime;

    /**
     * 最后的消息
     */
    private String lastMessage;

    private Boolean isTop= false;


}
