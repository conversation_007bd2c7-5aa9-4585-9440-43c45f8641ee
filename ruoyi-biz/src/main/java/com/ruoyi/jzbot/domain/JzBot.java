package com.ruoyi.jzbot.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * bot查看对象 jz_bot
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class JzBot extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** botId */
    @Excel(name = "botId")
    private String botId;

    /** wxid */
    private String wxid;

    /** 企微userId */
    @Excel(name = "企微userId")
    private String weixin;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nickname;

    /** 头像 */
    @Excel(name = "头像")
    private String avatar;

    /** 是否在线 */
    @Excel(name = "是否在线")
    private String online;

    @Excel(name = "二维码")
    private String qrCode;

    @Excel(name = "托管时间")
    private Date joinTime;

    @Excel(name = "开启AI数")
    private Integer openCount;

    @Excel(name = "总群数")
    private Integer totalCount;

    //好友数量
    private Integer friendsNumber;
    private Integer surplusFriendsNumber;

    /** 小组级token */
    @Excel(name = "小组级token")
    private String token;

    /** 启用状态 */
    private String status;


    private List<String> weixinList;

}
