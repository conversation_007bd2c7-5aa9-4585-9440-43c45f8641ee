package com.ruoyi.jzbot.domain.req;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 句子聊天记录请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class JzChatMessageReq extends BaseEntity {

    /**
     * 聊天ID，标识一个bot与一个客户的会话
     */
    private String chatId;

    /**
     * 信息来源 0群 1私聊
     */
    private String messageSource;

    /**
     * 机器人ID，标识哪个bot参与了会话
     */
    private String botId;

    /**
     * 消息ID，每条消息的唯一标识
     */
    private String messageId;

    /**
     * 消息类型（如文本、图片等的整数标识）
     */
    private Integer messageType;

    /**
     * 消息内容
     */
    private String message;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 消息是否由托管账号自己发送
     */
    private Integer isSelf;

    /**
     * 是否@bot
     */
    private Integer isEt;

    /**
     * 开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;


    private Integer pageNum;

    private Integer pageSize;

    private List<String> messageIdList;

}
