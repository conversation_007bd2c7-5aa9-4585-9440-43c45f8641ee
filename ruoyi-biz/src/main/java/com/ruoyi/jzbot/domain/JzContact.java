package com.ruoyi.jzbot.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.biz.domain.BizTaggable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 联系人对象 jz_contact
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class JzContact extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 会话id */
    @Excel(name = "会话id")
    private String chatId;

    /** 所属 bot */
    @Excel(name = "所属 bot")
    private String botId;

    /** 联系人微信 */
    @Excel(name = "联系人微信")
    private String wxid;

    /** userid */
    private String weixin;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nickname;

    /** 备注 */
    @Excel(name = "备注")
    private String alias;

    /** 头像 */
    @Excel(name = "头像")
    private String avatarUrl;

    /** 性别 */
    @Excel(name = "性别")
    private String gender;

    /** 是否删除 */
    @Excel(name = "是否删除")
    private String deleted;

    /** unionId */
    @Excel(name = "unionId")
    private String unionId;

    /** 添加时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "添加时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date friendTime;

    /** AI启用状态 0-未启用 1-已启用 */
    @Excel(name = "AI启用状态" )
    private String status;

    @Excel(name = "AI智能体")
    private String agent;

    @Excel(name = "指令列表")
    private String instruction;

    //消息通知队列
    private String noticeEnv;

    private Date lastTime;

    private String lastMessage;


    private JzBot jzBot;

    private  List<BizTaggable> tagList;

    private List<String> chatIdList;


    private List<String> unionIdList;

    private  List<String> weixinList;

}
