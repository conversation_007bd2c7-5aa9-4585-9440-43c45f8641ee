package com.ruoyi.weimo.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.weimo.domain.WmUser;
import com.ruoyi.weimo.service.IWmUserService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 用户管理Controller
 * 
 * <AUTHOR>
 * @date 2024-03-11
 */
@RestController
@RequestMapping("/weimo/user")
public class WmUserController extends BaseController
{
    @Resource
    private IWmUserService wmUserService;

    /**
     * 查询用户管理列表
     */
    //@PreAuthorize("@ss.hasPermi('weimo:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(WmUser wmUser)
    {
        startPage();
        List<WmUser> list = wmUserService.selectWmUserList(wmUser);
        return getDataTable(list);
    }

    /**
     * 导出用户管理列表
     */
    //@PreAuthorize("@ss.hasPermi('weimo:user:export')")
    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WmUser wmUser)
    {
        List<WmUser> list = wmUserService.selectWmUserList(wmUser);
        ExcelUtil<WmUser> util = new ExcelUtil<WmUser>(WmUser.class);
        util.exportExcel(response, list, "用户管理数据");
    }

    /**
     * 获取用户管理详细信息
     */
    //@PreAuthorize("@ss.hasPermi('weimo:user:query')")
    @GetMapping(value = "/{wid}")
    public AjaxResult getInfo(@PathVariable("wid") Long wid)
    {
        return success(wmUserService.selectWmUserByWid(wid));
    }

    /**
     * 新增用户管理
     */
    //@PreAuthorize("@ss.hasPermi('weimo:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmUser wmUser)
    {
        return toAjax(wmUserService.insertWmUser(wmUser));
    }

    /**
     * 修改用户管理
     */
    //@PreAuthorize("@ss.hasPermi('weimo:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmUser wmUser)
    {
        return toAjax(wmUserService.updateWmUser(wmUser));
    }

    /**
     * 删除用户管理
     */
    //@PreAuthorize("@ss.hasPermi('weimo:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{wids}")
    public AjaxResult remove(@PathVariable Long[] wids)
    {
        return toAjax(wmUserService.deleteWmUserByWids(wids));
    }
}
