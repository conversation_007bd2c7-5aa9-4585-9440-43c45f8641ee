package com.ruoyi.weimo.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.weimo.domain.WmRights;
import com.ruoyi.weimo.service.IWmRightsService;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 微盟售后管理Controller
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
@RestController
@RequestMapping("/weimo/rights")
public class WmRightsController extends BaseController
{
    @Resource
    private IWmRightsService wmRightsService;

    /**
     * 查询微盟售后管理列表
     */
    //@PreAuthorize("@ss.hasPermi('weimo:rights:list')")
    @GetMapping("/list")
    public TableDataInfo list(WmRights wmRights)
    {
        startPage();
        List<WmRights> list = wmRightsService.selectWmRightsList(wmRights);
        return getDataTable(list);
    }

    /**
     * 导出微盟售后管理列表
     */
    //@PreAuthorize("@ss.hasPermi('weimo:rights:export')")
    @Log(title = "微盟售后管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WmRights wmRights)
    {
        List<WmRights> list = wmRightsService.selectWmRightsList(wmRights);
        ExcelUtil<WmRights> util = new ExcelUtil<WmRights>(WmRights.class);
        util.exportExcel(response, list, "微盟售后管理数据");
    }

    /**
     * 获取微盟售后管理详细信息
     */
    //@PreAuthorize("@ss.hasPermi('weimo:rights:query')")
    @GetMapping(value = "/{rightsId}")
    public AjaxResult getInfo(@PathVariable("rightsId") Long rightsId)
    {
        return success(wmRightsService.selectWmRightsByRightsId(rightsId));
    }

    /**
     * 新增微盟售后管理
     */
    //@PreAuthorize("@ss.hasPermi('weimo:rights:add')")
    @Log(title = "微盟售后管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmRights wmRights)
    {
        return toAjax(wmRightsService.insertWmRights(wmRights));
    }

    /**
     * 修改微盟售后管理
     */
    //@PreAuthorize("@ss.hasPermi('weimo:rights:edit')")
    @Log(title = "微盟售后管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmRights wmRights)
    {
        return toAjax(wmRightsService.updateWmRights(wmRights));
    }

    /**
     * 删除微盟售后管理
     */
    //@PreAuthorize("@ss.hasPermi('weimo:rights:remove')")
    @Log(title = "微盟售后管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{rightsIds}")
    public AjaxResult remove(@PathVariable Long[] rightsIds)
    {
        return toAjax(wmRightsService.deleteWmRightsByRightsIds(rightsIds));
    }
}
