package com.ruoyi.weimo.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.weimo.domain.WmActivity;
import com.ruoyi.weimo.service.IWmActivityService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 微盟行为Controller
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@RestController
@RequestMapping("/weimo/wmActivity")
public class WmActivityController extends BaseController
{
    @Resource
    private IWmActivityService wmActivityService;

    /**
     * 查询微盟行为列表
     */
    //@PreAuthorize("@ss.hasPermi('weimo:wmActivity:list')")
    @GetMapping("/list")
    public TableDataInfo list(WmActivity wmActivity)
    {
        startPage();
        List<WmActivity> list = wmActivityService.selectWmActivityList(wmActivity);
        return getDataTable(list);
    }

    /**
     * 导出微盟行为列表
     */
    //@PreAuthorize("@ss.hasPermi('weimo:wmActivity:export')")
    @Log(title = "微盟行为", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WmActivity wmActivity)
    {
        List<WmActivity> list = wmActivityService.selectWmActivityList(wmActivity);
        ExcelUtil<WmActivity> util = new ExcelUtil<WmActivity>(WmActivity.class);
        util.exportExcel(response, list, "微盟行为数据");
    }

    /**
     * 获取微盟行为详细信息
     */
    //@PreAuthorize("@ss.hasPermi('weimo:wmActivity:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(wmActivityService.selectWmActivityById(id));
    }

    /**
     * 新增微盟行为
     */
    //@PreAuthorize("@ss.hasPermi('weimo:wmActivity:add')")
    @Log(title = "微盟行为", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmActivity wmActivity)
    {
        return toAjax(wmActivityService.insertWmActivity(wmActivity));
    }

    /**
     * 修改微盟行为
     */
    //@PreAuthorize("@ss.hasPermi('weimo:wmActivity:edit')")
    @Log(title = "微盟行为", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmActivity wmActivity)
    {
        return toAjax(wmActivityService.updateWmActivity(wmActivity));
    }

    /**
     * 删除微盟行为
     */
    //@PreAuthorize("@ss.hasPermi('weimo:wmActivity:remove')")
    @Log(title = "微盟行为", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(wmActivityService.deleteWmActivityByIds(ids));
    }
}
