/*
 * Copyright 2013-2018 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ruoyi.weimo.controller;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.ERedisChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Description: 微盟消息接收
 *
 * <AUTHOR>
 * @date 2024/7/19 15:05
 */
@Slf4j
@RestController
@RequestMapping("/weimo/message")
@Anonymous
public class WeimobMsgController {

    @Resource
    private RedisCache redisCache;

    @PostMapping("/receive")
    public Map<String, Object> receive(@RequestBody Map<String, Object> params) {

        // 取params自行进行业务处理
        log.info("接受微盟消息: {}", JSON.toJSONString(params));

        redisCache.publish(ERedisChannel.WM_MESSAGE_CHANNEL.getCode(), params);

        // 消息成功的情况
        Map<String, Object> ack = new HashMap<>();
        Map<String, Object> code = new HashMap<>();
        code.put("errcode", 0);
        code.put("errmsg", "success");
        ack.put("code", code);

        // 消息异常情况:可返回errcode!=0和自定义errmsg,平台会做5次消息重试，
        // 分别间隔 5 秒、 30 秒 、 60 秒、5 分钟、10 分钟 。

        return ack;
    }
}
