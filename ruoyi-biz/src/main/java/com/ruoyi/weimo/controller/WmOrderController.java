package com.ruoyi.weimo.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.domain.dto.*;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.weimo.service.IWeiMobService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 微盟行为Controller
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@RestController
@RequestMapping("/weimo/wmOrder")
@Anonymous
public class WmOrderController extends BaseController
{
    @Resource
    private IWeiMobService weiMobService;

    /**
     * 查询微盟订单详情
     * @param request
     * @return
     */
    @PostMapping("/detail")
    @Anonymous
    public AjaxResult detail(@RequestBody @Valid WmOrderDetailReq request) {

        return success(weiMobService.pullOrderDetail(request.getOrderNo()));
    }

    /**
     * 查询订单列表
     * @param request
     * @return
     */
    @PostMapping("/list")
    @Anonymous
    public AjaxResult list(@RequestBody @Valid WmOrderListReq request) {
        WMOrderPageQueryReq req = new WMOrderPageQueryReq();
        req.setPageNum(1);
        req.setPageSize(5);

        WMOrderPageQueryParamReq pageQueryParamReq = new WMOrderPageQueryParamReq();
        pageQueryParamReq.setUserWid(request.getWid());
        req.setQueryParameter(pageQueryParamReq);
        JSONObject jsonObject = weiMobService.sendPostToPullOrder(req);

        return  success(jsonObject.getJSONArray("pageList"));
    }


    /**
     * 查询微盟订单售后详情
     * @param request
     * @return
     */
    @PostMapping("/rights_detail")
    @Anonymous
    public AjaxResult rightsDetail(@RequestBody @Valid WmOrderRightsReq request) {

        return success(weiMobService.pullRightsDetail(request.getRightsId()));
    }

}
