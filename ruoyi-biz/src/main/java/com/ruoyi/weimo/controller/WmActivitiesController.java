package com.ruoyi.weimo.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import javax.annotation.Resource;

import com.ruoyi.biz.domain.req.GeneratingReq;
import com.ruoyi.biz.service.IBizSalesPitchService;
import com.ruoyi.common.constant.ProductConstants;
import com.ruoyi.weimo.domain.req.ActivityCorpusReq;
import com.ruoyi.weimo.domain.req.ActivityRelationReq;
import com.ruoyi.weimo.domain.vo.WmActivitiesVo;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.weimo.domain.WmActivities;
import com.ruoyi.weimo.service.IWmActivitiesService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 活动管理Controller
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@RestController
@RequestMapping("/weimo/activities")
public class WmActivitiesController extends BaseController {
    @Resource
    private IWmActivitiesService wmActivitiesService;

    @Resource
    private IBizSalesPitchService bizSalesPitchService;

    /**
     * 查询活动管理列表
     */
    //@PreAuthorize("@ss.hasPermi('weimo:activities:list')")
    @GetMapping("/list")
    public TableDataInfo list(WmActivities wmActivities) {
        startPage();
        List<WmActivities> list = wmActivitiesService.selectWmActivitiesList(wmActivities);
        return getDataTable(list);
    }

    /**
     * 导出活动管理列表
     */
    //@PreAuthorize("@ss.hasPermi('weimo:activities:export')")
    @Log(title = "活动管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WmActivities wmActivities) {
        List<WmActivities> list = wmActivitiesService.selectWmActivitiesList(wmActivities);
        ExcelUtil<WmActivities> util = new ExcelUtil<WmActivities>(WmActivities.class);
        util.exportExcel(response, list, "活动管理数据");
    }

    /**
     * 获取活动管理详细信息
     */
    //@PreAuthorize("@ss.hasPermi('weimo:activities:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(wmActivitiesService.selectWmActivitiesById(id));
    }

    /**
     * 新增活动管理
     */
    //@PreAuthorize("@ss.hasPermi('weimo:activities:add')")
    @Log(title = "活动管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmActivities wmActivities) {
        wmActivities.setCreateBy(getUsername());
        return toAjax(wmActivitiesService.insertWmActivities(wmActivities));
    }

    /**
     * 修改活动管理
     */
    //@PreAuthorize("@ss.hasPermi('weimo:activities:edit')")
    @Log(title = "活动管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmActivities wmActivities) {
        wmActivities.setUpdateBy(getUsername());
        return toAjax(wmActivitiesService.updateWmActivities(wmActivities));
    }

    /**
     * 删除活动管理
     */
    //@PreAuthorize("@ss.hasPermi('weimo:activities:remove')")
    @Log(title = "活动管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(wmActivitiesService.deleteWmActivitiesByIds(ids));
    }

    /**
     * 修改关键词
     * @param wmActivities wmActivities
     * @return 修改结果
     */
//    @Log(title = "活动管理-设定关键词", businessType = BusinessType.UPDATE)
//    public AjaxResult editKeyWords(@RequestBody WmActivities wmActivities)
//    {
//        return toAjax(wmActivitiesService.updateWmActivities(wmActivities));
//    }

    /**
     * 获取活动关联的产品
     *
     * @param id id
     * @return 关联的产品
     */
    @GetMapping("/getActivityRefProducts/{id}")
    public AjaxResult getActivityRefProducts(@PathVariable("id") Long id) {
        return success(wmActivitiesService.getActivityRefProducts(id));
    }

    /**
     * 保存活动朋友圈话术
     *
     * @param req 请求参数
     * @return
     */
    @Log(title = "保存活动朋友圈话术", businessType = BusinessType.INSERT)
    @PostMapping("/saveActivityMoments")
    public AjaxResult saveActivityMoments(@RequestBody ActivityRelationReq req) {
        return toAjax(bizSalesPitchService.updateSalesPitch(req.getSalesPitchList(), req.getActivityId(), ProductConstants.SalesPitch.RefType.ACTIVITY));
    }

    /**
     * 保存活动语料
     *
     * @param req 请求参数
     * @return
     */
    @Log(title = "保存活动语料", businessType = BusinessType.INSERT)
    @PostMapping("/saveActivityCorpus")
    public AjaxResult saveActivityCorpus(@RequestBody ActivityCorpusReq req) {
        return toAjax(wmActivitiesService.saveActivityCorpus(req));
    }

    /**
     * 获取活动语料
     *
     * @param id 活动id
     * @return 活动语料
     */
    @GetMapping("/getActivityCorpus/{id}")
    public AjaxResult getActivityCorpus(@PathVariable("id") Long id) {
        return success(wmActivitiesService.getActivityCorpus(id));
    }

    /**
     * 获取活动朋友圈话术
     *
     * @param id id
     * @return 朋友圈话术
     */
    @GetMapping("/getActivityMoments/{id}")
    public AjaxResult getActivityMoments(@PathVariable("id") Long id) {
        return success(wmActivitiesService.getActivityMoments(id));
    }

    /**
     * 根据活动Id获取活动
     * @param activityIds 活动Id
     * @return 活动
     */
    @GetMapping("/getActivitiesByActivityIds")
    public AjaxResult getActivitiesByActivityIds(@RequestParam("activityIds") List<Long> activityIds) {
        return success(wmActivitiesService.getActivitiesByActivityIds(activityIds));
    }

    /**
     * 获取正在进行中的活动ID
     */
    @GetMapping("/getOngoingActivityIds")
    public AjaxResult getOngoingActivityIds() {
        return success(wmActivitiesService.getOngoingActivityIds());
    }

    /**
     * 批量修改单品的 AI导购推荐 状态
     *
     * @param ids    商品id
     * @param status ai导购推荐状态
     * @return 成功返回>1 失败返回0
     */
    @PutMapping("/batch-update/ai-purchase")
    public AjaxResult batchUpdateAiPurchase(Long[] ids, Integer status) {
        return toAjax(wmActivitiesService.updateAiRepurchase(Arrays.asList(ids), status));
    }
}
