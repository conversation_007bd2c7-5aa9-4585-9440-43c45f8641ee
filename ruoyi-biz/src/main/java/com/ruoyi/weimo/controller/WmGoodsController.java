package com.ruoyi.weimo.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.weimo.domain.WmGoods;
import com.ruoyi.weimo.service.IWmGoodsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 微盟商品Controller
 *
 * <AUTHOR>
 * @date 2024-03-26
 */
@RestController
@RequestMapping("/weimo/goods")
@Deprecated
public class WmGoodsController extends BaseController
{
    @Resource
    private IWmGoodsService wmGoodsService;

    /**
     * 查询微盟商品列表
     */
    //@PreAuthorize("@ss.hasPermi('weimo:goods:list')")
    @GetMapping("/list")
    public TableDataInfo list(WmGoods wmGoods)
    {
        startPage();
        List<WmGoods> list = wmGoodsService.selectWmGoodsList(wmGoods);
        return getDataTable(list);
    }

    /**
     * 导出微盟商品列表
     */
    //@PreAuthorize("@ss.hasPermi('weimo:goods:export')")
    @Log(title = "微盟商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WmGoods wmGoods)
    {
        List<WmGoods> list = wmGoodsService.selectWmGoodsList(wmGoods);
        ExcelUtil<WmGoods> util = new ExcelUtil<WmGoods>(WmGoods.class);
        util.exportExcel(response, list, "微盟商品数据");
    }

    /**
     * 获取微盟商品详细信息
     */
    //@PreAuthorize("@ss.hasPermi('weimo:goods:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(wmGoodsService.selectWmGoodsById(id));
    }

    /**
     * 新增微盟商品
     */
    //@PreAuthorize("@ss.hasPermi('weimo:goods:add')")
    @Log(title = "微盟商品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmGoods wmGoods)
    {
        return toAjax(wmGoodsService.insertWmGoods(wmGoods));
    }

    /**
     * 修改微盟商品
     */
    //@PreAuthorize("@ss.hasPermi('weimo:goods:edit')")
    @Log(title = "微盟商品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmGoods wmGoods)
    {
        return toAjax(wmGoodsService.updateWmGoods(wmGoods));
    }

    /**
     * 删除微盟商品
     */
    //@PreAuthorize("@ss.hasPermi('weimo:goods:remove')")
    @Log(title = "微盟商品", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(wmGoodsService.deleteWmGoodsByIds(ids));
    }
}
