package com.ruoyi.weimo.mapper;

import java.util.List;
import java.util.Set;

import com.ruoyi.weimo.domain.WmActivities;
import org.apache.ibatis.annotations.Param;

/**
 * 活动管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-25
 */
public interface WmActivitiesMapper 
{
    /**
     * 查询活动管理
     * 
     * @param id 活动管理主键
     * @return 活动管理
     */
    public WmActivities selectWmActivitiesById(Long id);

    /**
     * 查询活动管理列表
     * 
     * @param wmActivities 活动管理
     * @return 活动管理集合
     */
    public List<WmActivities> selectWmActivitiesList(WmActivities wmActivities);

    /**
     * 新增活动管理
     * 
     * @param wmActivities 活动管理
     * @return 结果
     */
    public int insertWmActivities(WmActivities wmActivities);

    /**
     * 修改活动管理
     * 
     * @param wmActivities 活动管理
     * @return 结果
     */
    public int updateWmActivities(WmActivities wmActivities);

    /**
     * 删除活动管理
     * 
     * @param id 活动管理主键
     * @return 结果
     */
    public int deleteWmActivitiesById(Long id);

    /**
     * 批量删除活动管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWmActivitiesByIds(Long[] ids);

    /**
     * 获取id
     * @param activityId 活动id
     * @return id
     */
    Long getIdByActivityId(Long activityId);

    /**
     * 批量查询
     * @param activityIds 集合
     * @return 集合
     */
    List<WmActivities> selectWmActivitiesByIds(@Param("activityIds") Set<Long> activityIds);

    /**
     * 批量查询
     * @param activityIds 集合
     * @return 集合
     */
    List<WmActivities> selectWmActivitiesByActivityIds(@Param("activityIds") List<Long> activityIds);

    /**
     * 批量修改 AI导购
     * @param ids 集合
     * @param status 状态
     * @return 结果
     */
    int updateAiRepurchase(@Param("ids") List<Long> ids, @Param("status") Integer status);

    /**
     * 查询进行中的活动id集合
     * @return 集合
     */
    List<Long> selectOngoingActivityIds();
}
