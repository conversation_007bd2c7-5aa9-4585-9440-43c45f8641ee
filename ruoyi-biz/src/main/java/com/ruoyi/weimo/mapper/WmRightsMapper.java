package com.ruoyi.weimo.mapper;

import java.util.List;
import com.ruoyi.weimo.domain.WmRights;

/**
 * 微盟售后管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-04
 */
public interface WmRightsMapper 
{
    /**
     * 查询微盟售后管理
     * 
     * @param rightsId 微盟售后管理主键
     * @return 微盟售后管理
     */
    public WmRights selectWmRightsByRightsId(Long rightsId);

    /**
     * 查询微盟售后管理列表
     * 
     * @param wmRights 微盟售后管理
     * @return 微盟售后管理集合
     */
    public List<WmRights> selectWmRightsList(WmRights wmRights);

    /**
     * 新增微盟售后管理
     * 
     * @param wmRights 微盟售后管理
     * @return 结果
     */
    public int insertOrUpdateWmRights(WmRights wmRights);

    /**
     * 修改微盟售后管理
     * 
     * @param wmRights 微盟售后管理
     * @return 结果
     */
    public int updateWmRights(WmRights wmRights);

    /**
     * 删除微盟售后管理
     * 
     * @param rightsId 微盟售后管理主键
     * @return 结果
     */
    public int deleteWmRightsByRightsId(Long rightsId);

    /**
     * 批量删除微盟售后管理
     * 
     * @param rightsIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWmRightsByRightsIds(Long[] rightsIds);
}
