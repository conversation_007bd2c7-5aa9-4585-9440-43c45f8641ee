package com.ruoyi.weimo.mapper;

import java.util.List;
import com.ruoyi.weimo.domain.WmUser;

/**
 * 用户管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
public interface WmUserMapper
{
    /**
     * 查询用户管理
     *
     * @param wid 用户管理主键
     * @return 用户管理
     */
    public WmUser selectWmUserByWid(Long wid);

    /**
     * 查询用户管理列表
     *
     * @param wmUser 用户管理
     * @return 用户管理集合
     */
    public List<WmUser> selectWmUserList(WmUser wmUser);

    /**
     * 新增用户管理
     *
     * @param wmUser 用户管理
     * @return 结果
     */
    public int insertWmUser(WmUser wmUser);

    /**
     * 修改用户管理
     *
     * @param wmUser 用户管理
     * @return 结果
     */
    public int updateWmUser(WmUser wmUser);

    /**
     * 删除用户管理
     *
     * @param wid 用户管理主键
     * @return 结果
     */
    public int deleteWmUserByWid(Long wid);

    /**
     * 批量删除用户管理
     *
     * @param wids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWmUserByWids(Long[] wids);

    /**
     * 批量新增
     * @param list
     */
    void insertBatch(List<WmUser> list);

    /**
     * 获取没有UnionId的用户
     */
    List<WmUser> selectWmUserNonUnionIdList();
}
