package com.ruoyi.weimo.config;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.constant.WmConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.ERedisChannel;
import com.ruoyi.weimo.domain.WmActivities;
import com.ruoyi.weimo.domain.WmActivityRule;
import com.ruoyi.weimo.domain.WmFullDiscountActivity;
import com.ruoyi.weimo.domain.WmPromotionDiscountActivity;
import com.ruoyi.weimo.domain.dto.ActivityPageQueryReq;
import com.ruoyi.weimo.domain.dto.MsgBodyActivity;
import com.ruoyi.weimo.domain.dto.WmMessage;
import com.ruoyi.weimo.enums.EActivitySource;
import com.ruoyi.weimo.enums.EActivityType;
import com.ruoyi.weimo.service.IWeiMobService;
import com.ruoyi.weimo.service.IWmActivitiesService;
import com.ruoyi.weimo.service.IWmCustomerService;
import com.ruoyi.weimo.service.IWmOrderService;
import com.ruoyi.weimo.service.IWmProductService;
import com.ruoyi.weimo.service.IWmRightsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;

@Configuration
@Slf4j
public class WeiMoMessageListen {

    @Resource
    private RedisCache redisCache;

    @Resource
    private RedisLock redisLock;

    @Resource
    private IWeiMobService weiMobService;

    @Resource
    private WeiMoConfig weiMoConfig;

    @Resource
    private IWmActivitiesService wmActivitiesService;

    @Resource
    private IWmProductService wmProductService;

    @Resource
    private IWmOrderService wmOrderService;

    @Resource
    private IWmCustomerService wmCustomerService;

    @Resource
    private IWmRightsService wmRightsService;

    public static final String WM_MESSAGE_LOCK = "wm_message_lock:%s";

    public WeiMoMessageListen(RedisMessageListenerContainer container) {
        container.addMessageListener((message, pattern) -> {
            if (!System.getProperty("os.name").contains("Linux")) {
                log.info("非Linux环境，不消费微盟消息");
                return;
            }

            String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);

            WmMessage wmMessage = JSON.parseObject(messageBody, WmMessage.class);
            if (wmMessage == null) {
                log.error("解析微盟消息体失败");
                return;
            }
            String lockTime = String.valueOf(System.currentTimeMillis() + 15 * 1000);
            boolean locked = redisLock.lock(String.format(WM_MESSAGE_LOCK, wmMessage.getId()), lockTime);
            if (!locked) {
                log.warn("微盟消息处理锁被占用, id: {}", wmMessage.getId());
                return;
            }
            log.info("开始处理微盟消息, topic: {}, event: {}, messageBody: {}", wmMessage.getTopic(), wmMessage.getEvent(), messageBody);
            try {
                switch (wmMessage.getTopic()) {
                    case WmConstants.Topic.WM_SHOP_ORDER:
                        wmOrderService.handleTopic(wmMessage);
                        break;
                    case WmConstants.Topic.WM_SHOP_PROMOTION:
                        handleActivityTopic(wmMessage);
                        break;
                    case WmConstants.Topic.WM_SHOP_GOODS:
                        wmProductService.handleTopic(wmMessage);
                        break;
                    case WmConstants.Topic.WM_SHOP_RIGHTS:
                        wmRightsService.handleTopic(wmMessage);
                        break;
                    case WmConstants.Topic.WM_CRM_CUSTOMER:
                        wmCustomerService.handleTopic(wmMessage);
                        break;
                    default:
                        log.warn("未处理到的微盟事件, event: {}", wmMessage.getTopic());
                }
            } catch (Exception e) {
                log.error("处理微盟消息异常, message: {}", messageBody, e);
            } finally {
                // 不释放锁的原因是防止多个消费者同时处理, 防重放
                // redisLock.unlock(String.format(WM_MESSAGE_LOCK, wmMessage.getId()), lockTime);
            }

        }, new PatternTopic(ERedisChannel.WM_MESSAGE_CHANNEL.getCode()));
    }

    /**
     * 处理微盟订单相关事件
     * @param wmMessage 微盟消息
     */
    private void handleActivityTopic(WmMessage wmMessage) {
        JSONObject msgBody = (JSONObject) wmMessage.getMsgBody();
        MsgBodyActivity msgBodyActivity = msgBody.to(MsgBodyActivity.class);

        // 监听到活动事件后执行对应操作
        ActivityPageQueryReq req = new ActivityPageQueryReq();
        req.setActivityId(msgBodyActivity.getActivityId());
        ActivityPageQueryReq.basicInfo basicInfo = req.new basicInfo();
        basicInfo.setVid(Long.parseLong(weiMoConfig.getVid()));
        basicInfo.setVidType(2);
        req.setBasicInfo(basicInfo);
        JSONObject jsonObject = new JSONObject();
        if (EActivityType.ACTIVITY_TYPE_1.getCode() == msgBodyActivity.getActivityType()) {
            jsonObject = weiMobService.sendPostToPullPromotionFullDiscount(req);
            dealPromotionFullDiscountActivity(jsonObject, wmMessage);
        } else if (EActivityType.ACTIVITY_TYPE_3.getCode() == msgBodyActivity.getActivityType()) {
            jsonObject = weiMobService.sendPostToPullPromotionDiscountActivity(req);
            dealPromotionPromotionDiscountActivity(jsonObject, wmMessage);
        }

    }

    /**
     * 处理微盟活动相关事件-限时折扣活动
     * @param jsonObject jsonObject
     */
    private void dealPromotionPromotionDiscountActivity(JSONObject jsonObject, WmMessage wmMessage) {
        WmActivities wmActivities = JSON.parseObject(jsonObject.toJSONString(), WmActivities.class);
        if (jsonObject.getJSONArray("cycleTimeList").isEmpty()) {
            wmActivities.setCycleTimeList(null);
        }
        WmPromotionDiscountActivity fullDiscountActivity = JSON.parseObject(jsonObject.toJSONString(), WmPromotionDiscountActivity.class);
        WmActivityRule wmActivityRule = JSON.parseObject(jsonObject.toJSONString(), WmActivityRule.class);
        // 判断活动是否已经存在
        Long activityId = wmActivities.getActivityId();
        Long id = wmActivitiesService.getIdByActivityId(activityId);

        wmActivities.setRefType(String.valueOf(fullDiscountActivity.getApplicationGoodsType()));
        wmActivityRule.setRefType(String.valueOf(fullDiscountActivity.getApplicationGoodsType()));

        if (!fullDiscountActivity.getScopeGoodsIdList().isEmpty()) {
            String collect = fullDiscountActivity.getScopeGoodsIdList().stream().map(String::valueOf).collect(Collectors.joining(","));
            wmActivities.setRefId(collect);
            wmActivityRule.setRefId(collect);
        }

        // 设置活动规则
        wmActivities.setRule(JSON.toJSONString(wmActivityRule));
        // 设置活动详情
        wmActivities.setDetails(JSON.toJSONString(fullDiscountActivity));
        // 设置活动来源
        wmActivities.setSource(EActivitySource.WEI_MO.getCode());
        if (WmConstants.Event.ACTIVITY_CREATE.equals(wmMessage.getEvent())) {
            if (id != null) {
                log.warn("微盟活动创建事件，限时折扣活动已经存在, activityId: {}", activityId);
                return;
            }
            //TODO 此处是否还需要再次判断？
            wmActivitiesService.insertWmActivities(wmActivities);
            log.info("微盟活动创建事件，限时折扣活动创建成功, activityId: {}", activityId);
        } else if (WmConstants.Event.ACTIVITY_UPDATE.equals(wmMessage.getEvent())) {
            if (id == null) {
                log.error("微盟活动更新事件，限时折扣活动不存在, activityId: {}", activityId);
                return;
            }
            wmActivities.setId(id);
            wmActivitiesService.updateWmActivities(wmActivities);
            log.info("微盟活动更新事件，限时折扣活动更新成功, activityId: {}", activityId);
        } else if (WmConstants.Event.ACTIVITY_DELETE.equals(wmMessage.getEvent())) {
            if (id == null) {
                log.error("微盟活动删除事件，限时折扣活动不存在, activityId: {}", activityId);
                return;
            }
            wmActivitiesService.deleteWmActivitiesById(id);
            log.info("微盟活动删除事件，限时折扣活动删除成功, activityId: {}", activityId);
        }
    }

    /**
     * 处理微盟活动相关事件-满减活动
     * @param jsonObject jsonObject
     */
    private void dealPromotionFullDiscountActivity(JSONObject jsonObject, WmMessage wmMessage) {
        WmActivities wmActivities = JSON.parseObject(jsonObject.toJSONString(), WmActivities.class);
        if (jsonObject.getJSONArray("cycleTimeList").isEmpty()) {
            wmActivities.setCycleTimeList(null);
        }
        WmFullDiscountActivity activity = JSON.parseObject(jsonObject.toJSONString(), WmFullDiscountActivity.class);
        WmActivityRule wmActivityRule = JSON.parseObject(jsonObject.toJSONString(), WmActivityRule.class);
        // 判断活动是否已经存在
        Long activityId = wmActivities.getActivityId();
        Long id = wmActivitiesService.getIdByActivityId(activityId);
        wmActivities.setRefType(String.valueOf(activity.getApplicationGoodsType()));
        wmActivityRule.setRefType(String.valueOf(activity.getApplicationGoodsType()));
        if (!activity.getSubIds().isEmpty()) {
            String collect = activity.getSubIds().stream().map(String::valueOf).collect(Collectors.joining(","));
            wmActivities.setRefId(collect);
            wmActivities.setRefId(collect);
        }
        // 设置活动详情
        wmActivities.setDetails(JSON.toJSONString(activity));
        // 设置活动规则
        wmActivities.setRule(JSON.toJSONString(wmActivityRule));
        // 设置活动来源
        wmActivities.setSource(EActivitySource.WEI_MO.getCode());
        if (WmConstants.Event.ACTIVITY_CREATE.equals(wmMessage.getEvent())) {
            if (id != null) {
                log.warn("微盟活动创建事件，满减满折活动已经存在, activityId: {}", activityId);
                return;
            }
            // 监听到新增活动时，生成版本记录，提交时若无语料或朋友圈，生成对应待办
            wmActivitiesService.insertWmActivities(wmActivities);
            log.info("微盟活动创建事件，满减满折活动创建成功, activityId: {}", activityId);
        } else if (WmConstants.Event.ACTIVITY_UPDATE.equals(wmMessage.getEvent())) {
            if (id == null) {
                log.error("微盟活动更新事件，满减满折活动不存在, activityId: {}", activityId);
                return;
            }
            wmActivities.setId(id);
            // 监听到修改活动时，生成版本记录，版本提交时若无语料或朋友圈，生成对应待办
            wmActivitiesService.updateWmActivities(wmActivities);
            log.info("微盟活动更新事件，满减满折活动更新成功, activityId: {}", activityId);
        } else if (WmConstants.Event.ACTIVITY_DELETE.equals(wmMessage.getEvent())) {
            if (id == null) {
                log.error("微盟活动删除事件，满减满折活动不存在, activityId: {}", activityId);
                return;
            }
            wmActivitiesService.deleteWmActivitiesById(id);
            log.info("微盟活动删除事件，满减满折活动删除成功, activityId: {}", activityId);
        }
    }
}
