package com.ruoyi.weimo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取微盟相关配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "weimo")
@Data
public class WeiMoConfig {

    private String clientId;
    private String clientSecret;

    private String accessToken;

    private String vid;

    private String shopId;

    //小程序配置
    private String appid;
    private String title;
    private String username;
    private String iconUrl;

    private String wxAppId;

}
