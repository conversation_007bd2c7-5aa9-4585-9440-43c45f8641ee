package com.ruoyi.weimo.utils;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.enums.EGenerateType;
import com.ruoyi.weimo.domain.WmActivities;
import com.ruoyi.weimo.domain.WmFullDiscountActivity;
import com.ruoyi.weimo.enums.*;
import com.ruoyi.weimo.domain.WmActivityRule;
import com.ruoyi.goods.domain.PmsCategory;
import com.ruoyi.goods.domain.PmsGoods;
import com.ruoyi.goods.domain.vo.ActivityRefVo;
import com.ruoyi.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 活动信息生成工具类
 * <AUTHOR>
 * @date 2024/10/9
 */
@Component
@Slf4j
public class ActivityInfoUtils {

    /**
     * 生成活动信息字符串
     *
     * @param wmActivities 活动对象
     * @param type         生成类型
     * @return 活动信息字符串
     */
    public String generateActivityInfo(WmActivities wmActivities, EGenerateType type) {
        StringBuilder build = new StringBuilder();
        if (wmActivities == null) return "暂无活动";

        // 活动名称
        build.append("活动名称：").append(wmActivities.getPromotionName()).append(";");

        if (wmActivities.getActivityType() != null) {
            build.append("活动类型：").append(EActivityType.getByCode(wmActivities.getActivityType()).getInfo()).append(";");
        }

        if (wmActivities.getStartTime() != null) {
            build.append("活动开始时间：").append(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", wmActivities.getStartTime())).append(";");
        }

        if (wmActivities.getEndTime() != null) {
            build.append("活动结束时间：").append(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", wmActivities.getEndTime())).append(";");
        }

        if (wmActivities.getPromotionStatus() != null) {
            build.append("活动状态：").append(Objects.requireNonNull(EActivityStatus.getByValue(wmActivities.getPromotionStatus().intValue())).getDesc()).append(";");
        }

        // 活动相关商品
        if (wmActivities.getActivityRef() != null) {
            ActivityRefVo activityRef = wmActivities.getActivityRef();
            if (EActivityRefType.ALL.getCode().equals(activityRef.getRefType())) {
                build.append("活动相关商品：全部商品").append(";");
            } else if (EActivityRefType.DEFAULT.getCode().equals(activityRef.getRefType()) || EActivityRefType.PART_PRODUCT.getCode().equals(activityRef.getRefType())) {
                // 判断活动是否为空。
                if (!CollectionUtils.isEmpty(activityRef.getRefProducts())) {
                    build.append("活动相关商品：").append(activityRef.getRefProducts().stream().map(PmsGoods::getName).collect(Collectors.joining("、"))).append(";");
                }
            } else if (EActivityRefType.PART_CATEGORY.getCode().equals(activityRef.getRefType())) {
                if (!CollectionUtils.isEmpty(activityRef.getRefCategories())) {
                    build.append("活动相关商品类目：").append(activityRef.getRefCategories().stream().map(PmsCategory::getName).collect(Collectors.joining("、"))).append(";");
                }
            }
        }

        // 处理活动优惠政策
        processActivityPolicy(wmActivities, build);

        // 活动来源
        if (StringUtils.isNotBlank(wmActivities.getSource())) {
            build.append("活动来源：").append(EActivitySource.getByCode(wmActivities.getSource()).getInfo()).append(";");
        }

        // 活动说明
        if (StringUtils.isNotBlank(wmActivities.getPromotionDescription())) {
            build.append("活动说明：").append(wmActivities.getPromotionDescription()).append(";");
        }

        // 活动关键词
        if (StringUtils.isNotBlank(wmActivities.getKeywords())) {
            build.append("活动关键词：").append(wmActivities.getKeywords()).append("。");
        }
        return build.toString();
    }

    /**
     * 处理活动优惠政策
     *
     * @param wmActivities 活动对象
     * @param build        拼接字符串的 StringBuilder
     */
    public void processActivityPolicy(WmActivities wmActivities, StringBuilder build) {
        if (wmActivities.getRule() == null) {
            log.warn("处理活动规则，活动规则为空");
            return;
        }
        WmActivityRule rule = JSONObject.parseObject(wmActivities.getRule(), WmActivityRule.class);

        build.append("活动规则：");
        // 只传必填的规则
        if (EActivityType.ACTIVITY_TYPE_1.getCode() == wmActivities.getActivityType()) {
            // 满减满折
            processFullDiscount(rule, build);
        } else {
            // 限时折扣
            processPromotionDiscount(rule, build);
        }

    }

    /**
     * 处理限时折扣
     * @param rule 活动规则
     * @param build 拼接字符串的 StringBuilder
     */
    private void processPromotionDiscount(WmActivityRule rule, StringBuilder build) {
        // 暂定只取限购设置以及使用人群
        int limitType = rule.getOverLimitCanOriginPriceBuy();
        int eachLimitNum = rule.getEachLimitNum();

        if (limitType == EActivityLimitType.NO_LIMIT.getCode()) {
            build.append("不限购;");
        } else {
            if (eachLimitNum != 0) {
                // 判断是否允许原价购买
                String originalPriceStatus = (limitType == EActivityLimitType.OVER_LIMIT_NO_ORIGINAL_PRICE.getCode()) ? "不可" : "可";
                build.append("超过限购").append(originalPriceStatus).append("原价购买，每人限购")
                        .append(eachLimitNum)
                        .append("件;");
            }
        }

    }

    /**
     * 处理满减满折
     * @param rule 活动规则
     * @param build 拼接字符串的 StringBuilder
     */
    private void processFullDiscount(WmActivityRule rule, StringBuilder build) {
        // 获取第一个 ResultPolymerBizVO 对象
        WmFullDiscountActivity.ResultPolymerBizVO polymerBizVO = rule.getResultPolymerBizVOList().get(0);

        // 判断是否为满元优惠且操作符为大于等于
        boolean isFullPrice = EActivityConditionType.FULL_PRICE.getCode() == rule.getConditionType().intValue();
//        boolean isGEOperator = EActivityConditionOperator.GE.getCode() == polymerBizVO.getConditionOperator();

        // 根据条件类型和操作符确定优惠类型和单位
        String discountType = isFullPrice ? "满元优惠" : "满件优惠";
        String conditionUnit = isFullPrice ? "元" : "件";

        // 构建优惠条件部分
        build.append(discountType)
                .append("：满")
                .append(polymerBizVO.getConditionValue())
                .append(conditionUnit)
                .append("，");

        // 获取优惠内容描述
        String discountDesc = EActivityDiscountContentType.getDesc(polymerBizVO.getResultType());
        build.append(discountDesc);

        // 根据优惠内容类型决定后缀
        String discountSuffix = discountDesc.contains("减钱") ? "元" : "折";
        build.append(polymerBizVO.getResultValue()).append(discountSuffix);

        // 构建每单最多优惠部分
        if (polymerBizVO.getMaxAmount() == 0) {
            build.append(";");
        } else {
            build.append("，每单最多优惠").append(polymerBizVO.getMaxAmount()).append("元;");
        }
    }

}
