package com.ruoyi.weimo.domain.dto;

import lombok.Data;

/**
 * 售后信息类
 */
@Data
public class MsgBodyRights {
    
    /**
     * 订单编号。可以通过 weimob_shop/order/list/search 接口获取该 ID。
     */
    private Long orderNo;

    /**
     * 售后单号。可以通过商家后台售后列表或 weimob_shop/rights/list/search 接口获取该 ID。
     */
    private Long rightsId;

    /**
     * 更新前的售后状态
     */
    private Integer statusBefore;

    /**
     * 更新后的售后状态
     */
    private Integer status;

    /**
     * 售后信息
     */
    private RightsInfo rightsInfo;

    /**
     * 订单信息
     */
    private OrderInfo orderInfo;


    /**
     * 售后信息类
     */
    @Data
    public static class RightsInfo {
        
        /**
         * 销售组织架构节点 ID。组织的唯一标识，是 创建组织 时自动生成的 ID，可以通过 bos/organization/getList 接口获取该 ID。
         */
        private Long vid;

        /**
         * 退款类型。支持的类型包括：1-线上退款；2-线下退款；99-无需退款。
         */
        private Integer refundType;

        /**
         * 售后状态。支持的类型包括：1-买家发起售后；2-等待买家退货；3-买家已退货；5-系统退款中；6-售后完成；7-买家已取消；8-商家已拒绝；9-退款失败；10-商家退款中；20-换货中。
         */
        private Integer rightsStatus;

        /**
         * 售后类型。支持的类型包括：1-退货退款；2-退款；5-退换货。
         */
        private Integer rightsType;

        /**
         * 服务组织架构节点 ID。组织的唯一标识，是 创建组织 时自动生成的 ID，可以通过 bos/organization/getList 接口获取该 ID。
         */
        private Long processVid;

        /**
         * 售后方式。支持的类型包括：1-买家申请售后；2-商家取消订单；5-收银台退款；6-外部平台商家售后；7-优惠券到期自动退款；8-系统自动售后；9-商家发起售后；10-付费券发券失败发起售后；20-买家取消订单。
         */
        private Integer rightsCauseType;

        /**
         * 售后来源。支持的类型包括：0-系统内部订单；100-历史导入订单；101-友朋；102-芸智；103-乐美；104-博申；201-分销市场供货商；401-全渠道导入订单。
         */
        private Integer rightsSource;
    }

    /**
     * 订单信息类
     */
    @Data
    public static class OrderInfo {

        /**
         * 订单类型。支持的类型包括：1- B2C订单；99-充值订单；98-积分订单；97-消费订单；96-虚拟订单。
         */
        private Integer orderType;

        /**
         * 订单来源。支持的类型包括：0-系统内部订单。
         */
        private Integer orderSource;

        /**
         * 渠道来源。支持的类型包括：0-公众号；1-小程序；2-H5；3-QQ；4-微博；5-字节跳动小程序；6-支付宝小程序；7-PC后台；8-安卓app；9-苹果app；10-百度智能小程序；11-PAD；12-自有APP；13-微信小程序webview；14-微信小程序webview-直播；15-大屏扫码；16-企业微信；17-抖音原生小程序；18-芸智免密；19-QQ原生小程序；20-QQ小程序H5页面；21-支付宝原生小程序；22-微信小商店；23-快手原生小程序；24-快手小程序H5页面；101-分销商。
         */
        private Integer channelType;

        /**
         * 父订单号。可以通过 weimob_shop/order/detail/get 接口获取该 ID。
         */
        private Long parentOrderNo;

        /**
         * 业务来源类型。支持的类型包括：1-普通订单；2-商户助手APP；3-收银台；4-扫码购；5-普通货柜；6-免密货柜；7-门店自助。
         */
        private Integer bizSourceType;
    }
}