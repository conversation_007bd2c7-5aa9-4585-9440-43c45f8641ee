package com.ruoyi.weimo.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.utils.CustomerStrToJsonArraySerialize;
import com.ruoyi.common.utils.CustomerStrToJsonObjectSerialize;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/7/31
 */
@Data
public class WmActivitiesVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 活动 ID
     */
    @Excel(name = "活动 ID")
    private Long activityId;

    /**
     * 活动类型
     */
    private Long activityType;

    /**
     * 活动类型 - 翻译
     */
    private String activityTypeStr;

    /**
     * 活动子类型
     */
    private Long activitySubType;

    /**
     * 活动名称
     */
    @Excel(name = "活动名称")
    private String promotionName;

    /**
     * 活动时间类型，支持的类型包括：0-固定时间；1-周期时间。
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Long timeType;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 统计日期，格式为：yy-mm-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date day;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 周期时间列表 json
     */
    @JsonSerialize(using = CustomerStrToJsonArraySerialize.class)
    private String cycleTimeList;

    /**
     * 活动状态
     */
    @Excel(name = "活动状态")
    private Long promotionStatus;

    /**
     * 活动状态 - 翻译
     */
    private String promotionStatusStr;

    /**
     * 活动图片
     */
    @Excel(name = "活动图片")
    private String promotionImage;

    /**
     * 是否长期有效
     */
    @Excel(name = "是否长期有效")
    private Long isPromotionLongTime;

    /**
     * 是否长期有效 - 翻译
     */
    private String isPromotionLongTimeStr;

    /**
     * 来源渠道，0:手动创建，1:微盟
     */
    @Excel(name = "来源渠道，0:手动创建，1:微盟")
    private String source;

    /**
     * 关键词
     */
    @Excel(name = "关键词")
    private String keywords;

    /**
     * 关联类型：101-全部商品；102-部分商品；103-部分类目；104-部分分组；105-部分场所(商品）。
     */
    private String refType;

    /**
     * 关联ID
     */
    private String refId;

    /**
     * 关联名称
     */
    private String refName;

    /**
     * 活动对应规则
     */
//    @JsonSerialize(using = CustomerStrToJsonObjectSerialize.class)
    private String rule;

    /**
     * 活动详情
     */
//    @JsonSerialize(using = CustomerStrToJsonObjectSerialize.class)
    private String details;

}
