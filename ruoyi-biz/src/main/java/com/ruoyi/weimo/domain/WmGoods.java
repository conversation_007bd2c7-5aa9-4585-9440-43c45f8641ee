package com.ruoyi.weimo.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 微盟商品对象 wm_goods
 * 
 * <AUTHOR>
 * @date 2024-03-26
 */
public class WmGoods extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String title;

    /** 商品副标题 */
    @Excel(name = "商品副标题")
    private String subTitle;

    /** 商品主图Url */
    @Excel(name = "商品主图Url")
    private String defaultImageUrl;

    /** 外部商品Code */
    @Excel(name = "外部商品Code")
    private String outerGoodsCode;

    /** 一级类目 */
    @Excel(name = "一级类目")
    private Long category;

    /** 二级类目 */
    @Excel(name = "二级类目")
    private Long subCategory;

    /** 商品可售总库存 */
    @Excel(name = "商品可售总库存")
    private Long goodsStockNum;

    /** 最大销售价格 */
    @Excel(name = "最大销售价格")
    private BigDecimal maxSalePrice;

    /** 最小销售价格 */
    @Excel(name = "最小销售价格")
    private BigDecimal minSalePrice;

    /** 商品总销量 */
    @Excel(name = "商品总销量")
    private Long realSaleNum;

    /** 商品销售类型 1-现货 2-预售 3-抽签售 */
    @Excel(name = "商品销售类型 1-现货 2-预售 3-抽签售")
    private Long soldType;

    /** 是否是多规格 0-单规格 1-多规格 */
    @Excel(name = "是否是多规格 0-单规格 1-多规格")
    private Long isMultiSku;

    /** 上架状态 0-下架 1上架 */
    @Excel(name = "上架状态 0-下架 1上架")
    private Long isOnline;

    /** 商品是否可售 0-不可收 1可售 */
    @Excel(name = "商品是否可售 0-不可收 1可售")
    private Long isCanSell;

    /** 商品序号 */
    @Excel(name = "商品序号")
    private Long sort;

    /** 上架时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上架时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date onlineTime;

    /** 商品详情json */
    @Excel(name = "商品详情json")
    private String detailInfo;

    /** 0未删除 1已删除 */
    @Excel(name = "0未删除 1已删除")
    private Long deleteFlag;

    /** skuIdList */
    @Excel(name = "skuIdList")
    private String skuList;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setSubTitle(String subTitle) 
    {
        this.subTitle = subTitle;
    }

    public String getSubTitle() 
    {
        return subTitle;
    }
    public void setDefaultImageUrl(String defaultImageUrl) 
    {
        this.defaultImageUrl = defaultImageUrl;
    }

    public String getDefaultImageUrl() 
    {
        return defaultImageUrl;
    }
    public void setOuterGoodsCode(String outerGoodsCode) 
    {
        this.outerGoodsCode = outerGoodsCode;
    }

    public String getOuterGoodsCode() 
    {
        return outerGoodsCode;
    }
    public void setCategory(Long category) 
    {
        this.category = category;
    }

    public Long getCategory() 
    {
        return category;
    }
    public void setSubCategory(Long subCategory) 
    {
        this.subCategory = subCategory;
    }

    public Long getSubCategory() 
    {
        return subCategory;
    }
    public void setGoodsStockNum(Long goodsStockNum) 
    {
        this.goodsStockNum = goodsStockNum;
    }

    public Long getGoodsStockNum() 
    {
        return goodsStockNum;
    }
    public void setMaxSalePrice(BigDecimal maxSalePrice) 
    {
        this.maxSalePrice = maxSalePrice;
    }

    public BigDecimal getMaxSalePrice() 
    {
        return maxSalePrice;
    }
    public void setMinSalePrice(BigDecimal minSalePrice) 
    {
        this.minSalePrice = minSalePrice;
    }

    public BigDecimal getMinSalePrice() 
    {
        return minSalePrice;
    }
    public void setRealSaleNum(Long realSaleNum) 
    {
        this.realSaleNum = realSaleNum;
    }

    public Long getRealSaleNum() 
    {
        return realSaleNum;
    }
    public void setSoldType(Long soldType) 
    {
        this.soldType = soldType;
    }

    public Long getSoldType() 
    {
        return soldType;
    }
    public void setIsMultiSku(Long isMultiSku) 
    {
        this.isMultiSku = isMultiSku;
    }

    public Long getIsMultiSku() 
    {
        return isMultiSku;
    }
    public void setIsOnline(Long isOnline) 
    {
        this.isOnline = isOnline;
    }

    public Long getIsOnline() 
    {
        return isOnline;
    }
    public void setIsCanSell(Long isCanSell) 
    {
        this.isCanSell = isCanSell;
    }

    public Long getIsCanSell() 
    {
        return isCanSell;
    }
    public void setSort(Long sort) 
    {
        this.sort = sort;
    }

    public Long getSort() 
    {
        return sort;
    }
    public void setOnlineTime(Date onlineTime) 
    {
        this.onlineTime = onlineTime;
    }

    public Date getOnlineTime() 
    {
        return onlineTime;
    }
    public void setDetailInfo(String detailInfo) 
    {
        this.detailInfo = detailInfo;
    }

    public String getDetailInfo() 
    {
        return detailInfo;
    }
    public void setDeleteFlag(Long deleteFlag) 
    {
        this.deleteFlag = deleteFlag;
    }

    public Long getDeleteFlag() 
    {
        return deleteFlag;
    }
    public void setSkuList(String skuList) 
    {
        this.skuList = skuList;
    }

    public String getSkuList() 
    {
        return skuList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("subTitle", getSubTitle())
            .append("defaultImageUrl", getDefaultImageUrl())
            .append("outerGoodsCode", getOuterGoodsCode())
            .append("category", getCategory())
            .append("subCategory", getSubCategory())
            .append("goodsStockNum", getGoodsStockNum())
            .append("maxSalePrice", getMaxSalePrice())
            .append("minSalePrice", getMinSalePrice())
            .append("realSaleNum", getRealSaleNum())
            .append("soldType", getSoldType())
            .append("isMultiSku", getIsMultiSku())
            .append("isOnline", getIsOnline())
            .append("isCanSell", getIsCanSell())
            .append("sort", getSort())
            .append("onlineTime", getOnlineTime())
            .append("detailInfo", getDetailInfo())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("deleteFlag", getDeleteFlag())
            .append("skuList", getSkuList())
            .toString();
    }
}
