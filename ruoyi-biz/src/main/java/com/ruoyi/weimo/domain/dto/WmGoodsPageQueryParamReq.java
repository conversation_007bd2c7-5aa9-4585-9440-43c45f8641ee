package com.ruoyi.weimo.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 新增商品表
 *
 * <AUTHOR> yuanjiangping
 * @since : 2023-10-09 15:33
 */
@Data
public class WmGoodsPageQueryParamReq {

    /**
     * 商品状态
     */
    private Integer goodsStatus;

    /**
     * 商品品类
     */
    private Long classifyId;

    /**
     * 商品的搜索内容
     */
    private String search;

    /**
     * 商品查询结果的排序方式
     */
    private Integer sort;

    /**
     * 商品内容的搜索类型，与 search 字段搭配使用。当不传时默认为 1。
     */
    private Integer searchType;

    /**
     * 待查询商品的最小销售价
     */
    private Double minSalePrice;

    /**
     * 待查询商品的最大销售价
     */
    private Double maxSalePrice;

    /**
     * 搜索匹配类型
     */
    private Integer searchOptionType;


    /**
     * 待查询商品的标签
     */
    private List<Long> goodsTagIdList;

    /**
     * 待查询商品的更新结束时间
     */
    private Long startUpdateTime;

    /**
     * 待查询商品的更新起始时间
     */
    private Long endUpdateTime;

    /**
     * 排序类型，与 sort 字段搭配使用。
     */
    private Integer sortType;
}
