package com.ruoyi.weimo.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 微盟活动规则类
 * <AUTHOR>
 * @date 2024/7/25
 */
@Data
public class WmActivityRule {

    /**
     * 优惠条件类型。支持的类型包括：102-满元；103-满件。
     */
    private Long conditionType;

    /** 订单级规则和赠品信息 */
    private List<WmFullDiscountActivity.ResultPolymerBizVO> resultPolymerBizVOList;

    /** 人群选择类型，支持的类型通过：1-全部人群；2-部分人群。 */
    private int selectPeopleType;

    /** 人群选择ID */
    private long selectPeopleRuleId;

    /**
     * 关联类型：101-全部商品；102-部分商品；103-部分类目；104-部分分组；105-部分场所(商品）。
     */
    private String refType;

    /**
     * 关联ID
     */
    private String refId;

    /** 下单可用抵扣，支持的类型包括：25-储值卡；33-兑换卡；23-积分；24-余额。 */
    private List<Integer> availableDeduct;

    /** 下单可用优惠，支持的类型包括：18-会员权益价；12-限时折扣；14-限量抢购；19-特权价；26-直播价；21-拼团；5-优惠券/码；11-满减邮。 */
    private List<Integer> availablePromotion;

    /**
     * 限购类型设置 支持的类型包括： -1-不限购 ；0-超过限购不可原价购买 ；1-超过限购可原价购买。
     */
    private int overLimitCanOriginPriceBuy;
    /** 限购数量 */
    private int eachLimitNum;

    /** 关单时间 */
    private int orderCloseTime;

    /** 适用场景 适用场景，支持的类型包括：1-线上订单；2-线下开单。 */
    private List<Integer> tradeScene;

    /** 预热类型 0-不预热；1-立即预热；2、指定时间预热*/
    private Integer prepareType;

    /** 活动预热时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date prepareTime;
}
