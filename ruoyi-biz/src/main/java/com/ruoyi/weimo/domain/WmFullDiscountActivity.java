package com.ruoyi.weimo.domain;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 满减满折活动详情
 * <AUTHOR>
 * @date 2024/7/25
 */
@Data
@NoArgsConstructor
public class WmFullDiscountActivity {

    /** 专题页类型。支持的类型包括：1-系统默认；2-自定义。 */
    private int topicStyleType;

    /** 活动图片，仅 topicStyleType 为自定义时返回。 */
    private String promotionImage;

    /** 活动描述 */
    private String promotionDescription;

    /** 活动标签 ID */
    private long promotionTag;

    /** 活动标签名称 */
    private String promotionTagName;

    /** 是否展示划线价 */
    private boolean showLinePrice;

    /** 是否进行手机验证 */
    private int verifyPhoneNumber;

    /** 优惠条件类型。支持的类型包括：102-满元；103-满件。 */
    private int conditionType;

    /** 计算类型。支持的类型包括：1-最高层级享受；2-单层级多次享受。 */
    private int calculateType;

    /** 订单级规则和赠品信息 */
    private List<ResultPolymerBizVO> resultPolymerBizVOList;


    /** 规则商品限购 */
    private ResultLimitInfo resultLimitInfo;


    /** SKU 信息列表 */
    private List<PromotionSkuBizVO> promotionSkuBizVOList;


    /** 限购数量（个人）（仅限购时返回） */
    private int eachLimitNumSku;

    /** 活动 ID */
    private long activityId;

    /** 活动类型，支持的类型包括：1-满减满折；3-限时折扣；12-N元N件；13-优惠套装；14-加价购；32-满赠；37-x件x折。 */
    private int activityType;

    /** 活动名称 */
    private String promotionName;

    /** 开始时间，格式为时间戳，单位精确到毫秒。 */
    private long startTime;

    /** 结束时间，格式为时间戳，单位精确到毫秒。 */
    private long endTime;

    /** 是否同步打标，支持的类型包括：1-否；2-是。 */
    private int markingType;

    /** 标签 ID */
    private long tagRuleId;

    /** 人群选择类型，支持的类型通过：1-全部人群；2-部分人群。 */
    private int selectPeopleType;

    /** 人群选择ID */
    private long selectPeopleRuleId;

    /** 选择类型，支持的类型包括：101-全部商品；102-部分商品；103-部分类目；104-部分分组；105-部分场所(商品）。 */
    private int applicationGoodsType;

    /** 选择类型，支持的类型包括：0-正选；1-反选。 */
    private int checkType;

    /** 指定商品/类目/分组 ID 列表 */
    private List<Long> subIds;

    /** 适用门店类型，支持的类型包括：201-全部门店；202-指定区域；203: */
    private int selectStoreType;

    /** 门店/区域 ID 列表。适用门店类型为 202/203 时会返回。 */
    private List<Long> selectStoreIds;

    /** 适用场景，支持的类型包括：1-线上订单；2-线下开单(若只⼊参了该参数，则默认映射为「app开单」)；3-app开单；4-扫码购。多个类型之间用逗号隔开。 */
    private List<Integer> tradeScene;

    /** 下单可用抵扣，支持的类型包括：25-储值卡；33-兑换卡；23-积分；24-余额。 */
    private List<Integer> availableDeduct;

    /** 下单可用优惠，支持的类型包括：18-会员权益价；12-限时折扣；14-限量抢购；19-特权价；26-直播价；21-拼团；5-优惠券/码；11-满减邮。 */
    private List<Integer> availablePromotion;

    /** 专题页分享图片 URL 地址 */
    private String promotionShareImage;

    /** 反选门店 ID 集合，即当前活动不适用的门店 ID 集合。 */
    private List<Long> excludeStoreIds;

    /** 不包括的商品id */
    private List<Long> excludeSubIds;

    /**
     * 订单级规则和赠品信息的内部类
     */
    @Data
    @NoArgsConstructor
    public static class ResultPolymerBizVO {
        /** 优惠内容对应的类型。支持的类型包括；1001-减钱；1002-打折；1003-一口价；1004-减免数量；1005-赠送数量；1006-膨胀倍数；2001-包邮；2002-减邮；3001-规则商品；3002-优惠券；3003-积分；3004-余额。 */
        private int resultType;

        /** 结果值 */
        private double resultValue;

        /** 条件运算符，支持的类型包括：1-大于等于；2-小于等于 ；3-大于；4-小于；5-等于。 */
        private int conditionOperator;

        /** 优惠门槛 */
        private double conditionValue;

        /** 最大优惠金额 */
        private double maxAmount;

        /** 单品类型，支持的类型包括：1-商品级；2-SKU级。 */
        private int itemType;

        /** 单品 ID */
        private long itemId;

        /** 限制区域类型，支持的类型包括：0-不限制区域；1-限制区域。 */
        private int limitDistrictType;

        /** 配送区域地址 JSON */
        private String district;

        private ResultLimitInfo resultLimitInfo;

        private List<PromotionSkuBizVO> promotionSkuBizVOList;
    }

    /**
     * 规则商品限购的内部类
     */
    @Data
    @NoArgsConstructor
    public static class ResultLimitInfo {

        /** 节点唯一标识 */
        private String nodeId;

        /** 节点路径 */
        private String path;

        /** 当前节点业务 ID */
        private long limitBizId;

        /** 当前节点业务类型（标识 limitBizId 的类型）。支持的类型包括：1-activity；2-goods；3-sku；4-level。 */
        private int limitBizType;

        /** 当前节点具体的限购对象类型。支持的类型包括：1-activity；2-goods；3-sku；4-level。 */
        private int limitType;

        /** 限购数量（个人） */
        private int eachLimitNum;

        /** 限购数量（个人） */
        private int totalLimitNum;

        /** eachLimitNum 已售数量 */
        private int eachSoldNum;

        /** totalLimitNum 已售数量 */
        private int totalSoldNum;

        /** 用户可购买数量。Min(eachLimitNum-soldNum,totalLimitNum-totalSoldNum)。 */
        private int canBuyNum;

    }

    /**
     * SKU 信息列表的内部类
     */
    @Data
    @NoArgsConstructor
    public static class PromotionSkuBizVO {
        /** 商品 ID */
        private long goodsId;

        /** SKU ID，全局唯一。 */
        private long skuId;

        /** 活动价 */
        private double activityPrice;

        /** 可换购数量 */
        private int canBuyNumSku;
    }
}
