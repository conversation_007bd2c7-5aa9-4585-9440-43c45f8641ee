package com.ruoyi.weimo.domain.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/22
 */
@Data
@NoArgsConstructor
public class ActivityPageQueryReq {

    private Integer pageNum;

    private Integer pageSize;

    private Long activityId;

    private queryParameter queryParameter;

    private basicInfo basicInfo;

    @Data
    @NoArgsConstructor
    public class queryParameter{
        /**
         * 活动类型
         */
        private Integer activityType;

        /**
         * 活动子类型
         */
        private Integer activitySubType;

        /**
         * 活动状态
         */
        private List<Integer> status;

        /**
         * 开始时间
         */
        private Long startTime;

        /**
         * 结束时间
         */
        private Long endTime;

        /**
         * 活动名称
         */
        private String promotionName;
    }

    @Data
    @NoArgsConstructor
    public class basicInfo{
        /**
         * vid
         */
        private Long vid;

        /**
         * vid类型
         */
        private Integer vidType;
    }


}
