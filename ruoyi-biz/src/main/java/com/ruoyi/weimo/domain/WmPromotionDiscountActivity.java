package com.ruoyi.weimo.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 限时折扣活动详情
 *
 * <AUTHOR>
 * @date 2024/7/25
 */
@Data
@NoArgsConstructor
public class WmPromotionDiscountActivity {
    private int timeType; // 活动时间类型
    private List<CycleTime> cycleTimeList; //周期时间列表
    private int repeatType; // 周期类型
    private String repeatDay; // 具体到哪一天
    private String repeatStartInterval; // 时效开始时间
    private String repeatEndInterval; // 时效结束时间
    private int overLimitCanOriginPriceBuy; // 限购类型设置
    private int eachLimitNum; // 限购数量
    private int orderCloseTime; // 关单时间
    private List<Long> scopeGoodsIdList; // 适用商品 ID 列表
    private int prepareType; // 预热类型
    private long prepareTime; // 预热时间
    private int isPreheatCanOriginPriceBuy; // 预热是否可原价购买
    private long promotionTag; // 活动标签 ID
    private String promotionTagName; // 活动标签名称
    private String promotionDescription; // 活动描述
    private String promotionImage; // 活动图片
    private int promotionStatus; // 活动状态
    private long activityId; // 活动 ID
    private int activityType; // 活动类型
    private String promotionName; // 活动名称
    private long startTime; // 开始时间
    private long endTime; // 结束时间
    private int markingType; // 是否同步打标
    private long tagRuleId; // 标签 ID
    private int selectPeopleType; // 人群选择类型
    private long selectPeopleRuleId; // 人群选择 ID
    private int applicationGoodsType; // 活动商品类型
    private int checkType; // 是否反选
    private List<Long> subIds; // 指定商品/类目/分组 ID 列表
    private int selectStoreType; // 适用门店类型
    private List<Long> selectStoreIds; // 门店/区域 ID 集合
    private List<Integer> tradeScene; // 适用场景
    private List<Integer> availableDeduct; // 下单可用抵扣类型
    private List<Integer> availablePromotion; // 下单可用优惠
    private List<Long> excludeStoreIds; // 反选门店 ID 集合

    @Data
    @NoArgsConstructor
    static class CycleTime{
        // 周期类型，支持的类型包括：1-每天周期；2-每周周期；3-每月周期；4-指定日期；5-每年周期。
        private long repeatType;
        // 具体到哪一天。
        // repeatType 为1：不需要设置 repeatDay 值；
        // repeatType 为 2：1-7（每周一至七）；
        // repeatType为 3：1-31（每月 1-31 天( 0：月末)）；
        // repeatType 为4：指定日期:存储日期（2021-09-22）；
        // repeatType 为 5：每年的具体某个时间段（比如：0201-0315，每年的 2 月 1 号到 3 月 15 号）。
        private String repeatDay;

        //时效开始时间，格式为日期字符串："HH:mm:ss"。
        @JsonFormat(pattern = "HH:mm:ss")
        private String repeatStartInterval;

        //时效结束时间，格式为日期字符串："HH:mm:ss"。
        @JsonFormat(pattern = "HH:mm:ss")
        private String repeatEndInterval;
    }
}
