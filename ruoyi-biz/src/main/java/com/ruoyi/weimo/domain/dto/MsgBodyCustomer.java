package com.ruoyi.weimo.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 微盟消息, 用户消息体
 */
@Data
public class MsgBodyCustomer {

    /**
     * 成为客户的场景类型。可能出现的场景类型包括：1-普通创建身份；2-开放平台导入创建身份。
     */
    private Integer membershipCreateSceneType;

    /**
     * 会员方案ID
     */
    private Long membershipPlanId;

    /**
     * 会员方案类型。可能出现的方案类型包括：1-会员；2-客户；3-粉丝。
     */
    private Integer membershipType;

    /**
     * 客户编号列表。客户编号是用户身份的唯一标识。可以通过B端页面客户列表/客户详情找到该编号。
     */
    private List<Long> widList;
}
