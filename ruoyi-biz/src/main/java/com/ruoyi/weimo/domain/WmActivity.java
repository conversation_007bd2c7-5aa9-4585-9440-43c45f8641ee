package com.ruoyi.weimo.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 微盟行为对象 wm_activity
 * 
 * <AUTHOR>
 * @date 2024-03-22
 */
public class WmActivity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 产品实例ID */
    @Excel(name = "产品实例ID")
    private String targetProductInstanceId;

    /** 停留时长(秒) */
    @Excel(name = "停留时长(秒)")
    private String visitDuration;

    /** 设备或者应用匿名用户 ID，标识对应设备身份。 */
    @Excel(name = "设备或者应用匿名用户 ID，标识对应设备身份。")
    private String cuid;

    /** 微信unionId */
    @Excel(name = "微信unionId")
    private String unionid;

    /** 来源渠道 */
    @Excel(name = "来源渠道")
    private String sourceEnd;

    /** 微信openId */
    @Excel(name = "微信openId")
    private String openId;

    /** 用户 IP */
    @Excel(name = "用户 IP")
    private String ip;

    /** 会话 ID */
    @Excel(name = "会话 ID")
    private String sessionId;

    /** 访问页面 */
    @Excel(name = "访问页面")
    private String pageName;

    /** 页面URL */
    @Excel(name = "页面URL")
    private String url;

    /** 小程序场景 */
    @Excel(name = "小程序场景")
    private String scene;

    /** 组织ID */
    @Excel(name = "组织ID")
    private String vid;

    /** 产品ID */
    @Excel(name = "产品ID")
    private String targetProductId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String wid;

    /** 行为详情 */
    @Excel(name = "行为详情")
    private String jsonData;

    /** 事件来源 */
    @Excel(name = "事件来源")
    private Long etSource;

    /** 商铺ID */
    @Excel(name = "商铺ID")
    private String bosId;

    /** 数据来源 */
    @Excel(name = "数据来源")
    private String dataSource;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTargetProductInstanceId(String targetProductInstanceId) 
    {
        this.targetProductInstanceId = targetProductInstanceId;
    }

    public String getTargetProductInstanceId() 
    {
        return targetProductInstanceId;
    }
    public void setVisitDuration(String visitDuration) 
    {
        this.visitDuration = visitDuration;
    }

    public String getVisitDuration() 
    {
        return visitDuration;
    }
    public void setCuid(String cuid) 
    {
        this.cuid = cuid;
    }

    public String getCuid() 
    {
        return cuid;
    }
    public void setUnionid(String unionid) 
    {
        this.unionid = unionid;
    }

    public String getUnionid() 
    {
        return unionid;
    }
    public void setSourceEnd(String sourceEnd) 
    {
        this.sourceEnd = sourceEnd;
    }

    public String getSourceEnd() 
    {
        return sourceEnd;
    }
    public void setOpenId(String openId) 
    {
        this.openId = openId;
    }

    public String getOpenId() 
    {
        return openId;
    }
    public void setIp(String ip) 
    {
        this.ip = ip;
    }

    public String getIp() 
    {
        return ip;
    }
    public void setSessionId(String sessionId) 
    {
        this.sessionId = sessionId;
    }

    public String getSessionId() 
    {
        return sessionId;
    }
    public void setPageName(String pageName) 
    {
        this.pageName = pageName;
    }

    public String getPageName() 
    {
        return pageName;
    }
    public void setUrl(String url) 
    {
        this.url = url;
    }

    public String getUrl() 
    {
        return url;
    }
    public void setScene(String scene) 
    {
        this.scene = scene;
    }

    public String getScene() 
    {
        return scene;
    }
    public void setVid(String vid) 
    {
        this.vid = vid;
    }

    public String getVid() 
    {
        return vid;
    }
    public void setTargetProductId(String targetProductId) 
    {
        this.targetProductId = targetProductId;
    }

    public String getTargetProductId() 
    {
        return targetProductId;
    }
    public void setWid(String wid) 
    {
        this.wid = wid;
    }

    public String getWid() 
    {
        return wid;
    }
    public void setJsonData(String jsonData) 
    {
        this.jsonData = jsonData;
    }

    public String getJsonData() 
    {
        return jsonData;
    }
    public void setEtSource(Long etSource) 
    {
        this.etSource = etSource;
    }

    public Long getEtSource() 
    {
        return etSource;
    }
    public void setBosId(String bosId) 
    {
        this.bosId = bosId;
    }

    public String getBosId() 
    {
        return bosId;
    }
    public void setDataSource(String dataSource) 
    {
        this.dataSource = dataSource;
    }

    public String getDataSource() 
    {
        return dataSource;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("targetProductInstanceId", getTargetProductInstanceId())
            .append("visitDuration", getVisitDuration())
            .append("cuid", getCuid())
            .append("unionid", getUnionid())
            .append("sourceEnd", getSourceEnd())
            .append("openId", getOpenId())
            .append("ip", getIp())
            .append("sessionId", getSessionId())
            .append("pageName", getPageName())
            .append("url", getUrl())
            .append("scene", getScene())
            .append("vid", getVid())
            .append("targetProductId", getTargetProductId())
            .append("wid", getWid())
            .append("jsonData", getJsonData())
            .append("etSource", getEtSource())
            .append("createTime", getCreateTime())
            .append("bosId", getBosId())
            .append("dataSource", getDataSource())
            .toString();
    }
}
