package com.ruoyi.weimo.domain.vo;

import lombok.Data;

/**
 * <AUTHOR> yuanjiangping
 * @since : 2023-10-09 15:33
 */
@Data
public class WmGoodsPageRes {

    /**
     *
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 商品副标题
     */
    private String subTitle;

    /**
     * 创建商品组织ID
     */
    private String createVid;

    /**
     * 商品主图Url
     */
    private String defaultImageUrl;

    /**
     * 外部商品Code
     */
    private String outerGoodsCode;

    /**
     * 商品可售总库存
     */
    private WmGoodsStockRes goodsStock;

    /**
     * 最大销售价格
     */
    private WmGoodsPriceRes goodsPrice;

    /**
     * 商品总销量
     */
    private Long realSaleNum;

    /**
     * 商品销售类型 1-现货 2-预售 3-抽签售
     */
    private Long soldType;

    /**
     * 一级商品品类
     */
    private Integer goodsType;

    /**
     * 二级商品品类
     */
    private Integer subGoodsType;

    /**
     * 是否是多规格 true-多规格；false-单规格。
     */
    private Boolean isMultiSku;

    /**
     * 上架状态 true-上架；false-下架。
     */
    private Boolean isOnline;

    /**
     * 商品是否可售 true-可售；false-不可售。
     */
    private Boolean isCanSell;

    /**
     * 商品序号
     */
    private Long sort;

    /**
     * 上架时间
     */
    private Long onlineTime;

}
