package com.ruoyi.weimo.domain.dto;

import lombok.Data;

/**
 * 消息对象类
 */
@Data
public class WmMessage<T> {
    /**
     * 消息的唯一标识
     */
    private String id;

    /**
     * 消息主题名称
     */
    private String topic;

    /**
     * 消息事件名称
     */
    private String event;

    /**
     * 用户 ID，是微盟用户身份的唯一标识
     */
    private String wid;

    /**
     * 微盟商业操作系统 ID
     */
    private Long bosId;

    /**
     * 防篡改签名：md5(clientId+id+msgBody+clientSecret)
     */
    private String sign;

    /**
     * 消息内容
     */
    private T msgBody;

    /**
     * 版本号
     */
    private Integer version;

}

