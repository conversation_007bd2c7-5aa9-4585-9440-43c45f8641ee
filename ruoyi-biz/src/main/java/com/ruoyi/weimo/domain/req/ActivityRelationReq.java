package com.ruoyi.weimo.domain.req;

import com.ruoyi.goods.domain.req.SalesPitchVo;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/26
 */
@Data
public class ActivityRelationReq {
    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 活动话术列表。
     */
    @NotNull(message = "活动话术必填")
    private List<SalesPitchVo> salesPitchList;
}
