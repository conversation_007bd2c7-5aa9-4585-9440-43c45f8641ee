package com.ruoyi.weimo.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户管理对象 wm_user
 * 
 * <AUTHOR>
 * @date 2024-03-11
 */
public class WmUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long wid;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nickname;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 头像 */
    @Excel(name = "头像")
    private String headUrl;

    /** unionId */
    @Excel(name = "unionId")
    private String unionId;

    public void setWid(Long wid) 
    {
        this.wid = wid;
    }

    public Long getWid() 
    {
        return wid;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setNickname(String nickname) 
    {
        this.nickname = nickname;
    }

    public String getNickname() 
    {
        return nickname;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setHeadUrl(String headUrl) 
    {
        this.headUrl = headUrl;
    }

    public String getHeadUrl() 
    {
        return headUrl;
    }
    public void setUnionId(String unionId) 
    {
        this.unionId = unionId;
    }

    public String getUnionId() 
    {
        return unionId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("wid", getWid())
            .append("name", getName())
            .append("nickname", getNickname())
            .append("phone", getPhone())
            .append("headUrl", getHeadUrl())
            .append("unionId", getUnionId())
            .toString();
    }
}
