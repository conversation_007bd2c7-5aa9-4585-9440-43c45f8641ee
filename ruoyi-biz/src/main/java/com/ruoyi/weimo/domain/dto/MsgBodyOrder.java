package com.ruoyi.weimo.domain.dto;

import lombok.Data;

/**
 * 订单消息内容类
 */
@Data
public class MsgBodyOrder {
    /**
     * 订单类型
     * 1-普通订单
     * 96-虚拟订单
     * 97-消费订单
     * 11-全渠道B2C订单
     */
    private Integer orderType;

    /**
     * 订单来源
     * 0-系统内部订单
     * 100-历史导入订单
     * 101-友朋
     * 102-芸智
     * 103-芸智
     * 104-博申
     * 201-分销市场供货商
     * 401-外部订单
     */
    private Integer orderSource;

    /**
     * 订单编号
     */
    private Long orderNo;

    /**
     * 产品 ID
     */
    private Long productId;

    /**
     * 交付类型
     * 1-商家配送
     * 2-同城限时达
     * 3-到店交易
     * 4-门店交易
     * 5-无需物流
     */
    private Integer deliveryType;

    /**
     * 产品实例 Id
     */
    private Long productInstanceId;

    /**
     * 真实订单状态
     * 0-创建
     * 1-部分支付
     * 2-已支付
     * 3-待发货
     * 4-部分发货
     * 5-已发货
     * 7-确认收货（后台显示已完成）
     * 8-完成（即订单已确认收货，且过了售后期）
     * 9-取消
     */
    private Integer orderStatus;

    /**
     * 渠道来源
     * 0-公众号
     * 1-小程序
     * 2-H5
     * 3-QQ
     * 4-微博
     * 5-字节跳动小程序
     * 6-支付宝小程序
     * 7-PC后台
     * 8-安卓app
     * 9-苹果app
     * 10-百度智能小程序
     * 11-PAD
     * 12-自有APP
     * 13-微信小程序webview
     * 14-微信小程序webview-直播
     * 15-大屏扫码
     * 16-企业微信
     * 17-抖音原生小程序
     * 18-芸智免密
     * 19-QQ原生小程序
     * 20-QQ小程序H5页面
     * 21-支付宝原生小程序
     * 22-微信小商店
     * 23-快手原生小程序
     * 24-快手小程序H5页面
     * 101-分销商
     */
    private Integer channelType;

    /**
     * 更新时间，时间戳，毫秒级
     */
    private Long updateTime;

    /**
     * 父单号
     */
    private Long parentOrderNo;

    /**
     * 销售渠道
     * 10001-线上网店
     * 10002-线下门店
     */
    private Integer saleChannelType;

    /**
     * 支付类型
     * 1-线上支付
     * 2-线下支付
     */
    private Integer paymentType;

    /**
     * 销售组织架构节点 ID
     */
    private Long vid;
    /**
     * 微盟id
     */
    private Long wid;

    /**
     * 创建时间，时间戳，毫秒级
     */
    private Long createTime;

    /**
     * 商家 ID
     */
    private Long merchantId;

    /**
     * 订单业务来源
     * 1-普通订单
     * 2-商户助手APP
     * 3-收银台
     * 5-普通货柜
     * 6-免密货柜
     * 7-扫码购
     * 10-换货订单
     * 12-先试后买
     */
    private Integer bizSourceType;

    /**
     * 支付状态
     * 0-未支付
     * 1-部分支付
     * 2-已支付
     */
    private Integer paymentStatus;
}
