package com.ruoyi.weimo.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ruoyi.common.utils.CustomerStrToJsonArraySerialize;
import com.ruoyi.common.utils.CustomerStrToJsonObjectSerialize;
import com.ruoyi.goods.domain.vo.ActivityRefVo;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 活动管理对象 wm_activities
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
public class WmActivities extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 活动 ID
     */
    @Excel(name = "活动 ID")
    private Long activityId;

    /**
     * 活动类型
     */
    private Long activityType;

    /**
     * 活动子类型
     */
    private Long activitySubType;

    /**
     * 活动名称
     */
    @Excel(name = "活动名称")
    private String promotionName;

    /**
     * 活动时间类型，支持的类型包括：0-固定时间；1-周期时间。
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Long timeType;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 统计日期，格式为：yy-mm-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date day;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 周期时间列表 json
     */
    @JsonSerialize(using = CustomerStrToJsonArraySerialize.class)
    private String cycleTimeList;

    /**
     * 活动状态
     */
    @Excel(name = "活动状态")
    private Long promotionStatus;

    /**
     * 活动图片
     */
    @Excel(name = "活动图片")
    private String promotionImage;

    /**
     * 是否长期有效
     */
    @Excel(name = "是否长期有效")
    private Long isPromotionLongTime;

    /**
     * 来源渠道，0:手动创建，1:微盟
     */
    @Excel(name = "来源渠道，0:手动创建，1:微盟")
    private String source;

    /**
     * 关键词
     */
    @Excel(name = "关键词")
    private String keywords;

    /**
     * 是否开启AI 导购推荐: 0-否, 1-是
     */
    @Excel(name = "是否开启AI 导购推荐: 0-否, 1-是")
    private Integer isAiPurchaseGuide;

    /**
     * 关联类型：101-全部商品；102-部分商品；103-部分类目；104-部分分组；105-部分场所(商品）。
     */
    private String refType;

    /**
     * 关联ID
     */
    private String refId;

    /**
     * 活动描述
     */
    private String promotionDescription;

    /**
     * 活动对应规则
     */
    @JsonSerialize(using = CustomerStrToJsonObjectSerialize.class)
    private String rule;

    /**
     * 活动详情
     */
    @JsonSerialize(using = CustomerStrToJsonObjectSerialize.class)
    private String details;


    /**
     * 关联商品/类目
     */
    private ActivityRefVo activityRef;

    /**
     * 语料库ID
     */
    private Long corpusDocumentId;

    /**
     * 朋友圈语料库ID
     */
    private Long momentCorpusDocumentId;

    public String getPromotionDescription() {
        return promotionDescription;
    }

    public void setPromotionDescription(String promotionDescription) {
        this.promotionDescription = promotionDescription;
    }

    public Long getCorpusDocumentId() {
        return corpusDocumentId;
    }

    public void setCorpusDocumentId(Long corpusDocumentId) {
        this.corpusDocumentId = corpusDocumentId;
    }

    public Long getMomentCorpusDocumentId() {
        return momentCorpusDocumentId;
    }

    public void setMomentCorpusDocumentId(Long momentCorpusDocumentId) {
        this.momentCorpusDocumentId = momentCorpusDocumentId;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityType(Long activityType) {
        this.activityType = activityType;
    }

    public Long getActivityType() {
        return activityType;
    }

    public void setActivitySubType(Long activitySubType) {
        this.activitySubType = activitySubType;
    }

    public Long getActivitySubType() {
        return activitySubType;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    public String getPromotionName() {
        return promotionName;
    }

    public void setTimeType(Long timeType) {
        this.timeType = timeType;
    }

    public Long getTimeType() {
        return timeType;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setDay(Date day) {
        this.day = day;
    }

    public Date getDay() {
        return day;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setCycleTimeList(String cycleTimeList) {
        this.cycleTimeList = cycleTimeList;
    }

    public String getCycleTimeList() {
        return cycleTimeList;
    }

    public void setPromotionStatus(Long promotionStatus) {
        this.promotionStatus = promotionStatus;
    }

    public Long getPromotionStatus() {
        return promotionStatus;
    }

    public void setPromotionImage(String promotionImage) {
        this.promotionImage = promotionImage;
    }

    public String getPromotionImage() {
        return promotionImage;
    }

    public void setIsPromotionLongTime(Long isPromotionLongTime) {
        this.isPromotionLongTime = isPromotionLongTime;
    }

    public Long getIsPromotionLongTime() {
        return isPromotionLongTime;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return source;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setRefType(String refType) {
        this.refType = refType;
    }

    public String getRefType() {
        return refType;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public String getRefId() {
        return refId;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }

    public String getRule() {
        return rule;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public String getDetails() {
        return details;
    }

    public ActivityRefVo getActivityRef() {
        return activityRef;
    }

    public void setActivityRef(ActivityRefVo activityRef) {
        this.activityRef = activityRef;
    }

    public Integer getIsAiPurchaseGuide() {
        return isAiPurchaseGuide;
    }

    public void setIsAiPurchaseGuide(Integer isAiPurchaseGuide) {
        this.isAiPurchaseGuide = isAiPurchaseGuide;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("activityId", getActivityId())
                .append("activityType", getActivityType())
                .append("activitySubType", getActivitySubType())
                .append("promotionName", getPromotionName())
                .append("timeType", getTimeType())
                .append("startTime", getStartTime())
                .append("day", getDay())
                .append("endTime", getEndTime())
                .append("cycleTimeList", getCycleTimeList())
                .append("promotionStatus", getPromotionStatus())
                .append("promotionImage", getPromotionImage())
                .append("isPromotionLongTime", getIsPromotionLongTime())
                .append("source", getSource())
                .append("keywords", getKeywords())
                .append("isAiPurchaseGuide", getIsAiPurchaseGuide())
                .append("refType", getRefType())
                .append("refId", getRefId())
                .append("promotionDescription", getPromotionDescription())
                .append("rule", getRule())
                .append("details", getDetails())
                .append("activityRef", getActivityRef())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
