package com.ruoyi.weimo.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 微盟售后管理对象 wm_rights
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WmRights extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 售后单号 */
    private Long rightsId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private Long orderNo;

    /** 销售组织架构节点 ID */
    @Excel(name = "销售组织架构节点 ID")
    private Long vid;

    /** 服务组织架构节点 ID */
    @Excel(name = "服务组织架构节点 ID")
    private Long processVid;

    /** 售后状态: 1-买家发起售后；2-等待买家退货；3-买家已退货；5-系统退款中；6-售后完成；7-买家已取消；8-商家已拒绝；9-退款失败；10-商家退款中；20-换货中；30-商家拒绝_待平台介入；31-待平台介入_仲裁中 */
    @Excel(name = "售后状态: 1-买家发起售后；2-等待买家退货；3-买家已退货；5-系统退款中；6-售后完成；7-买家已取消；8-商家已拒绝；9-退款失败；10-商家退款中；20-换货中；30-商家拒绝_待平台介入；31-待平台介入_仲裁中")
    private Integer rightsStatus;

    /** 售后类型: 1-退货退款；2-退款；5-退换货 */
    @Excel(name = "售后类型: 1-退货退款；2-退款；5-退换货")
    private Integer rightsType;

    /** 售后来源: 0-系统内部订单；100-历史导入订单；101-友朋；102-芸智；103-乐美；104-博申；201-分销市场供货商；401-全渠道导入订单 */
    @Excel(name = "售后来源: 0-系统内部订单；100-历史导入订单；101-友朋；102-芸智；103-乐美；104-博申；201-分销市场供货商；401-全渠道导入订单")
    private Integer rightsSource;

    /** 售后方式: 1-买家申请售后；2-商家取消订单；5-收银台退款；6-外部平台商家售后；7-优惠券到期自动退款；8-系统自动售后；9-商家发起售后；10-付费券发券失败发起售后；20-买家取消订单 */
    @Excel(name = "售后方式: 1-买家申请售后；2-商家取消订单；5-收银台退款；6-外部平台商家售后；7-优惠券到期自动退款；8-系统自动售后；9-商家发起售后；10-付费券发券失败发起售后；20-买家取消订单")
    private Integer rightsCauseType;

    /** 退款类型: 1-线上退款；2-线下退款；99-无需退款 */
    @Excel(name = "退款类型: 1-线上退款；2-线下退款；99-无需退款")
    private Integer refundType;

    /** 币种: 1-人民币；2-法国法郎；3-港元；4-瑞士法郎；5-美元；6-加拿大元；7-英镑；8-荷兰盾；9-德国马克；10-比利时法郎；11-日元；12-澳大利亚元 */
    @Excel(name = "币种: 1-人民币；2-法国法郎；3-港元；4-瑞士法郎；5-美元；6-加拿大元；7-英镑；8-荷兰盾；9-德国马克；10-比利时法郎；11-日元；12-澳大利亚元")
    private String currency;

    /** 业务处理类型, 1-会员处理, 99-其他 */
    @Excel(name = "业务处理类型, 1-会员处理, 99-其他")
    private String bizProcessType;

    /** 处理状态, 0-未处理, 1-已处理 */
    @Excel(name = "处理状态, 0-未处理, 1-已处理")
    private String hasProcess;


}
