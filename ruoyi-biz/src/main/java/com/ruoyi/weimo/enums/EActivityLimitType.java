package com.ruoyi.weimo.enums;

import lombok.Getter;

/**
 * 限购类型设置，支持的类型包括： -1-不限购 ；0-超过限购不可原价购买 ；1-超过限购可原价购买。
 * <AUTHOR>
 * @date 2024/10/10
 */
@Getter
public enum EActivityLimitType {
    NO_LIMIT(-1, "不限购"),
    OVER_LIMIT_NO_ORIGINAL_PRICE(0, "超过限购不可原价购买"),
    OVER_LIMIT_CAN_ORIGINAL_PRICE(1, "超过限购可原价购买");

    private final Integer code;
    private final String desc;

    EActivityLimitType(Integer code, String desc)
    {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code)
    {
        for (EActivityLimitType item : EActivityLimitType.values())
        {
            if (item.getCode().equals(code))
            {
                return item.getDesc();
            }
        }
        return null;
    }
}
