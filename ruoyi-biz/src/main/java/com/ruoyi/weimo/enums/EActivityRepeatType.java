package com.ruoyi.weimo.enums;

import lombok.Getter;

/**
 * 活动周期类型 支持的类型包括：1-每天周期；2-每周周期；3-每月周期；4-指定日期；5-每年周期。
 * <AUTHOR>
 * @date 2024/10/10
 */
@Getter
public enum EActivityRepeatType {
    DAILY(1, "每天"),
    WEEKLY(2, "每周"),
    MONTHLY(3, "每月"),
    SPECIFIED_DATE(4, "指定日期"),
    YEARLY(5, "每年");

    private final Integer code;
    private final String desc;

    EActivityRepeatType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (EActivityRepeatType item : EActivityRepeatType.values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return null;
    }
}
