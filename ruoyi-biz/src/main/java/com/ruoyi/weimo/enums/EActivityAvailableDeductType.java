package com.ruoyi.weimo.enums;

import lombok.Getter;

/**
 * 下单可用抵扣类型，支持的类型包括：23-积分；24-余额；25-储蓄卡。
 * <AUTHOR>
 * @date 2024/10/10
 */
@Getter
public enum EActivityAvailableDeductType {
    POINT(23, "积分"),
    BALANCE(24, "余额"),
    CREDIT_CARD(25, "储蓄卡"),
    EXCHANGE_CARD(33, "兑换卡")
    ;

    private final Integer code;
    private final String desc;

    EActivityAvailableDeductType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (EActivityAvailableDeductType type : EActivityAvailableDeductType.values()) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }
}
