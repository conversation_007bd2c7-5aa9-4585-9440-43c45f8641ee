package com.ruoyi.weimo.enums;

import lombok.Getter;

/**
 * 活动预热类型
 *
 * <AUTHOR>
 * @date 2024/10/10
 */
@Getter
public enum EActivityPrepareType {
    NO_PREHEAT(0, "不预热"),
    IMMEDIATE_PREHEAT(1, "立即预热"),
    SPECIFIED_TIME_PREHEAT(2, "指定时间预热");

    private final Integer code;
    private final String desc;

    EActivityPrepareType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EActivityPrepareType getByCode(Integer code) {
        for (EActivityPrepareType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
