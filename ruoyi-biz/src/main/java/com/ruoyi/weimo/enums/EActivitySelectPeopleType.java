package com.ruoyi.weimo.enums;

import lombok.Getter;

/**
 * 人群选择类型，支持的类型包括：1-全部人群；2-部分人群。
 * <AUTHOR>
 * @date 2024/10/10
 */
@Getter
public enum EActivitySelectPeopleType {

    ALL(1, "全部人群"),

    PART(2, "部分人群");

    private final Integer code;
    private final String desc;

    EActivitySelectPeopleType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (EActivitySelectPeopleType value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }

}
