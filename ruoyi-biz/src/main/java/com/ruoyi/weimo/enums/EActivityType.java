package com.ruoyi.weimo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/24
 */
@Getter
public enum EActivityType {
    ACTIVITY_TYPE_1(1, "满减满折"),

    ACTIVITY_TYPE_3(3, "限时折扣");

    private final long code;
    private final String info;

    EActivityType(long code, String info) {
        this.code = code;
        this.info = info;
    }

    public static EActivityType getByCode(long code) {
        for (EActivityType e : values()) {
            if (e.getCode() == code) {
                return e;
            }
        }
        return null;
    }
}
