package com.ruoyi.weimo.enums;

import lombok.Getter;

/**
 * 活动来源枚举 0:手动创建；1:微盟
 * <AUTHOR>
 * @date 2024/7/25
 */
@Getter
public enum EActivitySource {
    MANUAL("0", "手动创建"),
    WEI_MO("1", "微盟");

    private final String code;
    private final String info;

    EActivitySource(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public static EActivitySource getByCode(String code) {
        for (EActivitySource e : EActivitySource.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("没有找到对应的枚举");
    }

}
