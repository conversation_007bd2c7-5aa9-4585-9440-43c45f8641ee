package com.ruoyi.weimo.enums;

import lombok.Getter;

/**
 * 下单可用优惠，支持的类型包括：1-满减满折；2-第X件X折；5-优惠券/码；9-订单满赠；10-订单换购；11-满减邮；15-定金膨胀；16-阶梯价；17-单品换购。
 * <AUTHOR>
 * @date 2024/10/10
 */
@Getter
public enum EActivityAvailablePromotionType {
    FULL_DISCOUNT(1, "满减满折"),
    FIRST_X_DISCOUNT(2, "第X件X折"),
    COUPON(5, "优惠券/码"),
    FULL_GIFT(9, "订单满赠"),
    ORDER_EXCHANGE(10, "订单换购"),
    FULL_POSTAGE(11, "满减邮"),
    LIMITED_TIME_DISCOUNT(12, "限时折扣"),
    LIMITED_QUANTITY_SALE(14, "限量抢购"),
    DEPOSIT_INFLATION(15, "定金膨胀"),
    STEP_PRICE(16, "阶梯价"),
    ORDER_EXCHANGE_NEW(17, "单品换购"),
    MEMBER_PRIVILEGE_PRICE(18, "会员权益价"),
    PRIVILEGE_PRICE(19, "特权价"),
    GROUP_BUY(21, "拼团"),
    LIVE_PRICE(26, "直播价"),

    ;

    private final Integer code;
    private final String name;
    EActivityAvailablePromotionType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        for (EActivityAvailablePromotionType type : EActivityAvailablePromotionType.values()) {
            if (type.getCode().equals(code)) {
                return type.getName();
            }
        }
        return null;
    }
}
