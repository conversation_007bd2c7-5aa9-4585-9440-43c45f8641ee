package com.ruoyi.weimo.enums;

import lombok.Getter;

/**
 * 条件运算符，支持的类型包括：1-大于等于；2-小于等于 ；3-大于；4-小于；5-等于。
 *
 * <AUTHOR>
 * @date 2024/10/10
 */
@Getter
public enum EActivityConditionOperator {
    GE(1, "大于等于"),
    LE(2, "小于等于"),
    GT(3, "大于"),
    LT(4, "小于"),
    EQ(5, "等于");

    private final Integer code;
    private final String desc;

    EActivityConditionOperator(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (EActivityConditionOperator item : values()) {
            if (item.code.equals(code)) {
                return item.desc;
            }
        }
        return null;
    }
}
