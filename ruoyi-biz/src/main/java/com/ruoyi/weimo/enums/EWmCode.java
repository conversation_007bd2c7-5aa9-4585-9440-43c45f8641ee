/**
 * @Title ECoinType.java @Package com.ogc.standard.enums @Description
 * <AUTHOR>
 * @date 2018年3月13日 上午11:30:16
 * @version V1.0
 */
package com.ruoyi.weimo.enums;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @since: 2023年10月09日
 * @history:
 */
public enum EWmCode {
    SUCCESS("0", "成功"),
    INVALID_ACCESS_TOKEN("80001001000119", "AccessToken失效");

    EWmCode(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
