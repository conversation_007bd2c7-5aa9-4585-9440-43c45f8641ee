package com.ruoyi.weimo.enums;

import lombok.Getter;

/**
 * 适用场景，支持的类型包括：1-线上订单；2-线下开单。
 *
 * <AUTHOR>
 * @date 2024/10/10
 */
@Getter
public enum EActivityTradeScene {
    ONLINE_ORDER(1, "线上订单"),
    OFFLINE_ORDER(2, "线下开单");

    private final Integer code;
    private final String desc;

    EActivityTradeScene(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EActivityTradeScene getByCode(Integer code) {
        for (EActivityTradeScene e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

}
