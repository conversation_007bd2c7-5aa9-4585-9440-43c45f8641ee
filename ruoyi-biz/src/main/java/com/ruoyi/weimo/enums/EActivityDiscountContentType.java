package com.ruoyi.weimo.enums;

import lombok.Getter;

/**
 * 优惠内容对应的类型。支持的类型包括；1001-减钱；1002-打折；1003-一口价； 1004-减免数量；1005-赠送数量；1006-膨胀倍数；2001-包邮；2002-减邮； 3001-规则商品；3002-优惠券；3003-积分；3004-余额。
 * <AUTHOR>
 * @date 2024/10/10
 */
@Getter
public enum EActivityDiscountContentType {
    REDUCE_MONEY(1001, "减钱"),
    DISCOUNT(1002, "打折"),
    FIXED_PRICE(1003, "一口价"),
    REDUCE_NUMBER(1004, "减免数量"),
    GIFT_NUMBER(1005, "赠送数量"),
    INFLATE_MULTIPLE(1006, "膨胀倍数"),
    FREE_SHIPPING(2001, "包邮"),
    REDUCE_SHIPPING(2002, "减邮"),
    RULE_GOODS(3001, "规则商品"),
    COUPON(3002, "优惠券"),
    INTEGRAL(3003, "积分"),
    BALANCE(3004, "余额");

    private final Integer code;
    private final String desc;

    EActivityDiscountContentType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (EActivityDiscountContentType item : values()) {
            if (item.code.equals(code)) {
                return item.desc;
            }
        }
        return null;
    }
}
