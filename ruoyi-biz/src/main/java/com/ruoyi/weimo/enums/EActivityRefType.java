package com.ruoyi.weimo.enums;

import lombok.Getter;

/**
 * 活动关联类型:101-全部商品；102-部分商品；103-部分类目；104-部分分组；105-部分场所(商品）。
 * <AUTHOR>
 * @date 2024/7/25
 */
@Getter
public enum EActivityRefType {
    DEFAULT("0", "部分商品"),
    ALL("101", "全部商品"),
    PART_PRODUCT("102", "部分商品"),
    PART_CATEGORY("103", "部分类目"),
    PART_GROUP("104", "部分分组"),
    PART_PLACE("105", "部分场所(商品)");

    private final String code;

    private final String desc;

    EActivityRefType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EActivityRefType getByCode(String code) {
        for (EActivityRefType value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
