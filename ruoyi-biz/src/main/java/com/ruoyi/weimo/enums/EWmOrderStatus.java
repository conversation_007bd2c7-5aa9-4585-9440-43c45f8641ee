/**
 * @Title ECoinType.java @Package com.ogc.standard.enums @Description
 * <AUTHOR>
 * @date 2018年3月13日 上午11:30:16
 * @version V1.0
 */
package com.ruoyi.weimo.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 微盟订单状态枚举
 */
@Getter
public enum EWmOrderStatus {

    /**
     * 微盟那边的订单状态
     * 0-创建；1-部分支付；2-已支付；3-待发货；4-部分发货；5-已发货；7-确认收货；8-完成；9-取消
     * oms_order表中订单状态
     * 1:待付款  （用户刚下单）
     * 2:代发货  （用户付完款 等待商城发货）
     * 3:代收货  （商城已经发货 等待用户确认收货）
     * 4:已完成  （用户已经确认收货 订单结束）
     * 5:取消订单 （用户未付款前取消订单）
     * 6:退款通过  （用户已经付款但是商城还未发货，用户发出退款申请，商城同意退款）
     * 7:退货通过   （用户已经确认收货后用户发出退货申请，商城同意所有退货申请 ，一个订单可能有多个单品）
     */

    CREATE("0", "1"),
    PARTIAL_PAYMENT("1", "2"),
    PAID("2", "2"),
    PENDING_SHIPMENT("3", "2"),
    PARTIAL_SHIPMENT("4", "3"),
    SHIPPED("5", "3"),
    CONFIRM_RECEIPT("7", "4"),
    COMPLETE("8", "4"),
    CANCEL("9", "5");

    EWmOrderStatus(String wmCode, String omsCode) {
        this.wmCode = wmCode;
        this.omsCode = omsCode;
    }

    private final String wmCode;

    private final String omsCode;


    private static final Map<String, EWmOrderStatus> wmCodeCollect = Arrays.stream(EWmOrderStatus.values())
            .collect(Collectors.toMap(EWmOrderStatus::getWmCode, Function.identity()));

    @JsonCreator
    public static EWmOrderStatus fromWmCode(String wmCode) {
        return wmCodeCollect.get(wmCode);
    }

}
