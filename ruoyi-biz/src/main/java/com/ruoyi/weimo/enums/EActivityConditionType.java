package com.ruoyi.weimo.enums;

import lombok.Getter;

/**
 * 优惠条件类型。支持的类型包括：102-满元；103-满件。
 *
 * <AUTHOR>
 * @date 2024/10/10
 */
@Getter
public enum EActivityConditionType {
    FULL_PRICE(102, "满元"),
    FULL_NUMBER(103, "满件");

    private final Integer code;
    private final String desc;

    EActivityConditionType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (EActivityConditionType item : values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return null;
    }
}
