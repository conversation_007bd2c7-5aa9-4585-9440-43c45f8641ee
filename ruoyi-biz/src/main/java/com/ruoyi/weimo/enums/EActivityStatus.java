package com.ruoyi.weimo.enums;

import lombok.Getter;

/**
 * 活动状态枚举
 * 1：暂停 2：已结束 3：已删除 4：预热 5：进行中 6：未开始
 * <AUTHOR>
 * @date 2024/7/30
 */
@Getter
public enum EActivityStatus {
    /**
     * 暂停
     */
    PAUSE(1, "暂停"),
    /**
     * 已结束
     */
    END(2, "已结束"),
    /**
     * 已删除
     */
    DELETE(3, "已删除"),
    /**
     * 预热
     */
    PREHEAT(4, "预热"),
    /**
     * 进行中
     */
    GOING_ON(5, "进行中"),
    /**
     * 未开始
     */
    NOT_STARTED(6, "未开始");

    private final Integer value;
    private final String desc;

    EActivityStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static EActivityStatus getByValue(Integer value) {
        for (EActivityStatus status : EActivityStatus.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        return null;
    }
}
