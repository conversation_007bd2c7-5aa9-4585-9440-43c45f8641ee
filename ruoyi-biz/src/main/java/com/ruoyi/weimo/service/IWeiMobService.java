package com.ruoyi.weimo.service;



import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.domain.dto.WMOrderPageQueryReq;
import com.ruoyi.weimo.domain.WmUser;
import com.ruoyi.weimo.domain.dto.ActivityPageQueryReq;
import com.ruoyi.weimo.domain.dto.WmGoodsPageReq;
import com.ruoyi.weimo.domain.vo.WmGoodsPageRes;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/10/13 15:13
 */
public interface IWeiMobService {

    /**
     * 获取AccessToken
     * @return
     */
    String getAccessTokenByRedis();

    /**
     * 拉取用户
     * @return
     */
    List<WmUser> pullWmUserList(Integer pageNum);

    List<WmUser> pullWmUserList(Integer pageNum, List<Map<String, Object>> queryParams);
    /**
     * 获取用户的UnionId
     * @param wid
     * @return
     */
    String getWmUserUnionId(Long wid);

    /**
     * 拉取微盟商品列表
     * @param req
     * @return
     */
    List<WmGoodsPageRes> pullAllWeiMoGoods(WmGoodsPageReq req);

    /**
     * 拉取微盟云平台商品详情
     * @param goodsId 商品ID
     * @return 商品详情JSON格式
     */
    JSONObject pullWMGoodsDetail(String goodsId);

    /**
     * 拉取商品类目
     * @param parentCategoryId
     * @return
     */
    JSONObject pullGoodsCategory(Long parentCategoryId);

    /**
     * 拉取商品类目规格
     * @return
     */
    JSONObject pullGoodsCategoryKuspec(Long categoryId, int pageNum);

    /**
     * 拉取规格值列表
     * @param id
     * @param pageNum
     * @return
     */
    JSONObject pullGoodsCategoryKuspecVaule(Long id, int pageNum);



    /**
     * 调用微盟拉取订单列表接口
     * @param req
     * @return
     */
    JSONObject sendPostToPullOrder(WMOrderPageQueryReq req);


    /**
     * 同步微盟订单
     */
    void pullOrder();

    /**
     * 拉取微盟活动
     */
    void pullActivitiesFromWM();

    /**
     * 拉取订单信息
     * @param orderNo
     * @return
     */
    JSONObject pullOrderDetail(String orderNo);
//
//
//    /**
//     * 拉取微盟促销活动
//     *
//     * @param req
//     * @return
//     */
//    JSONObject pullWmMarketActivityPage(ActivityPageQueryReq req);
//
//
    /**
     * 获取商品的分享 URL
     * @param goodsId
     * @return
     */
    String getWmGoodsShareUrl(Long goodsId);

    /**
     * 获取微盟微客的
     * @param wId
     * @param url
     * @return
     */
    String getWmSharUrl(String wId,String url);


    /**
     * 获取售后订单详情
     * @param rightsId
     * @return
     */
    JSONObject pullRightsDetail(String rightsId);


    /**
     * 根据unionId获取微盟ID
     * @param unionId
     * @return
     */
    String getWmIdByUnionId(String unionId);
//
//    /**
//     * 设置客户为微客
//     * @param wid
//     */
//    void distributorCreate(Long wid);
//
//    /**
//     * 获取活动详情
//     *
//     * @param activityId
//     * @param activityType
//     * @return
//     */
//    JSONObject getActivityInfo(Long activityId, String activityType);
//
//    /**
//     * 获取限时折扣商品规则
//     * @param activityId
//     * @return
//     */
//    JSONArray getActivityDiscountGoodsRule(Long activityId);
//
//    /**
//     * 优惠券核销
//     *
//     * @param couponTemplateId
//     * @param couponCode
//     */
//    boolean couponConsume(Long wid,Long couponTemplateId, String couponCode);
//
//
//    /**
//     * 获取优惠券列表
//     * @param wid
//     * @return
//     */
//    JSONArray getCouponListByWId(Long wid);
//
//    /**
//     * 获取所有分组
//     * @return
//     */
//    JSONObject getGoodsClassifyList();

    /**
     * 发送请求并解析响应,拉取满减满折活动
     * @param req 请求对象
     * @return 响应对象
     */
    JSONObject sendPostToPullPromotionFullDiscount(ActivityPageQueryReq req);

    /**
     * 发送请求并解析响应,拉取限时折扣活动
     * @param req 请求对象
     * @return 响应对象
     */
    JSONObject sendPostToPullPromotionDiscountActivity(ActivityPageQueryReq req);

    /**
     * 检查活动状态
     */
    void checkActivityStatus();


    /**
     * 获取主wid
     * @param wid
     * @return
     */
    String getMainWid(String wid);

    /**
     * 创建微盟用户为微客
     *
     * @param wid wid
     */
    void distributorCreate(Long wid);

    /**
     * 获取微客信息列表
     *
     * @param wid 微盟id
     */
    JSONArray distributorList(Long wid);

    /**
     * 新建客户微客关系
     *
     * @param weikeWid        微客id
     * @param customerWidList 绑定微客的客户id列表
     */
    boolean distributorBind(Long weikeWid, List<Long> customerWidList);


    /**
     * 查询客户-微客关系
     *
     * @param customerWid 待查询关系链的用户 ID, 必传
     * @param isBind      待查询的关系链绑定类型。类型包括：0-未绑定；1-已绑定。不传默认为 1。, 非必传
     * @param verbose     是否获取待查询微客的用户详情。true-查询；false-不查询。不传默认为 false 。 非必传
     * @return result
     */
    JSONObject customerDistributorGet(Long customerWid, Integer isBind, Boolean verbose);


    /**
     * 获取客户基础信息
     *
     * @param widList wid list
     * @return 响应对象
     */
    JSONObject crmCustomerGetList(List<Long> widList);

    /**
     * 查询售后管理列表
     * @param pageNum
     * @param pageSize
     * @param queryParameter
     * @return
     */
    JSONObject shopRightsList(int pageNum, int pageSize, Map<String, Object> queryParameter);
}
