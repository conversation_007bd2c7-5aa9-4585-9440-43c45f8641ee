package com.ruoyi.weimo.service.impl;

import com.ruoyi.biz.service.IBizUserService;
import com.ruoyi.weimo.domain.WmUser;
import com.ruoyi.weimo.mapper.WmUserMapper;
import com.ruoyi.weimo.service.IWeiMobService;
import com.ruoyi.weimo.service.IWmUserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 用户管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Service
public class WmUserServiceImpl implements IWmUserService {
    @Resource
    private WmUserMapper wmUserMapper;

    @Resource
    private IWeiMobService weiMobService;

    @Resource
    private IBizUserService bizUserService;


    /**
     * 查询用户管理
     *
     * @param wid 用户管理主键
     * @return 用户管理
     */
    @Override
    public WmUser selectWmUserByWid(Long wid) {
        return wmUserMapper.selectWmUserByWid(wid);
    }

    /**
     * 查询用户管理列表
     *
     * @param wmUser 用户管理
     * @return 用户管理
     */
    @Override
    public List<WmUser> selectWmUserList(WmUser wmUser) {
        return wmUserMapper.selectWmUserList(wmUser);
    }

    /**
     * 新增用户管理
     *
     * @param wmUser 用户管理
     * @return 结果
     */
    @Override
    public int insertWmUser(WmUser wmUser) {
        return wmUserMapper.insertWmUser(wmUser);
    }

    /**
     * 修改用户管理
     *
     * @param wmUser 用户管理
     * @return 结果
     */
    @Override
    public int updateWmUser(WmUser wmUser) {
        return wmUserMapper.updateWmUser(wmUser);
    }

    /**
     * 批量删除用户管理
     *
     * @param wids 需要删除的用户管理主键
     * @return 结果
     */
    @Override
    public int deleteWmUserByWids(Long[] wids) {
        return wmUserMapper.deleteWmUserByWids(wids);
    }

    /**
     * 删除用户管理信息
     *
     * @param wid 用户管理主键
     * @return 结果
     */
    @Override
    public int deleteWmUserByWid(Long wid) {
        return wmUserMapper.deleteWmUserByWid(wid);
    }

    @Override
    public void pullWmUser() {

        Integer pageNum = 1;
        while (true) {
            List<WmUser> userList = weiMobService.pullWmUserList(pageNum);
            if (CollectionUtils.isEmpty(userList)) {
                return;
            }
            //开始批量新增或者更新
            wmUserMapper.insertBatch(userList);

            bizUserService.batchAddUpdateByWmUser(userList);

            pageNum++;
        }
    }

    @Override
    @Deprecated
    public void getWmUserUnionId() {
        //todo 微盟小程序换过开发者平台，目前还是以前的开发的UnionId，只有用户重新登录小程序才可以
//        List<WmUser> userList = wmUserMapper.selectWmUserNonUnionIdList();
        List<WmUser> userList = wmUserMapper.selectWmUserList(new WmUser());
        for (WmUser wmUser : userList) {
            String unionId = weiMobService.getWmUserUnionId(wmUser.getWid());
            if (StringUtils.isNotBlank(unionId)) {
                wmUser.setUnionId(unionId);
                //wmUserMapper.updateWmUser(wmUser);

                //绑定用户操作
                //bizUserService.handleBindUser("wm", wmUser.getWid().toString(), unionId, wmUser.getNickname(), wmUser.getHeadUrl());
            }
        }

    }
}
