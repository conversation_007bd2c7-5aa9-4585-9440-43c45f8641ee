package com.ruoyi.weimo.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.ruoyi.biz.domain.BizMembershipToken;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.biz.mapper.BizMembershipTokenMapper;
import com.ruoyi.biz.service.IBizMembershipService;
import com.ruoyi.common.constant.ConfigConstants;
import com.ruoyi.common.constant.RightsConstants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.constant.WmConstants;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.weimo.domain.WmRights;
import com.ruoyi.weimo.domain.dto.MsgBodyRights;
import com.ruoyi.weimo.domain.dto.WmMessage;
import com.ruoyi.weimo.mapper.WmRightsMapper;
import com.ruoyi.weimo.service.IWeiMobService;
import com.ruoyi.weimo.service.IWmRightsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 微盟售后管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
@Service
@Slf4j
public class WmRightsServiceImpl implements IWmRightsService {
    @Resource
    private IWeiMobService weiMobService;

    @Resource
    private WmRightsMapper wmRightsMapper;

    @Resource
    private IBizMembershipService bizMembershipService;

    @Resource
    private BizMembershipTokenMapper bizMembershipTokenMapper;

    @Resource
    private ISysConfigService sysConfigService;


    @Override
    public void handleTopic(WmMessage wmMessage) {
        MsgBodyRights msgBodyRights = JSONObject.parseObject((String) wmMessage.getMsgBody(), MsgBodyRights.class);
        log.info("handle rights topic, event: {}", wmMessage.getEvent());
        switch (wmMessage.getEvent()) {
            case WmConstants.Event.CREATE:
                handleCreate(msgBodyRights);
                break;
            case WmConstants.Event.INFO_UPDATE:
                handleUpdate(msgBodyRights);
                break;
            case WmConstants.Event.STATUS_UPDATE:
                handleStatusUpdate(msgBodyRights);
                break;
            default:
                break;
        }
    }

    private void handleUpdate(MsgBodyRights msgBodyRights) {
        log.info("handle rights update, id: {}", msgBodyRights.getRightsId());
        WmRights wmRights = wmRightsMapper.selectWmRightsByRightsId(msgBodyRights.getRightsId());
        if (wmRights == null) {
            log.info("未找到微盟售后记录");
            return;
        }

        // 更新微盟售后记录
        MsgBodyRights.RightsInfo rightsInfo = msgBodyRights.getRightsInfo();
        wmRights.setOrderNo(msgBodyRights.getOrderNo());
        wmRights.setVid(rightsInfo.getVid());
        wmRights.setProcessVid(rightsInfo.getProcessVid());
        wmRights.setRightsStatus(msgBodyRights.getStatus());
        wmRights.setRightsType(rightsInfo.getRightsType());
        wmRights.setRightsSource(rightsInfo.getRightsSource());
        wmRights.setRightsCauseType(rightsInfo.getRightsCauseType());
        wmRights.setRefundType(rightsInfo.getRefundType());
        wmRights.setCurrency(null);
        wmRights.setUpdateBy(UserConstants.SYS_USER);
        wmRights.setUpdateTime(new Date());
        wmRightsMapper.updateWmRights(wmRights);
    }


    private void handleCreate(MsgBodyRights msgBodyRights) {

        log.info("handle rights create, id: {}", msgBodyRights.getRightsId());
        String bizProcessType = "99";
        BizMembershipToken bizMembershipToken = bizMembershipTokenMapper.selectBySourceId(msgBodyRights.getOrderNo().toString());
        if (bizMembershipToken != null) {
            bizProcessType = "1";
        }

        MsgBodyRights.RightsInfo rightsInfo = msgBodyRights.getRightsInfo();
        WmRights wmRights = new WmRights();
        wmRights.setRightsId(msgBodyRights.getRightsId());
        wmRights.setOrderNo(msgBodyRights.getOrderNo());
        wmRights.setVid(rightsInfo.getVid());
        wmRights.setProcessVid(rightsInfo.getProcessVid());
        wmRights.setRightsStatus(msgBodyRights.getStatus());
        wmRights.setRightsType(rightsInfo.getRightsType());
        wmRights.setRightsSource(rightsInfo.getRightsSource());
        wmRights.setRightsCauseType(rightsInfo.getRightsCauseType());
        wmRights.setRefundType(rightsInfo.getRefundType());
        wmRights.setCurrency(null);
        wmRights.setBizProcessType(bizProcessType);
        wmRights.setHasProcess(EBoolean.NO.getCode());
        wmRights.setCreateBy(UserConstants.SYS_USER);
        wmRights.setCreateTime(new Date());
        insertWmRights(wmRights);
    }


    private void handleStatusUpdate(MsgBodyRights msgBodyRights) {
        log.info("handle rights status update, id: {}", msgBodyRights.getRightsId());
        MsgBodyRights.RightsInfo rightsInfo = msgBodyRights.getRightsInfo();
        // 处理退款情况
        if (rightsInfo.getRightsType() == 1 || rightsInfo.getRightsType() == 2) {

            // 用户购买会员业务处理.
            if (rightsInfo.getRightsStatus() == 6) {
                WmRights wmRights = wmRightsMapper.selectWmRightsByRightsId(msgBodyRights.getRightsId());
                bizMembershipService.handleReturnMember(wmRights);
            }
        }
    }

    /**
     * 查询微盟售后管理
     *
     * @param rightsId 微盟售后管理主键
     * @return 微盟售后管理
     */
    @Override
    public WmRights selectWmRightsByRightsId(Long rightsId) {
        return wmRightsMapper.selectWmRightsByRightsId(rightsId);
    }

    /**
     * 查询微盟售后管理列表
     *
     * @param wmRights 微盟售后管理
     * @return 微盟售后管理
     */
    @Override
    public List<WmRights> selectWmRightsList(WmRights wmRights) {
        return wmRightsMapper.selectWmRightsList(wmRights);
    }

    /**
     * 新增微盟售后管理
     *
     * @param wmRights 微盟售后管理
     * @return 结果
     */
    @Override
    public int insertWmRights(WmRights wmRights) {
        wmRights.setCreateTime(DateUtils.getNowDate());
        return wmRightsMapper.insertOrUpdateWmRights(wmRights);
    }

    /**
     * 修改微盟售后管理
     *
     * @param wmRights 微盟售后管理
     * @return 结果
     */
    @Override
    public int updateWmRights(WmRights wmRights) {
        wmRights.setUpdateTime(DateUtils.getNowDate());
        return wmRightsMapper.updateWmRights(wmRights);
    }

    /**
     * 批量删除微盟售后管理
     *
     * @param rightsIds 需要删除的微盟售后管理主键
     * @return 结果
     */
    @Override
    public int deleteWmRightsByRightsIds(Long[] rightsIds) {
        return wmRightsMapper.deleteWmRightsByRightsIds(rightsIds);
    }

    /**
     * 删除微盟售后管理信息
     *
     * @param rightsId 微盟售后管理主键
     * @return 结果
     */
    @Override
    public int deleteWmRightsByRightsId(Long rightsId) {
        return wmRightsMapper.deleteWmRightsByRightsId(rightsId);
    }

    @Override
    public void scanWmRightsNotProcess() {
        try {
            // 分页参数
            int pageSize = 20;
            int pageNum = 1;

            // 分页查询运营计划列表，获取关联商品的运营计划
            while (true) {
                PageHelper.startPage(pageNum, pageSize);
                List<WmRights> wmRightsList = wmRightsMapper.selectWmRightsList(new WmRights() {{
                    // 目前之后会员类型售后业务处理
                    setBizProcessType(RightsConstants.ProcessType.membership);
                    setHasProcess(EBoolean.NO.getCode());
                }});

                if (CollectionUtils.isEmpty(wmRightsList)) {
                    break;
                }

                for (WmRights wmRights : wmRightsList) {
                    try {
                        if (RightsConstants.ProcessType.membership.equals(wmRights.getBizProcessType())) {
                            log.info("处理会员类型售后服务业务");
                            bizMembershipService.handleReturnMember(wmRights);
                        }

                    } catch (Exception e) {
                        log.error("处理会员类型售后服务业务异常, rightsId: {}", wmRights.getRightsId(), e);
                    }
                }

                // 防止死循环
                pageNum++;
            }
        } catch (Exception e) {
            log.error("处理关联商品分值计算时异常", e);
        }
    }

    @Override
    public void pullWmRights() {
        try {
            // 分页参数
            int pageSize = 20;
            int pageNum = 1;

            while (true) {
                HashMap<String, Object> paramMap = new HashMap<>();
                Instant now = Instant.now();
                // 扫描七天内售后单
                Instant before = now.minusSeconds(60 * 60 * 24 * 70);
                paramMap.put("queryTime", new HashMap<String, Object>() {{
                    put("startTime", before.toEpochMilli());
                    put("endTime", now.toEpochMilli());
                    put("type", 0);
                }});

                JSONObject jsonObject = weiMobService.shopRightsList(pageNum, pageSize, paramMap);
                JSONArray pageList = jsonObject.getJSONArray("pageList");
                if (CollectionUtils.isEmpty(pageList)) {
                    return;
                }

                for (Object object : pageList) {

                    JSONObject item = (JSONObject) object;
                    JSONObject merchantInfo = item.getJSONObject("merchantInfo");
                    JSONObject rightsOrder = item.getJSONObject("rightsOrder");
                    JSONObject originOrder = item.getJSONObject("originOrder");
                    JSONArray rightsItems = item.getJSONArray("rightsItems");
                    String orderNo = originOrder.getString("orderNo");

                    WmRights wmRights = new WmRights();
                    wmRights.setRightsId(rightsOrder.getLong("rightsId"));
                    wmRights.setOrderNo(originOrder.getLong("orderNo"));
                    wmRights.setVid(merchantInfo.getLong("vid"));
                    wmRights.setProcessVid(merchantInfo.getLong("processVid"));
                    wmRights.setRightsStatus(rightsOrder.getInteger("rightsStatus"));
                    wmRights.setRightsType(rightsOrder.getInteger("rightsType"));
                    wmRights.setRightsSource(rightsOrder.getInteger("rightsSource"));
                    wmRights.setRightsCauseType(rightsOrder.getInteger("rightsCauseType"));
                    wmRights.setRefundType(rightsOrder.getInteger("refundType"));
                    wmRights.setCurrency(rightsOrder.getString("currency"));
                    wmRights.setBizProcessType(getBizProcessType(rightsItems));
                    wmRights.setHasProcess(EBoolean.NO.getCode());
                    wmRights.setCreateBy(UserConstants.SYS_USER);
                    wmRights.setCreateTime(new Date());

                    wmRightsMapper.insertOrUpdateWmRights(wmRights);

                }
                pageNum ++ ;
            }

        } catch (Exception e) {
            log.error("更新微盟售后管理单异常, error", e);
        }
    }

    private String getBizProcessType(JSONArray rightsItems) {
        String memberProductId = sysConfigService.selectConfigByKey(ConfigConstants.Membership.NINE_NINE);
        String member365ProductId = sysConfigService.selectConfigByKey(ConfigConstants.Membership.THREE_SIXTY_FIVE);

        String bizProcessType = "99";

        boolean bisMembership = rightsItems.stream().map(x -> (JSONObject) x)
                .anyMatch(x -> {
                    Long productId = x.getJSONObject("goodsInfo").getLong("goodsId");
                    return productId.equals(Long.parseLong(member365ProductId)) || productId.equals(Long.parseLong(memberProductId));
                });
        if (bisMembership) {
            bizProcessType = "1";
        }
        return bizProcessType;
    }

}
