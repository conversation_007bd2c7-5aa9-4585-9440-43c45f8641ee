package com.ruoyi.weimo.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.domain.dto.WMOrderPageQueryParamReq;
import com.ruoyi.biz.domain.dto.WMOrderPageQueryParamTimeReq;
import com.ruoyi.biz.domain.dto.WMOrderPageQueryReq;
import com.ruoyi.biz.service.IBizUserService;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.order.service.IOmsOrderService;
import com.ruoyi.weimo.config.WeiMoConfig;
import com.ruoyi.weimo.domain.WmActivities;
import com.ruoyi.weimo.domain.WmActivityRule;
import com.ruoyi.weimo.domain.WmFullDiscountActivity;
import com.ruoyi.weimo.domain.WmPromotionDiscountActivity;
import com.ruoyi.weimo.domain.WmUser;
import com.ruoyi.weimo.domain.dto.ActivityPageQueryReq;
import com.ruoyi.weimo.domain.dto.WmGoodsPageReq;
import com.ruoyi.weimo.domain.vo.WmGoodsPageRes;
import com.ruoyi.weimo.enums.EActivitySource;
import com.ruoyi.weimo.enums.EActivityStatus;
import com.ruoyi.weimo.enums.EActivityType;
import com.ruoyi.weimo.enums.EWmCode;
import com.ruoyi.weimo.service.IWeiMobService;
import com.ruoyi.weimo.service.IWmActivitiesService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/10/13 16:15
 */
@Service
@Slf4j
public class IWeiMobServiceImpl implements IWeiMobService {

    /**
     * 拉取微盟客户列表
     * <a href="https://dopen.weimob.com/apigw/bos/v2.1/user/search?accesstoken=ACCESS_TOKEN">...</a>
     */
    private static String PULL_USER_URL = "https://dopen.weimob.com/apigw/bos/v2.1/user/search";

    /**
     * 获取用户渠道
     */
    private static String USER_CHANNEL_URL = "https://dopen.weimob.com/apigw/bos/v2.0/user/channel/getList?accesstoken=ACCESS_TOKEN";

    /**
     * 微盟获取AccessToken Url
     * <a href="https://dopen.weimob.com/fuwu/b/oauth2/token?grant_type=client_credentials&client_id=">...</a>{client_id}&client_secret={client_secret}&shop_id={shop_id}&shop_type={shop_type}
     */
    private static String GET_ACCESS_TOKEN_URL = "https://dopen.weimob.com/fuwu/b/oauth2/token";
    /**
     * 根据商品ID列表查询商品信息
     * <a href="https://dopen.weimob.com/apigw/weimob_shop/v2.0/goods/getListById?accesstoken=ACCESS_TOKEN">...</a>
     */
    private static String PULL_GOODS_GETLISTBYID_URL = "https://dopen.weimob.com/apigw/weimob_shop/v2.0/goods/getListById";
    /**
     * 拉取微盟促销活动URL
     * <a href="https://dopen.weimob.com/apigw/weimob_shop/v2.0/marketactivity/getList?accesstoken=ACCESS_TOKEN">...</a>
     */
    private static String PULL_MARKET_ACTIVITY_URL = "https://dopen.weimob.com/apigw/weimob_shop/v2.0/marketactivity/getList";
    /**
     * 拉取微盟商品列表接口
     * <a href="https://dopen.weimob.com/apigw/weimob_shop/v2.0/goods/getList?accesstoken=ACCESS_TOKEN">...</a>
     */
    private static String PULL_GOODS_URL = "https://dopen.weimob.com/apigw/weimob_shop/v2.0/goods/getList";
    /**
     * 拉取微盟商品详情接口
     * <a href="https://dopen.weimob.com/apigw/weimob_shop/v2.0/goods/get?accesstoken=ACCESS_TOKEN">...</a>
     */
    private static String PULL_GOODS_DETAIL_URL = "https://dopen.weimob.com/apigw/weimob_shop/v2.0/goods/get";


    /**
     * 获取一级/二级类目 <a href="https://dopen.weimob.com/apigw/weimob_shop/v2.0/order/list/search?accesstoken=ACCESS_TOKEN">...</a>
     */
    private static String PULL_CATEGORY_URL = "https://dopen.weimob.com/apigw/weimob_shop/v2.0/goods/category/getList?accesstoken=ACCESS_TOKEN";

    private static String PULL_CATEGORY_SKUSPEC_URL = "https://dopen.weimob.com/apigw/weimob_shop/v2.0/goods/category/skuspec/getList?accesstoken=ACCESS_TOKEN";


    private static String PULL_CATEGORY_SKUSPEC_CONTENT_URL = "https://dopen.weimob.com/apigw/weimob_shop/v2.0/goods/category/skuspec/content/getList?accesstoken=ACCESS_TOKEN";


    /**
     * 拉取微盟订单列表接口 <a href="https://dopen.weimob.com/apigw/weimob_shop/v2.0/order/list/search?accesstoken=ACCESS_TOKEN">...</a>
     */
    private static String PULL_ORDER_URL = "https://dopen.weimob.com/apigw/weimob_shop/v2.0/order/list/search";
    /**
     * 拉取订单详情
     * <a href="https://doc.weimobcloud.com/detail?menuId=19&childMenuId=1&tag=1510&id=3282&isold=2">...</a>
     */
    private static String PULL_ORDER_DETAIL_URL = "https://dopen.weimob.com/apigw/weimob_shop/v2.0/order/detail/get";
    /**
     * 商品的分享 URL
     */
    private static String WEIMOB_DISTRIBUTION_SHARE_GOODS_URL = "https://dopen.weimob.com/apigw/weimob_shop/v2.0/goods/url/getList";
    /**
     * 微盟微客的推荐URL 获取
     */
    private static String WEIMOB_DISTRIBUTION_SHARE_URL = "https://dopen.weimob.com/apigw/weimob_distribution/v2.0/process/url/getList";
    /**
     * 订单售后详情
     */
    private static String RIGHTS_DETAIL_GET_URL = "https://dopen.weimob.com/apigw/weimob_shop/v2.0/rights/detail/get";

    /**
     * 添加微客
     */
    private static String DISTRIBUTION_CREATE_URL = "https://dopen.weimob.com/apigw/weimob_distribution/v2.0/distributor/create";

    /**
     * 查询微客信息列表
     */
    private static String DISTRIBUTION_LIST_URL = "https://dopen.weimob.com/apigw/weimob_distribution/v2.0/distributor/getList";

    /**
     * 新建客户微客关系
     */
    private static String DISTRIBUTION_BIND_URL = "https://dopen.weimob.com/apigw/weimob_distribution/v2.0/customer/distributor/bind";

    /**
     * 查询客户-微客关系
     */
    private static String CUSTOMER_DISTRIBUTOR_GET_URL = "https://dopen.weimob.com/apigw/weimob_distribution/v2.0/customer/distributor/get";
    /**
     * 满减活动详情
     */
    private static String PROMOTION_FULLDISCOUNT_URL = "https://dopen.weimob.com/apigw/weimob_shop/v2.0/promotion/fulldiscount/get";
    /**
     * 限时折扣详情
     */
    private static String PROMOTION_DISCOUNT_URL = "https://dopen.weimob.com/apigw/weimob_shop/v2.0/promotion/discount/get";
    /**
     * 限时折扣商品规则详情
     */
    private static String PROMOTION_DISCOUNT_GOODS_URL = "https://dopen.weimob.com/apigw/weimob_shop/v2.0/promotion/discount/goods/get";

    /**
     * 优惠券核销
     */
    private static String COUPON_CONSUME_URL = "https://dopen.weimob.com/apigw/weimob_crm/v2.0/coupon/consume";
    /**
     * 用户优惠券列表
     */
    private static String COUPON_CUSTOMER_GETLISTURL = "https://dopen.weimob.com/apigw/weimob_crm/v2.0/coupon/customer/getList";

    /**
     * 客户管理-获取客户基础信息
     */
    private static String CRM_CUSTOMER_GETLIST = "https://dopen.weimob.com/apigw/weimob_crm/v2.0/customer/getList";
    /**
     * 商品全量分组
     */
    private static String GOODS_CLASSIFY_URL = "https://dopen.weimob.com/apigw/weimob_shop/v2.0/goods/classify/getList";
    /**
     * 获取主wid
     */
    private static String GET_SUPER_WID_URL = "https://dopen.weimob.com/apigw/bos/v2.0/user/superwid/get";

    /**
     * 获取主wid
     */
    private static String RIGHTS_LIST_URL = "https://dopen.weimob.com/apigw/weimob_shop/v2.0/rights/list/search";

    /**
     * 获取微盟accessToken的参数
     */
    private static String grantType = "client_credentials";

    @Resource
    private WeiMoConfig weiMoConfig;

    @Resource
    private RedisCache redisCache;

    @Resource
    private IOmsOrderService omsOrderService;

    @Resource
    private IBizUserService bizUserService;

    @Resource
    private IWmActivitiesService wmActivitiesService;

    @Resource
    private IWeiMobService weiMobService;


    @Value("${spring.profiles.active}")
    private String active;

    /**
     * 获取微盟accessToken
     *
     * @return accessToken
     */
    @Override
    public String getAccessTokenByRedis() {
//        String accessToken;
//        String accessTokenKey = CacheConstants.ACCESS_TOKEN_KEY + "WM";
//        if (redisCache.hasKey(accessTokenKey)) {
//            accessToken = redisCache.getCacheObject(accessTokenKey);
//        } else {
//            if ("dev".equals(active)|| "lily".equals(active)) {
//                //往 v2的lily 拿token
//                JSONObject jsonObject = HttpUtils.postToJsonObject("https://lily-boss.dongai.upbrosai.cn/api/core/wm/access_token", new HashMap<>());
//                redisCache.setCacheObject(accessTokenKey, jsonObject.getString("access_token"));
//                return jsonObject.getString("access_token");
//            }
//
//            JSONObject resultJson = getAccessToken();
//            accessToken = resultJson.getString("access_token");
//            if (StringUtils.isEmpty(accessToken)) {
//                return null;
//            }
//            redisCache.setCacheObject(accessTokenKey, accessToken,
//                    resultJson.getInteger("expires_in"), TimeUnit.SECONDS);
//        }
        return weiMoConfig.getAccessToken();
    }

    @Override
    public List<WmUser> pullWmUserList(Integer pageNum) {
        return pullWmUserList(pageNum, null);
    }

    @Override
    public List<WmUser> pullWmUserList(Integer pageNum, List<Map<String, Object>> queryParams) {
        if (pageNum == null) {
            pageNum = 1;
        }
        String url = PULL_USER_URL + "?accesstoken=" + getAccessTokenByRedis();

        HashMap<String, Object> param = new HashMap<>();
        param.put("vid", weiMoConfig.getVid());
        if (CollUtil.isNotEmpty(queryParams)) {
            param.put("query", queryParams);
        }

        //需要查询的字段
        List<String> fields = new ArrayList<>();
        fields.add("wid");
        fields.add("name");
        fields.add("phone");
        fields.add("nickname");
        fields.add("headUrl");
        fields.add("becomeCustomerTime");
        fields.add("customerStatus");
        param.put("fields", fields);
        param.put("pageNum", pageNum);
        param.put("pageSize", 100);
        param.put("isReturnPageResult", 1);

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
//        if (EWmCode.INVALID_ACCESS_TOKEN.getCode().equals(codeJson.getString("errcode"))){
//            String accessTokenKey = CacheConstants.ACCESS_TOKEN_KEY + "WM";
//            redisCache.deleteObject(accessTokenKey);
//            throw new ServiceException("微盟token无效");
//        }
        if (!EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return null;
        }
        JSONObject dataJson = JSON.parseObject(resultJson.getString("data"));

        String jsonString = dataJson.getString("result");
        List<JSONObject> customerList = JSON.parseArray(jsonString, JSONObject.class);
        List<WmUser> users = new ArrayList<>(customerList.size());
        //参数组装
        for (JSONObject customer : customerList) {
            WmUser user = new WmUser();
            String wid = customer.getString("wid");
            String name = customer.getString("name");
            String nickName = customer.getString("nickname");
            if (StringUtils.isBlank(nickName)) {
                continue;
            }
            String headUrl = customer.getString("headUrl");
            String customerStatus = customer.getString("customerStatus");
            String becomeCustomerTime = customer.getString("becomeCustomerTime");
            user.setWid(Long.valueOf(wid));
            user.setName(name);
            user.setNickname(nickName);
            user.setHeadUrl(headUrl);
            user.setCreateTime(new Date(Long.parseLong(becomeCustomerTime)));
            users.add(user);
        }
        return users;
    }

    @Override
    public String getWmUserUnionId(Long wid) {
        String url = USER_CHANNEL_URL.replace("ACCESS_TOKEN", getAccessTokenByRedis());

        HashMap<String, Object> param = new HashMap<>();
        param.put("wid", wid);
        param.put("channelTypeList", Collections.singletonList(2));
        param.put("pageNum", 0);
        param.put("pageSize", 10);
        param.put("queryType", 2);
        param.put("needDistinct", true);

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
//        if (EWmCode.INVALID_ACCESS_TOKEN.getCode().equals(codeJson.getString("errcode"))){
//            String accessTokenKey = CacheConstants.ACCESS_TOKEN_KEY + "WM";
//            redisCache.deleteObject(accessTokenKey);
//            throw new ServiceException("微盟token无效");
//        }
        if (!EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return null;
        }
        JSONObject dataJson = JSON.parseObject(resultJson.getString("data"));

        JSONArray infoDtoList = dataJson.getJSONArray("userChannelInfoDtoList");
        if (CollectionUtils.isEmpty(infoDtoList)) {
            return null;
        }

        return infoDtoList.getJSONObject(0).getString("openId");
    }

    public static void main(String[] args) {


//        String url = GET_SUPER_WID_URL.concat("?accesstoken=").concat("7e0aab4b-0345-40e0-8f69-6fc436816199");
//
//        HashMap<String, Object> param = new HashMap<>();
//        param.put("wid", Long.valueOf("10907112129"));
//
//        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
//        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
//
//        System.out.println(resultJson.getJSONObject("data").getString("superWid"));


        String url = USER_CHANNEL_URL.replace("ACCESS_TOKEN", "7e0aab4b-0345-40e0-8f69-6fc436816199");

        HashMap<String, Object> param = new HashMap<>();
        param.put("wid", "11339550420");
        param.put("channelTypeList", Collections.singletonList(2));
        param.put("pageNum", 0);
        param.put("pageSize", 10);
        param.put("queryType", 2);
        param.put("needDistinct", true);

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));

        System.out.println(resultJson);


    }


    /**
     * 发送请求拉取微盟商品列表接口
     *
     * @param req
     * @return
     */
    @Override
    public List<WmGoodsPageRes> pullAllWeiMoGoods(WmGoodsPageReq req) {
        String url = PULL_GOODS_URL + "?accesstoken=" + getAccessTokenByRedis();

        JSONObject param = JSON.parseObject(JSON.toJSONString(req));
        JSONObject basicInfo = new JSONObject();
        basicInfo.put("vid", weiMoConfig.getVid());
        param.put("basicInfo", basicInfo.toJSONString());
        param.put("pageNum", req.getPageNum() == null ? 1 : req.getPageNum());
        param.put("pageSize", req.getPageSize() == null ? 20 : req.getPageSize());

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
//        if (EWmCode.INVALID_ACCESS_TOKEN.getCode().equals(codeJson.getString("errcode"))){
//            String accessTokenKey = CacheConstants.ACCESS_TOKEN_KEY + "WM";
//            redisCache.deleteObject(accessTokenKey);
//            throw new ServiceException("微盟token无效");
//        }
        if (!EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return null;
        }

        JSONObject dataJson = JSON.parseObject(resultJson.getString("data"));
        int pageSize = Integer.parseInt(dataJson.getString("pageSize"));
        if (pageSize > 0) {
            String pageListStr = JSONArray.toJSONString(dataJson.get("pageList"));
            return JSON.parseArray(pageListStr, WmGoodsPageRes.class);
        }
        return null;
    }


    @Override
    public JSONObject pullWMGoodsDetail(String goodsId) {
        String url = PULL_GOODS_DETAIL_URL + "?accesstoken=" + getAccessTokenByRedis();

        HashMap<String, Object> param = new HashMap<>();
        JSONObject basicInfo = new JSONObject();
        basicInfo.put("vid", weiMoConfig.getVid());
        param.put("basicInfo", basicInfo.toJSONString());
        param.put("goodsId", goodsId);

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
//        if (EWmCode.INVALID_ACCESS_TOKEN.getCode().equals(codeJson.getString("errcode"))){
//            String accessTokenKey = CacheConstants.ACCESS_TOKEN_KEY + "WM";
//            redisCache.deleteObject(accessTokenKey);
//            throw new ServiceException("微盟token无效");
//        }
        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return JSON.parseObject(resultJson.getString("data"));
        } else {
            return null;
        }
    }

    @Override
    public JSONObject pullGoodsCategory(Long parentCategoryId) {
        String url = PULL_CATEGORY_URL.replace("ACCESS_TOKEN", getAccessTokenByRedis());
        HashMap<String, Object> param = new HashMap<>();

        JSONObject basicInfo = new JSONObject();
        basicInfo.put("vid", weiMoConfig.getVid());
        param.put("basicInfo", basicInfo.toJSONString());
        param.put("parentCategoryId", parentCategoryId);

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return resultJson.getJSONObject("data");
        } else {
            return null;
        }
    }

    @Override


    public JSONObject pullGoodsCategoryKuspec(Long categoryId, int pageNum) {
        String url = PULL_CATEGORY_SKUSPEC_URL.replace("ACCESS_TOKEN", getAccessTokenByRedis());
        HashMap<String, Object> param = new HashMap<>();

        JSONObject queryParameter = new JSONObject();
        queryParameter.put("categoryId", categoryId);
        param.put("queryParameter", queryParameter);

        JSONObject basicInfo = new JSONObject();
        basicInfo.put("vid", weiMoConfig.getVid());
        param.put("basicInfo", basicInfo.toJSONString());
        param.put("pageNum", pageNum);
        param.put("pageSize", 100);

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return resultJson.getJSONObject("data");
        } else {
            throw new ServiceException(codeJson.toJSONString());
        }
    }

    @Override
    public JSONObject pullGoodsCategoryKuspecVaule(Long specId, int pageNum) {
        String url = PULL_CATEGORY_SKUSPEC_CONTENT_URL.replace("ACCESS_TOKEN", getAccessTokenByRedis());
        HashMap<String, Object> param = new HashMap<>();

        JSONObject queryParameter = new JSONObject();
        queryParameter.put("specId", specId);
        param.put("queryParameter", queryParameter);

        JSONObject basicInfo = new JSONObject();
        basicInfo.put("vid", weiMoConfig.getVid());
        param.put("basicInfo", basicInfo.toJSONString());
        param.put("pageNum", pageNum);
        param.put("pageSize", 100);

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return resultJson.getJSONObject("data");
        } else {
            throw new ServiceException(codeJson.toJSONString());
        }
    }


    /**
     * 获取微盟accessToken
     *
     * @return accessToken
     */
    private JSONObject getAccessToken() {
        String url = GET_ACCESS_TOKEN_URL
                + "?grant_type=" + grantType
                + "&client_id=" + weiMoConfig.getClientId()
                + "&client_secret=" + weiMoConfig.getClientSecret()
                + "&shop_id=" + weiMoConfig.getShopId()
                + "&shop_type=business_operation_system_id";

        String result = HttpUtils.sendPost(url, "");
        if (StringUtils.isEmpty(result)) {
            throw new ServiceException("获取微盟Token失败");
        }
        return JSON.parseObject(result);
    }


//
//    @Override
//    public JSONObject pullWmMarketActivityPage(ActivityPageQueryReq req) {
//        String url = PULL_MARKET_ACTIVITY_URL + "?accesstoken=" + getAccessTokenByRedis();
//
//        HashMap<String, Object> param = new HashMap<>();
//        JSONObject basicInfo = new JSONObject();
//        basicInfo.put("vid", WM_VID);
//        basicInfo.put("vidType", 2);
//        param.put("basicInfo", basicInfo.toJSONString());
//        param.put("pageNum", req.getPageNum() == null ? 1 : req.getPageNum());
//        param.put("pageSize", req.getPageSize() == null ? 20 : req.getPageSize());
//        param.put("queryParameter", req.getQueryParameter());
//
//        String result = HttpPostUtil.post(url, param);
////        log.info("WM&pullWmMarketActivityPage&result={}", result);
//        JSONObject resultJson = JSON.parseObject(result);
//        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
//        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
//            JSONObject dataJson = JSON.parseObject(resultJson.getString("data"));
//            return dataJson;
//        } else {
//            return null;
//        }
//    }

    @Override
    public String getWmGoodsShareUrl(Long goodsId) {
        String url = WEIMOB_DISTRIBUTION_SHARE_GOODS_URL + "?accesstoken=" + getAccessTokenByRedis();
        HashMap<String, Object> param = new HashMap<>();
        JSONObject basicInfo = new JSONObject();
        basicInfo.put("vid", weiMoConfig.getVid());
        param.put("basicInfo", basicInfo.toJSONString());
        param.put("goodsIdList", Collections.singletonList(goodsId));
        param.put("isQueryScene", true);

        JSONObject jsonObject = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(jsonObject.getString("code"));

        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return JSON.parseObject(jsonObject.getString("data"))
                    .getJSONArray("goodsUrlOutputList").getJSONObject(0).getString("miniUrl");
        } else {
            log.error("获取微盟商品推荐链接失败");
            throw new ServiceException(codeJson.getString("errmsg"));
        }
    }

    @Override
    public String getWmSharUrl(String wId, String originUrl) {
        String url = WEIMOB_DISTRIBUTION_SHARE_URL + "?accesstoken=" + getAccessTokenByRedis();
        HashMap<String, Object> param = new HashMap<>();
        JSONArray originUrlList = new JSONArray();
        originUrlList.add(originUrl);
        param.put("originUrlList", originUrlList);
        param.put("tileParamMap", new JSONObject());
        param.put("vid", weiMoConfig.getVid());
        param.put("vidType", 2);
        param.put("type", 2);
        param.put("channel", 1);
        param.put("weikeWid", wId);
        JSONObject jsonObject = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(jsonObject.getString("code"));

        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return JSON.parseObject(jsonObject.getString("data"))
                    .getJSONArray("sdpUrlList").getString(0);
        } else {
            log.error("获取微盟微客推荐链接失败");
            throw new ServiceException(codeJson.getString("errmsg"));
        }
    }


    @Override
    public JSONObject pullRightsDetail(String rightsId) {
        String url = RIGHTS_DETAIL_GET_URL + "?accesstoken=" + getAccessTokenByRedis();

        HashMap<String, Object> param = new HashMap<>();
        param.put("rightsId", rightsId);

        JSONObject jsonObject = HttpUtils.postToJsonObject(url, param);
        return jsonObject.getJSONObject("data");
    }

    @Override
    public String getWmIdByUnionId(String unionId) {
        String url = GET_SUPER_WID_URL + "?accesstoken=" + getAccessTokenByRedis();
        HashMap<String, Object> param = new HashMap<>();
        param.put("source", "2");
        param.put("originalId", unionId);
        JSONObject jsonObject = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(jsonObject.getString("code"));

        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            JSONObject data = jsonObject.getJSONObject("data");
            if (data != null) {
                return data.getString("superWid");
            } else {
                return null;
            }
        } else {
            log.error("获取微盟wid失败,uid= {},error:{}",unionId,codeJson.toJSONString());
            throw new ServiceException(codeJson.getString("errmsg"));
        }
    }
//

//
//
//    @Override
//    public JSONObject getActivityInfo(Long activityId, String activityType) {
//
//        String baseUrl = null;
//
//        if (EActivityType.ACTIVITY_1.getCode().equals(Integer.valueOf(activityType))) {
//            baseUrl = PROMOTION_FULLDISCOUNT_URL;
//        } else if (EActivityType.ACTIVITY_3.getCode().equals(Integer.valueOf(activityType))) {
//            baseUrl = PROMOTION_DISCOUNT_URL;
//        } else {
//            return new JSONObject();
//        }
//
//        String url = baseUrl + "?accesstoken=" + getAccessTokenByRedis();
//
//        HashMap<String, Object> param = new HashMap<>();
//        JSONObject basicInfo = new JSONObject();
//        basicInfo.put("vidType", "2");
//        basicInfo.put("vid", WM_VID);
//        param.put("basicInfo", basicInfo.toJSONString());
//        param.put("activityId", activityId);
//        String result = HttpPostUtil.post(url, param);
//        JSONObject resultJson = JSON.parseObject(result);
//        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
//        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
//            return JSON.parseObject(resultJson.getString("data"));
//        } else {
//            return new JSONObject();
//        }
//    }
//
//    @Override
//    public JSONArray getActivityDiscountGoodsRule(Long activityId) {
//
//        String url = PROMOTION_DISCOUNT_GOODS_URL + "?accesstoken=" + getAccessTokenByRedis();
//
//        HashMap<String, Object> param = new HashMap<>();
//        JSONObject basicInfo = new JSONObject();
//        basicInfo.put("vid", WM_VID);
//        basicInfo.put("vidType", "2");
//        param.put("basicInfo", basicInfo.toJSONString());
//        JSONObject queryParameter = new JSONObject();
//        queryParameter.put("activityId", activityId);
//        queryParameter.put("activityType", 3);
//        param.put("queryParameter", queryParameter);
//        param.put("pageNum", 1);
//        param.put("pageSize", 100);
//
//        String result = HttpPostUtil.post(url, param);
//        JSONObject resultJson = JSON.parseObject(result);
//        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
//        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
//            JSONArray jsonArray = JSON.parseObject(resultJson.getString("data")).getJSONArray("pageList");
//            //补充商品信息
//            for (int i = 0; i < jsonArray.size(); i++) {
//                JSONObject jsonObject = jsonArray.getJSONObject(i);
//                JSONObject goods = pullWMGoodsDetail(jsonObject.getString("goodsId"));
//
//                JSONObject goodsBase = new JSONObject();
//                goodsBase.put("title", goods.getString("title"));
//                goodsBase.put("defaultImageUrl", goods.getString("defaultImageUrl"));
//                goodsBase.put("goodsDesc", goods.getString("goodsDesc"));
//                goodsBase.put("categoryList", goods.getJSONArray("categoryList"));
//                goodsBase.put("skuList", goods.getJSONArray("skuList"));
//
//                jsonObject.put("goodsBase", goodsBase);
//            }
//
//            return jsonArray;
//        } else {
//            return new JSONArray();
//        }
//    }
//
//
//    @Override
//    public boolean couponConsume(Long wid,Long couponTemplateId, String couponCode) {
//        String url = COUPON_CONSUME_URL + "?accesstoken=" + getAccessTokenByRedis();
//
//        Map<String, Object> map = new HashMap<>();
//        map.put("orderAmount", 0.1);
//        map.put("operator", 0);
//        //1- 线上核销 ；2- 线下核销。
//        map.put("useResourceType", 2);
//        map.put("useScene", 7);
//        map.put("wid", wid);
//        map.put("vid", WM_VID);
//
//        JSONObject operateCoupons = new JSONObject();
//        operateCoupons.put("couponTemplateId", couponTemplateId);
//        operateCoupons.put("code", couponCode);
//        map.put("operateCoupons", operateCoupons);
//
//        String result = HttpPostUtil.post(url, map);
//        JSONObject resultJson = JSON.parseObject(result);
//        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
//        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
//            return resultJson.getBoolean("data");
//        } else {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),codeJson.getString("errmsg"));
////            return false;
//        }
//
//    }
//
//    @Override
//    public JSONArray getCouponListByWId(Long wid) {
//        JSONArray list = new JSONArray();
//        String url = COUPON_CUSTOMER_GETLISTURL + "?accesstoken=" + getAccessTokenByRedis();
//        int pageNum = 1;
//        Map<String, Object> map = new HashMap<>();
//        map.put("wid", wid);
//        map.put("pageSize", 20);
//        //待获取列表的优惠券的券码状态。类型包括：1-未使用；2-已使用；3-已过期。
//        map.put("statusRange", Collections.singletonList(1));
//        while (true) {
//            map.put("pageNum", pageNum);
//            String result = HttpPostUtil.post(url, map);
//            JSONObject resultJson = JSON.parseObject(result);
//            JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
//            if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
//                JSONArray jsonArray = JSON.parseObject(resultJson.getString("data")).getJSONArray("pageList");
//                if (CollectionUtils.isEmpty(jsonArray)) {
//                    break;
//                }
//                list.addAll(jsonArray);
//
//                if (jsonArray.size() <= 20) {
//                    break;
//                }
//            } else {
//                break;
//            }
//            pageNum++;
//        }
//        return list;
//    }
//
//    @Override
//    public JSONObject getGoodsClassifyList() {
//        JSONArray goodsClassifyList = new JSONArray();
//        JSONArray systemClassifyList = new JSONArray();
//        String url =    GOODS_CLASSIFY_URL + "?accesstoken=" + getAccessTokenByRedis();
//        int pageNum = 1;
//        Map<String, Object> map = new HashMap<>();
//        map.put("pageSize", 200);
//        JSONObject basicInfo = new JSONObject();
//        basicInfo.put("vid", WM_VID);
//        map.put("basicInfo", basicInfo.toJSONString());
//
//        while (true) {
//            map.put("pageNum", pageNum);
//            String result = HttpPostUtil.post(url, map);
//            JSONObject resultJson = JSON.parseObject(result);
//            JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
//            if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
//                JSONArray jsonArray = JSON.parseObject(resultJson.getString("data")).getJSONArray("goodsClassifyList");
//                JSONArray system = JSON.parseObject(resultJson.getString("data")).getJSONArray("systemClassifyList");
//
//                if (CollectionUtils.isEmpty(jsonArray)) {
//                    break;
//                }
//                goodsClassifyList.addAll(jsonArray);
//                systemClassifyList.addAll(system);
//
//                if (jsonArray.size() <= 200) {
//                    break;
//                }
//            } else {
//                break;
//            }
//            pageNum++;
//        }
//
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("goodsClassifyList",goodsClassifyList);
//        jsonObject.put("systemClassifyList",systemClassifyList);
//        return jsonObject;
//    }
//

//
//
//    @Override
//    public JSONObject getWmGoodsByGoodsIdList(List<Long> goodsIdList) {
//        String url = PULL_GOODS_GETLISTBYID_URL + "?accesstoken=" + getAccessTokenByRedis();
//
//        JSONObject param = new JSONObject();
//        JSONObject basicInfo = new JSONObject();
//        basicInfo.put("vid", WM_VID);
//        param.put("basicInfo", basicInfo);
//        param.put("goodsIdList", goodsIdList);
//
//        String result = HttpPostUtil.post(url, param);
////        log.info("WM&pullWMGoodsPage&result={}", result);
//        JSONObject resultJson = JSON.parseObject(result);
//        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
//        if (!EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
//            return null;
//        }
//        return resultJson;
//    }
//
//


    @Override
    public JSONObject sendPostToPullOrder(WMOrderPageQueryReq req) {
        String url = PULL_ORDER_URL + "?accesstoken=" + getAccessTokenByRedis();

        HashMap<String, Object> param = new HashMap<>();
        if (req.getQueryParameter() == null) {
            WMOrderPageQueryParamReq queryParamReq = new WMOrderPageQueryParamReq();
            req.setQueryParameter(queryParamReq);
        }

        WMOrderPageQueryParamReq queryParamReq = req.getQueryParameter();
        if (queryParamReq.getQueryTime() == null) {
            //默认按创建时间最近30天查询
            WMOrderPageQueryParamTimeReq queryParamTimeReq = new WMOrderPageQueryParamTimeReq();
            //查询20天内订单
            queryParamTimeReq.setStartTime(DateUtils.addDays(new Date(), -20).getTime());
            queryParamTimeReq.setEndTime(System.currentTimeMillis());
            //0-创建时间；1-更新时间；2-支付时间
            queryParamTimeReq.setType(0);
            queryParamReq.setQueryTime(queryParamTimeReq);
        }

        //订单查询范围。不传则只返回订单信息，不会返回订单项信息、履约信息、售后信息。
        //1-订单项信息；2-履约信息；3-售后信息
        List<Integer> orderDomains = new ArrayList<>();
        orderDomains.add(1);
//        orderDomains.add(2);
//        orderDomains.add(3);
        queryParamReq.setOrderDomains(orderDomains);

        req.setQueryParameter(queryParamReq);

        param.put("queryParameter", req.getQueryParameter());
        param.put("pageNum", req.getPageNum() == null ? 1 : req.getPageNum());
        param.put("pageSize", req.getPageSize() == null ? 50 : req.getPageSize());

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return resultJson.getJSONObject("data");
        } else {
            throw new ServiceException(codeJson.toJSONString());
        }

    }

    @Override
    public JSONObject pullOrderDetail(String orderNo) {
        String url = PULL_ORDER_DETAIL_URL + "?accesstoken=" + getAccessTokenByRedis();

        List<Integer> domains = new ArrayList<>();
        domains.add(1);
        domains.add(2);
        domains.add(3);

        HashMap<String, Object> param = new HashMap<>();
        param.put("orderDomains", domains);
        param.put("orderNo", orderNo);

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        return resultJson.getJSONObject("data");
    }
//
//    /**
//     * 返回商品体组装
//     *
//     * @param resList
//     * @return
//     */
//    public List<Goods> changeWmGoodsToGoods(List<WmGoodsPageRes> resList) {
//        List<Goods> goodsList = new ArrayList<>(resList.size());
//        for (WmGoodsPageRes res : resList) {
//            Goods goods = new Goods();
//            goods.setId(res.getGoodsId());
//            goods.setTitle(res.getTitle());
//            goods.setSubTitle(res.getSubTitle());
//            goods.setCreateVid(res.getCreateVid());
//            goods.setDefaultImageUrl(res.getDefaultImageUrl());
//            goods.setGoodsStockNum(res.getGoodsStock().getGoodsStockNum());
//            goods.setMaxSalePrice(res.getGoodsPrice().getMaxSalePrice());
//            goods.setMinSalePrice(res.getGoodsPrice().getMinSalePrice());
//            goods.setRealSaleNum(res.getRealSaleNum());
//            goods.setSoldType(res.getSoldType());
//            goods.setSort(res.getSort());
//            goods.setGoodsType(res.getGoodsType());
//            goods.setSubGoodsType(res.getSubGoodsType());
//            goods.setIsMultiSku(res.getIsMultiSku() != null && res.getIsMultiSku() ? 1 : 0);
//            goods.setIsOnline(res.getIsOnline() != null && res.getIsOnline() ? 1 : 0);
//            goods.setIsCanSell(res.getIsCanSell() != null && res.getIsCanSell() ? 1 : 0);
//            goods.setOnlineTime(new Date(res.getOnlineTime()));
//            goods.setCreateTime(new Date());
//            goods.setUpdateTime(new Date());
//
//            //查询商品详情
//            JSONObject detail = pullWMGoodsDetail(res.getGoodsId().toString());
//            if (detail != null) {
//                goods.setDetailInfo(detail.toJSONString());
//            }
//            goodsList.add(goods);
//        }
//        return goodsList;
//    }
//
//
//    /**
//     * 获取微盟accessToken
//     *
//     * @return accessToken
//     */
//    public String getAccessToken() {
//        HashMap<String, String> param = new HashMap<>();
//        String url = GET_ACCESS_TOKEN_URL
//                + "?grant_type=" + grantType
//                + "&client_id=" + clientId
//                + "&client_secret=" + clientSecret
//                + "&shop_id=" + SHOP_ID
//                + "&shop_type=business_operation_system_id";
//
//        String result = HttpPostUtil.post(url, param);
////        log.info("WM&getAccessToken&result={}", result);
//        if (StringUtils.isEmpty(result)) {
//            return null;
//        }
//        JSONObject resultJson = JSON.parseObject(result);
//        String accessToken = resultJson.getString("access_token");
//        if (StringUtils.isEmpty(accessToken)) {
//            return null;
//        }
//        return accessToken;
//    }

    @Override
    public void pullOrder() {

        WMOrderPageQueryReq req = new WMOrderPageQueryReq();
        int pageNum = 1;
        int pageSize = 50;
        while (true) {
            req.setPageNum(pageNum);
            req.setPageSize(pageSize);
            List<JSONObject> pageList = sendPostToPullOrder(req).getJSONArray("pageList").toList(JSONObject.class);

            omsOrderService.saveWmOrders(pageList);

            if (pageList.size() < 50) {
                break;
            }
            pageNum += 1;
        }
    }

    @Override
    public void pullActivitiesFromWM() {
        final int initialPageNum = 1;
        final int pageSize = 20;
        int pageNum = initialPageNum;
        int activityType = (int) EActivityType.ACTIVITY_TYPE_1.getCode();

        while (true) {
            ActivityPageQueryReq req = buildRequest(pageNum, pageSize, activityType);
            // 发送请求并解析响应的逻辑
            List<JSONObject> pageList = sendPostToPullActivity(req).getJSONArray("pageList").toList(JSONObject.class);
            // 处理数据入库
            this.processPageList(pageList);

            if (pageList.size() < pageSize) {
                if (activityType == EActivityType.ACTIVITY_TYPE_1.getCode()) {
                    // 切换到另一种活动类型
                    activityType = (int) EActivityType.ACTIVITY_TYPE_3.getCode();
                    // 重置页码
                    pageNum = initialPageNum;
                } else {
                    // 所有活动类型的数据都已拉取完毕
                    break;
                }
            } else {
                pageNum += 1; // 继续拉取下一页数据
            }
        }
    }

    /**
     * 构建请求对象
     *
     * @param pageNum      页码
     * @param pageSize     页大小
     * @param activityType 1-限时抢购，2-拼团，3-秒杀
     * @return 请求对象
     */
    private ActivityPageQueryReq buildRequest(int pageNum, int pageSize, int activityType) {
        // 构建请求的逻辑，可以进一步拆分
        ActivityPageQueryReq req = new ActivityPageQueryReq(); // 重用请求对象
        ActivityPageQueryReq.queryParameter queryParameter = req.new queryParameter();
        queryParameter.setActivityType(activityType);

        ActivityPageQueryReq.basicInfo basicInfo = req.new basicInfo();
        basicInfo.setVid(Long.parseLong(weiMoConfig.getVid()));
        basicInfo.setVidType(2);

        req.setPageNum(pageNum);
        req.setPageSize(pageSize);
        req.setQueryParameter(queryParameter);
        req.setBasicInfo(basicInfo);
        return req;
    }


    /**
     * 解析并处理拉取到的数据
     *
     * @param pageList 拉取到的数据
     */
    private void processPageList(List<JSONObject> pageList) {
        // 处理数据入库的逻辑
        for (JSONObject jsonObject : pageList) {
            JSONObject object = new JSONObject();

            WmActivities wmActivities = JSON.parseObject(jsonObject.toJSONString(), WmActivities.class);
            if (jsonObject.getJSONArray("cycleTimeList").isEmpty()) {
                wmActivities.setCycleTimeList(null);
            }
            wmActivities.setSource("1");
            // 时间戳字段翻译
            wmActivities.setCreateTime(wmActivities.getCreateTime());
            // 查询活动详情，存入对应字段
            ActivityPageQueryReq req = buildRequest(0, 0, 0);
            req.setActivityId(wmActivities.getActivityId());
            if (EActivityType.ACTIVITY_TYPE_1.getCode() == wmActivities.getActivityType()) {
                object = sendPostToPullPromotionFullDiscount(req);
                dealPromotionFullDiscountActivity(object, wmActivities);
            } else {
                object = sendPostToPullPromotionDiscountActivity(req);
                dealPromotionPromotionDiscountActivity(object, wmActivities);
            }

            String md5 = Md5Utils.hash(JSON.toJSONString(wmActivities));
            //判断这次的活动信息和上次的是否一致。若不一致，进行推送
            String cacheKey = String.format(CacheConstants.CACHE_ACTIVITIES_SYNC_LIST_V3, wmActivities.getActivityId());
            Object o = redisCache.getCacheObject(cacheKey);
            if (o != null && o.toString().equals(md5)) {
                return;
            }
            redisCache.setCacheObject(cacheKey, md5);

            // 拉取到不一样的活动，判断是走更新还是新增。
            // 判断是不是真的修改 查看商品
            Long id = wmActivitiesService.getIdByActivityId(wmActivities.getActivityId());
            if (id != null) {
                wmActivities.setId(id);
                // 更新活动，生成版本记录，提交版本时若改活动不存在相同类型的待办，则创建待办，否则不创建待办
                wmActivitiesService.updateWmActivities(wmActivities);
            } else {
                // 新增活动，生成版本记录，提交版本时若改活动不存在相同类型的待办，则创建待办，否则不创建待办
                wmActivitiesService.insertWmActivities(wmActivities);
            }

        }
    }

    /**
     * 解析活动详情并存入对应字段-限时折扣活动
     *
     * @param object       拉取到的活动详情
     * @param wmActivities 拉取到的活动信息
     */
    private void dealPromotionPromotionDiscountActivity(JSONObject object, WmActivities wmActivities) {
        WmPromotionDiscountActivity activity = JSON.parseObject(object.toJSONString(), WmPromotionDiscountActivity.class);
        wmActivities.setRefType(String.valueOf(activity.getApplicationGoodsType()));
        if (!activity.getScopeGoodsIdList().isEmpty()) {
            wmActivities.setRefId(activity.getScopeGoodsIdList().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        // 设置活动详情
        wmActivities.setDetails(JSON.toJSONString(activity));
        wmActivities.setRule(JSON.toJSONString(JSON.parseObject(object.toJSONString(), WmActivityRule.class)));
    }

    /**
     * 解析活动详情并存入对应字段-满减满折活动
     *
     * @param object       拉取到的活动详情
     * @param wmActivities 拉取到的活动信息
     */
    private void dealPromotionFullDiscountActivity(JSONObject object, WmActivities wmActivities) {
        WmFullDiscountActivity activity = JSON.parseObject(object.toJSONString(), WmFullDiscountActivity.class);
        // 设置类型
        wmActivities.setRefType(String.valueOf(activity.getApplicationGoodsType()));
        if (!activity.getSubIds().isEmpty()) {
            wmActivities.setRefId(activity.getSubIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        // 设置活动详情
        wmActivities.setDetails(JSON.toJSONString(activity));
        // 活动规则
        wmActivities.setRule(JSON.toJSONString(JSON.parseObject(object.toJSONString(), WmActivityRule.class)));


    }

    /**
     * 发送请求并解析响应,拉取满减满折活动
     *
     * @param req 请求对象
     * @return 响应对象
     */
    @Override
    public JSONObject sendPostToPullPromotionFullDiscount(ActivityPageQueryReq req) {
        String url = PROMOTION_FULLDISCOUNT_URL + "?accesstoken=" + getAccessTokenByRedis();
        HashMap<String, Object> param = new HashMap<>();
        param.put("activityId", req.getActivityId());
        param.put("basicInfo", req.getBasicInfo());

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return resultJson.getJSONObject("data");
        } else {
            throw new ServiceException(codeJson.toJSONString());
        }
    }

    /**
     * 发送请求并解析响应,拉取限时折扣活动
     *
     * @param req 请求对象
     * @return 响应对象
     */
    @Override
    public JSONObject sendPostToPullPromotionDiscountActivity(ActivityPageQueryReq req) {
        String url = PROMOTION_DISCOUNT_URL + "?accesstoken=" + getAccessTokenByRedis();
        HashMap<String, Object> param = new HashMap<>();
        param.put("activityId", req.getActivityId());
        param.put("basicInfo", req.getBasicInfo());

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return resultJson.getJSONObject("data");
        } else {
            throw new ServiceException(codeJson.toJSONString());
        }
    }


    @Override
    public void checkActivityStatus() {
        WmActivities wmActivities = new WmActivities();
        wmActivities.setSource(EActivitySource.MANUAL.getCode());
        // 获取手动创建的所有活动
        List<WmActivities> list = wmActivitiesService.selectWmActivitiesList(wmActivities);
        if (list.isEmpty()) {
            return;
        }
        list.forEach(item -> {
            // 处理未开始的活动
            if (EActivityStatus.NOT_STARTED.getValue() == item.getPromotionStatus().intValue()) {
                dealNotStartedActivity(item);
            }

            // 处理进行中的活动
            if (EActivityStatus.GOING_ON.getValue() == item.getPromotionStatus().intValue()) {
                dealGoingOnActivity(item);
            }
        });
    }

    @Override
    public String getMainWid(String wid) {
        String url = GET_SUPER_WID_URL.concat("?accesstoken=").concat(getAccessTokenByRedis());

        HashMap<String, Object> param = new HashMap<>();
        param.put("wid", Long.valueOf(wid));

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return resultJson.getJSONObject("data").getString("superWid");
        } else {
            throw new ServiceException("获取微盟用户信息失败，请检查微盟编号是否正确!");
        }
    }

    @Override
    public void distributorCreate(Long wid) {
        String url = DISTRIBUTION_CREATE_URL + "?accesstoken=" + getAccessTokenByRedis();

        HashMap<String, Object> param = new HashMap<>();
        param.put("vid", weiMoConfig.getVid());
        param.put("customerWidList", Collections.singletonList(wid));

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));

        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            JSONObject dataJson = JSON.parseObject(resultJson.getString("data"));
            if (dataJson.getInteger("failedCount") > 0) {
                log.error("添加微客失败{}", resultJson.toJSONString());
                throw new ServiceException("添加失败");
            }
        } else {
            log.error("添加微客失败{}", resultJson.toJSONString());
            throw new ServiceException("添加失败");
        }
    }

    @Override
    public JSONArray distributorList(Long wid) {
        String url = DISTRIBUTION_LIST_URL + "?accesstoken=" + getAccessTokenByRedis();

        HashMap<String, Object> param = new HashMap<>();
        param.put("vid", weiMoConfig.getVid());
        param.put("weikeWidList", Collections.singletonList(wid));

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));

        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return JSON.parseObject(resultJson.getString("data")).getJSONArray("responseVOList");
        } else {
            log.error("获取微客信息失败: {}", resultJson.toJSONString());
            throw new ServiceException("获取微客信息失败");
        }
    }

    @Override
    public boolean distributorBind(Long weikeWid, List<Long> customerWidList) {
        String url = DISTRIBUTION_BIND_URL + "?accesstoken=" + getAccessTokenByRedis();

        HashMap<String, Object> param = new HashMap<>();
        param.put("vid", weiMoConfig.getVid());
        param.put("weikeWid", weikeWid);
        param.put("customerWidList", customerWidList);

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));

        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            JSONObject dataJson = JSON.parseObject(resultJson.getString("data"));
            if (dataJson.getInteger("failedCount") > 0) {
                log.error("绑定客户微客关系失败: {}", resultJson.toJSONString());
                return false;
            }
            return true;
        } else {
            log.error("绑定客户微客关系失败: {}", resultJson.toJSONString());
            return false;
        }
    }

    @Override
    public JSONObject customerDistributorGet(Long customerWid, Integer isBind, Boolean verbose) {
        String url = CUSTOMER_DISTRIBUTOR_GET_URL + "?accesstoken=" + getAccessTokenByRedis();

        HashMap<String, Object> param = new HashMap<>();
        param.put("vid", weiMoConfig.getVid());
        param.put("customerWid", customerWid);
        param.put("isBind", isBind);
        param.put("verbose", verbose);

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));

        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return JSON.parseObject(resultJson.getString("data"));
        } else {
            log.error("查询客户-微客关系失败: {}", resultJson.toJSONString());
            throw new ServiceException("查询客户-微客关系失败: ");
        }
    }

    /**
     * 处理进行中的活动
     *
     * @param item 活动对象
     */
    private void dealGoingOnActivity(WmActivities item) {
        if (item.getEndTime().getTime() < System.currentTimeMillis()) {
            // 结束
            item.setPromotionStatus(EActivityStatus.END.getValue().longValue());
            wmActivitiesService.updateWmActivities(item);
        }
    }

    /**
     * 处理未开始的活动
     *
     * @param item 未开始的活动对象
     */
    private void dealNotStartedActivity(WmActivities item) {
        if (item.getStartTime().getTime() < System.currentTimeMillis()) {
            // 开始
            item.setPromotionStatus(EActivityStatus.GOING_ON.getValue().longValue());
            // 走更新，同时发布AI
            wmActivitiesService.updateWmActivities(item);
        }
    }


    /**
     * 发送请求并解析响应
     *
     * @param req 请求对象
     * @return 响应对象
     */
    public JSONObject sendPostToPullActivity(ActivityPageQueryReq req) {
        String url = PULL_MARKET_ACTIVITY_URL + "?accesstoken=" + getAccessTokenByRedis();

        HashMap<String, Object> param = new HashMap<>();
        param.put("basicInfo", req.getBasicInfo());
        param.put("pageNum", req.getPageNum() == null ? 1 : req.getPageNum());
        param.put("pageSize", req.getPageSize() == null ? 20 : req.getPageSize());
        param.put("queryParameter", req.getQueryParameter());

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return resultJson.getJSONObject("data");
        } else {
            throw new ServiceException(codeJson.toJSONString());
        }
    }

    public JSONObject crmCustomerGetList(List<Long> widList) {
        String url = CRM_CUSTOMER_GETLIST + "?accesstoken=" + getAccessTokenByRedis();

        HashMap<String, Object> param = new HashMap<>();
        param.put("vid", weiMoConfig.getVid());
        param.put("widList", widList);
        param.put("resultType", new int[]{1});

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return resultJson.getJSONObject("data");
        } else {
            throw new ServiceException(codeJson.toJSONString());
        }
    }

    public JSONObject shopRightsList(int pageNum, int pageSize, Map<String, Object> queryParameter) {
        String url = RIGHTS_LIST_URL + "?accesstoken=" + getAccessTokenByRedis();

        HashMap<String, Object> param = new HashMap<>();
        param.put("vid", weiMoConfig.getVid());
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("queryParameter", queryParameter);

        JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
        JSONObject codeJson = JSON.parseObject(resultJson.getString("code"));
        if (EWmCode.SUCCESS.getCode().equals(codeJson.getString("errcode"))) {
            return resultJson.getJSONObject("data");
        } else {
            throw new ServiceException(codeJson.toJSONString());
        }
    }

}
