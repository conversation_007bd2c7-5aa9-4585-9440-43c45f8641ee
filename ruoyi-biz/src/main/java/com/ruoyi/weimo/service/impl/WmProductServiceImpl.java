package com.ruoyi.weimo.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.constant.WmConstants;
import com.ruoyi.goods.service.IPmsGoodsService;
import com.ruoyi.weimo.domain.dto.MsgBodyCustomer;
import com.ruoyi.weimo.domain.dto.MsgBodyProduct;
import com.ruoyi.weimo.domain.dto.WmMessage;
import com.ruoyi.weimo.domain.vo.WmGoodsPageRes;
import com.ruoyi.weimo.service.IWmProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * Description: new java files header..
 *
 * <AUTHOR>
 * @date 2024/8/22 15:02
 */
@Service
@Slf4j
public class WmProductServiceImpl implements IWmProductService {
    @Resource
    private IPmsGoodsService pmsGoodsService;

    @Override
    public void handleTopic(WmMessage wmMessage) {
        MsgBodyProduct msgBodyProduct = JSONObject.parseObject((String) wmMessage.getMsgBody(), MsgBodyProduct.class);
        log.info("handle product topic, event: {}", wmMessage.getEvent());
        switch (wmMessage.getEvent()) {

            case WmConstants.Event.CREATE:
            case WmConstants.Event.UPDATE:
                // 处理商城商品创建和更新
                pmsGoodsService.createOrUpdateWmGoods(new WmGoodsPageRes(){{
                    setGoodsId(msgBodyProduct.getGoodsId());
                }});
                break;
            case WmConstants.Event.DELETE:
                break;
            default:
                break;
        }

    }
}
