package com.ruoyi.weimo.service.impl;

import java.util.*;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import javax.annotation.Resource;

import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.weimo.domain.dto.WmGoodsPageQueryParamReq;
import com.ruoyi.weimo.domain.dto.WmGoodsPageReq;
import com.ruoyi.weimo.domain.vo.WmGoodsPageRes;
import com.ruoyi.weimo.service.IWeiMobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ruoyi.weimo.mapper.WmGoodsMapper;
import com.ruoyi.weimo.domain.WmGoods;
import com.ruoyi.weimo.service.IWmGoodsService;
import org.springframework.util.CollectionUtils;

/**
 * 微盟商品Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-26
 */
@Service
@Slf4j
@Deprecated
public class WmGoodsServiceImpl implements IWmGoodsService
{
    @Resource
    private WmGoodsMapper wmGoodsMapper;


    @Resource
    private IWeiMobService weiMobService;

    @Resource
    private RedisCache redisCache;

    /**
     * 查询微盟商品
     *
     * @param id 微盟商品主键
     * @return 微盟商品
     */
    @Override
    public WmGoods selectWmGoodsById(Long id)
    {
        return wmGoodsMapper.selectWmGoodsById(id);
    }

    /**
     * 查询微盟商品列表
     *
     * @param wmGoods 微盟商品
     * @return 微盟商品
     */
    @Override
    public List<WmGoods> selectWmGoodsList(WmGoods wmGoods)
    {
        return wmGoodsMapper.selectWmGoodsList(wmGoods);
    }

    /**
     * 新增微盟商品
     *
     * @param wmGoods 微盟商品
     * @return 结果
     */
    @Override
    public int insertWmGoods(WmGoods wmGoods)
    {
        wmGoods.setCreateTime(DateUtils.getNowDate());
        return wmGoodsMapper.insertWmGoods(wmGoods);
    }

    /**
     * 修改微盟商品
     *
     * @param wmGoods 微盟商品
     * @return 结果
     */
    @Override
    public int updateWmGoods(WmGoods wmGoods)
    {
        wmGoods.setUpdateTime(DateUtils.getNowDate());
        return wmGoodsMapper.updateWmGoods(wmGoods);
    }

    /**
     * 批量删除微盟商品
     *
     * @param ids 需要删除的微盟商品主键
     * @return 结果
     */
    @Override
    public int deleteWmGoodsByIds(Long[] ids)
    {
        return wmGoodsMapper.deleteWmGoodsByIds(ids);
    }

    /**
     * 删除微盟商品信息
     *
     * @param id 微盟商品主键
     * @return 结果
     */
    @Override
    public int deleteWmGoodsById(Long id)
    {
        return wmGoodsMapper.deleteWmGoodsById(id);
    }

    @Override
    public void pullWMGoodsPage() {
        WmGoodsPageReq req = new WmGoodsPageReq();
        WmGoodsPageQueryParamReq queryParamReq = new WmGoodsPageQueryParamReq();
        queryParamReq.setEndUpdateTime(System.currentTimeMillis());
        req.setPageNum(1);
        req.setPageSize(20);
        req.setQueryParameter(queryParamReq);
        int pageNum = 1;

        while (true) {
            req.setPageNum(pageNum);
            List<WmGoodsPageRes> list = weiMobService.pullAllWeiMoGoods(req);
            if (CollectionUtils.isEmpty(list)) {
                return ;
            }
            for (WmGoodsPageRes res : list) {
                createOrUpdateWmGoods(res);
            }
            pageNum += 1;
        }
    }

    private void createOrUpdateWmGoods(WmGoodsPageRes res) {
//        JSONObject detail = weiMobService.pullWMGoodsDetail(res.getGoodsId().toString());
//        WmGoods  goods = new WmGoods();
//        goods.setId(res.getGoodsId());
//        goods.setTitle(res.getTitle());
//        goods.setSubTitle(res.getSubTitle());
//        goods.setDefaultImageUrl(res.getDefaultImageUrl());
//        goods.setGoodsStockNum(res.getGoodsStock().getGoodsStockNum());
//        goods.setMaxSalePrice(res.getGoodsPrice().getMaxSalePrice());
//        goods.setMinSalePrice(res.getGoodsPrice().getMinSalePrice());
//        goods.setRealSaleNum(res.getRealSaleNum());
//        goods.setSoldType(res.getSoldType());
//        goods.setSort(res.getSort());
//        goods.setIsMultiSku(res.getIsMultiSku() != null && res.getIsMultiSku() ? 1L : 0L);
//        goods.setIsOnline(res.getIsOnline() != null && res.getIsOnline() ? 1L : 0L);
//        goods.setIsCanSell(res.getIsCanSell() != null && res.getIsCanSell() ? 1L : 0L);
//        goods.setUpdateTime(new Date());
//        goods.setDetailInfo(detail.toJSONString());
//        JSONArray categoryList = detail.getJSONArray("categoryList");
//        goods.setCategory(categoryList.getJSONObject(0).getLong("categoryId"));
//        goods.setSubCategory(categoryList.getJSONObject(1).getLong("categoryId"));
//
//        List<String> skuIdList = new ArrayList<>();
//        JSONArray list = detail.getJSONArray("skuList");
//        for (int i = 0; i < list.size(); i++) {
//            skuIdList.add(list.getJSONObject(i).getString("skuId"));
//        }
//        goods.setSkuList(StringUtils.join(skuIdList, ","));
//        wmGoodsMapper.insertWmGoods(goods);
//
//        //开始检查商品是否需要同步，检查商品库是否配置了
//        BizGoods bizGoods = new BizGoods();
//        BeanUtils.copyBeanProp(bizGoods,goods);
//        bizGoods.setId(IdUtils.generator());
//        bizGoods.setOuterId(goods.getId().toString());
//        bizGoods.setPlatform(EGoodsPlatform.WEIMO.getCode());
//        bizGoods.setSkuJson(list.toJSONString());
//        bizGoodsService.insertBizGoods(bizGoods);
//
//        BizGoods oldGoods = bizGoodsService.selectBizGoodsByOuterId(EGoodsPlatform.WEIMO.getCode(), goods.getId().toString());
//        if (StringUtils.isBlank(oldGoods.getCustomName()) && StringUtils.isBlank(oldGoods.getFabe())){
//            return;
//        }
//
//        JSONObject fabe = JSONObject.parseObject(oldGoods.getFabe());
//
//        //开始通知逻辑
//        detail.put("customName", oldGoods.getCustomName());
//        detail.put("FABE", fabe.getString("FABE"));
//        detail.put("goodsId",oldGoods.getId());
//        detail.put("platform",EGoodsPlatform.WEIMO.getCode());
//
//        JSONArray jsonArray = fabe.getJSONArray("skuList");
//        JSONObject sku = new JSONObject();
//        for (int i = 0; i < jsonArray.size(); i++) {
//            sku.put(String.valueOf(jsonArray.getJSONObject(i).getLong("skuId")), jsonArray.getJSONObject(i).getString("FABE"));
//        }
//
//        for (int i = 0; i < list.size(); i++) {
//            String fabex = sku.getString(list.getJSONObject(i).getString("skuId"));
//            list.getJSONObject(i).put("FABE", StringUtils.isNotBlank(fabex) ? fabex : "");
//        }
//
//        String md5 = Md5Utils.hash(detail.toJSONString());
//
//        //判断这次的商品信息和上次的是否一致。若不一致，进行推送
//        String cacheKey = String.format(CacheConstants.CACHE_WM_GOODS_INFO_LIST, oldGoods.getId());
//        Object o = redisCache.getCacheObject(cacheKey);
//        if (o != null && o.toString().equals(md5)) {
//            return;
//        }
//        redisCache.setCacheObject(cacheKey, md5);
//
//        if (!detail.getBoolean("isOnline") || !detail.getBoolean("isCanSell")) {
//            sendDeleteGoodsToAlex(Collections.singletonList(oldGoods.getId()));
//            return;
//        }
//
//        HashMap<String, Object> map = new HashMap<>();
//        map.put("event",  "update");
//        //组装所需数据
//        detail.put("FABE", StringUtils.isNotBlank(detail.getString("FABE")) ? detail.getString("FABE") : "");
//        for (int i = 0; i < list.size(); i++) {
//            list.getJSONObject(i).put("inventoryStatus", "0");
//            if (list.getJSONObject(i).getInteger("skuStockNum") <= 10) {
//                list.getJSONObject(i).put("inventoryStatus", "1");
//                if (list.getJSONObject(i).getInteger("skuStockNum") <= 0) {
//                    list.getJSONObject(i).put("inventoryStatus", "2");
//                }
//            }
//        }
//        detail.put("skuList", list);
//        map.put("info", detail);
//
//        JSONObject result = HttpUtils.getJsonObject("wmAiNotifyurl", map);
//        log.info("通知 Alex商品信息返回:{},商品信息：{}", result, JSON.toJSONString(map));
    }

    private void sendDeleteGoodsToAlex(List<Long> goodsIdList) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("event", "delete");
        map.put("goodsIdList", goodsIdList);
        //todo 连接对接，失败报警
        JSONObject result = HttpUtils.postToJsonObject("wmAiNotifyurl", map);
        log.info("通知 Alex商品删除:{}", result.toJSONString());

        //todo 语料库删除
        for (Long id : goodsIdList) {
            //删除该商品的语料库
//            corpusDataService.removeByMetadate(id);
        }

    }
}
