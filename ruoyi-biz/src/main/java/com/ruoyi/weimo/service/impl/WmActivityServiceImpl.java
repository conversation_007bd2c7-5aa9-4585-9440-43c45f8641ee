package com.ruoyi.weimo.service.impl;

import java.util.HashMap;
import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;

import javax.annotation.Resource;

import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.weimo.domain.dto.WmAvtivityDto;
import com.ruoyi.weimo.service.IWeiMobService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.ruoyi.weimo.mapper.WmActivityMapper;
import com.ruoyi.weimo.domain.WmActivity;
import com.ruoyi.weimo.service.IWmActivityService;

/**
 * 微盟行为Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Service
public class WmActivityServiceImpl implements IWmActivityService {
    @Resource
    private WmActivityMapper wmActivityMapper;
    @Resource
    IWeiMobService iWeiMobService;


    /**
     * 查询微盟行为
     *
     * @param id 微盟行为主键
     * @return 微盟行为
     */
    @Override
    public WmActivity selectWmActivityById(Long id) {
        return wmActivityMapper.selectWmActivityById(id);
    }

    /**
     * 查询微盟行为列表
     *
     * @param wmActivity 微盟行为
     * @return 微盟行为
     */
    @Override
    public List<WmActivity> selectWmActivityList(WmActivity wmActivity) {
        return wmActivityMapper.selectWmActivityList(wmActivity);
    }

    /**
     * 新增微盟行为
     *
     * @param wmActivity 微盟行为
     * @return 结果
     */
    @Override
    public int insertWmActivity(WmActivity wmActivity) {
        wmActivity.setCreateTime(DateUtils.getNowDate());
        return wmActivityMapper.insertWmActivity(wmActivity);
    }

    /**
     * 修改微盟行为
     *
     * @param wmActivity 微盟行为
     * @return 结果
     */
    @Override
    public int updateWmActivity(WmActivity wmActivity) {
        return wmActivityMapper.updateWmActivity(wmActivity);
    }

    /**
     * 批量删除微盟行为
     *
     * @param ids 需要删除的微盟行为主键
     * @return 结果
     */
    @Override
    public int deleteWmActivityByIds(Long[] ids) {
        return wmActivityMapper.deleteWmActivityByIds(ids);
    }

    /**
     * 删除微盟行为信息
     *
     * @param id 微盟行为主键
     * @return 结果
     */
    @Override
    public int deleteWmActivityById(Long id) {
        return wmActivityMapper.deleteWmActivityById(id);
    }

    @Override
    public void pullWmAvtivity(WmAvtivityDto wmAvtivityDto) {
        try {
            String accessTokenByRedis = iWeiMobService.getAccessTokenByRedis();
            String url = "https://dopen.weimob.com/apigw/weimob_bi/v2.0/user/behaviors/original/get?accesstoken=" + accessTokenByRedis;
            HashMap<String, Object> param = new HashMap<>();
            param.put("startTime", wmAvtivityDto.getStartTime());
            param.put("endTime", wmAvtivityDto.getEndTime());
            param.put("limit", 1000);
            JSONObject resultJson = HttpUtils.postToJsonObject(url, param);
            if (resultJson.getJSONObject("code").getString("errmsg").equals("success")) {
                ObjectMapper objectMapper = new ObjectMapper();
                List<WmActivity> wmActivityList = objectMapper.readValue(resultJson.getJSONObject("data").getJSONObject("data").getString("list"), new TypeReference<List<WmActivity>>() {
                });
                //批量插入
                wmActivityMapper.batchInsertWmActivity(wmActivityList);
            }else{
                System.out.println("获取数据失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
