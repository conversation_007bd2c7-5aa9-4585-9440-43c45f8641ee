package com.ruoyi.weimo.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.ruoyi.biz.domain.BizSalesPitch;
import com.ruoyi.biz.domain.CorpusDocument;
import com.ruoyi.goods.domain.PmsGoods;
import com.ruoyi.goods.domain.vo.ActivityRefVo;
import com.ruoyi.weimo.domain.WmActivities;
import com.ruoyi.weimo.domain.req.ActivityCorpusReq;
import com.ruoyi.weimo.domain.vo.WmActivitiesVo;

/**
 * 活动管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-25
 */
public interface IWmActivitiesService 
{
    /**
     * 查询活动管理
     * 
     * @param id 活动管理主键
     * @return 活动管理
     */
    public WmActivities selectWmActivitiesById(Long id);

    /**
     * 查询活动管理列表
     * 
     * @param wmActivities 活动管理
     * @return 活动管理集合
     */
    public List<WmActivities> selectWmActivitiesList(WmActivities wmActivities);

    /**
     * 新增活动管理
     * 
     * @param wmActivities 活动管理
     * @return 结果
     */
    public int insertWmActivities(WmActivities wmActivities);

    /**
     * 修改活动管理
     * 
     * @param wmActivities 活动管理
     * @return 结果
     */
    public int updateWmActivities(WmActivities wmActivities);

    /**
     * 批量删除活动管理
     * 
     * @param ids 需要删除的活动管理主键集合
     * @return 结果
     */
    public int deleteWmActivitiesByIds(Long[] ids);

    /**
     * 删除活动管理信息
     * 
     * @param id 活动管理主键
     * @return 结果
     */
    public int deleteWmActivitiesById(Long id);

    /**
     * 根据活动id获取id
     * @param activityId 活动id
     * @return id
     */
    Long getIdByActivityId(Long activityId);

    /**
     * 获取活动相关商品
     * @param id 活动id
     * @return 返回活动相关商品
     */
    ActivityRefVo getActivityRefProducts(Long id);

    /**
     * 保存活动语料
     * @param req 请求参数
     * @return 返回结果
     */
    int saveActivityCorpus(ActivityCorpusReq req);

    /**
     * 批量获取活动
     * @param activityIds 活动id集合
     * @return 返回活动
     */
    Map<Long, WmActivities> selectWmActivitiesByIds(Set<Long> activityIds);

    /**
     * 获取活动语料
     * @param id 活动id
     * @return 返回活动语料
     */
    List<CorpusDocument> getActivityCorpus(Long id);

    /**
     * 获取活动朋友圈话术
     * @param id 活动id
     * @return 返回活动朋友圈话术
     */
    List<BizSalesPitch> getActivityMoments(Long id);

    /**
     * 根据商品id获取活动
     * @param goods 商品
     * @return 返回活动
     */
    List<WmActivitiesVo> getActivitiesByProductId(PmsGoods goods);

    /**
     * 根据活动id获取活动
     * @param activityIds 活动id集合
     * @return 返回活动
     */
    List<WmActivitiesVo> getActivitiesByActivityIds(List<Long> activityIds);

    /**
     * 修改活动ai复购状态
     *
     * @param activityIds activityIds
     * @param status 状态: 0 关闭. 1 开启
     * @return rows
     */
    int updateAiRepurchase(List<Long> activityIds, Integer status);

    /**
     * 获取当前活动id
     * @return
     */
    List<Long> getOngoingActivityIds();
}
