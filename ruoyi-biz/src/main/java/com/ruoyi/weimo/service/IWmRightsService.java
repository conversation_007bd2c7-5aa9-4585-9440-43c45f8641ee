package com.ruoyi.weimo.service;

import com.ruoyi.weimo.domain.WmRights;
import com.ruoyi.weimo.domain.dto.WmMessage;

import java.util.List;

/**
 * 微盟售后管理Service接口
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
public interface IWmRightsService {

    /**
     * 处理微盟相关消息
     *
     * @param wmMessage wm消息信息
     */
    void handleTopic(WmMessage wmMessage);

    /**
     * 查询微盟售后管理
     *
     * @param rightsId 微盟售后管理主键
     * @return 微盟售后管理
     */
    public WmRights selectWmRightsByRightsId(Long rightsId);

    /**
     * 查询微盟售后管理列表
     *
     * @param wmRights 微盟售后管理
     * @return 微盟售后管理集合
     */
    public List<WmRights> selectWmRightsList(WmRights wmRights);

    /**
     * 新增微盟售后管理
     *
     * @param wmRights 微盟售后管理
     * @return 结果
     */
    public int insertWmRights(WmRights wmRights);

    /**
     * 修改微盟售后管理
     *
     * @param wmRights 微盟售后管理
     * @return 结果
     */
    public int updateWmRights(WmRights wmRights);

    /**
     * 批量删除微盟售后管理
     *
     * @param rightsIds 需要删除的微盟售后管理主键集合
     * @return 结果
     */
    public int deleteWmRightsByRightsIds(Long[] rightsIds);

    /**
     * 删除微盟售后管理信息
     *
     * @param rightsId 微盟售后管理主键
     * @return 结果
     */
    public int deleteWmRightsByRightsId(Long rightsId);


    /**
     * 扫描未处理的微盟售后管理
     * @return
     */
    public void scanWmRightsNotProcess();

    /**
     * 同步微盟售后订单
     */
    public void pullWmRights();

}
