package com.ruoyi.weimo.service;

import java.util.List;
import com.ruoyi.weimo.domain.WmGoods;

/**
 * 微盟商品Service接口
 *
 * <AUTHOR>
 * @date 2024-03-26
 */
@Deprecated
public interface IWmGoodsService
{
    /**
     * 查询微盟商品
     *
     * @param id 微盟商品主键
     * @return 微盟商品
     */
    public WmGoods selectWmGoodsById(Long id);

    /**
     * 查询微盟商品列表
     *
     * @param wmGoods 微盟商品
     * @return 微盟商品集合
     */
    public List<WmGoods> selectWmGoodsList(WmGoods wmGoods);

    /**
     * 新增微盟商品
     *
     * @param wmGoods 微盟商品
     * @return 结果
     */
    public int insertWmGoods(WmGoods wmGoods);

    /**
     * 修改微盟商品
     *
     * @param wmGoods 微盟商品
     * @return 结果
     */
    public int updateWmGoods(WmGoods wmGoods);

    /**
     * 批量删除微盟商品
     *
     * @param ids 需要删除的微盟商品主键集合
     * @return 结果
     */
    public int deleteWmGoodsByIds(Long[] ids);

    /**
     * 删除微盟商品信息
     *
     * @param id 微盟商品主键
     * @return 结果
     */
    public int deleteWmGoodsById(Long id);

    /**
     * 拉取微盟商品
     */
    void pullWMGoodsPage();
}
