package com.ruoyi.weimo.service;

import java.util.List;

import com.ruoyi.weimo.domain.WmActivity;
import com.ruoyi.weimo.domain.dto.WmAvtivityDto;

/**
 * 微盟行为Service接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
public interface IWmActivityService {
    /**
     * 查询微盟行为
     *
     * @param id 微盟行为主键
     * @return 微盟行为
     */
    public WmActivity selectWmActivityById(Long id);

    /**
     * 查询微盟行为列表
     *
     * @param wmActivity 微盟行为
     * @return 微盟行为集合
     */
    public List<WmActivity> selectWmActivityList(WmActivity wmActivity);

    /**
     * 新增微盟行为
     *
     * @param wmActivity 微盟行为
     * @return 结果
     */
    public int insertWmActivity(WmActivity wmActivity);

    /**
     * 修改微盟行为
     *
     * @param wmActivity 微盟行为
     * @return 结果
     */
    public int updateWmActivity(WmActivity wmActivity);

    /**
     * 批量删除微盟行为
     *
     * @param ids 需要删除的微盟行为主键集合
     * @return 结果
     */
    public int deleteWmActivityByIds(Long[] ids);

    /**
     * 删除微盟行为信息
     *
     * @param id 微盟行为主键
     * @return 结果
     */
    public int deleteWmActivityById(Long id);

    void pullWmAvtivity(WmAvtivityDto   wmAvtivityDto);
}
