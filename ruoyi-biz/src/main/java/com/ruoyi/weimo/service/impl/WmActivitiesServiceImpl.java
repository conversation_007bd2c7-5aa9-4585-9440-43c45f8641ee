package com.ruoyi.weimo.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.biz.domain.BizSalesPitch;
import com.ruoyi.biz.domain.BizVersionRecord;
import com.ruoyi.biz.domain.CorpusDocument;
import com.ruoyi.biz.domain.req.GeneratingReq;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.biz.enums.EDatasetsEnum;
import com.ruoyi.biz.enums.EGenerateType;
import com.ruoyi.biz.enums.EVersionRefType;
import com.ruoyi.biz.service.IBizSalesPitchService;
import com.ruoyi.biz.service.IBizVersionRecordService;
import com.ruoyi.biz.service.ICorpusDocumentService;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.ConfigConstants;
import com.ruoyi.common.constant.ProductConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.EVersionEvent;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.goods.domain.PmsCategory;
import com.ruoyi.goods.domain.PmsGoods;
import com.ruoyi.goods.domain.vo.ActivityRefVo;
import com.ruoyi.goods.mapper.PmsGoodsMapper;
import com.ruoyi.goods.service.IPmsCategoryService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.weimo.domain.WmActivities;
import com.ruoyi.weimo.domain.req.ActivityCorpusReq;
import com.ruoyi.weimo.domain.vo.WmActivitiesVo;
import com.ruoyi.weimo.enums.EActivityRefType;
import com.ruoyi.weimo.enums.EActivityStatus;
import com.ruoyi.weimo.enums.EActivityType;
import com.ruoyi.weimo.mapper.WmActivitiesMapper;
import com.ruoyi.weimo.service.IWmActivitiesService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 活动管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@Service
@Slf4j
public class WmActivitiesServiceImpl implements IWmActivitiesService {
    @Resource
    private WmActivitiesMapper wmActivitiesMapper;

    @Resource
    private PmsGoodsMapper pmsGoodsMapper;

    @Resource
    private IPmsCategoryService pmsCategoryService;

    @Resource
    private ICorpusDocumentService corpusDocumentService;

    @Resource
    private RedisCache redisCache;

    @Resource
    private IBizVersionRecordService bizVersionRecordService;

    @Resource
    private IBizSalesPitchService bizSalesPitchService;

    @Resource
    private ISysConfigService sysConfigService;

    /**
     * 查询活动管理
     *
     * @param id 活动管理主键
     * @return 活动管理
     */
    @Override
    public WmActivities selectWmActivitiesById(Long id) {
        // 获取该活动关联的商品/类目等信息
        WmActivities wmActivities = wmActivitiesMapper.selectWmActivitiesById(id);
        wmActivities.setActivityRef(this.getActivityRefProducts(id));
        return wmActivities;
    }

    /**
     * 查询活动管理列表
     *
     * @param wmActivities 活动管理
     * @return 活动管理
     */
    @Override
    public List<WmActivities> selectWmActivitiesList(WmActivities wmActivities) {
        List<WmActivities> list = wmActivitiesMapper.selectWmActivitiesList(wmActivities);
        list.forEach(item -> {
            // 判断是否存在语料
            item.setCorpusDocumentId(this.getActivityCorpus(item.getId()).isEmpty() ? null : this.getActivityCorpus(item.getId()).get(0).getId());
            // 图片翻译
            item.setPromotionImage(StringUtils.isNotBlank(item.getPromotionImage()) ? item.getPromotionImage().startsWith("//") ? "https:" + item.getPromotionImage() : item.getPromotionImage() : null);
        });
        return list;
    }

    /**
     * 新增活动管理
     *
     * @param wmActivities 活动管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertWmActivities(WmActivities wmActivities) {
        if (wmActivities.getCreateTime() == null) {
            wmActivities.setCreateTime(DateUtils.getNowDate());
        }

        // 生成活动ID
        if (wmActivities.getActivityId() == null) {
            wmActivities.setActivityId(IdUtils.generator());
        }

//        wmActivities.setCreateBy();
        int i = wmActivitiesMapper.insertWmActivities(wmActivities);
        // 判断活动状态是否为已结束/删除/暂停
        if (wmActivities.getPromotionStatus() != null &&
                (wmActivities.getPromotionStatus().intValue() == EActivityStatus.END.getValue() ||
                        wmActivities.getPromotionStatus().intValue() == EActivityStatus.DELETE.getValue() ||
                        wmActivities.getPromotionStatus().intValue() == EActivityStatus.PAUSE.getValue())) {
            // 设置AI导购推荐为否
            wmActivities.setIsAiPurchaseGuide(Integer.parseInt(EBoolean.NO.getCode()));

            // 生成版本记录
            sendActivityToAlex(wmActivities.getId(), true);
        }
        return i;
    }

    /**
     * 根据活动id查询活动信息
     *
     * @param id 活动id
     * @return
     */
    private WmActivitiesVo querySimpleActivityById(Long id) {
        WmActivities wmActivities = wmActivitiesMapper.selectWmActivitiesById(id);
        if (wmActivities == null) {
            log.warn("activity does not exist, activity id: {}", id);
            return null;
        }
        wmActivities.setActivityRef(this.getActivityRefProducts(id));
        // TODO 类型转换
        // wmActivities转成WmActivitiesVo
        WmActivitiesVo wmActivitiesVo = new WmActivitiesVo();
        BeanUtils.copyProperties(wmActivities, wmActivitiesVo);
        transaction(wmActivitiesVo, wmActivities);

        return wmActivitiesVo;
    }

    /**
     * 常量翻译
     *
     * @param wmActivitiesVo wmActivitiesVo
     * @param wmActivities   wmActivities
     */
    public static void transaction(WmActivitiesVo wmActivitiesVo, WmActivities wmActivities) {
        wmActivitiesVo.setActivityTypeStr(wmActivities.getActivityType() != null ? EActivityType.getByCode(wmActivities.getActivityType()).getInfo() : "");
        wmActivitiesVo.setIsPromotionLongTimeStr(wmActivities.getIsPromotionLongTime() != null && wmActivities.getIsPromotionLongTime().equals(1L) ? "是" : "否");
        wmActivitiesVo.setPromotionStatusStr(wmActivities.getPromotionStatus() != null ? EActivityStatus.getByValue(wmActivitiesVo.getPromotionStatus().intValue()).getDesc() : "");
        // 活动相关商品
        if (wmActivities.getActivityRef() != null) {
            ActivityRefVo activityRef = wmActivities.getActivityRef();
            StringBuilder build = new StringBuilder();
            if (EActivityRefType.ALL.getCode().equals(activityRef.getRefType())) {
                wmActivitiesVo.setRefName(EActivityRefType.ALL.getDesc());
            } else if (EActivityRefType.DEFAULT.getCode().equals(activityRef.getRefType()) || EActivityRefType.PART_PRODUCT.getCode().equals(activityRef.getRefType())) {
                // 判断活动是否为空。
                if (!CollectionUtils.isEmpty(activityRef.getRefProducts())) {
                    build.append(activityRef.getRefProducts().stream().map(PmsGoods::getName).collect(Collectors.joining("、"))).append(";");
                    wmActivitiesVo.setRefName(StringUtils.isNotBlank(build.toString()) ? build.toString() : "暂无商品");
                }
            } else if (EActivityRefType.PART_CATEGORY.getCode().equals(activityRef.getRefType())) {
                if (!CollectionUtils.isEmpty(activityRef.getRefCategories())) {
                    build.append(activityRef.getRefCategories().stream().map(PmsCategory::getName).collect(Collectors.joining("、"))).append(";");
                    wmActivitiesVo.setRefName(StringUtils.isNotBlank(build.toString()) ? build.toString() : "暂无类目");
                }
            }
        } else {
            wmActivitiesVo.setRefName("暂无商品/类目");
        }
    }

    /**
     * 修改活动管理
     *
     * @param wmActivities 活动管理
     * @return 结果
     */
    @Override
    public int updateWmActivities(WmActivities wmActivities) {
        wmActivities.setUpdateTime(DateUtils.getNowDate());

        // 判断活动状态是否为已结束/删除/暂停
        if (wmActivities.getPromotionStatus() != null &&
                (wmActivities.getPromotionStatus().intValue() == EActivityStatus.END.getValue() ||
                        wmActivities.getPromotionStatus().intValue() == EActivityStatus.DELETE.getValue() ||
                        wmActivities.getPromotionStatus().intValue() == EActivityStatus.PAUSE.getValue())) {
            // 设置AI导购推荐为否
            wmActivities.setIsAiPurchaseGuide(Integer.parseInt(EBoolean.NO.getCode()));
            // 生成版本记录
            sendActivityToAlex(wmActivities.getId(), true);
        }
        // 增加了AI导购推荐来判断是否需要通知AI活动
//        else {
//            // 生成版本记录
//            sendActivityToAlex(wmActivities.getId(), false);
//        }
        return wmActivitiesMapper.updateWmActivities(wmActivities);
    }

    /**
     * 批量删除活动管理
     *
     * @param ids 需要删除的活动管理主键
     * @return 结果
     */
    @Override
    public int deleteWmActivitiesByIds(Long[] ids) {
        //通知AI活动
        Arrays.stream(ids).forEach(id -> sendActivityToAlex(id, true));
        return wmActivitiesMapper.deleteWmActivitiesByIds(ids);
    }

    /**
     * 删除活动管理信息
     *
     * @param id 活动管理主键
     * @return 结果
     */
    @Override
    public int deleteWmActivitiesById(Long id) {
        // 通知AI活动
        sendActivityToAlex(id, true);
        return wmActivitiesMapper.deleteWmActivitiesById(id);
    }

    @Override
    public Long getIdByActivityId(Long activityId) {
        return wmActivitiesMapper.getIdByActivityId(activityId);
    }

    @Override
    public ActivityRefVo getActivityRefProducts(Long id) {
        if (id == null) {
            log.warn("activity id is null!");
            return null;
        }
        WmActivities wmActivities = wmActivitiesMapper.selectWmActivitiesById(id);
        if (wmActivities == null) {
            log.warn("activity does not exist, activity id: {}", id);
            return null;
        }

        if (wmActivities.getRefId() == null) {
            log.warn("activity ref id is null, activity id: {}", id);
            return null;
        }

        if (wmActivities.getRefType() == null) {
            log.warn("activity ref type is null, activity id: {}", id);
            return null;
        }
        ActivityRefVo activityRefVo = new ActivityRefVo();
        // 获取关联类型
        String refType = wmActivities.getRefType();
        activityRefVo.setRefType(refType);
        activityRefVo.setRefTypeCn(Objects.requireNonNull(EActivityRefType.getByCode(refType)).getDesc());
        String[] split = wmActivities.getRefId().split(",");
        // 转成String类型的List
        List<String> refIds = Arrays.asList(split);
        // 根据类型获取对应商品
        if (EActivityRefType.ALL.getCode().equals(refType)) {
            // 全部商品
            activityRefVo.setRefProducts(pmsGoodsMapper.selectPmsGoodsList(new PmsGoods()));
        } else if (EActivityRefType.PART_PRODUCT.getCode().equals(refType) || EActivityRefType.DEFAULT.getCode().equals(refType)) {
            // 部分商品
            activityRefVo.setRefProducts(pmsGoodsMapper.queryProductsByIds(refIds));
        } else if (EActivityRefType.PART_CATEGORY.getCode().equals(refType)) {
            // 部分类目
            // 查询类目表
            activityRefVo.setRefCategories(pmsCategoryService.queryAllByIds(refIds));
        }
        return activityRefVo;
    }

    @Override
    public int saveActivityCorpus(ActivityCorpusReq req) {
        // 保存活动语料
        GeneratingReq generatingReq = new GeneratingReq();
        generatingReq.setRefId(req.getActivityId().toString());
        generatingReq.setType(EGenerateType.ACTIVITY_CORPUS);
        return corpusDocumentService.saveMaterials(req.getMaterials(), generatingReq);
    }

    @Override
    public Map<Long, WmActivities> selectWmActivitiesByIds(Set<Long> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return Collections.emptyMap();
        }
        List<WmActivities> list = wmActivitiesMapper.selectWmActivitiesByIds(activityIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(WmActivities::getId, wmActivities -> wmActivities));
    }

    @Override
    public List<CorpusDocument> getActivityCorpus(Long id) {
        if (id == null) return Collections.emptyList();

        List<CorpusDocument> corpusDocuments = corpusDocumentService.selectCorpusDocumentList(new CorpusDocument() {{
            setMetadata(String.valueOf(id));
            setKnowledgeEnum(EDatasetsEnum.ACTIVITY_KB.getCode());
        }});

        if (CollectionUtils.isNotEmpty(corpusDocuments)) {
            return corpusDocuments;
        }

        return Collections.emptyList();
    }

    @Override
    public List<BizSalesPitch> getActivityMoments(Long id) {
        if (id == null) return Collections.emptyList();

        List<BizSalesPitch> bizSalesPitches = bizSalesPitchService.selectBizSalesPitchList(new BizSalesPitch() {{
            setRefId(id);
            setRefType(ProductConstants.SalesPitch.RefType.ACTIVITY);
        }});

        if (CollectionUtils.isNotEmpty(bizSalesPitches)) {
            return bizSalesPitches;
        }

        return Collections.emptyList();
    }

    @Override
    public List<WmActivitiesVo> getActivitiesByProductId(PmsGoods pmsGoods) {
        List<WmActivitiesVo> activityVoList = new ArrayList<>();

        // 查询指定状态的活动列表：未开始、预热、进行中
        List<WmActivities> wmActivities = this.selectWmActivitiesList(new WmActivities() {{
            setParams(new HashMap<String, Object>() {{
                put("status", Arrays.asList(
                        EActivityStatus.PREHEAT.getValue(),
                        EActivityStatus.NOT_STARTED.getValue(),
                        EActivityStatus.GOING_ON.getValue()
                ));
            }});
        }});

        // 如果没有符合条件的活动，直接返回空列表
        if (wmActivities.isEmpty()) {
            return Collections.emptyList();
        }

        // 遍历查询到的活动并处理每个活动
        for (WmActivities activity : wmActivities) {
            String refType = activity.getRefType();
            String refId = activity.getRefId();

            // 处理适用于所有商品的活动
            if (EActivityRefType.ALL.getCode().equals(refType)) {
                WmActivitiesVo wmActivitiesVo = convertToVo(activity);
                activityVoList.add(wmActivitiesVo);
            }
            // 处理针对特定商品的活动
            else if (EActivityRefType.PART_PRODUCT.getCode().equals(refType) ||
                    EActivityRefType.DEFAULT.getCode().equals(refType)) {
                if (refId.contains(String.valueOf(pmsGoods.getId()))) {
                    WmActivitiesVo wmActivitiesVo = convertToVo(activity);
                    activityVoList.add(wmActivitiesVo);
                }
            }
            // 处理针对特定分类的活动
            else if (EActivityRefType.PART_CATEGORY.getCode().equals(refType)) {
                Long firstCateId = pmsGoods.getFirstCateId();
                Long secondCateId = pmsGoods.getSecondCateId();

                if (refId.contains(String.valueOf(firstCateId)) || refId.contains(String.valueOf(secondCateId))) {
                    WmActivitiesVo wmActivitiesVo = convertToVo(activity);
                    activityVoList.add(wmActivitiesVo);
                }
            }
        }

        return activityVoList;
    }


    @Override
    public List<WmActivitiesVo> getActivitiesByActivityIds(List<Long> activityIds) {
        if (activityIds.isEmpty()) return Collections.emptyList();
        List<WmActivities> wmActivities = wmActivitiesMapper.selectWmActivitiesByActivityIds(activityIds);

        List<WmActivitiesVo> list = new ArrayList<>();
        wmActivities.forEach(activity -> {
            WmActivitiesVo activitiesVo = new WmActivitiesVo();
            BeanUtils.copyProperties(activity, activitiesVo);
            transaction(activitiesVo, activity);
            list.add(activitiesVo);
        });
        return list;
    }

    @Override
    public int updateAiRepurchase(List<Long> activityIds, Integer status) {
        // 校验导购数量限制
        int aiPurchaseLimit = sysConfigService.getInt(ConfigConstants.Activity.AI_PURCHASE_LIMIT, 10);
        List<WmActivities> wmActivities = wmActivitiesMapper.selectWmActivitiesList(new WmActivities() {{
            setIsAiPurchaseGuide(1);
        }});

        if (wmActivities.size() > aiPurchaseLimit) {
            throw new ServiceException("开启导购数量超过限制");
        }

        for (Long id : activityIds) {
            // 如果关闭 ai 导购推荐则删除 ai
            if (Integer.valueOf(0).equals(status)) {
                sendActivityToAlex(id, true);
            } else {
                // 判断是否有删除记录, 如果有则删除'删除记录'
                List<BizVersionRecord> deleteRecords = bizVersionRecordService.selectBizVersionRecordList(new BizVersionRecord() {{
                    setRefId(id);
                    setRefType(EVersionRefType.ACTIVITY.getCode());
                    setEvent(EVersionEvent.delete.name());
                }});

                if (CollUtil.isNotEmpty(deleteRecords)) {
                    bizVersionRecordService.deleteBizVersionRecordByIds(deleteRecords.stream()
                            .map(BizVersionRecord::getId)
                            .collect(Collectors.toList())
                            .toArray(new Long[]{}));
                } else {
                    // 如果没有删除记录并开启导购, 则发送活动
                    sendActivityToAlex(id, false);
                }
            }
        }

        return wmActivitiesMapper.updateAiRepurchase(activityIds, status);
    }

    @Override
    public List<Long> getOngoingActivityIds() {
        return wmActivitiesMapper.selectOngoingActivityIds();
    }

    /**
     * 将WmActivities对象转换为WmActivitiesVo，并处理其事务逻辑。
     *
     * @param activity 要转换的活动对象
     * @return 转换后的活动视图对象
     */
    private WmActivitiesVo convertToVo(WmActivities activity) {
        WmActivitiesVo wmActivitiesVo = new WmActivitiesVo();
        BeanUtils.copyProperties(activity, wmActivitiesVo);
        transaction(wmActivitiesVo, activity);
        return wmActivitiesVo;
    }

    /**
     * 同步活动逻辑
     *
     * @param id      活动id
     * @param delFlag 删除标记
     */
    private void sendActivityToAlex(Long id, boolean delFlag) {
        String event = "update";
        String json = "";
        String name = "已删除活动";

        if (delFlag) {
            event = "delete";
        } else {
            //开始判断是否需要同步到AI
            WmActivitiesVo activitiesVo = querySimpleActivityById(id);

            String md5 = Md5Utils.hash(JSON.toJSONString(activitiesVo));
            //判断这次的活动信息和上次的是否一致。若不一致，进行推送
            String cacheKey = String.format(CacheConstants.CACHE_ACTIVITIES_INFO_LIST_V3, activitiesVo.getId());
            Object o = redisCache.getCacheObject(cacheKey);
            if (o != null && o.toString().equals(md5)) {
                return;
            }
            redisCache.setCacheObject(cacheKey, md5);

            // 活动的状态
            if (EActivityStatus.DELETE.getValue() == activitiesVo.getPromotionStatus().intValue()) {
                event = "delete";
            }

            json = JSON.toJSONString(activitiesVo);
            name = activitiesVo.getPromotionName();
        }

        bizVersionRecordService.insertRecord(EVersionRefType.ACTIVITY, id, event, json, name);
    }

}
