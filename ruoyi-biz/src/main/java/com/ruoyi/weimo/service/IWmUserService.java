package com.ruoyi.weimo.service;

import java.util.List;
import com.ruoyi.weimo.domain.WmUser;

/**
 * 用户管理Service接口
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
public interface IWmUserService
{
    /**
     * 查询用户管理
     *
     * @param wid 用户管理主键
     * @return 用户管理
     */
    public WmUser selectWmUserByWid(Long wid);

    /**
     * 查询用户管理列表
     *
     * @param wmUser 用户管理
     * @return 用户管理集合
     */
    public List<WmUser> selectWmUserList(WmUser wmUser);

    /**
     * 新增用户管理
     *
     * @param wmUser 用户管理
     * @return 结果
     */
    public int insertWmUser(WmUser wmUser);

    /**
     * 修改用户管理
     *
     * @param wmUser 用户管理
     * @return 结果
     */
    public int updateWmUser(WmUser wmUser);

    /**
     * 批量删除用户管理
     *
     * @param wids 需要删除的用户管理主键集合
     * @return 结果
     */
    public int deleteWmUserByWids(Long[] wids);

    /**
     * 删除用户管理信息
     *
     * @param wid 用户管理主键
     * @return 结果
     */
    public int deleteWmUserByWid(Long wid);


    /**
     * 拉取微盟用户列表
     */
    void pullWmUser();

    /**
     * 获取微盟用户的UnionId
     */
    void getWmUserUnionId();
}
