package com.ruoyi.weimo.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.domain.BizDistributeRecord;
import com.ruoyi.biz.domain.BizMembershipToken;
import com.ruoyi.biz.domain.dto.WMOrderPageQueryParamReq;
import com.ruoyi.biz.domain.dto.WMOrderPageQueryReq;
import com.ruoyi.biz.domain.req.TempHandlerReq;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.biz.enums.ETempHandlerType;
import com.ruoyi.biz.mapper.BizDistributeRecordMapper;
import com.ruoyi.biz.mapper.BizMembershipTokenMapper;
import com.ruoyi.biz.service.IBizMembershipService;
import com.ruoyi.common.constant.ConfigConstants;
import com.ruoyi.common.constant.MembershipConstants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.constant.WmConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.ERedisChannel;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.goods.service.IPmsRelationshipService;
import com.ruoyi.order.domain.OmsOrder;
import com.ruoyi.order.domain.OmsOrderSku;
import com.ruoyi.order.mapper.OmsOrderMapper;
import com.ruoyi.order.mapper.OmsOrderSkuMapper;
import com.ruoyi.order.service.IOmsOrderService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.weimo.domain.dto.MsgBodyOrder;
import com.ruoyi.weimo.domain.dto.WmMessage;
import com.ruoyi.weimo.enums.EWmOrderStatus;
import com.ruoyi.weimo.service.IWeiMobService;
import com.ruoyi.weimo.service.IWmOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 微盟订单相关业务处理
 *
 * <AUTHOR>
 * @date 2024/8/22 15:02
 */

@Service
@Slf4j
public class WmOrderServiceImpl implements IWmOrderService {

    @Resource
    private OmsOrderMapper omsOrderMapper;

    @Resource
    private OmsOrderSkuMapper omsOrderSkuMapper;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private IWeiMobService weiMobService;

    @Resource
    private IOmsOrderService omsOrderService;

    @Resource
    private IPmsRelationshipService pmsRelationshipService;

    @Resource
    private IBizMembershipService bizMembershipService;

    @Resource
    private BizMembershipTokenMapper bizMembershipTokenMapper;

    @Resource
    private BizDistributeRecordMapper bizDistributeRecordMapper;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private RedisCache redisCache;

    @Override
    public void handleTopic(WmMessage wmMessage) {
        MsgBodyOrder msgBodyOrder = JSONObject.parseObject((String) wmMessage.getMsgBody(), MsgBodyOrder.class);
        log.info("handle order topic, event: {}", wmMessage.getEvent());

        log.info("get order detail info, order no: {}", msgBodyOrder.getOrderNo());
        JSONObject orderDetail = weiMobService.pullOrderDetail(msgBodyOrder.getOrderNo().toString());

        switch (wmMessage.getEvent()) {
            case WmConstants.Event.CREATE:
            case WmConstants.Event.UPDATE:
                // 订单创建 || 更新事件
                handleOrderSave(msgBodyOrder, orderDetail);
                break;
            case WmConstants.Event.DELETE:
                break;
            case WmConstants.Event.STATUS_UPDATE:
                // 订单状态更新事件
                handleStatusUpdate(msgBodyOrder, orderDetail);
                break;
            default:
                break;
        }
    }

    private void handleStatusUpdate(MsgBodyOrder msgBodyOrder, JSONObject orderDetail) {
        OmsOrder omsOrderUpdate = new OmsOrder();
        omsOrderUpdate.setOrderCode(msgBodyOrder.getOrderNo().toString());
        String omsCode = EWmOrderStatus.fromWmCode(msgBodyOrder.getOrderStatus().toString()).getOmsCode();
        omsOrderUpdate.setStatus(omsCode);
        omsOrderMapper.updateOmsOrderByOrderNo(omsOrderUpdate);

        omsOrderUpdate.setCustomerId(msgBodyOrder.getWid());
        omsOrderUpdate.setOrderCode(msgBodyOrder.getOrderNo().toString());
        // 根据订单状态更新用户体温
        if (EWmOrderStatus.PENDING_SHIPMENT.getOmsCode().equals(omsCode) && sysConfigService.isOpen(ConfigConstants.IS_TEMP_CHECK_OPEN)) {
            log.info("更新用户体温信息, orderNo: {}", msgBodyOrder.getOrderNo());
            TempHandlerReq req = new TempHandlerReq();
            req.setType(ETempHandlerType.BUY_HANDLER);
            req.setOmsOrder(omsOrderUpdate);
            redisCache.publish(ERedisChannel.DEAL_TEMP_CHANNEL.getCode(), req);
        }

        log.info("update oms order, orderNo: {}, status: {}", msgBodyOrder.getOrderNo(), msgBodyOrder.getOrderStatus());

        if (EWmOrderStatus.PAID.getWmCode().equals(msgBodyOrder.getOrderStatus().toString())) {
            // 用户购买会员业务处理.
            bizMembershipService.handleBuyMember(msgBodyOrder, orderDetail);

            // 商品之间关联关系业务处理
            handleProductRelation(msgBodyOrder);

        } else if (EWmOrderStatus.CONFIRM_RECEIPT.getWmCode().equals(msgBodyOrder.getOrderStatus().toString())) {
        }

    }


    /**
     * 商品之间关联关系业务处理
     */
    private void handleProductRelation(MsgBodyOrder msgBodyOrder) {
        // 查看当前订单在库中状态, 未完成状态才处理商品关联关系, 防止消息重复消费
        OmsOrder omsOrderParam = new OmsOrder();
        omsOrderParam.setOrderCode(msgBodyOrder.getOrderNo().toString());
        List<OmsOrder> omsOrderList = omsOrderMapper.selectOmsOrderList(omsOrderParam);
        if (CollectionUtils.isNotEmpty(omsOrderList)) {
            // 如果没有计算过商品关联关系
            if (EBoolean.NO.getCode().equals(omsOrderList.get(0).getHasCalculatedRelation())) {
                pmsRelationshipService.pmsRelationshipHandel(msgBodyOrder.getWid().toString(), msgBodyOrder.getOrderNo().toString());
            }
        }
    }

    private void handleOrderSave(MsgBodyOrder msgBodyOrder, JSONObject orderDetail) {
        WMOrderPageQueryReq req = new WMOrderPageQueryReq();
        req.setPageNum(1);
        req.setPageSize(10);
        req.setQueryParameter(WMOrderPageQueryParamReq.builder()
                .searchType(4)
                .keyword(msgBodyOrder.getOrderNo().toString())
                .build());
        List<JSONObject> pageList = weiMobService.sendPostToPullOrder(req).getJSONArray("pageList").toList(JSONObject.class);
        if (pageList.size() > 1) {
            log.warn("微盟订单创建事件, 订单号重复, orderNo: {}", msgBodyOrder.getOrderNo());
        }

        int rows = omsOrderService.saveWmOrders(pageList);
        log.info("insert or update wm orders num: {}", rows);
    }


    @Override
    public void checkMemberOrders() {
        log.info("start check membership orders");
        // 1. 定义 SQL 查询，找出遗漏的订单
        String sqlTemplate = "select oo.order_code,\n" +
                "       oo.customer_id,\n" +
                "       oo.pay_time,\n" +
                "       oo.status,\n" +
                "       oos.spu_id,\n" +
                "       bmt.id as token_id,\n" +
                "       bmt.source_id\n" +
                "from oms_order oo\n" +
                "         inner join oms_order_sku oos on oo.id = oos.order_id\n" +
                "         left join biz_membership_token bmt on oo.order_code = bmt.source_id\n" +
                "where oo.status in ('2','3','4')\n" +
                "    and spu_id = {}\n" +
                "  and bmt.id is null;";

        // 2. 处理9.9会员
        String memberProductId = sysConfigService.selectConfigByKey(ConfigConstants.Membership.NINE_NINE);
        if (StringUtils.isNotEmpty(memberProductId)) {
            String executeSql = StrUtil.format(sqlTemplate, memberProductId);
            log.info("execute sql ordinary member: {}", executeSql);

            List<Map<String, Object>> missingOrders99 = jdbcTemplate.queryForList(executeSql);

            // 3. 遍历结果集，处理每一个遗漏的订单
            for (Map<String, Object> order : missingOrders99) {
                // 可修改为批量插入
                BizMembershipToken token = createToken(order, MembershipConstants.Type.NINE_NINE);
                bizMembershipTokenMapper.insertBizMembershipToken(token);
//            bizMembershipService.tryOpenMemberByToken(token);
            }
        }


        // 3. 处理365会员
        String member365ProductId = sysConfigService.selectConfigByKey(ConfigConstants.Membership.THREE_SIXTY_FIVE);
        if (StringUtils.isNotEmpty(member365ProductId)) {
            String executeSql = StrUtil.format(sqlTemplate, member365ProductId);
            log.info("execute sql distribution member: {}", executeSql);
            List<Map<String, Object>> missingOrders365 = jdbcTemplate.queryForList(executeSql);
            for (Map<String, Object> order : missingOrders365) {
                // 可修改为批量插入
                BizMembershipToken token = createToken(order, MembershipConstants.Type.THREE_SIXTY_FIVE);
                bizMembershipTokenMapper.insertBizMembershipToken(token);

                // 更新分销记录购买会员状态
                bizDistributeRecordMapper.updateByDownstream(new BizDistributeRecord() {
                    {
                        setDownstream(token.getUnionId());
                        setBoughtMember(true);
                    }
                });

//            bizMembershipService.tryOpenMemberByToken(token);
            }
        }

    }

    private BizMembershipToken createToken(Map<String, Object> order, String type) {
        String orderCode = MapUtil.getStr(order, "order_code");
        Long customerId = MapUtil.getLong(order, "customer_id");
        Date payTime = MapUtil.getDate(order, "pay_time");
        String wmUserUnionId = weiMobService.getWmUserUnionId(customerId);
        log.info("create token, type: {}, orderCode: {}, customerId: {}, payTime: {}, wmUserUnionId: {}",
                type, orderCode, customerId, payTime, wmUserUnionId);

        BizMembershipToken bizMembershipToken = new BizMembershipToken();
        bizMembershipToken.setId(IdUtils.generator());
        bizMembershipToken.setUnionId(wmUserUnionId);
        bizMembershipToken.setSourceId(orderCode);
        bizMembershipToken.setSourceType(MembershipConstants.Token.SourceType.WEIMOB);
        bizMembershipToken.setMembershipType(type);
        bizMembershipToken.setPurchaseDate(payTime == null ? new Date() : payTime);
        bizMembershipToken.setStatus(MembershipConstants.Token.Status.UNUSED);
        bizMembershipToken.setCreateBy(UserConstants.SYS_USER);
        bizMembershipToken.setCreateTime(new Date());
        return bizMembershipToken;
    }
}
