package com.ruoyi.weimo.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.domain.BizDistributeRecord;
import com.ruoyi.biz.mapper.BizDistributeRecordMapper;
import com.ruoyi.biz.service.IDistributeService;
import com.ruoyi.common.constant.DistributeConstants;
import com.ruoyi.common.constant.WmConstants;
import com.ruoyi.weimo.domain.WmUser;
import com.ruoyi.weimo.domain.dto.MsgBodyCustomer;
import com.ruoyi.weimo.domain.dto.WmMessage;
import com.ruoyi.weimo.mapper.WmUserMapper;
import com.ruoyi.weimo.service.IWeiMobService;
import com.ruoyi.weimo.service.IWmCustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;


/**
 * Description: new java files header..
 *
 * <AUTHOR>
 * @date 2024/8/22 15:02
 */

@Service
@Slf4j
public class WmCustomerServiceImpl implements IWmCustomerService {
    @Resource
    private IWeiMobService weiMobService;

    @Resource
    private WmUserMapper wmUserMapper;

    @Resource
    private BizDistributeRecordMapper bizDistributeRecordMapper;

    @Resource
    private IDistributeService distributeService;

    @Override
    public void handleTopic(WmMessage wmMessage) {
        MsgBodyCustomer msgBodyCustomer = JSONObject.parseObject((String) wmMessage.getMsgBody(), MsgBodyCustomer.class);
        log.info("handle customer topic, event: {}", wmMessage.getEvent());

        switch (wmMessage.getEvent()) {
            case WmConstants.Event.CREATE:
                // 微盟新增用户处理
                handleCustomerCreate(msgBodyCustomer);
                break;
            case WmConstants.Event.UPDATE:
                break;
            case WmConstants.Event.DELETE:
                break;

            default:
                break;
        }

    }

    private void handleCustomerCreate(MsgBodyCustomer msgBodyCustomer) {

        // 落库微盟用户
        JSONObject jsonObject = weiMobService.crmCustomerGetList(msgBodyCustomer.getWidList());
        List<WmUser> wmUserList = new ArrayList<>();
        jsonObject.getJSONArray("userBaseInfoList").forEach(
                item -> {
                    JSONObject userBaseInfo = (JSONObject) item;
                    WmUser wmUser = new WmUser();
                    wmUser.setWid(userBaseInfo.getLong("wid"));
                    wmUser.setName(userBaseInfo.getString("name"));
                    wmUser.setNickname(userBaseInfo.getString("nickname"));
                    wmUser.setHeadUrl(userBaseInfo.getString("avatarUrl"));
                    wmUser.setCreateTime(new Date());
                    wmUserList.add(wmUser);
                });

        //开始批量新增或者更新
        wmUserMapper.insertBatch(wmUserList);

        // 分销者关系业务处理
        handleDistributor(wmUserList);

    }

    private void handleDistributor(List<WmUser> wmUserList) {
        for (WmUser wmUser : wmUserList) {
            String mainWid = weiMobService.getMainWid(wmUser.getWid().toString());
            String wmUserUnionId = weiMobService.getWmUserUnionId(Long.parseLong(mainWid));
            BizDistributeRecord bizDistributeRecord = bizDistributeRecordMapper.selectByDownstream(wmUserUnionId);
            distributeService.handleBoundRelation(bizDistributeRecord, mainWid);
        }
    }


}
