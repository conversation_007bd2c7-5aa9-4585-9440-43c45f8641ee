package com.ruoyi.goods.domain.req;

import com.ruoyi.goods.domain.PmsSku;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设置复购周期请求
 *
 * <AUTHOR>
 * @date 2024/7/17 10:34
 */

@Data
public class RepurchaseReq {

    /**
     * 商品ID，唯一标识一个商品。
     */
    private Long goodsId;

    /**
     * 商品的复购周期，以天为单位。表示用户通常多久会再次购买该商品。
     */
//    @NotNull(message = "spu的复购周期必填")
    private Integer repurchaseCycle;

    /**
     * SKU复购周期列表，包含每个SKU的复购周期。
     */
    private List<PmsSku> skuList;

    /**
     * 销售话术列表，包含推荐该商品时使用的各种销售策略或话术。
     */
    @NotNull(message = "复购营销话术必填")
    private List<SalesPitchVo> salesPitchList;

}
