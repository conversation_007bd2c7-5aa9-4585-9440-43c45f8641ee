package com.ruoyi.goods.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.goods.domain.PmsSpec;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 商品和规格值的关联对象 pms_goods_spec_value
 *
 * <AUTHOR>
 * @date 2020-07-24
 */
@Data
public class GoodsSpecValueVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 规格id  对应pms_spec表中的id
     */
    @Excel(name = "规格id  对应pms_spec表中的id")
    private Long specId;

    /**
     * 规格值id  对应pms_spec_value表中的id
     */
    @Excel(name = "规格值id  对应pms_spec_value表中的id")
    private String specValueId;

    /**
     * 规格值
     */
    @Excel(name = "规格值")
    private String valueRemark;


    /**
     * 规格信息
     */
    private String specName;

}
