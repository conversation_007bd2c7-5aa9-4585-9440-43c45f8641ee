package com.ruoyi.goods.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 单品的规格值对象 pms_sku_spec_value
 *
 * <AUTHOR>
 * @date 2020-07-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PmsSkuSpecValue extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 商品id
     */
    @Excel(name = "商品id")
    private Long spuId;

    /**
     * 单品id   对应pms_sku表中的id
     */
    @Excel(name = "单品id   对应pms_sku表中的id")
    private String skuId;

    /**
     * 规格id 对应pms_spec表中的id
     */
    @Excel(name = "规格id 对应pms_spec表中的id")
    private Long specId;

    /**
     * 规格值id  对应pms_spec_value 表中的id
     */
    @Excel(name = "规格值id  对应pms_spec_value 表中的id")
    private String specValueId;

    /**
     * 规格值
     */
    @Excel(name = "规格值")
    private String valueRemark;

    /**
     * 删除标记 0 未删除 1 删除  默认0
     */
    private int delFlag;


    private PmsSpec spec;

    /**
     * sku级别复购周期
     */
    private Integer repurchaseCycle;

    /**
     * 设置单品id和商品id
     *
     * @param skuId 单品id
     * @param spuId 商品id
     */
    public void setSkuIdAndSpuId(String skuId, long spuId) {
        this.skuId = skuId;
        this.spuId = spuId;
    }

}
