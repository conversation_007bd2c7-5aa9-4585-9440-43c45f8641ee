package com.ruoyi.goods.domain.req;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Description: new java files header..
 *
 * <AUTHOR>
 * @date 2024/7/18 16:39
 */
@Data
public class ProductRelationReq {
    /**
     *
     */
    private Long id;

    /**
     * 商品 id
     */
    @NotNull(message = "商品不能为空")
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 关联商品 id
     */
    @NotNull(message = "关联商品不能为空")
    private Long relProductId;

    /**
     * 关联商品名称
     */
    private String relProductName;

    /**
     * 关联系得分
     */
    private Integer relationScore;

    /**
     * 推荐优先顺序, 5个级别加置顶 置顶值为-1
     */
    @NotNull(message = "关联优先级不能为空")
    @Max(value = 5, message = "关联优先级不能大于5")
    private Integer relLevel;

    /**
     * 销售话术列表，包含推荐该商品时使用的各种销售策略或话术。
     */
    @NotNull(message = "销售话术列表不能为空")
    private List<SalesPitchVo> salesPitchList;
}
