package com.ruoyi.goods.domain.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设置普通商品推荐话术
 *
 * <AUTHOR>
 * @date 2024/7/17 10:34
 */

@Data
public class RecommendProductReq {

    /**
     * 商品ID，唯一标识一个商品。
     */
    private Long goodsId;

    /**
     * 销售话术列表，包含推荐该商品时使用的各种销售策略或话术。
     */
    @NotNull(message = "朋友圈话术必填")
    private List<SalesPitchVo> salesPitchList;

}
