package com.ruoyi.goods.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.goods.domain.PmsSpec;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 单品的规格值对象 pms_sku_spec_value
 *
 * <AUTHOR>
 * @date 2020-07-24
 */
@Data
public class SkuSpecValueVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 单品id   对应pms_sku表中的id
     */
    @Excel(name = "单品id   对应pms_sku表中的id")
    private String skuId;

    /**
     * 规格id 对应pms_spec表中的id
     */
    @Excel(name = "规格id 对应pms_spec表中的id")
    private Long specId;

    /**
     * 规格值id  对应pms_spec_value 表中的id
     */
    @Excel(name = "规格值id  对应pms_spec_value 表中的id")
    private String specValueId;

    /**
     * 规格值
     */
    @Excel(name = "规格值")
    private String valueRemark;


    //规格值名称
    private String specName;

}
