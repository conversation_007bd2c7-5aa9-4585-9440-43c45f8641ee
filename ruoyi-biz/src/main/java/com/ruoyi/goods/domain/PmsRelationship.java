package com.ruoyi.goods.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.NumberUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品关联关系对象 pms_relationship
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PmsRelationship extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Long id;

    /**
     * 商品 id
     */
    @Excel(name = "商品 id")
    private Long productId;

    /**
     * 商品名称
     */
    @Excel(name = "商品名称")
    private String productName;

    /**
     * 关联商品 id
     */
    @Excel(name = "关联商品 id")
    private Long relProductId;

    /**
     * 关联商品名称
     */
    @Excel(name = "关联商品名称")
    private String relProductName;

    /**
     * 相关性得分, 实际值乘100
     */
    @Excel(name = "相关性得分, 实际值乘100")
    private Integer relationScore;

    /**
     * 手动增加分数, 实际值乘100
     */
    @Excel(name = "手动增加分数, 实际值乘100")
    private Integer addedScore;

    /**
     * 总得分, 实际值乘100
     */
    @Excel(name = "总得分, 实际值乘100")
    private Integer totalScore;

    /**
     * 推荐优先顺序, 总共 5 个级别
     */
    @Excel(name = "推荐优先顺序, 总共 5 个级别")
    private Integer relLevel;

    /**
     * 状态, 0-待确认话术, 1-已确认
     */
    @Excel(name = "状态, 0-待确认话术, 1-已确认")
    private Integer state;

    public Integer getTotalScore() {
        if (this.relationScore == null && this.addedScore == null) {
            return null;
        }

        return NumberUtil.getNonNullValue(this.relationScore) +
                NumberUtil.getNonNullValue(this.addedScore);
    }
}
