package com.ruoyi.goods.domain.vo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * 单品对象 pms_sku
 *
 * <AUTHOR>
 * @date 2020-07-24
 */
@Data
public class SkuVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String id;


    //库存状态 0 充足，1一般，2无
    private String inventoryStatus;

    /**
     * 单品价格
     */
    @Excel(name = "单品价格")
    private BigDecimal price;

    /**
     * 单品的重量
     */
    @Excel(name = "单品的重量")
    private BigDecimal weight;

    //sku卖点
    private String fabe;

    /**
     * 复购周期
     */
    private Integer repurchaseCycle;

//    /**
//     * 单品图片
//     */
//
//    private List<PmsSkuImage> skuImages;

    /**
     * 单品规格值
     */

    private List<SkuSpecValueVo> skuSpecValues;


}
