package com.ruoyi.goods.domain.req;

import com.ruoyi.biz.domain.BizSalesPitch;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 话术对象 biz_sales_pitch
 *
 * <AUTHOR>
 * @date 2024-07-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SalesPitchVo extends BizSalesPitch {

    /**
     * 是否删除
     */
    private Boolean isDelete;

    /**
     * 该话术来源，由谁创建
     */
    private String createBy;

}
