package com.ruoyi.goods.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.goods.domain.*;
import com.ruoyi.weimo.domain.vo.WmActivitiesVo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品对象 pms_goods
 *
 * <AUTHOR>
 * @date 2020-07-24
 */
@Data
public class GoodsVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 商品名称
     */
    @Excel(name = "商品名称")
    private String name;

    /**
     * 商品副标题
     */
    @Excel(name = "商品副标题")
    private String subtitle;

    //自定义名称
    private String customName;

    //最小销售价
    private BigDecimal minSalePrice;

    //最大销售价
    private BigDecimal maxSalePrice;


    //商品卖点
    private String fabe;

    /**
     * 复购周期
     */
    private Integer repurchaseCycle;

    /**
     * 一级分类id
     */
    @Excel(name = "一级分类id")
    private Long firstCateId;

    /**
     * 二级分类id
     */
    @Excel(name = "二级分类id")
    private Long secondCateId;


    /**
     * 是否是虚拟商品 0 否 1 是默认0
     */
    @Excel(name = "是否是虚拟商品 0 否 1 是默认0")
    private String isVirtual;

    @Excel(name = "商品上架状态 0 下架  1上架 ")
    private String shelvesStatus;

    /**
     * 是否开启AI 导购推荐: 0-否, 1-是
     */
    @Excel(name = "是否开启AI 导购推荐: 0-否, 1-是")
    private String isAiPurchaseGuide;

    //槽位
    private String slotMap;

    /**
     * 商品下面的单品
     */
    private List<SkuVo> skus;

    /**
     * 商品规格值
     */
    private List<GoodsSpecValueVo> spuSpecValues;

    /**
     * 商品关联活动
     */
    private List<WmActivitiesVo> activities;

    /**
     * 一级分类
     */
    private PmsCategory firstCategory;

    /**
     * 二级分类
     */
    private PmsCategory secondCategory;

    /**
     * 品牌
     */
    private PmsBrand brand;

    /**
     * 商品属性值
     */
    private List<PmsGoodsAttributeValue> spuAttributeValues;

}
