package com.ruoyi.goods.domain.vo;

import com.ruoyi.goods.domain.PmsCategory;
import com.ruoyi.goods.domain.PmsGoods;
import lombok.Data;

import java.util.List;

/**
 * 活动相关 商品+类目 实体
 * <AUTHOR>
 * @date 2024/7/26
 */
@Data
public class ActivityRefVo {

    /**
     * 关联类型
     */
    private String refType;

    /**
     * 关联类型中文
     */
    private String refTypeCn;

    /**
     * 关联商品
     */
    private List<PmsGoods> refProducts;

    /**
     * 关联类目
     */
    private List<PmsCategory> refCategories;

}
