package com.ruoyi.goods.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.github.pagehelper.PageHelper;
import com.ruoyi.goods.domain.req.ProductRelationReq;
import com.ruoyi.goods.domain.vo.PmsRelationshipVo;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.goods.domain.PmsRelationship;
import com.ruoyi.goods.service.IPmsRelationshipService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 商品关联关系Controller
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@RestController
@RequestMapping("/goods/relationship")
public class PmsRelationshipController extends BaseController
{
    @Resource
    private IPmsRelationshipService pmsRelationshipService;

    /**
     * 查询商品关联关系列表
     */
    //@PreAuthorize("@ss.hasPermi('goods:relationship:list')")
    @GetMapping("/list")
    public TableDataInfo list(PmsRelationship pmsRelationship)
    {
        startPage();
        List<PmsRelationship> list = pmsRelationshipService.selectPmsRelationshipList(pmsRelationship);
        return getDataTable(list);
    }

    /**
     * 查询商品关联关系列表, 按照相关性得分排序
     */
    //@PreAuthorize("@ss.hasPermi('goods:relationship:list')")
    @GetMapping("/list/rank")
    public TableDataInfo listRank(PmsRelationship pmsRelationship)
    {
        List<PmsRelationshipVo> list = pmsRelationshipService.selectPmsRelationshipListRank(pmsRelationship);
        return getDataTable(list);
    }

    /**
     * 导出商品关联关系列表
     */
    //@PreAuthorize("@ss.hasPermi('goods:relationship:export')")
    @Log(title = "商品关联关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PmsRelationship pmsRelationship)
    {
        List<PmsRelationship> list = pmsRelationshipService.selectPmsRelationshipList(pmsRelationship);
        ExcelUtil<PmsRelationship> util = new ExcelUtil<PmsRelationship>(PmsRelationship.class);
        util.exportExcel(response, list, "商品关联关系数据");
    }

    /**
     * 获取商品关联关系详细信息
     */
    //@PreAuthorize("@ss.hasPermi('goods:relationship:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(pmsRelationshipService.selectPmsRelationshipById(id));
    }

    /**
     * 新增商品关联关系
     */
    //@PreAuthorize("@ss.hasPermi('goods:relationship:add')")
    @Log(title = "商品关联关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody ProductRelationReq req)
    {
        return toAjax(pmsRelationshipService.insertPmsRelationship(req));
    }

    /**
     * 修改商品关联关系
     */
    //@PreAuthorize("@ss.hasPermi('goods:relationship:edit')")
    @Log(title = "商品关联关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProductRelationReq req)
    {
        return toAjax(pmsRelationshipService.updatePmsRelationship(req));
    }

    @PostMapping("/pinned")
    public AjaxResult pinned(@RequestBody ProductRelationReq req)
    {
        return toAjax(pmsRelationshipService.pinned(req));
    }

    @PostMapping("/unpinned")
    public AjaxResult unpinned(@RequestBody ProductRelationReq req)
    {
        return toAjax(pmsRelationshipService.unpinned(req));
    }

    /**
     * 删除商品关联关系
     */
    //@PreAuthorize("@ss.hasPermi('goods:relationship:remove')")
    @Log(title = "商品关联关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(pmsRelationshipService.deletePmsRelationshipByIds(ids));
    }
}
