package com.ruoyi.goods.mapper;

import java.util.List;

import com.ruoyi.goods.domain.PmsRelationship;
import org.apache.ibatis.annotations.Param;

/**
 * 商品关联关系Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
public interface PmsRelationshipMapper {
    /**
     * 查询商品关联关系
     *
     * @param id 商品关联关系主键
     * @return 商品关联关系
     */
    public PmsRelationship selectPmsRelationshipById(Long id);

    /**
     * 查询商品关联关系, 根据排名
     *
     * @param id 商品关联关系主键
     * @return 商品关联关系
     */
    public PmsRelationship selectPmsRelationshipByProductIdAndLevel(
            @Param("productId") Long productId, @Param("level") Integer level, @Param("lowScore") Integer lowScore);

    /**
     * 查询商品关联关系列表
     *
     * @param pmsRelationship 商品关联关系
     * @return 商品关联关系集合
     */
    public List<PmsRelationship> selectPmsRelationshipList(PmsRelationship pmsRelationship);

    /**
     * 查询商品关联关系列表
     *
     * @param pmsRelationship 商品关联关系
     * @return 商品关联关系集合
     */
    public List<PmsRelationship> selectPmsRelationshipListRank(PmsRelationship pmsRelationship);

    /**
     * 新增商品关联关系
     *
     * @param pmsRelationship 商品关联关系
     * @return 结果
     */
    public int insertPmsRelationship(PmsRelationship pmsRelationship);

    /**
     * 新增商品关联关系
     *
     * @param pmsRelationship 商品关联关系
     * @return 结果
     */
    public int insertOrUpdatePmsRelationship(PmsRelationship pmsRelationship);

    /**
     * 修改商品关联关系
     *
     * @param pmsRelationship 商品关联关系
     * @return 结果
     */
    public int updatePmsRelationship(PmsRelationship pmsRelationship);

    /**
     * 删除商品关联关系
     *
     * @param id 商品关联关系主键
     * @return 结果
     */
    public int deletePmsRelationshipById(Long id);

    /**
     * 批量删除商品关联关系
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePmsRelationshipByIds(Long[] ids);

    /**
     * 根据产品id查询关系
     *
     * @param productId
     * @param relProductId
     */
    PmsRelationship selectByProductId(@Param("productId") Long productId, @Param("relProductId") Long relProductId);
}
