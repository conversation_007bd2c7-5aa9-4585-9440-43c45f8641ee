package com.ruoyi.goods.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import com.ruoyi.biz.component.UserPlanHandlerComponent;
import com.ruoyi.biz.domain.BizOperatePlan;
import com.ruoyi.biz.domain.req.GeneratingReq;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.biz.enums.EGenerateType;
import com.ruoyi.biz.enums.EOperatePlanRefType;
import com.ruoyi.biz.service.IBizOperatePlanService;
import com.ruoyi.biz.service.IBizOperatePointService;
import com.ruoyi.biz.service.IBizSalesPitchService;
import com.ruoyi.biz.service.IMaterialService;
import com.ruoyi.common.constant.ConfigConstants;
import com.ruoyi.common.constant.ProductConstants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.NumberUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.goods.domain.PmsGoods;
import com.ruoyi.goods.domain.PmsRelationship;
import com.ruoyi.goods.domain.req.ProductRelationReq;
import com.ruoyi.goods.domain.vo.PmsRelationshipVo;
import com.ruoyi.goods.mapper.PmsGoodsMapper;
import com.ruoyi.goods.mapper.PmsRelationshipMapper;
import com.ruoyi.goods.service.IPmsRelationshipService;
import com.ruoyi.mapstruct.EntityConverter;
import com.ruoyi.mapstruct.VoConverter;
import com.ruoyi.order.domain.OmsOrder;
import com.ruoyi.order.mapper.OmsOrderMapper;
import com.ruoyi.system.service.IPmsRelationshipRecordService;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品关联关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@Service
@Slf4j
public class PmsRelationshipServiceImpl implements IPmsRelationshipService {
    @Resource
    private PmsRelationshipMapper pmsRelationshipMapper;

    @Resource
    private IBizSalesPitchService salesPitchService;

    @Resource
    private IMaterialService materialServiceImpl;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private OmsOrderMapper omsOrderMapper;

    @Resource
    private PmsGoodsMapper pmsGoodsMapper;

    @Resource
    @Lazy
    private IBizOperatePlanService bizOperatePlanService;

    @Resource
    private IBizOperatePointService bizOperatePointService;

    @Resource
    private UserPlanHandlerComponent userPlanHandlerComponent;

    @Resource
    private IPmsRelationshipRecordService pmsRelationshipRecordService;

    /**
     * 查询商品关联关系
     *
     * @param id 商品关联关系主键
     * @return 商品关联关系
     */
    @Override
    public PmsRelationship selectPmsRelationshipById(Long id) {
        return pmsRelationshipMapper.selectPmsRelationshipById(id);
    }

    /**
     * 查询商品关联关系列表
     *
     * @param pmsRelationship 商品关联关系
     * @return 商品关联关系
     */
    @Override
    public List<PmsRelationship> selectPmsRelationshipList(PmsRelationship pmsRelationship) {
        return pmsRelationshipMapper.selectPmsRelationshipList(pmsRelationship);
    }

    /**
     * 查询商品关联关系列表
     *
     * @param pmsRelationship 商品关联关系
     * @return 商品关联关系
     */
    @Override
    public List<PmsRelationshipVo> selectPmsRelationshipListRank(PmsRelationship pmsRelationship) {
        int RANKING_THRESHOLD = Integer.parseInt(sysConfigService.selectConfigByKey(ConfigConstants.RELATION_RANKING_THRESHOLD)) * 100;

        // 获取置顶关联商品
        PmsRelationship relationParam = new PmsRelationship();
        relationParam.setProductId(pmsRelationship.getProductId());
        relationParam.setParams(new HashMap<String, Object>() {{
            put("added", true); // 查找有新增分数的关联关系
        }});
        List<PmsRelationship> pmsRelationships = pmsRelationshipMapper.selectPmsRelationshipList(relationParam);

        // 设置最低分, 实时根据分数排名获取前五个
        Page<Object> page = PageHelper.startPage(1, 5, "total_score desc");
        page.setCount(false);
        pmsRelationship.setRelationScore(RANKING_THRESHOLD);
        pmsRelationships.addAll(pmsRelationshipMapper.selectPmsRelationshipListRank(pmsRelationship));

        // 设置是否置顶
        List<PmsRelationshipVo> pmsRelationshipVoList = pmsRelationships.stream().map(item -> {
            PmsRelationshipVo vo = VoConverter.INSTANCE.toVo(item);
            vo.setPinned(vo.getAddedScore() > 0);
            return vo;
        }).collect(Collectors.toList());
        return pmsRelationshipVoList;
    }

    /**
     * 新增商品关联关系
     *
     * @param req 商品关联关系请求
     * @return 结果
     */
    @Override
    @Transactional
    public int insertPmsRelationship(ProductRelationReq req) {

        if (Objects.equals(req.getProductId(), req.getRelProductId())) {
            throw new ServiceException("不能关联自己");
        }

        // 增加关联关系时若为白名单商品，则不允许关联
        String whiteListConfig = sysConfigService.selectConfigByKey(ConfigConstants.RELATION_WHITE_LIST);
        if (StringUtils.isNotBlank(whiteListConfig)) {
            Set<String> whiteList = Arrays.stream(whiteListConfig.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());

            if (whiteList.contains(String.valueOf(req.getProductId()))) {
                String msg = "当前商品：" + req.getProductName() + " 在白名单中，无法关联！";
                throw new ServiceException(msg);
            }

            if (whiteList.contains(String.valueOf(req.getRelProductId()))) {
                String msg = "被关联商品：" + req.getRelProductName() + " 在白名单中，无法关联！";
                throw new ServiceException(msg);
            }
        }

        int changeRows = 0;
        PmsRelationship entity = EntityConverter.INSTANCE.toEntity(req);

        // 1. 设置关联商品话术
        changeRows += salesPitchService.updateSalesPitch(req.getSalesPitchList(), req.getRelProductId(),
                ProductConstants.SalesPitch.RefType.PRODUCT_RELATION);
        // 设置关联商品状态为: 1-已确认
        entity.setState(1);


        // 2. 填充关联性分数, 新增商品关联关系
        fillRelationScore(entity, false);
        changeRows += pmsRelationshipMapper.insertOrUpdatePmsRelationship(entity);

        // 3.新增关联商品的运营计划
        createRelationPlan(entity);

        // 3. 反向新增关联关系
        changeRows += saveRelationReverse(req, entity);

        return changeRows;
    }

    /**
     * 创建关联商品的运营计划
     *
     * @param entity 关联商品实例
     */
    private void createRelationPlan(PmsRelationship entity) {
        List<BizOperatePlan> bizOperatePlans = bizOperatePlanService.selectBizOperatePlanList(new BizOperatePlan() {{
            setRefType(EOperatePlanRefType.REF_TYPE_2.getCode());
            setRefId(entity.getProductId());
            setIsDelete(EBoolean.NO.getCode());
        }});
        if (CollectionUtils.isNotEmpty(bizOperatePlans)) {
            // 软删除运营计划再重新创建
            // 软删除计划和节点
            bizOperatePlanService.batchUpdateStatusToDelete(bizOperatePlans.stream().map(BizOperatePlan::getId).toArray(Long[]::new));
            bizOperatePointService.batchUpdateStatusToDeleteByPlanIds(bizOperatePlans.stream().map(BizOperatePlan::getId).toArray(Long[]::new));
        }
        userPlanHandlerComponent.createOperationPlan(pmsGoodsMapper.selectPmsGoodsById(entity.getProductId()), EOperatePlanRefType.REF_TYPE_2.getCode());
    }

    /**
     * 检查关联商品总得分是否达到创建阈值
     *
     * @param entity 待新增关联关系
     * @param flag   是否正向关联 true: 正向关联; false: 反向关联
     * @return 是否达到阈值
     */
    private boolean checkTotalScore(ProductRelationReq req, PmsRelationship entity, Boolean flag) {
        PmsRelationship relationship;
        if (flag) {
            relationship = pmsRelationshipMapper.selectByProductId(entity.getProductId(), entity.getRelProductId());
        } else {
            relationship = pmsRelationshipMapper.selectByProductId(req.getRelProductId(), req.getProductId());
        }
        // TODO 系统参数配置 || relationship.getTotalScore() > 1000
        return relationship != null;
    }

    private int saveRelationReverse(ProductRelationReq req, PmsRelationship entity) {
        PmsRelationship relationReverse = new PmsRelationship();
        relationReverse.setProductId(req.getRelProductId());
        relationReverse.setRelProductId(req.getProductId());
        relationReverse.setRelProductName(req.getProductName());
        relationReverse.setRelationScore((entity.getRelationScore() + 100) / 2);
        relationReverse.setTotalScore(relationReverse.getRelationScore());
        int i = pmsRelationshipMapper.insertOrUpdatePmsRelationship(relationReverse);
        log.info("insert or update reverse relation nums: {}", i);
        return i;
    }

    /**
     * 修改商品关联关系
     *
     * @param req 商品关联关系请求
     * @return 结果
     */
    @Override
    @Transactional
    public int updatePmsRelationship(ProductRelationReq req) {

        int changeRows = 0;

        // 1. 更新话术
        changeRows += salesPitchService.updateSalesPitch(req.getSalesPitchList(), req.getRelProductId(),
                ProductConstants.SalesPitch.RefType.PRODUCT_RELATION);

        // 2. 设置关联商品状态为: 1-已确认
        PmsRelationship pmsRelationship = new PmsRelationship();
        pmsRelationship.setId(req.getId());
        pmsRelationship.setState(1);
        pmsRelationshipMapper.updatePmsRelationship(pmsRelationship);

        return changeRows;
    }

    private void fillRelationScore(PmsRelationship entity, boolean isPinned) {
        int RANKING_THRESHOLD = sysConfigService.getInt(ConfigConstants.RELATION_RANKING_THRESHOLD, 10) * 100;
        int PINNED_ADD_SCORE = sysConfigService.getInt(ConfigConstants.PINNED_ADD_SCORE, 10) * 100;

        // 1.1 按照分数排序, 获得指定排名的关联商品
        PmsRelationship pmsRelationByLevel = pmsRelationshipMapper.selectPmsRelationshipByProductIdAndLevel(
                entity.getProductId(), entity.getRelLevel() - 1, RANKING_THRESHOLD);

        // 1.2.1 如果没有指定排名的, 则设置有推荐等级的最低分数
        if (pmsRelationByLevel == null) {
            entity.setRelationScore(RANKING_THRESHOLD);
        }
        // 1.2.2 如果有指定排名的且不是自己
        else if (!Objects.equals(entity.getRelProductId(), pmsRelationByLevel.getRelProductId())) {
            // 设置当前排名关联商品的分数 + 1
            if (isPinned) {
//                entity.setRelationScore(pmsRelationByLevel.getRelationScore());
                entity.setAddedScore(PINNED_ADD_SCORE);
            } else {
                entity.setRelationScore(pmsRelationByLevel.getRelationScore() + 100);
            }
        } else if (Objects.equals(entity.getRelProductId(), pmsRelationByLevel.getRelProductId())) {
            // 如果指定排名商品是自己
            throw new ServiceException("当前优先级商品已是该商品");
        }
        entity.setTotalScore(NumberUtil.getNonNullValue(entity.getRelationScore()) +
                NumberUtil.getNonNullValue(entity.getAddedScore()));
    }

    @Override
    public int pinned(ProductRelationReq req) {
        int rows = 0;

        int PINNED_ADD_SCORE_REVERSE = Integer.parseInt(sysConfigService.selectConfigByKey(ConfigConstants.PINNED_ADD_SCORE_REVERSE, "10")) * 100;


        // 1. 判断当前商品下关联商品是否已经有置顶,
        // 1.1 获取置顶关联商品
//        PmsRelationship relationParam = new PmsRelationship();
//        relationParam.setProductId(req.getProductId());
//        relationParam.setParams(new HashMap<String, Object>() {{
//            put("added", true); // 查找有新增分数的关联关系
//        }});
//        List<PmsRelationship> pmsRelationships = pmsRelationshipMapper.selectPmsRelationshipList(relationParam);
        // 1.2 若有直接拦截
//        if (CollectionUtils.isNotEmpty(pmsRelationships)) {
//            throw new ServiceException("当前商品已有指定商品, 不可再设置");
//        }

        // 2. 设置 added score
        PmsRelationship entity = EntityConverter.INSTANCE.toEntity(req);
        entity.setRelLevel(1);
        fillRelationScore(entity, true);
        rows += pmsRelationshipMapper.insertOrUpdatePmsRelationship(entity);

        // 创建关联商品运营计划
        createRelationPlan(entity);

        // 3. 设置反向 added score
        // 3.1 查询反向关系是否有 added score, 如果没有才设置
        PmsRelationship relationReverseParam = new PmsRelationship();
        relationReverseParam.setProductId(req.getRelProductId());
        relationReverseParam.setRelProductId(req.getProductId());
        List<PmsRelationship> relationReverseList = pmsRelationshipMapper.selectPmsRelationshipList(relationReverseParam);
        if (CollectionUtils.isNotEmpty(relationReverseList) && relationReverseList.get(0).getAddedScore() == 0) {
            PmsRelationship pmsRelationship = relationReverseList.get(0);
            pmsRelationship.setAddedScore(PINNED_ADD_SCORE_REVERSE);
            pmsRelationship.setTotalScore(pmsRelationship.getRelationScore() + pmsRelationship.getAddedScore());
            rows += pmsRelationshipMapper.updatePmsRelationship(pmsRelationship);
            log.info("update reverse relation, score: {}", PINNED_ADD_SCORE_REVERSE);
        }

        return rows;
    }

    @Override
    public int unpinned(ProductRelationReq req) {
        int rows = 0;

        // 1. 判断当前商品下关联商品是否已经有置顶,
        // 1.1 更新关联关系分数
        PmsRelationship relationship = pmsRelationshipMapper.selectPmsRelationshipById(req.getId());
        relationship.setTotalScore(relationship.getRelationScore());
        relationship.setAddedScore(0);
        rows += pmsRelationshipMapper.updatePmsRelationship(relationship);

        // 创建关联商品运营计划
        createRelationPlan(relationship);

        // 1.1 更新反向关联关系分数
        PmsRelationship relationshipReverse = pmsRelationshipMapper.selectByProductId(req.getRelProductId(), req.getProductId());
        relationshipReverse.setTotalScore(relationship.getRelationScore());
        relationshipReverse.setAddedScore(0);
        rows += pmsRelationshipMapper.updatePmsRelationship(relationshipReverse);

        return rows;
    }

    /**
     * 批量删除商品关联关系
     *
     * @param ids 需要删除的商品关联关系主键
     * @return 结果
     */
    @Override
    public int deletePmsRelationshipByIds(Long[] ids) {
        return pmsRelationshipMapper.deletePmsRelationshipByIds(ids);
    }

    /**
     * 删除商品关联关系信息
     *
     * @param id 商品关联关系主键
     * @return 结果
     */
    @Override
    public int deletePmsRelationshipById(Long id) {
        return pmsRelationshipMapper.deletePmsRelationshipById(id);
    }

    @Override
    @Transactional
    public void pmsRelationshipHandel(String wid, String wmOrderNo) {

        // TODO 暂时不计算关联商品
        if (true) {
            return;
        }
        // 获取当前订单所有商品
        List<PmsGoods> pmsGoodsCurrentOrder = pmsGoodsMapper.selectPmsGoodsListByOrderNo(wmOrderNo);
        log.info("products by current order: {}, orderNo: {}", pmsGoodsCurrentOrder.size(), wmOrderNo);

        // 获取 当前用户 7 天内所有订单
        OmsOrder omsOrderParam = new OmsOrder();
        HashMap<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("confirmWithinDays", 7);    //确认收货七天内
        omsOrderParam.setParams(paramMap);
        omsOrderParam.setCustomerId(Long.parseLong(wid));

        List<OmsOrder> confirmWithinOrders = omsOrderMapper.selectOmsOrderList(omsOrderParam);
        log.info("当前用户7天内所有订单数量:{}", confirmWithinOrders.size());

        confirmWithinOrders.stream()
                .filter(x -> !Objects.equals(wmOrderNo.toString(), x.getOrderCode()))   // 排序当前订单
                .forEach(order -> {
                    // 查询订单下的所有商品
                    List<PmsGoods> pmsGoods = pmsGoodsMapper.selectPmsGoodsListByOrderNo(order.getOrderCode());
                    if (CollectionUtils.isEmpty(pmsGoods)) {
                        log.error("当前订单没有对应商品, orderNo: {}", order.getOrderCode());
                    }

                    // 更新当前订单与七天内其他订单的关联关系
                    calculateRelation(pmsGoodsCurrentOrder, pmsGoods);

                });
        // 更新该订单为已计算过
        int i = omsOrderMapper.updateOmsOrderByOrderNo(new OmsOrder() {{
            setOrderCode(wmOrderNo);
            setHasCalculatedRelation(EBoolean.YES.getCode());
        }});
        log.info("update order relation, success num: {}", i);

    }

    private void calculateRelation(List<PmsGoods> pmsGoodsCurrentOrder, List<PmsGoods> pmsGoods) {
        int RANKING_THRESHOLD = Integer.parseInt(sysConfigService.selectConfigByKey(ConfigConstants.RELATION_RANKING_THRESHOLD)) * 100;

        log.info("开始计算商品关联分数值");
        pmsGoodsCurrentOrder.forEach(currentProduct -> {
            if (isWhiteListProduct(Long.toString(currentProduct.getId()))) {
                log.info("当前商品Id:{},商品名称:{},为白名单, 不建立关联关系", currentProduct.getId(), currentProduct.getName());
                return;
            }

            pmsGoods.forEach(hisProduct -> {
                if (Objects.equals(hisProduct.getId(), currentProduct.getId())) {
                    return;     // 相同商品不建立关联关系
                }

                if (isWhiteListProduct(Long.toString(hisProduct.getId()))) {
                    log.info("his 当前商品Id:{},商品名称:{},为白名单, 不建立关联关系", hisProduct.getId(), hisProduct.getName());
                    return;
                }

                PmsRelationship pr1 = new PmsRelationship();
                pr1.setProductId(hisProduct.getId());
                pr1.setRelProductId(currentProduct.getId());
                List<PmsRelationship> pmsRelationships = selectPmsRelationshipList(pr1);
                log.info("relation info, productId: {}, relProductsId: {}, his relation num: {}",
                        hisProduct.getId(), currentProduct.getId(), pmsRelationships.size());

                if (CollectionUtils.isEmpty(pmsRelationships)) {
                    PmsRelationship pmsRelationship = new PmsRelationship();
                    pmsRelationship.setProductId(hisProduct.getId());
                    pmsRelationship.setProductName(hisProduct.getName());
                    pmsRelationship.setRelProductId(currentProduct.getId());
                    pmsRelationship.setRelProductName(currentProduct.getName());
                    pmsRelationship.setRelationScore(100);
                    pmsRelationship.setCreateBy(UserConstants.SYS_USER);
                    int i = pmsRelationshipMapper.insertPmsRelationship(pmsRelationship);
                    pmsRelationshipRecordService.createRecord(pmsRelationship, 0);
                    log.info("create product relation, success num: {}", i);
                } else {
                    PmsRelationship pmsRelationship = pmsRelationships.get(0);
                    Integer beforeTotalScore = pmsRelationship.getTotalScore();
                    // 如果达到阈值则生成关联商品话术
                    if (pmsRelationship.getRelationScore() > RANKING_THRESHOLD &&
                            pmsRelationship.getRelationScore() + 100 >= RANKING_THRESHOLD) {
                        materialServiceImpl.autoGenerate(GeneratingReq.builder()
                                .type(EGenerateType.RELATION_PRODUCT)
                                .refId(pmsRelationship.getRelProductId().toString())
                                .build());

                        // 生成关联商品的运营计划
                        createRelationPlan(pmsRelationship);
                    }

                    pmsRelationship.setRelationScore(pmsRelationship.getRelationScore() + 100);
                    pmsRelationship.setUpdateTime(new Date());
                    pmsRelationship.setUpdateBy(UserConstants.SYS_USER);
                    int i = pmsRelationshipMapper.updatePmsRelationship(pmsRelationship);
                    pmsRelationshipRecordService.createRecord(pmsRelationship, beforeTotalScore);
                    log.info("update product relation, success num: {}", i);
                }

                PmsRelationship pr2 = new PmsRelationship();
                pr2.setProductId(currentProduct.getId());
                pr2.setRelProductId(hisProduct.getId());
                List<PmsRelationship> pmsRelationshipsOpposite = pmsRelationshipMapper.selectPmsRelationshipList(pr2);
                log.info("relation info, productId: {}, relProductsId: {}, his relation num: {}",
                        currentProduct.getId(), hisProduct.getId(), pmsRelationships.size());

                if (CollectionUtils.isEmpty(pmsRelationshipsOpposite)) {
                    PmsRelationship pmsRelationship = new PmsRelationship();
                    pmsRelationship.setProductId(currentProduct.getId());
                    pmsRelationship.setProductName(currentProduct.getName());
                    pmsRelationship.setRelProductId(hisProduct.getId());
                    pmsRelationship.setRelProductName(hisProduct.getName());
                    pmsRelationship.setRelationScore(50);
                    pmsRelationship.setCreateBy(UserConstants.SYS_USER);
                    int i = pmsRelationshipMapper.insertPmsRelationship(pmsRelationship);
                    pmsRelationshipRecordService.createRecord(pmsRelationship, 0);
                    log.info("create product relation, success num: {}", i);
                } else {
                    PmsRelationship pmsRelationshipOpposite = pmsRelationshipsOpposite.get(0);
                    Integer beforeTotalScore = pmsRelationshipOpposite.getTotalScore();
                    // 如果达到阈值则生成关联商品话术
                    if (pmsRelationshipOpposite.getRelationScore() < RANKING_THRESHOLD &&
                            pmsRelationshipOpposite.getRelationScore() + 50 >= RANKING_THRESHOLD) {
                        materialServiceImpl.autoGenerate(GeneratingReq.builder()
                                .type(EGenerateType.RELATION_PRODUCT)
                                .refId(pmsRelationshipOpposite.getRelProductId().toString())
                                .build());

                    }
                    pmsRelationshipOpposite.setRelationScore(pmsRelationshipOpposite.getRelationScore() + 50);
                    pmsRelationshipOpposite.setUpdateTime(new Date());
                    pmsRelationshipOpposite.setUpdateBy(UserConstants.SYS_USER);
                    int i = pmsRelationshipMapper.updatePmsRelationship(pmsRelationshipOpposite);
                    pmsRelationshipRecordService.createRecord(pmsRelationshipOpposite, beforeTotalScore);

                    log.info("update product relation, success num: {}", i);
                }
            });
        });
    }

    /**
     * 判断商品是否在白名单中
     * @param productId 商品id
     * @return true/false 是否在白名单中
     */
    public Boolean isWhiteListProduct(String productId) {
        boolean isWhite = false;
        String whiteListConfig = sysConfigService.selectConfigByKey(ConfigConstants.RELATION_WHITE_LIST);
        if (StringUtils.isNotBlank(whiteListConfig)) {
            Set<String> whiteList = Arrays.stream(whiteListConfig.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());
            if (whiteList.contains(productId)) {
                isWhite = true;
            }
        }
        return isWhite;
    }

    public void scanOrderToDealRelation() {
        try {
            // 分页参数
            int pageSize = 20;
            int pageNum = 1;

            // 分页查询运营计划列表，获取关联商品的运营计划
            while (true) {
                PageHelper.startPage(pageNum, pageSize, "create_time desc");
                List<OmsOrder> omsOrders = omsOrderMapper.selectOmsOrderList(new OmsOrder() {{
                    setHasCalculatedRelation(EBoolean.NO.getCode());
                }});

                if (CollectionUtils.isEmpty(omsOrders)) {
                    break;
                }

                // 处理每个运营计划
                for (OmsOrder omsOrder : omsOrders) {
                    try {

                        if ("5".equals(omsOrder.getStatus())) {
                            log.info("订单已取消， orderNo: {}", omsOrder.getOrderCode());
                            omsOrderMapper.updateOmsOrderByOrderNo(new OmsOrder() {{
                                setOrderCode(omsOrder.getOrderCode());
                                setHasCalculatedRelation(EBoolean.YES.getCode());
                            }});
                            continue;
                        }
                        if ("1".equals(omsOrder.getStatus())) {
                            continue;
                        }

                        pmsRelationshipHandel(omsOrder.getCustomerId().toString(), omsOrder.getOrderCode());

                    } catch (Exception e) {
                        log.error("处理关联商品分值计算时异常, orderNo: {}", omsOrder.getOrderCode(), e);
                    }
                }

                // 防止死循环
                pageNum++;
            }
        } catch (Exception e) {
            log.error("处理关联商品分值计算时异常", e);
        }

    }
}
