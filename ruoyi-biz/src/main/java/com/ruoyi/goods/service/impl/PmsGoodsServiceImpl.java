package com.ruoyi.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.component.ProductClipComponent;
import com.ruoyi.biz.component.UserPlanHandlerComponent;
import com.ruoyi.biz.config.PyOpenConfig;
import com.ruoyi.biz.domain.BizCollectProduct;
import com.ruoyi.biz.domain.BizOperatePlan;
import com.ruoyi.biz.domain.BizVersionRecord;
import com.ruoyi.biz.domain.req.ClipByWebReq;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.biz.enums.EOperatePlanRefType;
import com.ruoyi.biz.enums.EOperatePlanType;
import com.ruoyi.biz.enums.EVersionRefType;
import com.ruoyi.biz.service.IBizCollectProductService;
import com.ruoyi.biz.service.IBizOperatePlanService;
import com.ruoyi.biz.service.IBizSalesPitchService;
import com.ruoyi.biz.service.IBizVersionRecordService;
import com.ruoyi.biz.service.IBizWordAnalyzedService;
import com.ruoyi.biz.service.ICorpusDocumentService;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.ConfigConstants;
import com.ruoyi.common.constant.ProductConstants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.EJzSendMessageType;
import com.ruoyi.common.enums.EVersionEvent;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.bean.EntityUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.goods.domain.PmsBrand;
import com.ruoyi.goods.domain.PmsCategory;
import com.ruoyi.goods.domain.PmsGoods;
import com.ruoyi.goods.domain.PmsGoodsAttributeValue;
import com.ruoyi.goods.domain.PmsGoodsImage;
import com.ruoyi.goods.domain.PmsGoodsSpecValue;
import com.ruoyi.goods.domain.PmsSku;
import com.ruoyi.goods.domain.PmsSkuImage;
import com.ruoyi.goods.domain.PmsSkuItem;
import com.ruoyi.goods.domain.PmsSkuSpecValue;
import com.ruoyi.goods.domain.PmsSpec;
import com.ruoyi.goods.domain.PmsSpecValue;
import com.ruoyi.goods.domain.StoreSpu;
import com.ruoyi.goods.domain.dto.SpuSimpleSearchReq;
import com.ruoyi.goods.domain.req.BizMomentsReq;
import com.ruoyi.goods.domain.req.ProductDetailCleanReq;
import com.ruoyi.goods.domain.req.RecommendProductReq;
import com.ruoyi.goods.domain.req.RepurchaseReq;
import com.ruoyi.goods.domain.vo.GoodsSpecValueVo;
import com.ruoyi.goods.domain.vo.GoodsVo;
import com.ruoyi.goods.domain.vo.SkuSpecValueVo;
import com.ruoyi.goods.domain.vo.SkuVo;
import com.ruoyi.goods.mapper.PmsGoodsAttributeValueMapper;
import com.ruoyi.goods.mapper.PmsGoodsMapper;
import com.ruoyi.goods.mapper.PmsRelationshipMapper;
import com.ruoyi.goods.service.IPmsBrandService;
import com.ruoyi.goods.service.IPmsCategoryService;
import com.ruoyi.goods.service.IPmsGoodsAttributeValueService;
import com.ruoyi.goods.service.IPmsGoodsImageService;
import com.ruoyi.goods.service.IPmsGoodsService;
import com.ruoyi.goods.service.IPmsGoodsServiceSupportService;
import com.ruoyi.goods.service.IPmsGoodsSpecValueService;
import com.ruoyi.goods.service.IPmsSkuService;
import com.ruoyi.goods.service.IPmsSpecService;
import com.ruoyi.goods.service.IPmsSpecValueService;
import com.ruoyi.goods.utils.CommonConstant;
import com.ruoyi.goods.utils.PageHelper;
import com.ruoyi.goods.vo.SpuSearchCondition;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.weimo.config.WeiMoConfig;
import com.ruoyi.weimo.domain.vo.WmActivitiesVo;
import com.ruoyi.weimo.domain.vo.WmGoodsPageRes;
import com.ruoyi.weimo.service.IWeiMobService;
import com.ruoyi.weimo.service.IWmActivitiesService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 商品Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-07-24
 */
@Service
@Slf4j
public class PmsGoodsServiceImpl implements IPmsGoodsService {

    private static final Logger logger = LoggerFactory.getLogger(PmsGoodsServiceImpl.class);

    /**
     * 注入商品服务接口
     */
    @Autowired
    private IPmsGoodsServiceSupportService spuServicceSupportService;

    @Resource
    private IPmsSpecService pmsSpecService;

    @Resource
    private IPmsSpecValueService pmsSpecValueService;

    /**
     * 注入商品规格值服务接口
     */
    @Autowired
    private IPmsGoodsSpecValueService spuSpecValueService;

    /**
     * 注入单品服务接口
     */
    @Autowired
    private IPmsSkuService skuService;

    /**
     * 注入商品属性值服务接口
     */
    @Autowired
    private IPmsGoodsAttributeValueService spuAttributeValueService;

    /**
     * 注入商品图片
     */
    @Autowired
    private IPmsGoodsImageService spuImageService;

    /**
     * 注入品牌服务接口
     */
    @Autowired
    private IPmsBrandService brandService;
    /**
     * 注入商品数据库接口
     */
    @Resource
    private PmsGoodsMapper spuMapper;

    @Resource
    private IWeiMobService weiMobService;

    @Resource
    private WeiMoConfig weiMoConfig;

    /**
     * 注入分类服务接口
     */
    @Resource
    private IPmsCategoryService categoryService;

    @Resource
    private IBizCollectProductService bizCollectProductService;


//    /**
//     * 注入系统基本设置
//     */
//    @Autowired
//    private BaseInfoSetService baseInfoSetService;

//    /**
//     * 注入门店单品关联服务
//     */
//    @Autowired
//    private ITStoreSkuService storeSkuService;

    @Resource
    private PmsGoodsMapper pmsGoodsMapper;

    @Autowired
    private IPmsGoodsAttributeValueService pmsGoodsAttributeValueService;

    @Value("${spring.profiles.active}")
    private String active;
    @Resource
    private PmsRelationshipMapper pmsRelationshipMapper;

    @Resource
    private RedisCache redisCache;

    @Resource
    private ICorpusDocumentService corpusDocumentService;

    @Resource
    private IBizVersionRecordService bizVersionRecordService;

    @Resource
    private PyOpenConfig pyOpenConfig;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private IBizSalesPitchService salesPitchService;

    @Resource
    private IBizWordAnalyzedService bizWordAnalyzedService;

    @Resource
    private IWmActivitiesService wmActivitiesService;

    @Resource
    @Lazy
    private IBizOperatePlanService bizOperatePlanService;

    @Resource
    private UserPlanHandlerComponent userPlanHandlerComponent;

    @Resource
    private ProductClipComponent productClipComponent;

    @Resource
    private PmsGoodsAttributeValueMapper pmsGoodsAttributeValueMapper;

    @Resource
    private PmsGoodsAttributeValueMapper spuAttributeValueMapper;


    /**
     * 查询商品
     *
     * @param id 商品ID
     * @return 商品
     */
    @Override
    public PmsGoods selectPmsGoodsById(Long id) {
        return pmsGoodsMapper.selectPmsGoodsById(id);
    }

    /**
     * 查询商品列表
     *
     * @param pmsGoods 商品
     * @return 商品
     */
    @Override
    public List<PmsGoods> selectPmsGoodsList(PmsGoods pmsGoods) {
        return pmsGoodsMapper.selectPmsGoodsList(pmsGoods);
    }

    @Override
    public List<PmsGoods> querySpus(PmsGoods pmsGoods) {
        List<PmsGoods> list = setVisitUrl(setBrandAndCategorys(pmsGoodsMapper.selectPmsGoodsList(pmsGoods)));

//        Set<Long> productIdSet = new HashSet<>();
//        //查询商品是否同步到AI
//        try {
//            JSONObject res = HttpUtils.postToJsonObject(pyOpenConfig.getGoodsSyncCheck(), new HashMap<>());
//            List<Long> productIdList = res.getJSONObject("data").getJSONObject("object").getJSONArray("product_ids").toJavaList(Long.class);
//            //转为set
//            productIdSet = new HashSet<>(productIdList);
//        } catch (Exception e) {
//            logger.error("查询商品是否同步到AI失败", e);
//        }
//
//        Set<Long> finalProductIdSet = productIdSet;
//        list.forEach(goods -> goods.setIsSyncToAI(finalProductIdSet.contains(goods.getId())));
        return list;
    }

    @Override
    public PageHelper<PmsGoods> querySpus(PageHelper<PmsGoods> pageHelper, SpuSearchCondition spuSearchCondition) {

        logger.debug("querySpus and pageHelper:{} \r\n  spuSearchCondition:{}", pageHelper, spuSearchCondition);

        // 搜索参数
        Map<String, Object> params = spuSearchCondition.getSearchMap();

        return pageHelper.setListDates(setVisitUrl(setBrandAndCategorys(spuMapper.querySpus(pageHelper.getQueryParams(params, spuMapper.querySpuCount(params))))));
    }

    @Override
    public PageHelper<PmsGoods> queryAllStoreSpus(PageHelper<PmsGoods> pageHelper, SpuSearchCondition spuSearchCondition) {

        logger.debug("queryAllStoreSpus and pageHelper:{} \r\n spuSearchCondition:{} ", pageHelper, spuSearchCondition);

        // 搜索参数
        Map<String, Object> params = spuSearchCondition.getSearchMap();
        return pageHelper.setListDates(setBrandAndCategorys(spuMapper.queryAllStoreSpus(pageHelper.getQueryParams(params, spuMapper.queryAllStoreSpusCount(params)))));
    }

    @Override
    public PageHelper<PmsGoods> querySimpleSpus(PageHelper<PmsGoods> pageHelper, SpuSearchCondition spuSearchCondition) {
        logger.debug("querySimpleSpus and and pageHelper:{} \r\n  spuSearchCondition:{}", pageHelper, spuSearchCondition);
        // 搜索参数
        Map<String, Object> params = spuSearchCondition.getSearchMap();
        return pageHelper.setListDates(spuMapper.querySpus(pageHelper.getQueryParams(params, spuMapper.querySpuCount(params))));
    }

    /**
     * 新增商品
     *
     * @param spu 商品
     * @return 结果
     */
    @Override
    public int insertPmsGoods(PmsGoods spu) {
        logger.debug("addSpu and goods :{}", spu);

        if (Objects.isNull(spu)) {
            logger.error("addSpu fail due to goods is null...");
            return 0;
        }

        if (spu.getSkus().stream().anyMatch(PmsSku::hasBatchPriceAndMemberPrice)) {
            logger.error("addSpu fail due to exist sku has member price and batch both");
            return -1;
        }

        // 设置单品的店铺和审核状态
//        spu.setSkuStoreIdAndAuditAndShelvesStatus(baseInfoSetService.isSkuNeedAudit());
        spu.setSkuStoreIdAndAuditAndShelvesStatus(false);

        // 设置单品是否虚拟
        spu.setSkuIsVirtual();

        // 设置单品物流模版id
        spu.setSkuLogisticsTemplateId();

        // 设置商品的分类信息
//        setSpuCategory(spu);

        // 新增商品
        spu.setCreateTime(DateUtils.getNowDate());
        pmsGoodsMapper.insertPmsGoods(spu);

        // 设置商品关联信息的商品id
        spu.setSpuLinkedSpuId();

        // 新增商品服务支持
        spuServicceSupportService.addSpuServicceSupport(spu.getSpuServiceSupports());


        spu.getSpuSpecValues().stream().collect(Collectors.groupingBy(PmsGoodsSpecValue::getSpecId)).forEach((specId, specValues) -> {
            PmsSpec pmsSpec = specValues.get(0).getSpec();
            List<PmsSpecValue> specValueList = new ArrayList<>();
            //规格值
            specValues.forEach(specValue -> {
                PmsSpecValue pmsSpecValue = new PmsSpecValue();
                pmsSpecValue.setId(specValue.getSpecValueId());
                pmsSpecValue.setSpecId(specId);
                pmsSpecValue.setName(specValue.getValueRemark());
                specValueList.add(pmsSpecValue);
            });
            pmsSpec.setSpecValues(specValueList);

            //新增规格以及规格值
            pmsSpecService.insertPmsSpec(pmsSpec);
        });

        spuSpecValueService.updateSpuSpecValues(spu.getSpuSpecValues(), spu.getId());

//        // 新增商品规格值信息
//        spuSpecValueService.addSpuSpecValues(spu.getSpuSpecValues());


        spuImageService.updateSpuImages(spu.getSpuImages(), spu.getId());

        // 新增商品图片  updateSpuImages
//        spuImageService.addSpuImages(spu.getSpuImages());

        // 新增商品属性值
//        spuAttributeValueService.addSpuAttributeValues(spu.getSpuAttributeValues());

//        spuAttributeValueService.updateSpuAttributValues(spu.getSpuAttributeValues(), spu.getId());

        // 新增单品
        skuService.addSkus(spu.getSkus().stream().peek(sku -> sku.fixSkuNameForSave(spu.getName())).collect(Collectors.toList()));

        //通知AI商品
        sendGoodsToAlex(spu.getId(), false);

        return 1;
    }

    /**
     * 修改商品
     *
     * @param spu 商品
     * @return 结果
     */
    @Override
    public int updatePmsGoods(PmsGoods spu) {
        logger.debug("updateSpu  and spu:{}", spu);

        if (Objects.isNull(spu)) {
            logger.error("updateSpu fail due to spu is null....");
            return 0;
        }

        // 设置商品的默认图片
        spu.setDefaultPic();

        //todo  如果不是平台的商品并且审核开关打开 则需要判断是否修改了商品如果商品修改了 则商品下的所有单品都需要审核
//        if (CommonConstant.ADMIN_STOREID != spu.getStoreId() && baseInfoSetService.isSkuNeedAudit()) {
//            setSkuAuditStatus(spu);
//        }

        // 更新商品信息
        if (spuMapper.updateSpu(spu) == 0) {
            logger.error("updateSpu fail....");
            return 0;
        }

        // 设置店铺id
        spu.setSkuStoreIdAndShelvesStatus();

        // 设置单品物流模版id
        spu.setSkuLogisticsTemplateId();

        // 设置商品相关的商品id
        spu.setSpuLinkedSpuId();

        // 更新商品图片
        spuImageService.updateSpuImages(spu.getSpuImages(), spu.getId());

        // 更新商品属性值
        spuAttributeValueService.updateSpuAttributValues(spu.getSpuAttributeValues(), spu.getId());

        // 更新商品服务支持
        spuServicceSupportService.updateSpuServiceSupport(spu.getSpuServiceSupports(), spu.getId());

        // 更新商品规格值
        spuSpecValueService.updateSpuSpecValues(spu.getSpuSpecValues(), spu.getId());

        // 根据商品id和storeId查询商品信息
        Map<String, Object> params = new HashMap<>();
        params.put("id", spu.getId());
        params.put("storeId", spu.getStoreId());

        PmsGoods sputemp = spuMapper.querySpu(params);

        if (Objects.isNull(sputemp)) {
            logger.error("updateSpu fail...");
            return 0;
        }

        // 更新单品
        skuService.updateSkus(spu.getSkus().stream().peek(sku -> sku.fixSkuNameForSave(spu.getName())).collect(Collectors.toList()), spu.getId(), spu.getStoreId(), sputemp.getCommissionRate());

        //通知AI商品
        sendGoodsToAlex(spu.getId(), false);

        // 分析商品商业词汇
        ThreadPoolTaskExecutor executor = SpringUtils.getBean("threadPoolTaskExecutor");
        executor.execute(() -> bizWordAnalyzedService.analyse(spu.getId(), "0", true));

        return 1;
    }

    /**
     * 批量删除商品
     *
     * @param ids 需要删除的商品ID
     * @return 结果
     */
    @Override
    public int deletePmsGoodsByIds(Long[] ids) {
        //通知AI商品
        Arrays.stream(ids).forEach(id -> sendGoodsToAlex(id, true));
        return pmsGoodsMapper.deletePmsGoodsByIds(ids);
    }

    /**
     * 删除商品信息
     *
     * @param id 商品ID
     * @return 结果
     */
    @Override
    public int deletePmsGoodsById(Long id) {
        return pmsGoodsMapper.deletePmsGoodsById(id);
    }


    @Override
    public PmsGoods querySpu(long id, long storeId) {

        logger.debug("querySpu and id :{} \r\n storeId:{}", id, storeId);


        // 查询商品信息
        PmsGoods spu = querySimpleSpu(id, storeId);

        if (Objects.isNull(spu)) {
            logger.error("querySpu fail due to goods is not exist...");
            return spu;
        }

        // 设置商品的分类信息
        setSpuCategorys(spu);

        // 设置商品的品牌信息
        spu.setBrand(brandService.queryBrandById(spu.getBrandId(), CommonConstant.QUERY_WITH_NO_STORE));

        // 查询商品的服务支持
        spu.setSpuServiceSupports(spuServicceSupportService.queryBySpuId(id));

        // 查询商品的规格值信息(包括规格信息和规格值信息)
        spu.setSpuSpecValues(spuSpecValueService.queryBySpuIdWithSpec(id));

        // 查询商品图片
        spu.setSpuImages(spuImageService.queryBySpuId(id));

        // 查询商品属性值
        spu.setSpuAttributeValues(spuAttributeValueService.queryBySpuId(id));

        // 查询商品下面的单品信息
        spu.setSkus(skuService.querySkuBySpuId(id, spu.getStoreId(), PmsSkuItem.IMAGE, PmsSkuItem.MEMBER_PRICE, PmsSkuItem.SPEC, PmsSkuItem.BATCH));

        return spu;
    }

    @Override
    public GoodsVo querySpuSimpleById(long id, long storeId) {
        // 查询商品信息
        PmsGoods spu = querySimpleSpu(id, storeId);
        GoodsVo goodsVo = EntityUtils.copyData(spu, GoodsVo.class);

        goodsVo.setFirstCategory(categoryService.selectPmsCategoryById(spu.getFirstCateId()));
        goodsVo.setSecondCategory(categoryService.selectPmsCategoryById(spu.getSecondCateId()));
        // 设置商品的品牌信息
        goodsVo.setBrand(brandService.queryBrandById(spu.getBrandId(), CommonConstant.QUERY_WITH_NO_STORE));

        // 查询商品的规格值信息(包括规格信息和规格值信息)
        List<PmsGoodsSpecValue> pmsGoodsSpecValues = spuSpecValueService.queryBySpuIdWithSpec(id);

        //商品规格值列表
        List<GoodsSpecValueVo> goodsSpecValueVoList = pmsGoodsSpecValues.stream().map(pmsGoodsSpecValue -> {
            GoodsSpecValueVo res = EntityUtils.copyData(pmsGoodsSpecValue, GoodsSpecValueVo.class);
            res.setSpecName(pmsGoodsSpecValue.getSpec().getName());
            return res;
        }).collect(Collectors.toList());
        goodsVo.setSpuSpecValues(goodsSpecValueVoList);

        // 获取商品关联活动信息
        List<WmActivitiesVo> activities = wmActivitiesService.getActivitiesByProductId(spu);
        goodsVo.setActivities(activities);

        // 设置商品附加属性
        List<PmsGoodsAttributeValue> pmsGoodsAttributeValues = pmsGoodsAttributeValueMapper.queryBySpuId(id);
        List<PmsGoodsAttributeValue> pmsGoodsAttributeValuesCollect = pmsGoodsAttributeValues.stream().filter(pmsGoodsAttributeValue ->
                        "product_desc_clean".equals(pmsGoodsAttributeValue.getAttributeName()) || "product_fab_clean".equals(pmsGoodsAttributeValue.getAttributeName()))
                .collect(Collectors.toList());
        goodsVo.setSpuAttributeValues(pmsGoodsAttributeValuesCollect);

        List<PmsSku> skuList = skuService.querySkuBySpuId(id, spu.getStoreId(), PmsSkuItem.IMAGE, PmsSkuItem.SPEC);

        BigDecimal minPrice = BigDecimal.TEN.pow(10);
        BigDecimal maxPrice = BigDecimal.ZERO;

        List<SkuVo> skuVoList = new ArrayList<>();
        for (PmsSku entity : skuList) {
            SkuVo res = EntityUtils.copyData(entity, SkuVo.class);
            if (res.getPrice().compareTo(maxPrice) > 0) {
                maxPrice = res.getPrice();
            }
            if (res.getPrice().compareTo(minPrice) < 0) {
                minPrice = res.getPrice();
            }

            //库存状态 0 充足，1一般，2无
            res.setInventoryStatus("0");
            if (entity.getStock() <= 0) {
                res.setInventoryStatus("2");
            } else if (entity.getStock() <= 10) {
                res.setInventoryStatus("1");
            }
            //规格值
            List<SkuSpecValueVo> skuSpecValueVoList = entity.getSkuSpecValues().stream().map(skuSpecValue -> {
                SkuSpecValueVo specValueVo = EntityUtils.copyData(skuSpecValue, SkuSpecValueVo.class);
                specValueVo.setSpecName(skuSpecValue.getSpec().getName());
                return specValueVo;
            }).collect(Collectors.toList());
            res.setSkuSpecValues(skuSpecValueVoList);

            skuVoList.add(res);
        }

        goodsVo.setMinSalePrice(minPrice);
        goodsVo.setMaxSalePrice(maxPrice);


        goodsVo.setSpuSpecValues(goodsSpecValueVoList);
        goodsVo.setSkus(skuVoList);

        return goodsVo;
    }

    @Override
    public List<GoodsVo> spuSimpleSearch(SpuSimpleSearchReq spuSimpleSearchReq) {
        logger.debug("spuSimpleSearch and spuSimpleSearchReq:{}", JSON.toJSONString(spuSimpleSearchReq));

        List<PmsCategory> categoryList = categoryService.selectPmsCategoryList(new PmsCategory() {{
            setGrade(2);
            setName(spuSimpleSearchReq.getCategoryName());
        }});

        if (CollectionUtils.isEmpty(categoryList)) {
            return Collections.emptyList();
        }
        PmsCategory category = categoryList.get(0);

        PmsCategory firstCategory = categoryService.selectPmsCategoryById(category.getParentId());

        return spuMapper.selectPmsGoodsList(new PmsGoods() {{
            setFirstCateId(category.getParentId());
            setSecondCateId(category.getId());
        }}).stream().map(goods -> {
            GoodsVo goodsVo = EntityUtils.copyData(goods, GoodsVo.class);
            goodsVo.setFirstCategory(firstCategory);
            goodsVo.setSecondCategory(category);
            goodsVo.setBrand(brandService.queryBrandById(goods.getBrandId(), CommonConstant.QUERY_WITH_NO_STORE));
            return goodsVo;
        }).collect(Collectors.toList());
    }


    /**
     * 分页查询单品信息(目前用在社区团购新增团购页面选择列表查询)
     *
     * @param pageHelper 分页帮助类
     * @param storeId    店铺id
     * @param skuNo      单品编号
     * @param skuItemse  要设置的属性枚举
     * @return 返回单品信息 (包括单品的规格信息)
     */
    @Override
    public PageHelper<PmsSku> querySkus(PageHelper<PmsSku> pageHelper, long storeId, String name, String skuNo, PmsSkuItem... skuItemse) {

        logger.debug("querySkus and pageHelper:{} \r\n  skuSearchCondition:{}", pageHelper, storeId, name, skuNo, skuItemse);


        //获取单品分页结果集
        PageHelper<PmsSku> skuPageHelper = skuService.querySkusWithSpecs(pageHelper, storeId, name, skuNo, skuItemse);


        //根据spuID 查询移动版详情
        skuPageHelper.setList(skuPageHelper.getList().stream().map(sku -> sku.addMobileDesc(spuMapper.querySpu(sku.querySpuParam()).getMobileDesc())).collect(Collectors.toList()));

        return skuPageHelper;
    }


    @Transactional
    @Override
    public int deleteSpu(PmsGoods spu) {

        logger.debug("deleteSpu and goods:{}", spu);

        if (Objects.isNull(spu)) {
            logger.error("deleteSpu fail due to goods is null...");
            return 0;
        }
        // 删除商品信息
        if (spuMapper.deleteSpu(spu) == 0) {
            logger.error("deleteSpu fail...");
            return 0;
        }

        // 删除商品属性值
        spuAttributeValueService.deleteBySpuId(spu.getId());

        // 删除商品图片
        spuImageService.deleteBySpuId(spu.getId());

        // 删除商品服务支持
        spuServicceSupportService.deleteBySpuId(spu.getId());

        // 删除商品规格值
        spuSpecValueService.deleteBySpuId(spu.getId());

        // 删除单品
        skuService.deleteBySpuId(spu.getId(), spu.getStoreId());

        //通知AI商品
        sendGoodsToAlex(spu.getId(), true);
        return 1;
    }

    @Override
    public int deleteSpus(List<PmsGoods> spus) {
        logger.debug("deleteSpus and spus:{}", spus);

        if (CollectionUtils.isEmpty(spus)) {
            return 0;
        }

        spus.parallelStream().forEach(this::deleteSpu);
        return 1;
    }

    @Transactional
    @Override
    public int updateShelvesStatus(List<Long> spuIds, String status, long storeId, Consumer<Long> consumer) {
        logger.debug("updateShelvesStatus and spuIds:{} \r\n status:{} \r\n storeId:{}", spuIds, status, storeId);

        if (CollectionUtils.isEmpty(spuIds)) {
            logger.error("updateShelvesStatus fail due to spuIds is empty....");
            return 0;
        }

        // 修改商品的上下架状态
        updateSpuShelvesStatus(spuIds, status, storeId);

        // 修改单品的上下架状态
        skuService.updateShelvesStatus(spuIds, status, storeId);

        spuIds.forEach(id -> sendGoodsToAlex(id, false));

        // TODO 商品上下架，该商品有关的运营计划处理（复购和关联商品）
        this.processPlan(spuIds, status);


//        // 如果是上架 并且是店铺商品和审核开关开启
//        if (CommonConstant.ADMIN_STOREID != storeId && baseInfoSetService.isSkuNeedAudit() && "1".equals(status)) {
//            // 修改商品为待审核状态
//            updateSpuToAudit(spuIds, storeId);
//            // 修改单品为待审核状态
//            skuService.updateSkuToAudit(spuIds, storeId);
//        }

        return 1;
    }

    /**
     * 修改商品的状态为待审核状态
     *
     * @param spuIds  商品id
     * @param storeId 店铺id
     */
    private void updateSpuToAudit(List<Long> spuIds, long storeId) {
        logger.debug("updateSpuToAudit and spuIds:{} \r\n storeId:{}", spuIds, storeId);

        Map<String, Object> params = new HashMap<>();
        params.put("spuIds", spuIds);
        params.put("status", "2");
        params.put("storeId", storeId);

        spuMapper.updateSpuAuditStatus(params);
    }

    /**
     * 修改商品的上下架状态
     *
     * @param spuIds  商品id
     * @param status  上下架状态
     * @param storeId 店铺id
     */
    private void updateSpuShelvesStatus(List<Long> spuIds, String status, long storeId) {
        logger.debug("updateSpuShelvesStatus and spuIds:{} \r\n status:{} \r\n storeId:{}", spuIds, status, storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("spuIds", spuIds);
        params.put("status", status);
        params.put("storeId", storeId);
        spuMapper.updateShelvesStatus(params);
    }

    @Transactional
    @Override
    public int updateSpu(PmsGoods spu, Consumer<Long> consumer) {
        logger.debug("updateSpu  and goods:{}", spu);

        if (Objects.isNull(spu)) {
            logger.error("updateSpu fail due to goods is null....");
            return 0;
        }

        // 设置商品的默认图片
        spu.setDefaultPic();

//        // 如果不是平台的商品并且审核开关打开 则需要判断是否修改了商品如果商品修改了 则商品下的所有单品都需要审核
//        if (CommonConstant.ADMIN_STOREID != spu.getStoreId() && baseInfoSetService.isSkuNeedAudit()) {
//            setSkuAuditStatus(spu);
//        }

        // 更新商品信息
        if (spuMapper.updateSpu(spu) == 0) {
            logger.error("updateSpu fail....");
            return 0;
        }

        // 设置店铺id
        spu.setSkuStoreIdAndShelvesStatus();

        // 设置单品物流模版id
        spu.setSkuLogisticsTemplateId();

        // 设置商品相关的商品id
        spu.setSpuLinkedSpuId();

        // 更新商品图片
        spuImageService.updateSpuImages(spu.getSpuImages(), spu.getId());

        // 更新商品属性值
        spuAttributeValueService.updateSpuAttributValues(spu.getSpuAttributeValues(), spu.getId());

        // 更新商品服务支持
        spuServicceSupportService.updateSpuServiceSupport(spu.getSpuServiceSupports(), spu.getId());

        // 更新商品规格值
        spuSpecValueService.updateSpuSpecValues(spu.getSpuSpecValues(), spu.getId());

        // 根据商品id和storeId查询商品信息
        Map<String, Object> params = new HashMap<>();
        params.put("id", spu.getId());
        params.put("storeId", spu.getStoreId());

        PmsGoods sputemp = spuMapper.querySpu(params);

        if (Objects.isNull(sputemp)) {
            logger.error("updateSpu fail...");
            return 0;
        }

        // 更新单品
        skuService.updateSkus(spu.getSkus().stream().peek(sku -> sku.fixSkuNameForSave(spu.getName())).collect(Collectors.toList()), spu.getId(), spu.getStoreId(), sputemp.getCommissionRate());


        return 1;
    }

    @Override
    public String queryMobileDesc(long id) {
        logger.debug("queryMobileDesc and id :{}", id);
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("storeId", CommonConstant.QUERY_WITH_NO_STORE);

        // 查询商品信息
        PmsGoods spu = spuMapper.querySpu(params);


        return Objects.isNull(spu) ? "" : spu.getMobileDesc();
    }

    @Override
    public PmsGoods querySimpleSpu(long spuId, long storeId) {

        Map<String, Object> params = new HashMap<>();
        params.put("id", spuId);
        params.put("storeId", storeId);

        return spuMapper.querySpu(params);
    }

    /**
     * 根据三级分类id查询是否关联商品总条数
     *
     * @param ThirdCateId 商品三级分类id
     * @return 返回 0 即表示该三级分类不关联商品 >0 即表示该三级分类关联商品
     */
    @Override
    public int querySpuByThirdCateId(long ThirdCateId) {
        logger.debug("querySpuByThirdCateId and ThirdCateId :{}", ThirdCateId);
        return spuMapper.querySpuByThirdCateId(ThirdCateId);
    }


    @Override
    public int querySpuCountForEs() {
        logger.debug("querySpuCountForEs ......");
        return spuMapper.querySpuCountForEs();
    }


    @Override
    public List<PmsGoods> querySpuForEs(int start, int size) {
        logger.debug("querySpuForEs and start:{} \r\n size:{} \r\n", start, size);
        Map<String, Object> params = new HashMap<>();
        params.put("start", start);
        params.put("size", size);

        // 商品信息
        List<PmsGoods> spus = spuMapper.querySpuForEs(params);

        if (!CollectionUtils.isEmpty(spus)) {
            return spus.stream().map(spu -> this.querySpu(spu.getId(), CommonConstant.QUERY_WITH_NO_STORE)).collect(Collectors.toList());
        }

        return spus;
    }

    @Override
    public int updateCommission(long id, BigDecimal commissionRate, BigDecimal sCommissionRate, long storeId) {
        logger.debug("updateCommission and id :{} \r\n commission :{} \r\n sCommissionRate :{} \r\n storeId", id, commissionRate, sCommissionRate, storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("commissionRate", commissionRate);
        params.put("sCommissionRate", sCommissionRate);
        params.put("storeId", storeId);

        //给单品设置佣金比例
        if (skuService.updateCommission(id, commissionRate, sCommissionRate, storeId) == 0) {
            logger.error("updateCommission fail due to updateCommissionForSku fail");
            return 0;
        }
        //给商品设置佣金比例
        return spuMapper.updateCommission(params);
    }

    @Transactional
    @Override
    public int updateCommissions(List<Long> ids, BigDecimal commissionRate, BigDecimal sCommissionRate, long storeId) {
        logger.debug("updateCommissions and ids :{} \r\n commissionRate :{} \r\n sCommissionRate :{} \r\n storeId", ids, commissionRate, sCommissionRate, storeId);

        if (CollectionUtils.isEmpty(ids)) {
            logger.error("updateCommissions fail due to ids is empty");
            return 0;
        }
        ids.stream().forEach(id -> updateCommission(id, commissionRate, sCommissionRate, storeId));
        return 1;
    }

    @Transactional
    @Override
    public int auditPass(Consumer<Long> consumer, long spuId) {
        logger.debug("auditPass and spuId:{}", spuId);

        // 商品审核通过
        spuMapper.auditPass(spuId);

        // 单品审核通过
        skuService.auditPass(spuId);

        // 回调es 刷新es

        return 0;
    }

    @Transactional
    @Override
    public int auditRefuse(String reason, Consumer<Long> consumer, long spuId) {
        logger.debug("auditRefuse and spuId:{}", spuId);

        // 商品审核拒绝
        spuMapper.auditRefuse(spuId);

        // 单品审核拒绝
        skuService.auditRefuse(reason, spuId);
        // 回调es 刷新es

        return 0;
    }

    @Override
    @Transactional
    public int updateShelvesStatusByStoreIds(String status, List<Long> storeIds) {
        logger.debug("updateShelvesStatusByStoreIds and status:{} \r\n storeIds:{}", status, storeIds);
        Map<String, Object> params = new HashMap<>();
        params.put("status", status);
        params.put("storeIds", storeIds);
        //更改单品上下架状态
        skuService.updateShelvesStatusByStoreIds(status, storeIds);
        //更改商品上下架状态
        spuMapper.updateShelvesStatusByStoreIds(params);
        return 1;
    }

    @Override
    public BigDecimal queryCateRateBySkuId(String skuId) {

        logger.debug("queryCateRateBySkuId and skuId:{}", skuId);

        // 查询单品信息
        PmsSku sku = skuService.querySkuById(skuId);

        if (Objects.isNull(sku)) {
            logger.error("queryCateRateBySkuId fail due to sku is not exist....");
            return BigDecimal.ZERO;
        }

        // 查询商品信息
        PmsGoods spu = this.querySimpleSpu(sku.getSpuId(), sku.getStoreId());

        if (Objects.isNull(spu)) {
            logger.error("queryCateRateBySkuId fail due to goods is not exist...");
            return BigDecimal.ZERO;
        }

        // 查询商品的分类信息
        PmsCategory category = categoryService.queryCategoryById(spu.getThirdCateId());

        if (Objects.isNull(category)) {
            logger.error("queryCateRateBySkuId fail due to category is not exist...");
            return BigDecimal.ZERO;
        }

        return category.getRate();
    }

    @Override
    public int querySpuCountByTypeId(long typeId) {
        logger.debug("querySpuCountByTypeId and typeId:{}", typeId);
        return spuMapper.querySpuCountByTypeId(typeId);
    }

    @Override
    public PmsGoods querySeoBySpuId(long spuId) {
        logger.debug("querySeoBySpuId and spuId:{}", spuId);
        return spuMapper.querySeoBySpuId(spuId);
    }

    @Override
    public PageHelper<StoreSpu> queryStoreSpuList(PageHelper<StoreSpu> pageHelper, String name, long storeId) {
        logger.debug("queryStoreSpuList and pageHelper:{} \r\n name:{} \r\n storeId:{}", pageHelper, name, storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("name", name);
        //todo 网址        String siteUrl = baseInfoSetService.queryBaseInfoSet().getSiteUrlWithBias();
        String siteUrl = "/";
        return pageHelper.setListDates(spuMapper.queryStoreSpuList(pageHelper.getQueryParams(params, spuMapper.queryStoreSpuListCount(params))).stream().map(storeSpu -> {
            storeSpu.setVisitUrl(siteUrl + "#/spudetail?id=" + skuService.querySkuBySpuId(storeSpu.getSpuId(), CommonConstant.ADMIN_STOREID).stream().findFirst().orElse(new PmsSku()).getId());
            return storeSpu;
        }).collect(Collectors.toList()));
    }

    @Override
    public PageHelper<StoreSpu> queryStoreOnSaleSpuList(PageHelper<StoreSpu> pageHelper, String name, long storeId) {
        logger.debug("queryStoreOnSaleSpuList and pageHelper:{} \r\n name:{} \r\n storeId:{}", pageHelper, name, storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("name", name);
        params.put("storeId", storeId);
        return pageHelper.setListDates(spuMapper.queryStoreOnSaleSpuList(pageHelper.getQueryParams(params, spuMapper.queryStoreOnSaleSpuListCount(params))));
    }

    @Override
    public StoreSpu queryStoreSpu(long spuId, long storeId) {
        logger.debug("queryStoreSpu and spuId:{} \r\n storeId:{}", spuId, storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("id", spuId);
        params.put("storeId", CommonConstant.ADMIN_STOREID);
        StoreSpu storeSpu = new StoreSpu();
        PmsGoods spu = spuMapper.querySpu(params);
        if (Objects.isNull(spu)) {
            logger.debug("queryStoreSpu error : goods is null ");
            return null;
        }
        BeanUtils.copyProperties(spu, storeSpu);        // 查询商品的规格值信息(包括规格信息和规格值信息)
        storeSpu.setSpuSpecValues(spuSpecValueService.queryBySpuIdWithSpec(spuId));
//        //todo 查询商品下面的单品信息
//        storeSpu.setSkuList(skuService.querySkuBySpuId(spuId, CommonConstant.ADMIN_STOREID, PmsSkuItem.SPEC)
//                .stream().map(sku -> sku.buildStoreSkuList(storeSkuService.queryStoreSkuListBySkuId(sku.getId(), storeId))).collect(Collectors.toList()));

        return storeSpu;
    }

    @Override
    public List<PmsGoods> querySpuByIdsForExport(List<Long> ids, long storeId) {
        logger.debug("querySpuByIdsForExport and ids:{} \r\n storeId:{}", ids, storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("ids", ids);
        params.put("storeId", storeId);
        return spuMapper.querySpuByIdsForExport(params);
    }

    @Override
    public List<PmsGoods> queryAllSpuForExport(long storeId) {
        logger.debug("queryAllSpuForExport and storeId:{}", storeId);
        return spuMapper.queryAllSpuForExport(storeId);
    }

    /**
     * 分页查询未关联店铺三级分类的商品
     *
     * @param pageHelper 分页帮助类
     * @param name       商品名称
     * @param spuId      商品id
     * @param storeId    店铺id
     * @return 返回商品信息
     */
    @Override
    public PageHelper<PmsGoods> queryAllSpusWithoutStoreCategory(PageHelper<PmsGoods> pageHelper, String name, Long spuId, long storeId) {
        logger.debug("queryAllSpusWithoutStoreCategory and pageHelper:{} \r\n  name:{} \r\n spuId:{} \r\n storeId:{}", pageHelper, name, spuId, storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("name", name);
        params.put("id", spuId);
        params.put("storeId", storeId);
        return pageHelper.setListDates(spuMapper.queryAllSpusWithoutStoreCategory(pageHelper.getQueryParams(params, spuMapper.queryAllSpusWithoutStoreCategoryCount(params))));
    }

    /**
     * 根据店铺三级分类分页查询商品信息
     *
     * @param pageHelper   分页帮助类
     * @param name         商品名称
     * @param spuId        商品id
     * @param storeId      店铺id
     * @param storeTcateId 店铺三级分类id
     * @return 返回商品信息
     */
    @Override
    public PageHelper<PmsGoods> queryAllSpusByStoreCategory(PageHelper<PmsGoods> pageHelper, String name, Long spuId, long storeId, long storeTcateId) {
        logger.debug("queryAllSpusByStoreCategory and pageHelper:{} \r\n  name:{} \r\n spuId:{} \r\n storeId:{} \r\n storeTcateId:{}", pageHelper, name, spuId, storeId, storeTcateId);
        Map<String, Object> params = new HashMap<>();
        params.put("name", name);
        params.put("id", spuId);
        params.put("storeId", storeId);
        params.put("storeTcateId", storeTcateId);
        return pageHelper.setListDates(spuMapper.queryAllSpusByStoreCategory(pageHelper.getQueryParams(params, spuMapper.queryAllSpusByStoreCategoryCount(params))));
    }

    /**
     * 根据店铺三级分类查询商品id
     *
     * @param storeId      店铺id
     * @param storeTcateId 店铺三级分类id
     * @return 返回商品id数组
     */
    @Override
    public Long[] queryAllSpuIdByStoreCategory(long storeId, long storeTcateId) {
        logger.debug("queryAllSpuIdByStoreCategory and storeId:{} \r\n storeTcateId:{}", storeId, storeTcateId);
        Map<String, Object> params = new HashMap<>();
        params.put("storeId", storeId);
        params.put("storeTcateId", storeTcateId);
        return spuMapper.queryAllSpuIdByStoreCategory(params);
    }

    /**
     * 修改店铺三级分类关联商品
     *
     * @param ids          商品id数组
     * @param storeFcateId 店铺商品一级分类
     * @param storeScateId 店铺商品二级分类
     * @param storeTcateId 店铺商品三级分类
     * @param storeId      店铺id
     * @return 成功>0 失败0
     */
    @Override
    public int updateSpuWithStoreCategory(Long[] ids, long storeFcateId, long storeScateId, long storeTcateId, long storeId) {
        logger.debug("updateSpuWithStoreCategory and ids:{} \r\n  storeFcateId:{} \r\n storeScateId:{} \r\n storeTcateId:{} \r\n storeId:{}", ids, storeFcateId, storeScateId, storeTcateId, storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("ids", ids);
        params.put("storeFcateId", storeFcateId);
        params.put("storeScateId", storeScateId);
        params.put("storeTcateId", storeTcateId);
        params.put("storeId", storeId);
        return spuMapper.updateSpuWithStoreCategory(params);
    }

    /**
     * 取消店铺三级分类关联商品
     *
     * @param ids     商品id数组
     * @param storeId 店铺id
     * @return 成功>0 失败0
     */
    @Override
    public int cancelSpuWithStoreCategory(Long[] ids, long storeId) {
        logger.debug("cancelSpuWithStoreCategory and ids:{} \r\n storeId:{}", ids, storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("ids", ids);
        params.put("storeId", storeId);
        return spuMapper.cancelSpuWithStoreCategory(params);
    }

    /**
     * 根据三级分类id取消关联商品
     *
     * @param storeTcateId 三级分类id
     * @param storeId      店铺id
     * @return 成功>0 失败0
     */
    @Override
    public int cancelSpuWithStoreCategoryByStoreTcateId(long storeTcateId, long storeId) {
        logger.debug("cancelSpuWithStoreCategoryByStoreTcateId and storeTcateId:{} \r\n storeId:{}", storeTcateId, storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("storeTcateId", storeTcateId);
        params.put("storeId", storeId);
        return spuMapper.cancelSpuWithStoreCategoryByStoreTcateId(params);
    }

    /**
     * 修改商品物流模版id
     *
     * @param logisticsTemplateId        物流模版id
     * @param defaultLogisticsTemplateId 默认物流模版id
     * @param storeId                    店铺id
     * @return 成功>0 否则失败
     */
    @Override
    public int updateSpuLogisticsTemplateId(long logisticsTemplateId, long defaultLogisticsTemplateId, long storeId) {
        logger.debug("updateSpuLogisticsTemplateId and logisticsTemplateId:{} \r\n defaultLogisticsTemplateId :{} \r\n storeId:{}", logisticsTemplateId, defaultLogisticsTemplateId, storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("logisticsTemplateId", logisticsTemplateId);
        params.put("defaultLogisticsTemplateId", defaultLogisticsTemplateId);
        params.put("storeId", storeId);
        return spuMapper.updateSpuLogisticsTemplateId(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdateWmGoods(WmGoodsPageRes res) {
        Long storeId = 1L;
        String storeName = "微盟-" + weiMoConfig.getTitle();
        JSONObject detail = weiMobService.pullWMGoodsDetail(res.getGoodsId().toString());

        JSONArray categoryList = detail.getJSONArray("categoryList");

        PmsGoods goods = new PmsGoods();
        goods.setId(res.getGoodsId());
        goods.setName(detail.getString("title"));
        goods.setSubtitle(detail.getString("subTitle"));
//        goods.setPrice(res.getGoodsPrice().getMinSalePrice());
        goods.setPcDesc(detail.getString("goodsDesc"));
        goods.setMobileDesc(detail.getString("goodsDesc"));
        goods.setStoreId(storeId);
        goods.setStoreName(storeName);
        goods.setFirstCateId(categoryList.getJSONObject(0).getLong("categoryId"));
        goods.setSecondCateId(categoryList.getJSONObject(1).getLong("categoryId"));

        //品牌id
        JSONObject brandInfo = detail.getJSONObject("brandInfo");
        if (brandInfo != null && !brandInfo.isEmpty()) {
            PmsBrand brand = new PmsBrand();
            brand.setId(brandInfo.getLong("brandId"));
            brand.setName(brandInfo.getString("name"));
            brand.setUrl(brandInfo.getString("log"));
            brand.setStoreId(storeId);
            goods.setBrandId(brand.getId());
        }
        goods.setUrl(detail.getString("defaultImageUrl"));
        //商品上架状态 0 下架  1上架 2违规下架 默认0
        goods.setShelvesStatus(detail.getBoolean("isOnline") && detail.getBoolean("isCanSell") ? "1" : "0");
        //是否是虚拟商品 0 否 1 是默认0
        goods.setIsVirtual("1".equals(detail.getString("goodsType")) ? "0" : "1");
        goods.setVideo(detail.getString("goodsVideoUrl"));
        goods.setVideoPic(detail.getString("goodsVideoImageUrl"));
        goods.setUpdateTime(new Date());

        List<PmsGoodsImage> spuImageList = new ArrayList<>();
        //商品所有图
        List<String> imageUrls = detail.getJSONArray("goodsImageUrl").toList(String.class);
        for (String imageUrl : imageUrls) {
            PmsGoodsImage goodsImage = new PmsGoodsImage();
            goodsImage.setSpuId(goods.getId());
            goodsImage.setUrl(imageUrl);
            spuImageList.add(goodsImage);
        }

        //再洗商品的属性，规格，规格值，，sku
        //规格列表
        List<PmsGoodsSpecValue> spuSpecValues = new ArrayList<>();
        JSONArray specInfoList = detail.getJSONArray("specInfoList");
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(specInfoList)) {
//            log.error("商品不存在规格:{}", goods.getName());
//            return;
            specInfoList = new JSONArray();
        }

        for (int i = 0; i < specInfoList.size(); i++) {
            JSONObject specInfo = specInfoList.getJSONObject(i);
            PmsSpec spec = new PmsSpec();
            spec.setId(specInfo.getLong("specId"));
            spec.setName(specInfo.getString("specName"));
            //todo 处理规格 落地，和规格 和分类的关系
            List<JSONObject> list = specInfo.getJSONArray("skuSpecValueList").toList(JSONObject.class);
            for (JSONObject specValueInfo : list) {
                PmsGoodsSpecValue goodsSpecValue = new PmsGoodsSpecValue();
                goodsSpecValue.setSpuId(goods.getId());
                goodsSpecValue.setSpecId(specInfo.getLong("specId"));
                goodsSpecValue.setSpecValueId(specValueInfo.getString("specValueId"));
                goodsSpecValue.setValueRemark(specValueInfo.getString("specValueName"));
                goodsSpecValue.setSpec(spec);
                spuSpecValues.add(goodsSpecValue);
            }
        }
        //属性goodsPropertyList ->【propId，propName，propValueId，propValueName】

        //sku
        List<PmsSku> skus = new ArrayList<>();
        List<JSONObject> skuList = detail.getJSONArray("skuList").toList(JSONObject.class);
        for (JSONObject skuInfo : skuList) {
            PmsSku sku = new PmsSku();
            sku.setId(skuInfo.getLong("skuId").toString());
            sku.setSpuId(goods.getId());
            sku.setStock(skuInfo.getBigDecimal("skuStockNum").setScale(0, RoundingMode.DOWN).intValue());
            sku.setPrice(skuInfo.getBigDecimal("salePrice"));
            sku.setWeight(skuInfo.getBigDecimal("weight"));
            sku.setStoreId(storeId);
            sku.setShelvesStatus(skuInfo.getBoolean("isDisabled") ? "0" : "1");

            if (skuInfo.containsKey("imageUrls")) {
                List<String> skuImagesList = skuInfo.getJSONArray("imageUrls").toList(String.class);
                List<PmsSkuImage> skuImageList = skuImagesList.stream().map(url -> {
                    PmsSkuImage skuImage = new PmsSkuImage();
                    skuImage.setSkuIdAndSpuId(sku.getId(), goods.getId());
                    skuImage.setUrl(url);
                    return skuImage;
                }).collect(Collectors.toList());
                sku.setSkuImages(skuImageList);
            }

            //规格值
            List<JSONObject> specValueList = skuInfo.containsKey("skuSpecValueList") ? skuInfo.getJSONArray("skuSpecValueList").toList(JSONObject.class) : new ArrayList<>();

            List<PmsSkuSpecValue> pmsSkuSpecValues = specValueList.stream().map(specValue -> {
                PmsSkuSpecValue skuSpecValue = new PmsSkuSpecValue();
                skuSpecValue.setSkuIdAndSpuId(sku.getId(), goods.getId());
                skuSpecValue.setSpecId(specValue.getLong("specId"));
                skuSpecValue.setSpecValueId(specValue.getString("specValueId"));
                skuSpecValue.setValueRemark(specValue.getString("specValueName"));
                return skuSpecValue;
            }).collect(Collectors.toList());
            sku.setSkuSpecValues(pmsSkuSpecValues);
            skus.add(sku);
        }

        goods.setSpuSpecValues(spuSpecValues);
        goods.setSpuImages(spuImageList);
        goods.setSkus(skus);
//        goods.setSpuAttributeValues(spuAttributeValueMapper.queryBySpuId(res.getGoodsId()));

        insertPmsGoods(goods);

        // 分析商品商业词汇
        ThreadPoolTaskExecutor executor = SpringUtils.getBean("threadPoolTaskExecutor");
        executor.execute(() -> bizWordAnalyzedService.analyse(goods.getId(), "0", false));

        // TODO 商品上下架，该商品有关的运营计划处理（复购和关联商品）（异步执行）
        executor.execute(() -> {
            processPlan(Collections.singletonList(goods.getId()), goods.getShelvesStatus());
        });
    }

    /**
     * 处理上下架商品关联的运营计划
     *
     * @param goodIds 商品id集合
     * @param status  状态
     */
    private void processPlan(List<Long> goodIds, String status) {
        if (CollectionUtils.isEmpty(goodIds)) {
            return;
        }
        goodIds.forEach(goodId -> {
            List<BizOperatePlan> plans = bizOperatePlanService.selectBizOperatePlanList(new BizOperatePlan() {{
                setType(EOperatePlanType.TYPE_0.getCode());
                setParams(new HashMap<String, Object>() {{
                    put("refTypes", Arrays.asList(EOperatePlanRefType.REF_TYPE_1.getCode(), EOperatePlanRefType.REF_TYPE_2.getCode()));
                }});
                setRefId(goodId);
            }});
            if (plans.isEmpty()) return;
            // 上下架更改计划状态
            for (BizOperatePlan plan : plans) {
                // 上架
                if ("1".equals(status)) {
                    bizOperatePlanService.startPlanByIds(plan.getId());
                } else {
                    bizOperatePlanService.stopPlanByIds(plan.getId());
                }
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int editFabe(PmsGoods pmsGoods) {
        PmsGoods update = new PmsGoods();
        update.setCustomName(pmsGoods.getCustomName());
        update.setFabe(pmsGoods.getFabe());
        update.setId(pmsGoods.getId());
        int row = pmsGoodsMapper.updatePmsGoods(update);

        pmsGoods.getSkus().forEach(pmsSku -> {
            if (StringUtils.isBlank(pmsSku.getFabe())) {
                return;
            }
            PmsSku sku = new PmsSku();
            sku.setId(pmsSku.getId());
            sku.setFabe(pmsSku.getFabe());
            skuService.updatePmsSku(sku);
        });

        sendGoodsToAlex(pmsGoods.getId(), false);

        // 分析商品商业词汇
        ThreadPoolTaskExecutor executor = SpringUtils.getBean("threadPoolTaskExecutor");
        executor.execute(() -> bizWordAnalyzedService.analyse(pmsGoods.getId(), "0", true));

        return row;
    }

    @Override
    public JSONObject getJzSendMessagePayload(Long goodsId) {
        JSONObject message = new JSONObject();
        //todo 判断商品的发送形式，先默认文本，和小程序
        PmsGoods pmsGoods = pmsGoodsMapper.selectPmsGoodsById(goodsId);

        Integer messageType = null;
        JSONObject payload = new JSONObject();
        if (pmsGoods.getStoreId().equals(0L)) {
            messageType = EJzSendMessageType.Text.getCode();
            //平台文本发送，以后有其他的 根据店铺配置来
            payload.put("text", "【商品推荐】" + pmsGoods.getName() + "\n【商品ID】" + pmsGoods.getId());
        } else if (pmsGoods.getStoreId().equals(1L)) {
            messageType = EJzSendMessageType.APPLET.getCode();
            //开始组装商品小程序
            //无微客的链接
            String shareUrl = weiMobService.getWmGoodsShareUrl(goodsId);
            payload.put("appid", weiMoConfig.getAppid());
            payload.put("title", weiMoConfig.getTitle());
            payload.put("username", weiMoConfig.getUsername());
            payload.put("iconUrl", weiMoConfig.getIconUrl());
            payload.put("description", pmsGoods.getName());
            payload.put("pagePath", shareUrl);
            payload.put("thumbUrl", pmsGoods.getUrl());
        }

        //冗余  一个是直接发送，一个是群发，两个type 的key不一样，服了句子互动
        message.put("messageType", messageType);
        message.put("type", messageType);
        message.put("payload", payload);

        return message;
    }

    public static void main(String[] args) {
        String string = HttpUtils.sendGet("http://127.0.0.1:8000/api/goods/slot_mapping/124471894101159");
        System.out.println(string);
    }

    /**
     * 同步商品逻辑
     *
     * @param goodsId
     * @param delFlag
     */
    private void sendGoodsToAlex(Long goodsId, boolean delFlag) {

        String event = "update";
        String json = "";
        String name = "已删除商品";

        PmsGoods pmsGoods = pmsGoodsMapper.selectPmsGoodsById(goodsId);

        if (delFlag) {
            if (EBoolean.NO.getCode().equals(pmsGoods.getIsAiPurchaseGuide())) {
                // 更新状态
                pmsGoodsMapper.updatePmsGoods(new PmsGoods() {{
                    setId(goodsId);
                    setIsAiPurchaseGuide(EBoolean.NO.getCode());
                }});
            }

            event = "delete";
            String md5 = Md5Utils.hash(goodsId.toString());
            //判断这次的商品信息和上次的是否一致。若不一致，进行推送
            String cacheKey = String.format(CacheConstants.CACHE_WM_GOODS_INFO_LIST_V3, goodsId);
            Object o = redisCache.getCacheObject(cacheKey);
            if (o != null && o.toString().equals(md5)) {
                return;
            }
            redisCache.setCacheObject(cacheKey, md5);
            //删除商品语料
            corpusDocumentService.deleteCorpusDocumentByMetadata(goodsId, "PRODUCTS_KB");

        } else {
            //开始判断是否需要同步到AI
            GoodsVo goodsVo = querySpuSimpleById(goodsId, CommonConstant.QUERY_WITH_NO_STORE);
            if (StringUtils.isBlank(goodsVo.getCustomName())) {
                log.warn("通知缓存商品时【{}】【{}】未配置fabe", goodsId, goodsVo.getCustomName());
                return;
            }

            //todo 临时处理
            if ("dev".equals(active) || "stage".equals(active)) {
                //开始槽位填充
                if (StringUtils.isBlank(goodsVo.getSlotMap())) {
                    String url = String.format(pyOpenConfig.getGoodsSlot(), goodsId);
                    goodsVo.setSlotMap(HttpUtils.sendGet(url));
                    // 临时处理，slotMap不为空在更新
                    if (StringUtils.isNotBlank(goodsVo.getSlotMap())) {
                        pmsGoodsMapper.updatePmsGoods(new PmsGoods() {{
                            setId(goodsId);
                            setSlotMap(goodsVo.getSlotMap());
                        }});
                    }
                }
            }

            String md5 = Md5Utils.hash(JSON.toJSONString(goodsVo));
            //判断这次的商品信息和上次的是否一致。若不一致，进行推送
            String cacheKey = String.format(CacheConstants.CACHE_WM_GOODS_INFO_LIST_V3, goodsVo.getId());
            Object o = redisCache.getCacheObject(cacheKey);
            if (o != null && o.toString().equals(md5)) {
                return;
            }
            redisCache.setCacheObject(cacheKey, md5);

            //商品上架状态 0 下架  1上架
            if ("0".equals(goodsVo.getShelvesStatus()) || "0".equals(goodsVo.getIsAiPurchaseGuide())) {
                event = "delete";
            }
            json = JSON.toJSONString(goodsVo);
            name = goodsVo.getName();
        }

        bizVersionRecordService.insertRecord(EVersionRefType.GOODS, goodsId, event, json, name);


//        //执行了删除商品
//        if (delFlag) {
//            Map<String, Object> map = new HashMap<>();
//            map.put("goodsId", goodsId);
//            map.put("event", "delete");
//            log.info("通知缓存商品：\n{}\n", JSON.toJSONString(map));
//            //获取到商品库文档id
//            List<?> rows = difyService.getDatasetDocuments(DictUtils.getDictLabel(CacheConstants.KNOWLEDGE_BASE_ENUM,
//                            EDatasetsEnum.GOODS_DATA_BASE.getCode())
//                    , 1, 1, goodsId.toString(), "productId").getRows();
//
//            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(rows)) {
//                DocumentVo vo = (DocumentVo) rows.get(0);
//                map.put("document", vo.getId());
//            }
//            redisCache.push(CacheConstants.CACHE_GOODS_EVENT_LIST_V3, JSON.toJSONString(map));
//            return;
//        }
//        //开始判断是否需要同步到AI
//        GoodsVo goodsVo = querySpuSimpleById(goodsId, CommonConstant.QUERY_WITH_NO_STORE);
//        if (StringUtils.isBlank(goodsVo.getCustomName())) {
//            log.warn("通知缓存商品时【{}】【{}】未配置fabe", goodsId, goodsVo.getCustomName());
//            return;
//        }
//
//        String md5 = Md5Utils.hash(JSON.toJSONString(goodsVo));
//
//        //判断这次的商品信息和上次的是否一致。若不一致，进行推送
//        String cacheKey = String.format(CacheConstants.CACHE_WM_GOODS_INFO_LIST_V3, goodsVo.getId());
//        Object o = redisCache.getCacheObject(cacheKey);
//        if (o != null && o.toString().equals(md5)) {
//            return;
//        }
//        redisCache.setCacheObject(cacheKey, md5);
//
//        Map<String, Object> map = new HashMap<>();
//        map.put("goodsId", goodsVo.getId());
//        //商品上架状态 0 下架  1上架
//        if ("0".equals(goodsVo.getShelvesStatus())) {
//            //获取到商品库文档id
//            map.put("event", "delete");
//            List<?> rows = difyService.getDatasetDocuments(DictUtils.getDictLabel(CacheConstants.KNOWLEDGE_BASE_ENUM,
//                            EDatasetsEnum.GOODS_DATA_BASE.getCode())
//                    , 1, 1, goodsId.toString(), "productId").getRows();
//            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(rows)) {
//                DocumentVo vo = (DocumentVo) rows.get(0);
//                map.put("document", vo.getId());
//            }
//        } else {
//            map.put("event", "update");
//            map.put("info", goodsVo);
//        }
//        log.info("通知缓存商品：\n{}\n", JSON.toJSONString(map));
//        redisCache.push(CacheConstants.CACHE_GOODS_EVENT_LIST_V3, JSON.toJSONString(map));
    }

    /**
     * 设置单品的审核状态
     *
     * @param spu 商品信息
     */
    private void setSkuAuditStatus(PmsGoods spu) {

        // 设置商品为审核状态
//        spu.setAuditStatus(baseInfoSetService.isSkuNeedAudit());
        spu.setAuditStatus(false);

        // 设置商品下面单品的审核状态和商品一致
        spu.setSkuStatus();
    }

    /**
     * 设置商品的分类
     *
     * @param spu 商品信息
     */
    private void setSpuCategorys(PmsGoods spu) {
        spu.setFirstCategory(categoryService.selectPmsCategoryById(spu.getFirstCateId()));
        spu.setSecondCategory(categoryService.selectPmsCategoryById(spu.getSecondCateId()));
//        spu.setThirdCategory(categoryService.selectPmsCategoryById(spu.getThirdCateId()));
    }

    /**
     * 设置商品的分类和品牌数据
     *
     * @param spu 商品信息
     */
    private void setBrandAndCategory(PmsGoods spu) {
        spu.setBrand(brandService.selectPmsBrandById(spu.getBrandId()));
        spu.setSecondCategory(categoryService.selectPmsCategoryById(spu.getSecondCateId()));
//        spu.setThirdCategory(categoryService.selectPmsCategoryById(spu.getThirdCateId()));
    }

    /**
     * 设置商品的分类信息
     *
     * @param spu 商品信息
     */
    private void setSpuCategory(PmsGoods spu) {
        // 店铺的商品需要设置 平台的不需要 已经带过来
        if (spu.isStoreSpu()) {
            // 二级分类
            PmsCategory secondCate = categoryService.selectPmsCategoryById(categoryService.selectPmsCategoryById(spu.getThirdCateId()).getParentId());
            spu.setSecondCateId(secondCate.getId());
            spu.setFirstCateId(secondCate.getParentId());
        }
    }

    /**
     * 设置商品的三级分类和品牌信息
     *
     * @param spus 商品集合
     * @return 返回商品集合
     */
    private List<PmsGoods> setBrandAndCategorys(List<PmsGoods> spus) {

        spus.parallelStream().forEach(this::setBrandAndCategory);
        return spus;
    }

    /**
     * 设置访问商品的url
     *
     * @param spus 商品信息
     * @return 返回商品信息
     */
    private List<PmsGoods> setVisitUrl(List<PmsGoods> spus) {
        spus.stream().forEach(this::setVisitUrl);
        return spus;
    }


    /**
     * 设置访问商品的url
     *
     * @param spu 商品
     */
    private void setVisitUrl(PmsGoods spu) {
        // 根据商品id查询单品信息
        List<PmsSku> skus = skuService.querySkuBySpuId(spu.getId(), spu.getStoreId());
        if (!CollectionUtils.isEmpty(skus)) {
            //  goods.setVisitUrl(baseInfoSetService.queryBaseInfoSet().getSiteUrlWithBias() + "#/spudetail?id=" + skus.get(0).getId());
        }
    }

    @Override
    @Transactional
    public int saveRepurchase(RepurchaseReq req) {

        AtomicInteger result = new AtomicInteger(0);

        // 校验商品是否存在
        PmsGoods pmsGoods = selectPmsGoodsById(req.getGoodsId());
        if (pmsGoods == null) {
            log.warn("product does not exist, product id: {}", req.getGoodsId());
            throw new ServiceException("商品不存在");
        }

        long count = req.getSkuList().stream().filter(sku -> sku.getRepurchaseCycle() != null).count();
        if (req.getRepurchaseCycle() != null || count > 0) {
            if (CollectionUtils.isEmpty(req.getSalesPitchList())) {
                throw new ServiceException("设置复购周期, 话术必填");
            }
        }

        // 1. spu的复购周期有值则更新
        saveSpuRepurchase(req, pmsGoods, result);

        // 2. sku的复购周期有值则更新
        saveSkuRepurchase(req, result);

        // 3. 更新复购话术
        result.addAndGet(salesPitchService.updateSalesPitch(req.getSalesPitchList(), req.getGoodsId(),
                ProductConstants.SalesPitch.RefType.REPURCHASE));

        // 1.3 创建复购运营计划
        List<BizOperatePlan> bizOperatePlans = bizOperatePlanService.selectBizOperatePlanList(new BizOperatePlan() {{
            setRefType(EOperatePlanRefType.REF_TYPE_1.getCode());
            setRefId(req.getGoodsId());
        }});
        if (CollectionUtils.isEmpty(bizOperatePlans)) {
            userPlanHandlerComponent.createOperationPlan(pmsGoods, EOperatePlanRefType.REF_TYPE_1.getCode());
        }

        return result.get();
    }

    @Override
    public List<PmsGoods> queryProductsByIds(List<String> ids) {
        return pmsGoodsMapper.queryProductsByIds(ids);
    }


    private void saveSkuRepurchase(RepurchaseReq req, AtomicInteger result) {
        int REPURCHASE_CYCLE_MIN = Integer.parseInt(sysConfigService.selectConfigByKey(ConfigConstants.REPURCHASE_CYCLE_MIN));

        if (!CollectionUtils.isEmpty(req.getSkuList())) {
            req.getSkuList().forEach(sku -> {
                // 2.1 校验复购周期值不能小于 15
                if (sku.getRepurchaseCycle() != null) {
                    // 2.1 校验复购周期值不能小于 15
                    if (sku.getRepurchaseCycle() < REPURCHASE_CYCLE_MIN) {
                        throw new ServiceException("sku的复购周期值不能小于 " + REPURCHASE_CYCLE_MIN + ", 或者不填");
                    }

                    // 2.2 更新复购周期
                    PmsSku pmsSku = new PmsSku();
                    pmsSku.setId(sku.getId());
                    pmsSku.setRepurchaseCycle(sku.getRepurchaseCycle());
                    pmsSku.setModifyTime(new Date());
                    result.addAndGet(skuService.updatePmsSku(pmsSku));
                }
            });
        }
    }

    private void saveSpuRepurchase(RepurchaseReq req, PmsGoods pmsGoods, AtomicInteger result) {
        int REPURCHASE_CYCLE_MIN = Integer.parseInt(sysConfigService.selectConfigByKey(ConfigConstants.REPURCHASE_CYCLE_MIN));

        // 1.1 校验复购周期值不能小于 15
        if (req.getRepurchaseCycle() != null && req.getRepurchaseCycle() < REPURCHASE_CYCLE_MIN) {
            throw new ServiceException("spu的复购周期值不能小于 " + REPURCHASE_CYCLE_MIN);
        }

        // 1.2 更新复购周期
        PmsGoods param = new PmsGoods();
        param.setId(req.getGoodsId());
        param.setRepurchaseCycle(req.getRepurchaseCycle());
        param.setModifyTime(new Date());
        result.addAndGet(pmsGoodsMapper.updatePmsGoods(param));

    }


    @Override
    public int saveBziMoments(BizMomentsReq req) {
        return salesPitchService.updateSalesPitch(req.getSalesPitchList(), req.getGoodsId(),
                ProductConstants.SalesPitch.RefType.BIZ_MOMENTS);
    }

    @Override
    @Transactional
    public int updateAiRepurchase(List<Long> spuIds, Integer status) {

        int i = 0;
        // 校验导购数量限制
        int aiPurchaseLimit = sysConfigService.getInt(ConfigConstants.Products.AI_PURCHASE_LIMIT, 200);

        List<PmsGoods> pmsGoods = pmsGoodsMapper.selectPmsGoodsList(new PmsGoods() {{
            setIsAiPurchaseGuide(EBoolean.YES.getCode());
            setDelFlag(0);
        }});

        for (Long spuId : spuIds) {
            i += pmsGoodsMapper.updateAiRepurchase(spuIds, status);

            // 如果关闭 ai 导购推荐则删除 ai
            if (Integer.valueOf(0).equals(status)) {
                sendGoodsToAlex(spuId, false);
            } else {
                if (pmsGoods.size() + spuIds.size() > aiPurchaseLimit) {
                    throw new ServiceException("开启导购数量超过限制");
                }
                // 判断是否有删除记录, 如果有则删除'删除记录'
                List<BizVersionRecord> deleteRecords = bizVersionRecordService.selectBizVersionRecordList(new BizVersionRecord() {{
                    setRefId(spuId);
                    setRefType(EVersionRefType.GOODS.getCode());
                    setEvent(EVersionEvent.delete.name());
                }});

                if (CollUtil.isNotEmpty(deleteRecords)) {
                    bizVersionRecordService.deleteBizVersionRecordByIds(deleteRecords.stream()
                            .map(BizVersionRecord::getId)
                            .collect(Collectors.toList())
                            .toArray(new Long[]{}));
                    String cacheKey = String.format(CacheConstants.CACHE_WM_GOODS_INFO_LIST_V3, spuId);
                    redisCache.deleteObject(cacheKey);
                }

                sendGoodsToAlex(spuId, false);

                // 开启导购推荐, 尝试生成自动运营计划
                bizOperatePlanService.tryGenerateOperatePlan(spuId);
            }
        }
        return i;
    }

    @Override
    public int saveRecommendProduct(RecommendProductReq req) {
        return salesPitchService.updateSalesPitch(req.getSalesPitchList(), req.getGoodsId(),
                ProductConstants.SalesPitch.RefType.RECOMMEND_PRODUCT);
    }

    @Override
    public List<PmsGoods> getReBuyGoods(PmsGoods pmsGoods) {
        return pmsGoodsMapper.queryRebuyGoods(pmsGoods);
    }

    @Override
    public JSONObject productDetailClean(ProductDetailCleanReq req) {
        log.info("productDetailClean req:{}", JSON.toJSONString(req));
        PmsGoods pmsGoods = pmsGoodsMapper.selectPmsGoodsById(req.getProductId());
        List<PmsGoodsAttributeValue> spuAttributeValues = pmsGoodsAttributeValueMapper.selectPmsGoodsAttributeValueList(new PmsGoodsAttributeValue() {{
            setSpuId(req.getProductId());
        }});

        String productDetailImgUrl = null;
        String productDetailTextOcr = null;
        // 获取商品详情ocr结果
        if (spuAttributeValues != null) {
            Optional<PmsGoodsAttributeValue> productDetailImgUrlOpt = spuAttributeValues.stream()
                    .filter(x -> "product_detail_img_url".equals(x.getAttributeName())).findFirst();
            if (productDetailImgUrlOpt.isPresent()) {
                productDetailImgUrl = productDetailImgUrlOpt.get().getAttributeValue();
            }
            productDetailTextOcr = req.getProductDetailTextOcr();
        }

        // 如果没有则调ocr接口
        if (StringUtils.isBlank(productDetailTextOcr)) {
            // 如果没有ocr结果, 则获取ocr图片及结果
            JSONObject jsonObject = productClipComponent.dealDetailHtml(new ClipByWebReq() {{
                setProductId(req.getProductId().toString());
                setType("taobao");
                setDetailHtml(pmsGoods.getPcDesc());
            }});
            log.info("ocr info: " + jsonObject);
            productDetailImgUrl = jsonObject.getString("image_url");
            productDetailTextOcr = jsonObject.getString("ocr_text");

            // 保存或更新 product_detail_img_url
            Optional<PmsGoodsAttributeValue> productDetailImgUrlOpt = spuAttributeValues.stream()
                    .filter(x -> "product_detail_img_url".equals(x.getAttributeName())).findFirst();
            PmsGoodsAttributeValue imageUrl = new PmsGoodsAttributeValue();
            imageUrl.setSpuId(req.getProductId());
            imageUrl.setAttributeName("product_detail_img_url");
            imageUrl.setAttributeValue(jsonObject.getString("image_url"));
            imageUrl.setDelFlag(0);
            imageUrl.setCreateBy(UserConstants.SYS_USER);
            imageUrl.setCreateTime(new Date());
            if (productDetailImgUrlOpt.isPresent()) {
                imageUrl.setAttributeId(productDetailImgUrlOpt.get().getAttributeId());
                imageUrl.setAttributeValueId(productDetailImgUrlOpt.get().getAttributeValueId());
                pmsGoodsAttributeValueMapper.updatePmsGoodsAttributeValue(imageUrl);
            } else {
                imageUrl.setAttributeId(IdUtils.generator().toString());
                imageUrl.setAttributeValueId(IdUtils.generator().toString());
                pmsGoodsAttributeValueMapper.insertPmsGoodsAttributeValue(imageUrl);
            }


            // 保存或更新 product_detail_text_ocr
            Optional<PmsGoodsAttributeValue> productDetailTextOcrOpt = spuAttributeValues.stream()
                    .filter(x -> "product_detail_text_ocr".equals(x.getAttributeName())).findFirst();
            PmsGoodsAttributeValue ocrText = new PmsGoodsAttributeValue();
            ocrText.setSpuId(req.getProductId());
            ocrText.setAttributeName("product_detail_text_ocr");
            ocrText.setAttributeValue(jsonObject.getString("ocr_text"));
            ocrText.setDelFlag(0);
            ocrText.setCreateBy(UserConstants.SYS_USER);
            ocrText.setCreateTime(new Date());
            if (productDetailTextOcrOpt.isPresent()) {
                ocrText.setAttributeId(productDetailTextOcrOpt.get().getAttributeId());
                ocrText.setAttributeValueId(productDetailTextOcrOpt.get().getAttributeValueId());
                pmsGoodsAttributeValueMapper.updatePmsGoodsAttributeValue(ocrText);
            } else {
                ocrText.setAttributeId(IdUtils.generator().toString());
                ocrText.setAttributeValueId(IdUtils.generator().toString());
                pmsGoodsAttributeValueMapper.insertPmsGoodsAttributeValue(ocrText);
            }

        }

        JSONObject result = new JSONObject();

        // 将ocr内容清洗成文案
        HashMap<String, Object> params = new HashMap<>();
        params.put("product_name", pmsGoods.getName());
        params.put("product_detail_text_ocr", productDetailTextOcr);
        BizCollectProduct bizCollectProduct = new BizCollectProduct();
        bizCollectProduct.setSecondCategoryId(pmsGoods.getSecondCateId().toString());
        bizCollectProduct.setSecondCategoryIdList(Collections.singletonList(pmsGoods.getSecondCateId().toString()));
        params.put("example", bizCollectProductService.buildExample(bizCollectProduct));

        String productDescClean = productClipComponent.dataClean(params).trim();

        // 将清洗后的文案 提取卖点
        HashMap<String, Object> paramsExtractFAB = new HashMap<>();
        paramsExtractFAB.put("product_desc_clean", productDescClean);
        String productFabClean = productClipComponent.extractFAB(paramsExtractFAB).trim();


        // 保存或更新 product_desc_clean
        Optional<PmsGoodsAttributeValue> productDescAttrOpt = spuAttributeValues.stream()
                .filter(x -> "product_desc_clean".equals(x.getAttributeName())).findFirst();
        PmsGoodsAttributeValue productDescAttr = new PmsGoodsAttributeValue();
        productDescAttr.setSpuId(req.getProductId());
        productDescAttr.setAttributeName("product_desc_clean");
        productDescAttr.setAttributeValue(productDescClean);
        productDescAttr.setDelFlag(0);
        productDescAttr.setCreateBy(UserConstants.SYS_USER);
        productDescAttr.setCreateTime(new Date());
        if (productDescAttrOpt.isPresent()) {
            productDescAttr.setAttributeId(productDescAttrOpt.get().getAttributeId());
            productDescAttr.setAttributeValueId(productDescAttrOpt.get().getAttributeValueId());
            pmsGoodsAttributeValueMapper.updatePmsGoodsAttributeValue(productDescAttr);
        } else {
            productDescAttr.setAttributeId(IdUtils.generator().toString());
            productDescAttr.setAttributeValueId(IdUtils.generator().toString());
            pmsGoodsAttributeValueMapper.insertPmsGoodsAttributeValue(productDescAttr);
        }


        // 保存或更新 product_fab_clean
        Optional<PmsGoodsAttributeValue> productFabAttrOpt = spuAttributeValues.stream()
                .filter(x -> "product_fab_clean".equals(x.getAttributeName())).findFirst();
        PmsGoodsAttributeValue productFabAttr = new PmsGoodsAttributeValue();
        productFabAttr.setSpuId(req.getProductId());
        productFabAttr.setAttributeName("product_fab_clean");
        productFabAttr.setAttributeValue(productFabClean);
        productFabAttr.setDelFlag(0);
        productFabAttr.setCreateBy(UserConstants.SYS_USER);
        productFabAttr.setCreateTime(new Date());
        if (productFabAttrOpt.isPresent()) {
            productFabAttr.setAttributeId(productFabAttrOpt.get().getAttributeId());
            productFabAttr.setAttributeValueId(productFabAttrOpt.get().getAttributeValueId());
            pmsGoodsAttributeValueMapper.updatePmsGoodsAttributeValue(productFabAttr);
        } else {
            productFabAttr.setAttributeId(IdUtils.generator().toString());
            productFabAttr.setAttributeValueId(IdUtils.generator().toString());
            pmsGoodsAttributeValueMapper.insertPmsGoodsAttributeValue(productFabAttr);
        }


        result.put("product_detail_img_url", productDetailImgUrl);
        result.put("product_detail_text_ocr", productDetailTextOcr);
        result.put("product_desc_clean", productDescClean);
        result.put("product_fab_clean", productFabClean);
        log.info("clean result: {}", result);
        return result;
    }


    public void batchCleanProduct() {
        PageUtils.startPage(1, 10, false);
        List<PmsGoods> pmsGoods = pmsGoodsMapper.queryUncleanedProduct(new PmsGoods() {{
            setDelFlag(0);
            setShelvesStatus(EBoolean.YES.getCode());
//            setIsAiPurchaseGuide(EBoolean.YES.getCode());
        }});

        for (PmsGoods pmsGood : pmsGoods) {
            List<PmsGoodsAttributeValue> list = pmsGoodsAttributeValueService.selectPmsGoodsAttributeValueList(new PmsGoodsAttributeValue(){{
                setSpuId(pmsGood.getId());
            }});

            if (JSON.toJSONString(list).contains("product_desc_clean")){
                continue;
            }

            // 清洗商品数据
            productDetailClean(new ProductDetailCleanReq() {{
                setProductId(pmsGood.getId());
            }});

            // 更新导购
            sendGoodsToAlex(pmsGood.getId(), false);
        }
    }
}
