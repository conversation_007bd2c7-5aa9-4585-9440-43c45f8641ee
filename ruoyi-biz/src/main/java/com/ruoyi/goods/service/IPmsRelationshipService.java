package com.ruoyi.goods.service;

import java.util.List;
import com.ruoyi.goods.domain.PmsRelationship;
import com.ruoyi.goods.domain.req.ProductRelationReq;
import com.ruoyi.goods.domain.vo.PmsRelationshipVo;

/**
 * 商品关联关系Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
public interface IPmsRelationshipService 
{
    /**
     * 查询商品关联关系
     * 
     * @param id 商品关联关系主键
     * @return 商品关联关系
     */
    public PmsRelationship selectPmsRelationshipById(Long id);

    /**
     * 查询商品关联关系列表
     * 
     * @param pmsRelationship 商品关联关系
     * @return 商品关联关系集合
     */
    public List<PmsRelationship> selectPmsRelationshipList(PmsRelationship pmsRelationship);

    /**
     * 查询商品关联关系列表, 按照相关性得分排序
     *
     * @param pmsRelationship 商品关联关系
     * @return 商品关联关系集合
     */
    public List<PmsRelationshipVo> selectPmsRelationshipListRank(PmsRelationship pmsRelationship);

    /**
     * 新增商品关联关系
     * 
     * @param pmsRelationship 商品关联关系
     * @return 结果
     */
    public int insertPmsRelationship(ProductRelationReq pmsRelationship);

    /**
     * 修改商品关联关系
     * 
     * @param pmsRelationship 商品关联关系
     * @return 结果
     */
    public int updatePmsRelationship(ProductRelationReq pmsRelationship);

    /**
     * 批量删除商品关联关系
     * 
     * @param ids 需要删除的商品关联关系主键集合
     * @return 结果
     */
    public int deletePmsRelationshipByIds(Long[] ids);

    /**
     * 删除商品关联关系信息
     * 
     * @param id 商品关联关系主键
     * @return 结果
     */
    public int deletePmsRelationshipById(Long id);


    /**
     * 处理关联关系
     * @param wid
     * @param wmOrderNo
     * @return
     */
    void pmsRelationshipHandel(String wid, String wmOrderNo);


    /**
     * 将指定商品关联关系置顶
     * @param req req
     * @return
     */
    int pinned(ProductRelationReq req);

    /**
     * 取消商品关联关系置顶
     * @param req req
     * @return
     */
    int unpinned(ProductRelationReq req);

    /**
     * 定时任务扫描订单处理关联商品分值计算,
     */
    void scanOrderToDealRelation();
}
