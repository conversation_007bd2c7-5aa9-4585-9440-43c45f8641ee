package com.ruoyi.order.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.goods.utils.AdminLoginUtils;
import com.ruoyi.goods.utils.CommonConstant;
import com.ruoyi.order.domain.OmsOrder;
import com.ruoyi.order.service.IOmsOrderService;
import com.ruoyi.order.service.OrderServiceApi;
import com.ruoyi.order.vo.OrderItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单Controller
 *
 * <AUTHOR>
 * @date 2020-07-24
 */
@RestController
@RequestMapping("/order/OmsOrder")
public class OmsOrderController extends BaseController {
    @Autowired
    private IOmsOrderService omsOrderService;
    /**
     * 订单服务接口
     */
    @Autowired
    private OrderServiceApi orderServiceApi;


    /**
     * 查询订单列表
     */
    //@PreAuthorize("@ss.hasPermi('order:OmsOrder:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmsOrder omsOrder) {
        startPage();
        List<OmsOrder> list = omsOrderService.selectOmsOrderList(omsOrder);
        return getDataTable(list);
    }

    /**
     * 导出订单列表
     */
    //@PreAuthorize("@ss.hasPermi('order:OmsOrder:export')")
    @Log(title = "订单", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(OmsOrder omsOrder) {
        List<OmsOrder> list = omsOrderService.selectOmsOrderList(omsOrder);
        ExcelUtil<OmsOrder> util = new ExcelUtil<OmsOrder>(OmsOrder.class);
        return util.exportExcel(list, "OmsOrder");
    }

    /**
     * 获取订单详细信息
     */
    //@PreAuthorize("@ss.hasPermi('order:OmsOrder:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(orderServiceApi.queryOrderDetailById(id, CommonConstant.QUERY_WITH_NO_STORE, CommonConstant.QUERY_WITH_NO_CUSTOMER, OrderItem.LOG, OrderItem.ATTR, OrderItem.SKUS, OrderItem.CUSTOMER));
    }

    /**
     * 新增订单
     */
    //@PreAuthorize("@ss.hasPermi('order:OmsOrder:add')")
    @Log(title = "订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmsOrder omsOrder) {
        return toAjax(omsOrderService.insertOmsOrder(omsOrder));
    }

    /**
     * 修改订单
     */
    //@PreAuthorize("@ss.hasPermi('order:OmsOrder:edit')")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmsOrder omsOrder) {
        return toAjax(omsOrderService.updateOmsOrder(omsOrder));
    }

    /**
     * 删除订单
     */
    //@PreAuthorize("@ss.hasPermi('order:OmsOrder:remove')")
    @Log(title = "订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(omsOrderService.deleteOmsOrderByIds(ids));
    }


    /**
     * 根据订单id查询订单信息  (订单的所有信息 此接口慎用)
     *
     * @param id 订单id
     * @return 返回订单信息
     */
    @GetMapping("/order/{id}")
    public OmsOrder queryOrderById(@PathVariable long id) {
        return orderServiceApi.queryOrderDetailById(id, CommonConstant.QUERY_WITH_NO_STORE, CommonConstant.QUERY_WITH_NO_CUSTOMER, OrderItem.LOG, OrderItem.ATTR, OrderItem.SKUS);
    }

    /**
     * 查询打印订单详情
     *
     * @param ids 订单id
     * @return 返回打印订单详情
     */
    @GetMapping("/printorderdetails")
    public Map<String, Object> queryPrintOrderDetails(Long... ids) {
        Map<String, Object> result = new HashMap<>();
        result.put("orders", orderServiceApi.queryPrintOrderDetails(Arrays.asList(ids).stream().filter(Objects::nonNull).collect(Collectors.toList())));
        result.put("operator", AdminLoginUtils.getInstance().getManagerName());
        return result;
    }
}
