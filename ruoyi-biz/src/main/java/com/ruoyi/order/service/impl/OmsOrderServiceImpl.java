package com.ruoyi.order.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.domain.BizUser;
import com.ruoyi.biz.domain.req.TempHandlerReq;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.biz.enums.ETempHandlerType;
import com.ruoyi.biz.service.IBizUserService;
import com.ruoyi.common.constant.ConfigConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.ERedisChannel;
import com.ruoyi.goods.utils.CommonConstant;
import com.ruoyi.goods.utils.PageHelper;
import com.ruoyi.order.domain.OmsOrder;
import com.ruoyi.order.domain.OmsOrderSku;
import com.ruoyi.order.mapper.OmsOrderMapper;
import com.ruoyi.order.service.IOmsOrderAttrService;
import com.ruoyi.order.service.IOmsOrderService;
import com.ruoyi.order.service.IOmsOrderSkuService;
import com.ruoyi.order.vo.OrderItem;
import com.ruoyi.order.vo.QueryOrderCriteria;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.weimo.enums.EWmOrderStatus;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-07-24
 */
@Service
public class OmsOrderServiceImpl implements IOmsOrderService {
    @Autowired
    private OmsOrderMapper omsOrderMapper;
    /**
     * 调试日志
     */
    private Logger logger = LoggerFactory.getLogger(OmsOrderServiceImpl.class);

    /**
     * 注入订单数据库接口
     */
    @Autowired
    private OmsOrderMapper orderMapper;

    /**
     * 订单附属信息
     */
    @Autowired
    private IOmsOrderAttrService orderAttrService;

    /**
     * 订单单品服务接口
     */
    @Autowired
    private IOmsOrderSkuService orderSkuService;

    @Autowired
    private IBizUserService bizUserService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private RedisCache redisCache;


    @Override
    public PageHelper<OmsOrder> queryOrders(PageHelper<OmsOrder> pageHelper, QueryOrderCriteria queryCriteria, OrderItem... orderItems) {
        logger.debug("queryOrders and pageHelper:{} \r\n queryCriteria:{}\r\n orderItems:{}", pageHelper, queryCriteria, orderItems);

        Map<String, Object> params = queryCriteria.getQueryMap();

        // 订单信息
        List<OmsOrder> orders = orderMapper.queryOrders(pageHelper.getQueryParams(params, orderMapper.queryOrderCount(params)));

        // 订单不为空 填充订单信息
        if (!CollectionUtils.isEmpty(orders)) {
            orders.stream().forEach(order -> fillOrderOtherInfos(order, orderItems));
        }

        return pageHelper.setListDates(orders);
    }


    @Override
    public PageHelper<OmsOrder> queryStoreOrders(PageHelper<OmsOrder> pageHelper, QueryOrderCriteria queryCriteria) {

        logger.debug("queryStoreOrders and pageHelper:{} \r\n queryCriteria:{}", pageHelper, queryCriteria);

        Map<String, Object> params = queryCriteria.getQueryMap();

        return pageHelper.setListDates(orderMapper.queryStoreOrders(pageHelper.getQueryParams(params, orderMapper.queryStoreOrdersCount(params))));
    }

    @Override
    public int confirmOrder(long id, long storeId, Consumer<OmsOrder> orderConsumer, Consumer<OmsOrder> crowdfundingConsumer) {
        logger.debug("confirmOrder and id:{}", id);


        // 订单信息
        OmsOrder order = this.queryOrderDetailById(id, CommonConstant.QUERY_WITH_NO_CUSTOMER, storeId, OrderItem.SKUS);

        if (Objects.isNull(order)) {
            logger.error("confirmOrder fail due to order is not exist");
            return 0;
        }

        // 如果是定金预售订单第二次付款的时候需要判断是否到了可以支付的时间
        if (!order.isPresaleOrderCanPay()) {
            logger.error("confirmOrder fail due to order pay time is error...");
            return 0;
        }

        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("storeId", storeId);


        // 如果是拼团订单 则回调
        if (order.isGroupOrder() && Objects.nonNull(orderConsumer)) {
            orderConsumer.accept(order);
        }

        // 如果是众筹订单则回调
        if (order.isCrowdfundingOrder() && Objects.nonNull(crowdfundingConsumer)) {
            crowdfundingConsumer.accept(order);
        }


        // 如果订单是定金预售订单  并且第一阶段还没有付款 则修改订单为第一阶段已付款 否则直接更改订单状态
        if (order.isDepositPresaleOrder() && !order.isPresaleOnePayed()) {
            return orderMapper.confirmPreSaleOrder(params);
        } else if (order.isNoReturnOrder()) {
            // 如果是无回报支持订单则直接修改成为已完成订单
            return orderMapper.confirmOrderFinished(params);
        } else {
            // 确认订单
            return orderMapper.confirmOrder(params);
        }
    }


    @Override
    public int cancelOrder(long id, long storeId, String reason) {
        logger.debug("cancelOrder and id:{} \r\n storeid:{}", id, storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("storeId", storeId);
        params.put("reason", reason);
        return orderMapper.cancelOrder(params);
    }

    @Transactional
    @Override
    public int modifyPrice(long id, BigDecimal price, String reason, long storeId, String operationName) {
        logger.debug("modifyPrice and id:{} \r\n price:{} \r\n reason:{} \r\n storeId:{}", id, price, reason, storeId);

        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("storeId", storeId);
        params.put("price", price);

        // 查询订单信息
        OmsOrder order = this.queryOrderDetailById(id, CommonConstant.NO_LOGIN_CUSTOMERID, storeId, OrderItem.SKUS);

        // 订单信息为空 直接返回
        if (Objects.isNull(order)) {
            logger.error("modifyPrice fail due to order is not exist...");
            return 0;
        }

        if (!order.isCanModifyPrice()) {
            logger.error("modifyPrice fail due to order is presale order....");
            return -2;
        }

        //订单必须支付1毛钱
        if (!order.validateModifyPrice(price)) {
            logger.error("modifyPrice fail due to order must pay 0.1  and order money:{} \r\n and modify price:{}", order.getPrice(), price);
            return -1;
        }

        // 修改订单价格
        orderMapper.modifyPrice(params);

        // 计算每个单品优惠的价格
        order.calcModifyPriceEverySkuPrice(price);

        // 修改订单下每个单品的价格
        orderSkuService.updateOrderSkusPrice(order.getOrderSkus());

//        // 增加订单操作日志
//        orderOperatonLogService.insertOmsOrderOperationLog(OmsOrderOperationLog.buildForModifyPrice(id, reason, operationName));

        return 1;
    }

    @Override
    public int deliverOrder(long id, long storeId, String waybillCode, String companyName, String companyCode) {
        logger.debug("deliverOrder and id:{} \r\n storeId:{} \r\n companyName:{} \r\n companyCode:{}", id, storeId, companyName, companyCode);
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("storeId", storeId);
        params.put("waybillCode", waybillCode);
        params.put("companyName", companyName);
        params.put("companyCode", companyCode);
        return orderMapper.deliverOrder(params);
    }


    @Override
    public OmsOrder queryOrderDetailById(long id, long customerId, long storeId, OrderItem... orderItems) {
        logger.debug("queryOrderDetailById and id:{} \r\n storeId:{} \r\n: {} \r\n :{}", id, storeId, orderItems, customerId);

        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("storeId", storeId);
        params.put("customerId", customerId);

        // 查询订单信息
        OmsOrder order = orderMapper.queryOrderById(params);

        if (Objects.isNull(order)) {
            logger.error("queryOrderById fail...due to order is null....");
            return order;
        }

        // 设置订单的其他信息
        this.fillOrderOtherInfos(order, orderItems);

        return order;
    }

    @Transactional
    @Override
    public void saveOrder(OmsOrder order) {
        logger.debug("saveOrder and order:{}", order);

        if (Objects.isNull(order)) {
            logger.error("saveOrder fail due to order is empty....");
            return;
        }

        // 保存订单主表
        orderMapper.saveOrder(order);

        // 设置订单id
        order.setEveryOrderId();

        // 保存订单属性表
        orderAttrService.insertOmsOrderAttr(order.getOrderAttr());

        // 保存订单单品表
        orderSkuService.saveOrderSkus(order.getOrderSkus());
    }


    @Override
    public List<OmsOrder> queryToPayOrder(String orderCode, long customerId, OrderItem... orderItems) {
        logger.debug("queryToPayOrder and orderCode:{} \r\n customerId:{}", orderCode, customerId);

        Map<String, Object> params = new HashMap<>();
        params.put("orderCode", orderCode);
        params.put("customerId", customerId);

        List<OmsOrder> orders = orderMapper.queryToPayOrder(params);

        if (CollectionUtils.isEmpty(orders)) {
            return orders;
        }

        orders.stream().forEach(order -> this.fillOrderOtherInfos(order, orderItems));

        return orders;
    }

    @Override
    public PageHelper<OmsOrder> queryOrdersForSite(PageHelper<OmsOrder> pageHelper, QueryOrderCriteria queryCriteria, OrderItem... orderItems) {
        logger.debug("queryOrdersForSite and pageHelper:{} \r\n queryCriteria:{} \r\n orderItems:{}", pageHelper, queryCriteria, orderItems);
        // 获得查询参数
        Map<String, Object> params = queryCriteria.getQueryMapForSite();

        // 查询出来的订单
        List<OmsOrder> orders = orderMapper.queryOrdersForSite(pageHelper.getQueryParams(params, orderMapper.queryOrderCountForSite(params)));

        // 设置订单的其他信息
        if (!CollectionUtils.isEmpty(orders)) {
            orders.stream().forEach(order -> this.fillOrderOtherInfos(order, ArrayUtils.add(orderItems, OrderItem.BACK_PROGRESS)));
        }

        return pageHelper.setListDates(orders);
    }

    @Override
    public int confirmReceipt(long orderId, long customerId) {
        logger.debug("confirmReceipt and orderId:{} \r\n customerId:{}", orderId, customerId);
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("customerId", customerId);
        return orderMapper.confirmReceipt(params);
    }

    @Override
    public List<OmsOrder> queryOrdersForCancel() {
        logger.debug("queryOrdersForCancel......");
        return orderMapper.queryOrdersForCancel();
    }


    @Override
    public List<OmsOrder> queryDepositPreSaleOrdersForCancel() {
        logger.debug("queryDepositPreSaleOrdersForCancel......");
        return orderMapper.queryDepositPreSaleOrdersForCancel();
    }


    @Override
    public int toPayOrderCount(long customerId) {
        logger.debug("toPayOrderCount and customerId:{}", customerId);
        return orderMapper.toPayOrderCount(customerId);
    }

    @Override
    public int toDeliverOrderCount(long customerId) {
        logger.debug("toDeliverOrderCount and customerId:{}", customerId);
        return orderMapper.toDeliverOrderCount(customerId);
    }

    @Override
    public int toReceiptOrderCount(long customerId) {
        logger.debug("toReceiptOrderCount and customerId:{}", customerId);
        return orderMapper.toReceiptOrderCount(customerId);
    }

    @Override
    public int toEvaluateOrderCount(long customerId) {
        logger.debug("toEvaluateOrderCount and customerId:{}", customerId);
        return orderMapper.toEvaluateOrderCount(customerId);
    }

    @Override
    public int updateOrderBack(long orderId, String status) {
        logger.debug("updateOrderBack and orderId:{} \r\n status:{}", orderId, status);
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("status", status);
        return orderMapper.updateOrderBack(params);
    }

    @Override
    public int saleNum(long storeId) {
        logger.debug("saleNum and storeId:{}", storeId);
        return orderMapper.saleNum(storeId);
    }

    @Override
    public List<OmsOrder> queryOrderByOrderCode(String orderCode, long customerId, OrderItem... orderItems) {
        logger.debug("queryOrderById and orderCode:{} \r\n customerId:{}", orderCode, customerId);
        Map<String, Object> params = new HashMap<>();
        params.put("orderCode", orderCode);
        params.put("customerId", customerId);
        List<OmsOrder> orders = orderMapper.queryOrderByOrderCode(params);
        if (CollectionUtils.isEmpty(orders)) {
            logger.error("queryOrderById fail : query null");
            return null;
        }
        orders.stream().forEach(order -> this.fillOrderOtherInfos(order, orderItems));

        return orders;
    }

    @Override
    public int updateOrderEvluation(long orderId, long customerId) {
        logger.debug("updateOrderEvluation and orderId:{},customerId:{}", orderId, customerId);
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("customerId", customerId);
        return orderMapper.updateOrderEvluation(params);
    }


    @Override
    public List<OmsOrder> queryOrdersForConfirmReceipt(int day) {
        logger.debug("queryOrdersForConfirmReceipt and day:{}", day);
        return orderMapper.queryOrdersForConfirmReceipt(day);
    }

    @Override
    public PageHelper<OmsOrder> querySpreadOrders(PageHelper<OmsOrder> pageHelper, QueryOrderCriteria queryCriteria) {
        logger.debug("querySpreadOrders and pageHelper:{} \r\n queryCriteria:{}", pageHelper, queryCriteria);
        List<OmsOrder> orderList = orderMapper.querySpreadOrders(pageHelper.getQueryParams(queryCriteria.getQueryMap(), orderMapper.querySpreadOrdersCount(queryCriteria.getQueryMap())));
        // 设置订单下的单品信息
        orderList.forEach(order -> this.fillOrderOtherInfos(order, OrderItem.SKUS));
        // 过滤掉没有分销的单品
        orderList.forEach(order -> order.setOrderSkus(order.getOrderSkus().stream().filter(OmsOrderSku::hasSetCommissionRate).collect(Collectors.toList())));
        // 设置分销当前分销单品的佣金比例（主要是设置当前登入的人对于这笔订单是一级还是二级）
        orderList.forEach(order -> this.setSetCommissionRate(order, queryCriteria.getCustomerId()));
        return pageHelper.setListDates(orderList);
    }


    /**
     * 主要是设置当前登入的人对于这笔订单是一级还是二级
     *
     * @param order      订单
     * @param customerId 会员id
     */
    private void setSetCommissionRate(OmsOrder order, long customerId) {
        // 说明当前登入人是这个订单的二级分佣
        if (order.getSRecommended() == customerId && customerId != -1) {
            // 这边把单品的一级佣金改成了二级 这样前端直接拿一级佣金就行了
            order.setCommissionLevel(CommonConstant.SECOND_COMMISSION_LEVEL);
        }
    }

    @Override
    public List<OmsOrder> queryOrdersByIdsWithOrderSku(List<Long> ids, String status, long storeId) {
        logger.debug("queryOrdersByIdsWithOrderSku and ids:{} \r\n status:{} \r\n storeId:{}", Arrays.toString(ids.toArray()), status, storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("ids", ids);
        params.put("storeId", storeId);
        params.put("status", status);
        return orderMapper.queryOrdersByIds(params).stream().map(order -> {
            fillOrderOtherInfos(order, OrderItem.SKUS, OrderItem.ATTR);
            return order;
        }).collect(Collectors.toList());
    }

    @Override
    public List<OmsOrder> queryAllOrderWithOrderSku(String status, long storeId, Long marketingId) {
        logger.debug("queryAllOrderWithOrderSku and status:{} \r\n storeId:{} \r\n marketingId:{} ", status, storeId, marketingId);
        Map<String, Object> params = new HashMap<>();
        params.put("storeId", storeId);
        params.put("status", status);
        if (Objects.nonNull(marketingId)) {
            params.put("marketingId", marketingId);
        }
        return orderMapper.queryAllOrder(params).stream().map(order -> {
            fillOrderOtherInfos(order, OrderItem.SKUS, OrderItem.ATTR);
            return order;
        }).collect(Collectors.toList());
    }

    @Override
    public List<OmsOrder> queryNotPayGroupOrdersByIdsWithOrderSku(List<Long> ids, long storeId) {
        logger.debug("queryNotPayGroupOrdersByIdsWithOrderSku and ids:{} \r\n storeId:{}", Arrays.toString(ids.toArray()), storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("ids", ids);
        params.put("storeId", storeId);
        return orderMapper.queryNotPayGroupOrdersByIds(params).stream().map(order -> {
            fillOrderOtherInfos(order, OrderItem.SKUS, OrderItem.ATTR);
            return order;
        }).collect(Collectors.toList());
    }

    @Override
    public List<OmsOrder> queryAllNotPayGroupOrderWithOrderSku(long storeId) {
        logger.debug("queryAllNotPayGroupOrderWithOrderSku and  storeId:{} ", storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("storeId", storeId);
        return orderMapper.queryAllNotPayGroupOrder(params).stream().map(order -> {
            fillOrderOtherInfos(order, OrderItem.SKUS, OrderItem.ATTR);
            return order;
        }).collect(Collectors.toList());
    }

    @Override
    public BigDecimal querySaleAmountToday(long storeId) {
        logger.debug("querySaleAmountToday and storeId :{}", storeId);
        return orderMapper.querySaleAmountToday(storeId);
    }

    @Override
    public int querySaleCountToday(long storeId) {
        logger.debug("querySaleCountToday and storeId :{}", storeId);
        return orderMapper.querySaleCountToday(storeId);
    }

    @Override
    public BigDecimal querySaleAmountThisWeek(long storeId) {
        logger.debug("querySaleAmountThisWeek and storeId :{}", storeId);
        return orderMapper.querySaleAmountThisWeek(storeId);
    }

    @Override
    public int querySaleCountThisWeek(long storeId) {
        logger.debug("querySaleCountThisWeek and storeId :{}", storeId);
        return orderMapper.querySaleCountThisWeek(storeId);
    }


    @Override
    public int queryCrowdFundingOrderCount(long marketingId, long storeId) {
        logger.debug("queryCrowdFundingOrderCount and marketingId:{} \r\n storeId:{}", marketingId, storeId);
        return orderMapper.queryCrowdFundingOrderCount(marketingId, storeId);
    }

    @Override
    public int queryCrowFundingCustomerCount(long marketingId, long storeId) {
        logger.debug("queryCrowFundingCustomerCount and marketingId:{} \r\n storeId:{}", marketingId, storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("marketingId", marketingId);
        params.put("storeId", storeId);
        return orderMapper.queryCrowFundingCustomerCount(params);
    }

    @Override
    public List<OmsOrder> queryAllCrowdFundingOrderList(long marketingId, long storeId) {
        logger.debug("queryAllCrowdFundingOrderList and marketingId:{} \r\n storeId:{}", marketingId, storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("marketingId", marketingId);
        params.put("storeId", storeId);
        return orderMapper.queryAllCrowdFundingOrderList(params);
    }

    @Override
    public int updateCrowdFundingOrderLotteryStatus(long orderId) {
        logger.debug("updateCrowdFundingOrderLotteryStatus and orderId:{}", orderId);
        return orderMapper.updateCrowdFundingOrderLotteryStatus(orderId);
    }

    @Override
    public PageHelper<OmsOrder> queryCrowdFundingOrders(PageHelper<OmsOrder> pageHelper, QueryOrderCriteria queryCriteria) {
        logger.debug("queryCrowdFundingOrders and pageHelper:{} \r\n queryCriteria:{}", pageHelper, queryCriteria);
        Map<String, Object> params = queryCriteria.getQueryMap();
        return pageHelper.setListDates(orderMapper.queryCrowdFundingOrders(pageHelper.getQueryParams(params, orderMapper.queryCrowdFundingOrdersCount(params))));
    }

    @Override
    public int queryTodayPayedOrderNum(long storeId) {
        logger.debug("queryTodayPayedOrderNum and storeId:{}", storeId);
        return orderMapper.queryTodayPayedOrderNum(storeId);
    }

    @Override
    public int queryToDeliveryOrderNum(long storeId) {
        logger.debug("queryToDeliveryOrderNum and storeId:{}", storeId);
        return orderMapper.queryToDeliveryOrderNum(storeId);
    }

    @Override
    public int queryToPayedOrderNum(long storeId) {
        logger.debug("queryToPayedOrderNum and storeId:{}", storeId);
        Map<String, Object> params = new HashMap<>();
        params.put("storeId", storeId);
        params.put("status", "1");
        return orderMapper.queryOrderCount(params);
    }

    @Override
    public PageHelper<OmsOrder> queryCustomerCommunityOrders(PageHelper<OmsOrder> pageHelper, QueryOrderCriteria queryCriteria) {
        logger.debug("queryCustomerCommunityOrders and pageHelper:{} \r\n queryCriteria:{}", pageHelper, queryCriteria);
        Map<String, Object> params = queryCriteria.getQueryMapForCommunity();

        // 订单信息
        List<OmsOrder> orders = orderMapper.queryCustomerCommunityOrders(pageHelper.getQueryParams(params, orderMapper.queryCustomerCommunityOrdersNum(params)));


        if (!CollectionUtils.isEmpty(orders)) {
            orders.stream().forEach(order -> {
                fillOrderOtherInfos(order, OrderItem.SKUS);
            });
        }
        return pageHelper.setListDates(orders);
    }

    @Override
    public List<OmsOrder> queryCommunityOrdersForCancel() {
        logger.debug("begin to queryCommunityOrdersForCancel....");
        return orderMapper.queryCommunityOrdersForCancel();
    }

    @Override
    public PageHelper<OmsOrder> queryHeadCommunityOrders(PageHelper<OmsOrder> pageHelper, QueryOrderCriteria queryCriteria) {
        logger.debug("queryHeadCommunityOrders and pageHelper:{} \r\n queryCriteria:{}", pageHelper, queryCriteria);
        Map<String, Object> params = queryCriteria.getQueryMapForCommunity();

        // 订单信息
        List<OmsOrder> orders = orderMapper.queryHeadCommunityOrders(pageHelper.getQueryParams(params, orderMapper.queryHeadCommunityOrdersCount(params)));


        if (!CollectionUtils.isEmpty(orders)) {
            orders.stream().forEach(order -> {
                fillOrderOtherInfos(order, OrderItem.SKUS);
            });
        }
        return pageHelper.setListDates(orders);
    }

    @Override
    public List<OmsOrder> queryNoPayOrdersByCommunityBuyId(long communityBuyId) {
        logger.debug("queryNoPayOrdersByCommunityBuyId and communityBuyId");
        Map<String, Object> params = new HashMap<>();
        params.put("communityBuyId", communityBuyId);
        params.put("status", "1");
        List<OmsOrder> orders = orderMapper.queryOrdersByCommunityBuyId(params);

        if (!CollectionUtils.isEmpty(orders)) {
            orders.stream().forEach(order -> this.fillOrderOtherInfos(order, OrderItem.SKUS));
        }
        return orders;
    }

    @Override
    public List<OmsOrder> queryPayedOrdersByCommunityBuyId(long communityBuyId) {
        logger.debug("queryPayedOrdersByCommunityBuyId and communityBuyId:{}", communityBuyId);
        Map<String, Object> params = new HashMap<>();
        params.put("communityBuyId", communityBuyId);
        params.put("status", "2");
        List<OmsOrder> orders = orderMapper.queryOrdersByCommunityBuyId(params);
        if (!CollectionUtils.isEmpty(orders)) {
            orders.stream().forEach(order -> this.fillOrderOtherInfos(order, OrderItem.SKUS));
        }
        return orders;
    }

    @Override
    public List<OmsOrder> queryJoinCommunityOrders(long communityBuyCustomerId, long communityBuyId) {
        logger.debug("queryJoinCommunityOrders and communityBuyCustomerId :{} \r\n communityBuyId :{}", communityBuyCustomerId, communityBuyId);
        Map<String, Object> params = new HashMap<>();
        params.put("communityBuyCustomerId", communityBuyCustomerId);
        params.put("communityBuyId", communityBuyId);
        return orderMapper.queryJoinCommunityOrders(params);
    }

    /**
     * 查询社区团购已参团人数
     *
     * @param communityBuyCustomerId 社区团购的团长会员id
     * @param communityBuyId         社区团购的团购id
     * @return 社区团购已参团人数
     */
    @Override
    public int queryJoinCommunityOrdersCount(long communityBuyCustomerId, long communityBuyId) {
        logger.debug("queryJoinCommunityOrdersCount and communityBuyCustomerId:{} \r\n communityBuyId:{}", communityBuyCustomerId, communityBuyId);
        Map<String, Object> params = new HashMap<>();
        params.put("communityBuyCustomerId", communityBuyCustomerId);
        params.put("communityBuyId", communityBuyId);
        return orderMapper.queryJoinCommunityOrdersCount(params);
    }

    @Override
    public PageHelper<OmsOrder> queryCommunityOrders(PageHelper<OmsOrder> pageHelper, QueryOrderCriteria queryCriteria) {
        logger.debug("queryCommunityOrders and pageHelper :{} \r\n queryCriteria :{}", pageHelper, queryCriteria);
        Map<String, Object> params = queryCriteria.getQueryMapForCommunityAdmin();
        return pageHelper.setListDates(orderMapper.queryCommunityOrders(pageHelper.getQueryParams(params, orderMapper.queryCommunityOrderCount(params))));
    }

    /**
     * 根据团长的会员ID 和 团购活动ID 查询该团长在该活动中所有的销售订单  字段过多 仅查询必要字段
     *
     * @param customerId     团长的会员ID
     * @param communityBuyId 活动ID
     * @return 订单集
     */
    @Override
    public List<OmsOrder> queryCommunityOrders(Long customerId, Long communityBuyId, String status) {
        logger.debug("queryCommunityOrders and customerId and communityBuyId:{}", customerId, communityBuyId);
        Map<String, Object> param = new HashMap<>(3);
        param.put("communityBuyId", communityBuyId);
        param.put("headCustomerId", customerId);
        param.put("status", status);
        return orderMapper.queryCommunityBuyOrders(param);
    }

    @Override
    public List<OmsOrder> queryCommunityOrdersByIds(List<Long> ids) {
        logger.debug("queryCommunityOrdersByIds and ids:{}", Arrays.toString(ids.toArray()));
        Map<String, Object> params = new HashMap<>();
        params.put("ids", ids);
        return orderMapper.queryCommunityOrdersByIds(params);
    }

    @Override
    public List<OmsOrder> queryAllCommunityOrders(String mobile) {
        logger.debug("queryAllCommunityOrders and mobile :{}", mobile);
        Map<String, Object> params = new HashMap<>();
        params.put("communityBuyHeadMobile", mobile);
        return orderMapper.queryAllCommunityOrders(params);
    }

    @Override
    public int confirmCommunityOrderReceipt(long orderId, long customerId) {
        logger.debug("confirmCommunityOrderReceipt and orderId:{} \r\n customerId:{}", orderId, customerId);
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("customerId", customerId);
        return orderMapper.confirmCommunityOrderReceipt(params);
    }

    /**
     * 根据社区团购 id 查询订单 id 集(PC 端计算销量用, 因字段过多 ,  只查询必要信息)
     *
     * @param communityBuyId 社区团购 id
     * @return id 结果集
     */
    @Override
    public List<Long> queryOrderIdsByCommunityBuyId(Long communityBuyId) {
        logger.debug("queryOrderIdsByCommunityBuyId and communityBuyId :{}", communityBuyId);

        return orderMapper.queryOrderIdsByCommunityBuyId(communityBuyId);
    }

    /**
     * 填充订单的其他信息
     *
     * @param order      订单信息
     * @param orderItems 填充条件
     */
    private void fillOrderOtherInfos(OmsOrder order, OrderItem... orderItems) {

        // 设置订单下的单品信息
        if (ArrayUtils.contains(orderItems, OrderItem.SKUS)) {
            order.setOrderSkus(orderSkuService.queryByOrderId(order.getId()));
        }

        // 设置订单的退单状态
        if (ArrayUtils.contains(orderItems, OrderItem.BACK_PROGRESS)) {
//            //todo  根据订单id查询该订单的退单纪录
//            List<OmsBackOrder> backOrders = backOrderService.queryByOrderId(order.getId(), CommonConstant.QUERY_WITH_NO_CUSTOMER);
//
//            // 如果存在正在处理的退单，则设置订单退单进行中字段
//            if (!CollectionUtils.isEmpty(backOrders) && backOrders.stream().anyMatch(OmsBackOrder::isInBackProgress)) {
//                order.setInBackProgress(true);
//            }

        }

        // 设置订单的附属信息
        if (ArrayUtils.contains(orderItems, OrderItem.ATTR)) {
            // 设置订单的附属信息
            order.setOrderAttr(orderAttrService.queryByOrderId(order.getId()));

//            // 转化赠品信息
//            order.convertGiftToList();
        }

//        // 设置订单是否可以退款
//        if (ArrayUtils.contains(orderItems, OrderItem.CANREFUND)) {
//            order.setCanRefund(backOrderService.isCanApplyRefund(order.getId(), order.getCustomerId(), order.getOrderType()));
//        }

//        //设置订单是否可以退货
//        if (ArrayUtils.contains(orderItems, OrderItem.CANRETRUN)) {
//            order.setCanReturn(!CollectionUtils.isEmpty(backOrderService.getCanRetrunOrderSkus(order)) && !order.overCanReturnDays());
//        }


//        // 设置订单操作日志
//        if (ArrayUtils.contains(orderItems, OrderItem.LOG)) {
//            order.setOrderOperatonLogs(orderOperatonLogService.queryOrderOperatonLogByOrderId(order.getId()));
//        }

//        //设置快递100查询地址(无回报支持的订单 和虚拟商品订单  和社区团购没有快递信息)
//        if (ArrayUtils.contains(orderItems, OrderItem.EXPRESSURL) && !order.isNoReturnOrder() && !order.isVirtualOrder() && !order.isCommunityBuyOrder()) {
//            order.setExpress100Url(Express100Utils.getHtmlApiURl(order.getLogisticsCode(), order.getWaybillCode()));
//        }
    }

    /**
     * 查询订单
     *
     * @param id 订单ID
     * @return 订单
     */
    @Override
    public OmsOrder selectOmsOrderById(Long id) {
        return omsOrderMapper.selectOmsOrderById(id);
    }

    /**
     * 查询订单列表
     *
     * @param omsOrder 订单
     * @return 订单
     */
    @Override
    public List<OmsOrder> selectOmsOrderList(OmsOrder omsOrder) {
        return omsOrderMapper.selectOmsOrderList(omsOrder);
    }

    /**
     * 新增订单
     *
     * @param omsOrder 订单
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertOmsOrder(OmsOrder omsOrder) {
        int row = 0;
        OmsOrder omsOrderParam = new OmsOrder();
        omsOrderParam.setOrderCode(omsOrder.getOrderCode());
        List<OmsOrder> omsOrderList = omsOrderMapper.selectOmsOrderList(omsOrderParam);

        if (CollectionUtils.isEmpty(omsOrderList)) {
            row += omsOrderMapper.insertOmsOrder(omsOrder);
        } else {
            omsOrder.setId(omsOrderList.get(0).getId());
            row += omsOrderMapper.updateOmsOrder(omsOrder);
        }

        List<OmsOrderSku> orderSkus = omsOrder.getOrderSkus();

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orderSkus)) {
            orderSkus.forEach(omsOrderSku -> omsOrderSku.setOrderId(omsOrder.getId()));
            orderSkuService.saveOrderSkus(orderSkus);
        }

        return row;
    }

    /**
     * 修改订单
     *
     * @param omsOrder 订单
     * @return 结果
     */
    @Override
    public int updateOmsOrder(OmsOrder omsOrder) {
        return omsOrderMapper.updateOmsOrder(omsOrder);
    }

    /**
     * 批量删除订单
     *
     * @param ids 需要删除的订单ID
     * @return 结果
     */
    @Override
    public int deleteOmsOrderByIds(Long[] ids) {
        return omsOrderMapper.deleteOmsOrderByIds(ids);
    }

    /**
     * 删除订单信息
     *
     * @param id 订单ID
     * @return 结果
     */
    @Override
    public int deleteOmsOrderById(Long id) {
        return omsOrderMapper.deleteOmsOrderById(id);
    }

    /**
     * 查询该用户下的所有订单信息
     *
     * @param userId 用户id
     * @return 订单信息
     */
    @Override
    public List<OmsOrder> queryOrdersByUserId(Long userId) {
        // 获取该用户的wid
        BizUser bizUser = bizUserService.getBasicInfo(userId);
        Long wid = bizUser.getWid();
        if (wid == null) {
            return Collections.emptyList();
        }
        return orderMapper.getOrdersByWid(wid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveWmOrders(List<JSONObject> wmOrders) {
        logger.info("save wm orders, size: {}", wmOrders.size());
        int rows = 0;
        for (JSONObject jsonObject : wmOrders) {
            OmsOrder omsOrder = convertToOmsOrder(jsonObject);
            // 根据订单状态更新用户体温
            if (EWmOrderStatus.PENDING_SHIPMENT.getOmsCode().equals(omsOrder.getStatus()) && sysConfigService.isOpen(ConfigConstants.IS_TEMP_CHECK_OPEN)) {
                logger.info("更新用户体温信息, orderCode: {}", omsOrder.getOrderCode());
                TempHandlerReq req = new TempHandlerReq();
                req.setType(ETempHandlerType.BUY_HANDLER);
                req.setOmsOrder(omsOrder);
                redisCache.publish(ERedisChannel.DEAL_TEMP_CHANNEL.getCode(), req);
            }
            rows += insertOmsOrder(omsOrder); // 插入订单,
        }

        return rows;
    }

    @Override
    public List<OmsOrder> selectOmsOrderListByOrderIds(List<Long> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) return Collections.emptyList();
        return omsOrderMapper.selectOmsOrderListByOrderIds(orderIdList);
    }

    private OmsOrder convertToOmsOrder(JSONObject jsonObject) {
        OmsOrder omsOrder = new OmsOrder();

        JSONObject orderInfo = jsonObject.getJSONObject("orderInfo");
        JSONObject buyerInfo = orderInfo.getJSONObject("buyerInfo");
        String buyerRemark = buyerInfo.getString("buyerRemark");
        Long wid = buyerInfo.getLong("wid");

        JSONObject orderBaseInfo = orderInfo.getJSONObject("orderBaseInfo");
        String orderNo = orderBaseInfo.getString("orderNo");
        //0-创建；1-部分支付；2-已支付；3-待发货；4-部分发货；5-已发货；7-确认收货；8-完成；9-取消
        String orderStatus = orderBaseInfo.getString("orderStatus");
        String parentOrderNo = orderBaseInfo.getString("parentOrderNo");

        Long updateTime = orderBaseInfo.getLong("updateTime");

        Long createTime = orderBaseInfo.getLong("createTime");
        Long payTime = orderBaseInfo.getLong("payTime");
        //发货
        Long deliveryTime = orderBaseInfo.getLong("finishDeliveryTime");
        //订单收货时间
        Long confirmTime = orderBaseInfo.getLong("confirmTime");
        //完成
        Long finishTime = orderBaseInfo.getLong("finishTime");

        //订单项
        List<JSONObject> items = orderInfo.getJSONArray("items").toList(JSONObject.class);

        List<OmsOrderSku> orderSkuList = items.stream().map(item -> {
            OmsOrderSku omsOrderSku = new OmsOrderSku();
            omsOrderSku.setSkuId(item.getString("skuId"));
            omsOrderSku.setSkuName(item.getString("goodsTitle"));
            omsOrderSku.setSkuImage(item.getString("imageUrl"));
            omsOrderSku.setSkuPrice(item.getBigDecimal("salePrice"));
            omsOrderSku.setNum(item.getInteger("skuNum"));
            omsOrderSku.setPrice(item.getBigDecimal("salePrice"));
            omsOrderSku.setSpuId(item.getLong("goodsId"));
            return omsOrderSku;
        }).collect(Collectors.toList());


        JSONObject payInfo = orderInfo.getJSONObject("payInfo");
        //实付金额
        BigDecimal payAmount = payInfo.getBigDecimal("payAmount");
        //订单应付金额
        BigDecimal shouldPayAmount = payInfo.getBigDecimal("shouldPayAmount");
        //
        BigDecimal totalAmount = payInfo.getBigDecimal("totalAmount");
        //总折扣金额
        BigDecimal totalDiscountAmount = payInfo.getBigDecimal("totalDiscountAmount");

        omsOrder.setOrderCode(orderNo);
        omsOrder.setMasterOrderCode(StringUtils.isBlank(parentOrderNo) ? orderNo : parentOrderNo);
        omsOrder.setCustomerId(wid);
        omsOrder.setPrice(totalAmount);
        omsOrder.setPresalePrice(shouldPayAmount);
        omsOrder.setFreightPrice(BigDecimal.ZERO);
        omsOrder.setConcessionalRate(totalDiscountAmount);
        omsOrder.setStatus(EWmOrderStatus.fromWmCode(orderStatus).getOmsCode());
        omsOrder.setPayType("0");

        omsOrder.setStoreId(1L);
        omsOrder.setCreateTime(new Date(createTime));
        if (payTime != null) {
            omsOrder.setPayTime(new Date(payTime));
        }
        if (deliveryTime != null) {
            omsOrder.setDeliveryTime(new Date(deliveryTime));
        }

        if (confirmTime != null) {
            omsOrder.setReceivingTime(new Date(confirmTime));
        }

        omsOrder.setModifyTime(new Date(updateTime));
        omsOrder.setOrderSkus(orderSkuList);

        omsOrder.setIsFriend(EBoolean.NO.getCode());

        BizUser userCondition = new BizUser();
        userCondition.setWid(omsOrder.getCustomerId());
        List<BizUser> bizUserList = bizUserService.selectBizUserList(userCondition);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(bizUserList)) {
            if (StringUtils.isNotBlank(bizUserList.get(0).getWxid())) {
                omsOrder.setIsFriend(EBoolean.YES.getCode());
            }
        }
        return omsOrder;
    }

}
