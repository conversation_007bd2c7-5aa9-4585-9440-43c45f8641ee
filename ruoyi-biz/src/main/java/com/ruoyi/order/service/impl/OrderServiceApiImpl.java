package com.ruoyi.order.service.impl;


import com.ruoyi.biz.domain.BizUser;
import com.ruoyi.biz.service.IBizUserService;
import com.ruoyi.goods.service.IPmsGoodsService;
import com.ruoyi.goods.service.IPmsSkuService;
import com.ruoyi.goods.utils.CommonConstant;
import com.ruoyi.order.domain.OmsOrder;
import com.ruoyi.order.service.IOmsOrderService;
import com.ruoyi.order.service.OrderServiceApi;
import com.ruoyi.order.vo.OrderItem;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by 魔金商城 on 17/11/7.
 * 订单服务接口
 */
@Service
public class OrderServiceApiImpl implements OrderServiceApi {

    /**
     * 订单状态map
     */
    private static final Map<String, String> statusMap = new HashMap<>();

    static {
        statusMap.put("1", "待付款");
        statusMap.put("2", "待发货");
        statusMap.put("3", "待收货");
        statusMap.put("4", "已完成");
        statusMap.put("5", "已取消");
        statusMap.put("6", "退款通过");
        statusMap.put("7", "退货通过");
    }

    /**
     * 调试日志
     */
    private Logger logger = LoggerFactory.getLogger(OrderServiceApiImpl.class);

    /**
     * 注入单品服务接口
     */
    @Autowired
    private IPmsSkuService skuService;
    /**
     * 注入订单服务接口
     */
    @Autowired
    private IOmsOrderService orderService;

    /**
     * 注入商品服务接口
     */
    @Autowired
    private IPmsGoodsService spuService;

    @Autowired
    private IBizUserService bizUserService;


    @Override
    public OmsOrder queryOrderDetailById(long id, long customerId, long storeId, OrderItem... orderItems) {
        logger.debug("queryOrderDetailById and id:{} \r\n customerId:{} \r\n storeId:{} \r\n orderItems:{}", id, customerId, storeId, orderItems);

        // 查询订单详情
        OmsOrder order = orderService.queryOrderDetailById(id, customerId, storeId, orderItems);

        if (Objects.isNull(order)) {
            logger.error("order is not exist");
            return order;
        }

//        // 填充店铺信息
//        if (ArrayUtils.contains(orderItems, OrderItem.STORE_INFO)) {
//            TStoreInfo storeInfo = storeInfoService.queryStoreInfo(order.getStoreId());
//            if (Objects.nonNull(storeInfo)) {
//                order.setStoreName(storeInfo.getStoreName());
//            }
//        }

        // 填充会员信息
        if (ArrayUtils.contains(orderItems, OrderItem.CUSTOMER)) {

            if (order.getCustomerId() != null) {
                List<BizUser> userList = bizUserService.selectBizUserList(new BizUser() {{
                    setWid(order.getCustomerId());
                }});
                if (CollectionUtils.isEmpty(userList)) return order;
                order.setBizUser(userList.get(0));
//                order.setCustomerName("");
//                order.setCustomerMobile();
            }


//            UmsMember customer = customerService.queryCustomerWithNoPasswordById(order.getCustomerId());
//            if (Objects.nonNull(customer)) {
//                order.setCustomerName(customer.getUsername());
//                order.setCustomerMobile(customer.getMobile());
//            } else {
//                order.setCustomerName("");
//                order.setCustomerMobile("");
//            }
        }

//        // 填充社区团购团长信息
//        if (ArrayUtils.contains(orderItems, OrderItem.COMMUNITYHEAD)) {
//            order.setCommunityBuyHeadMobile("");
//            UmsMember customer = customerService.queryCustomerWithNoPasswordById(order.getCommunityBuyCustomerId());
//            if (Objects.nonNull(customer)) {
//                order.setCommunityBuyHeadMobile(customer.getMobile());
//            }
//        }

        return order;
    }


    @Override
    public List<OmsOrder> queryPrintOrderDetails(List<Long> ids) {
        logger.debug("queryPrintOrderDetails and ids:{}", ids);
        return queryPrintOrderDetailsForStore(ids, CommonConstant.QUERY_WITH_NO_STORE);
    }

    @Override
    public List<OmsOrder> queryPrintOrderDetailsForStore(List<Long> ids, long storeId) {
        logger.debug("queryPrintOrderDetails and ids:{} \r\n storeId:{}", ids, storeId);
        if (CollectionUtils.isEmpty(ids)) {
            logger.error("queryPrintOrderDetails fail due to ids is empty....");
            return Collections.emptyList();
        }

        return ids.parallelStream().map(id -> this.queryOrderDetailById(id, CommonConstant.NO_CUSTOMER_ID, storeId, OrderItem.ATTR, OrderItem.SKUS, OrderItem.STORE_INFO, OrderItem.CUSTOMER)).collect(Collectors.toList());
    }


}
