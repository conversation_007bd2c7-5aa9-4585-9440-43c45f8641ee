package com.ruoyi.order.service;


import com.ruoyi.order.domain.OmsOrder;
import com.ruoyi.order.vo.OrderItem;

import java.util.List;

/**
 * Created by 魔金商城 on 17/11/7.
 * 订单服务接口
 */
public interface OrderServiceApi {

    /**
     * 查询订单详情
     *
     * @param id         订单id
     * @param customerId 会员id
     * @param storeId    店铺id
     * @param orderItems 查询条件
     * @return 返回订单详情
     */
    OmsOrder queryOrderDetailById(long id, long customerId, long storeId, OrderItem... orderItems);


    /**
     * 查询打印订单的详情
     *
     * @param ids 订单id
     * @return 返回打印订单的详情
     */
    List<OmsOrder> queryPrintOrderDetails(List<Long> ids);

    /**
     * 查询打印订单的详情
     *
     * @param ids     订单id
     * @param storeId 店铺id
     * @return 返回打印订单的详情
     */
    List<OmsOrder> queryPrintOrderDetailsForStore(List<Long> ids, long storeId);

}
