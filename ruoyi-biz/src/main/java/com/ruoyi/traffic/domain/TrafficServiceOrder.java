package com.ruoyi.traffic.domain;

import java.math.BigDecimal;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流量服务订单对象 traffic_service_order
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TrafficServiceOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 服务订单ID */
    private Long id;

    /** 订单编号（唯一标识） */
    @Excel(name = "订单编号")
    private String orderNumber;

    /** 订单类型（1-好友订单，2-群聊订单） */
    @Excel(name = "订单类型", readConverterExp = "1=好友订单,2=群聊订单")
    private String orderType;

    /** 订单状态（0-未使用，1-使用中，2-已完成） */
    @Excel(name = "订单状态", readConverterExp = "0=未使用,1=使用中,2=已完成")
    private Long orderStatus;

    /** 订单总金额 */
    @Excel(name = "订单总金额")
    private BigDecimal totalAmount;

    /** 实际支付金额 */
    @Excel(name = "实际支付金额")
    private BigDecimal finalAmount;

    /** 总服务包数量（好友：好友*每天， 群聊：群聊*每天） */
    @Excel(name = "总服务包数量")
    private Long totalPackage;

    /** 可用服务包数量 */
    @Excel(name = "可用服务包数量")
    private Long availablePackage;

    /** 已用服务包数量 */
    @Excel(name = "已用服务包数量")
    private Long usedPackage;


}
