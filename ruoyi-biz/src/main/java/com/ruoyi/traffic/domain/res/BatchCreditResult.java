package com.ruoyi.traffic.domain.res;

import com.ruoyi.traffic.enums.CreditUsageTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/2
 */
@Data
public class BatchCreditResult {

    /**
     * 总积分
     */
    private BigDecimal totalCredits = BigDecimal.ZERO;

    private Map<CreditUsageTypeEnum, Integer> usageTypeCounts = new HashMap<>();

}
