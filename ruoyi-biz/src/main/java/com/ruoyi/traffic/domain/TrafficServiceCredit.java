package com.ruoyi.traffic.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流量服务积分对象 traffic_service_credit
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TrafficServiceCredit extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 积分ID */
    private Long id;

    /** 服务订单ID */
    @Excel(name = "服务订单ID")
    private Long orderId;

    /**
     * 对应用户/群名称
     */
    @Excel(name = "对应用户/群名称")
    private String name;

    /** 服务用户/群聊的ID */
//    @Excel(name = "服务用户/群聊的ID")
    private String refId;

    /** 积分类型（1-好友，2-群聊） */
    @Excel(name = "积分类型", readConverterExp = "1=好友,2=群聊")
    private String creditType;

    /** 透支状态（0-未透支，1-透支中，2-已清偿） */
    @Excel(name = "透支状态", readConverterExp = "0=未透支,1=透支中,2=已清偿")
    private Long overdraftStatus;

    /** 可用积分 */
    @Excel(name = "可用积分")
    private Long availableCredit;

    /** 已用积分 */
    @Excel(name = "已用积分")
    private Long usedCredit;

    /** 剩余积分数量 */
    @Excel(name = "剩余积分数量")
    private Long remainingCredits;

    /** 积分状态（0-未使用，1-使用中，2-已完成，3-已失效） */
    @Excel(name = "积分状态", readConverterExp = "0=未使用,1=使用中,2=已完成,3=已失效")
    private Long status;

    /** 积分有效期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "积分有效期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expiryDate;



    /**
     * 头像地址
     */
    private String avatarUrl;

}
