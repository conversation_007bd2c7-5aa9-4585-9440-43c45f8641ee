package com.ruoyi.traffic.domain;

import java.math.BigDecimal;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 倍率配置对象 multiplier_config
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MultiplierConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long id;

    /** 倍率配置名称 */
    @Excel(name = "倍率配置名称")
    private String name;

    /** 操作类型（如1-群发、2-AI问答，3-AI导购，4-图片生成） */
    @Excel(name = "操作类型", readConverterExp = "1=群发,2=AI问答,3=AI导购,4=图片生成")
    private String operationType;

    /** 倍率值（如10字1credit的倍率值为0.1） */
    @Excel(name = "倍率值")
    private BigDecimal multiplier;

    /** 基础单位（如每10字1credit） */
    @Excel(name = "基础单位")
    private Long baseUnit;

    /** 状态（0-禁用，1-启用） */
    @Excel(name = "状态", readConverterExp = "0=禁用,1=启用")
    private Long status;


}
