package com.ruoyi.traffic.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 积分明细对象 credit_details
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CreditDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 积分明细ID */
    private Long id;

    /** 积分ID */
    @Excel(name = "积分ID")
    private Long creditId;

    /** 消息ID */
    @Excel(name = "消息ID")
    private String messageId;

    /** 使用类型（1-群发，2-单聊，3-导购，4-图片生成） */
    @Excel(name = "使用类型", readConverterExp = "1=群发,2=单聊,3=导购,4=图片生成")
    private String usageType;

    /** 积分使用数量 */
    @Excel(name = "积分使用数量")
    private Long usageAmount;


}
