package com.ruoyi.traffic.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 服务积分表-透支状态枚举,0:未透支、1：透支中、2：已清偿
 * <AUTHOR>
 * @date 2024/12/19
 */
@Getter
@AllArgsConstructor
public enum OverDraftStatusEnum {

    UNDERWRITTEN(0L, "未透支"),
    IN_OVERDRAFT(1L, "透支中"),
    CLEARED(2L, "已清偿"),
    ;
    private final Long code;
    private final String desc;

    @JsonValue
    public Long getCode() {
        return code;
    }

    private static final Map<Long, OverDraftStatusEnum> CODE_MAP = Arrays.stream(OverDraftStatusEnum.values())
            .collect(Collectors.toMap(OverDraftStatusEnum::getCode, Function.identity()));

    @JsonCreator
    public static OverDraftStatusEnum getByCode(Long code) {
        return CODE_MAP.get(code);
    }

}
