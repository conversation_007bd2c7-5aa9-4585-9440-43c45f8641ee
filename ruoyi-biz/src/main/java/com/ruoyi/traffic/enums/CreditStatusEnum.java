package com.ruoyi.traffic.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 积分状态枚举
 */
@Getter
public enum CreditStatusEnum {

    UNUSED(0L, "未使用"),
    IN_USE(1L, "使用中"),
    COMPLETED(2L, "已完成"),
    EXPIRED(3L, "已失效");

    private final Long code;
    private final String desc;

    CreditStatusEnum(Long code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonValue
    public Long getCode() {
        return code;
    }

    private static final Map<Long, CreditStatusEnum> CODE_MAP = Arrays.stream(CreditStatusEnum.values())
            .collect(Collectors.toMap(CreditStatusEnum::getCode, Function.identity()));

    @JsonCreator
    public static CreditStatusEnum getByCode(Long code) {
        return CODE_MAP.get(code);
    }
}
