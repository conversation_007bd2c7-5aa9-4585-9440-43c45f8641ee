package com.ruoyi.traffic.enums;

import lombok.Getter;

/**
 * 代扣积分状态枚举，0-待处理，1-处理成功，2-处理失败
 * <AUTHOR>
 * @date 2024/12/23
 */
@Getter
public enum DeductionStatusEnum {
    PENDING("0", "待处理"),
    SUCCESS("1", "处理成功"),
    FAIL("2", "处理失败");

    private String code;
    private String desc;

    DeductionStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
