package com.ruoyi.traffic.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 积分类型枚举
 */
@Getter
public enum CreditTypeEnum {

    USER("1", "好友"),
    GROUP("2", "群聊");

    private final String code;
    private final String desc;

    CreditTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonValue
    public String getCode() {
        return code;
    }

    private static final Map<String, CreditTypeEnum> CODE_MAP = Arrays.stream(CreditTypeEnum.values())
            .collect(Collectors.toMap(CreditTypeEnum::getCode, Function.identity()));

    @JsonCreator
    public static CreditTypeEnum getByCode(String code) {
        return CODE_MAP.get(code);
    }
}
