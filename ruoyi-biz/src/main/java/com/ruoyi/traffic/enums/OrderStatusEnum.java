package com.ruoyi.traffic.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单状态枚举
 */
@Getter
public enum OrderStatusEnum {

    UNUSED(0L, "未使用"),
    IN_USE(1L, "使用中"),
    COMPLETED(2L, "已完成");

    private final Long code;
    private final String desc;

    OrderStatusEnum(Long code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonValue
    public Long getCode() {
        return code;
    }

    private static final Map<Long, OrderStatusEnum> CODE_MAP = Arrays.stream(OrderStatusEnum.values())
            .collect(Collectors.toMap(OrderStatusEnum::getCode, Function.identity()));

    @JsonCreator
    public static OrderStatusEnum getByCode(Long code) {
        return CODE_MAP.get(code);
    }
} 
