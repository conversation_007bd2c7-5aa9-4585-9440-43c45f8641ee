package com.ruoyi.traffic.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 积分使用类型枚举
 */
@Getter
public enum CreditUsageTypeEnum {

    GROUP("1", "群发"),
    PRIVATE("2", "单聊/AI问答"),
    SHOPPING("3", "AI导购"),
    IMAGE("4", "图片生成"),
    MINI_PROGRAM("5", "小程序"),
    ;
    private final String code;
    private final String desc;

    CreditUsageTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonValue
    public String getCode() {
        return code;
    }

    private static final Map<String, CreditUsageTypeEnum> CODE_MAP = Arrays.stream(CreditUsageTypeEnum.values())
            .collect(Collectors.toMap(CreditUsageTypeEnum::getCode, Function.identity()));

    @JsonCreator
    public static CreditUsageTypeEnum fromCode(String code) {
        return CODE_MAP.get(code);
    }
}
