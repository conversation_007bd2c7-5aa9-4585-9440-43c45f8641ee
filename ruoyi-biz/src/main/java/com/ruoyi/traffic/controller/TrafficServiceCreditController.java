package com.ruoyi.traffic.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.traffic.domain.TrafficServiceCredit;
import com.ruoyi.traffic.service.ITrafficServiceCreditService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 流量服务积分Controller
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@RestController
@RequestMapping("/traffic/trafficCredit")
public class TrafficServiceCreditController extends BaseController
{
    @Resource
    private ITrafficServiceCreditService trafficServiceCreditService;

    /**
     * 查询流量服务积分列表
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficCredit:list')")
    @GetMapping("/list")
    public TableDataInfo list(TrafficServiceCredit trafficServiceCredit)
    {
        startPage();
        List<TrafficServiceCredit> list = trafficServiceCreditService.selectTrafficServiceCreditList(trafficServiceCredit);
        return getDataTable(list);
    }

    /**
     * 导出流量服务积分列表
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficCredit:export')")
    @Log(title = "流量服务积分", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TrafficServiceCredit trafficServiceCredit)
    {
        List<TrafficServiceCredit> list = trafficServiceCreditService.selectTrafficServiceCreditList(trafficServiceCredit);
        ExcelUtil<TrafficServiceCredit> util = new ExcelUtil<TrafficServiceCredit>(TrafficServiceCredit.class);
        util.exportExcel(response, list, "流量服务积分数据");
    }

    /**
     * 获取流量服务积分详细信息
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficCredit:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(trafficServiceCreditService.selectTrafficServiceCreditById(id));
    }

    /**
     * 新增流量服务积分
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficCredit:add')")
    @Log(title = "流量服务积分", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TrafficServiceCredit trafficServiceCredit)
    {
        return toAjax(trafficServiceCreditService.insertTrafficServiceCredit(trafficServiceCredit));
    }

    /**
     * 修改流量服务积分
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficCredit:edit')")
    @Log(title = "流量服务积分", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TrafficServiceCredit trafficServiceCredit)
    {
        return toAjax(trafficServiceCreditService.updateTrafficServiceCredit(trafficServiceCredit));
    }

    /**
     * 删除流量服务积分
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficCredit:remove')")
    @Log(title = "流量服务积分", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(trafficServiceCreditService.deleteTrafficServiceCreditByIds(ids));
    }

    /**
     * 获取流量订单使用明细
     */
    @GetMapping("/getTrafficOrderDetailsByOrderId/{id}")
    public AjaxResult getTrafficOrderDetailsByOrderId(@PathVariable Long id){
        return success(trafficServiceCreditService.getTrafficOrderDetailsByOrderId(id));
    }
}
