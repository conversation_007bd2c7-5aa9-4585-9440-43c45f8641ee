package com.ruoyi.traffic.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.traffic.domain.res.CreditDetailResult;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.traffic.domain.CreditDetails;
import com.ruoyi.traffic.service.ICreditDetailsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 积分明细Controller
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@RestController
@RequestMapping("/traffic/trafficDetails")
public class CreditDetailsController extends BaseController
{
    @Resource
    private ICreditDetailsService creditDetailsService;

    /**
     * 查询积分明细列表
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficDetails:list')")
    @GetMapping("/list")
    public TableDataInfo list(CreditDetails creditDetails)
    {
        startPage();
        List<CreditDetails> list = creditDetailsService.selectCreditDetailsList(creditDetails);
        return getDataTable(list);
    }

    /**
     * 导出积分明细列表
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficDetails:export')")
    @Log(title = "积分明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CreditDetails creditDetails)
    {
        List<CreditDetails> list = creditDetailsService.selectCreditDetailsList(creditDetails);
        ExcelUtil<CreditDetails> util = new ExcelUtil<CreditDetails>(CreditDetails.class);
        util.exportExcel(response, list, "积分明细数据");
    }

    /**
     * 获取积分明细详细信息
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficDetails:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(creditDetailsService.selectCreditDetailsById(id));
    }

    /**
     * 新增积分明细
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficDetails:add')")
    @Log(title = "积分明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CreditDetails creditDetails)
    {
        return toAjax(creditDetailsService.insertCreditDetails(creditDetails));
    }

    /**
     * 修改积分明细
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficDetails:edit')")
    @Log(title = "积分明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CreditDetails creditDetails)
    {
        return toAjax(creditDetailsService.updateCreditDetails(creditDetails));
    }

    /**
     * 删除积分明细
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficDetails:remove')")
    @Log(title = "积分明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(creditDetailsService.deleteCreditDetailsByIds(ids));
    }

    /**
     * 获取流量积分使用明细
     */
    @GetMapping("/getCreditDetailsByCreditId/{id}")
    public AjaxResult getCreditDetailsByCreditId(@PathVariable Long id){
        return success(creditDetailsService.getCreditDetailsByCreditId(id));
    }

}
