package com.ruoyi.traffic.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.traffic.domain.MultiplierConfig;
import com.ruoyi.traffic.service.IMultiplierConfigService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 倍率配置Controller
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@RestController
@RequestMapping("/traffic/trafficMultiplierConfig")
public class MultiplierConfigController extends BaseController
{
    @Resource
    private IMultiplierConfigService multiplierConfigService;

    /**
     * 查询倍率配置列表
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficMultiplierConfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(MultiplierConfig multiplierConfig)
    {
        startPage();
        List<MultiplierConfig> list = multiplierConfigService.selectMultiplierConfigList(multiplierConfig);
        return getDataTable(list);
    }

    /**
     * 导出倍率配置列表
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficMultiplierConfig:export')")
    @Log(title = "倍率配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MultiplierConfig multiplierConfig)
    {
        List<MultiplierConfig> list = multiplierConfigService.selectMultiplierConfigList(multiplierConfig);
        ExcelUtil<MultiplierConfig> util = new ExcelUtil<MultiplierConfig>(MultiplierConfig.class);
        util.exportExcel(response, list, "倍率配置数据");
    }

    /**
     * 获取倍率配置详细信息
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficMultiplierConfig:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(multiplierConfigService.selectMultiplierConfigById(id));
    }

    /**
     * 新增倍率配置
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficMultiplierConfig:add')")
    @Log(title = "倍率配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MultiplierConfig multiplierConfig)
    {
        return toAjax(multiplierConfigService.insertMultiplierConfig(multiplierConfig));
    }

    /**
     * 修改倍率配置
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficMultiplierConfig:edit')")
    @Log(title = "倍率配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MultiplierConfig multiplierConfig)
    {
        return toAjax(multiplierConfigService.updateMultiplierConfig(multiplierConfig));
    }

    /**
     * 删除倍率配置
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficMultiplierConfig:remove')")
    @Log(title = "倍率配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(multiplierConfigService.deleteMultiplierConfigByIds(ids));
    }
}
