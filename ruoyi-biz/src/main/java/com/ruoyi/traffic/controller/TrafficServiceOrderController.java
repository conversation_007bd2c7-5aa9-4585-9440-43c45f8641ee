package com.ruoyi.traffic.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.traffic.domain.TrafficServiceOrder;
import com.ruoyi.traffic.service.ITrafficServiceOrderService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 流量服务订单Controller
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@RestController
@RequestMapping("/traffic/trafficOrder")
public class TrafficServiceOrderController extends BaseController
{
    @Resource
    private ITrafficServiceOrderService trafficServiceOrderService;

    /**
     * 查询流量服务订单列表
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficOrder:list')")
    @GetMapping("/list")
    public TableDataInfo list(TrafficServiceOrder trafficServiceOrder)
    {
        startPage();
        List<TrafficServiceOrder> list = trafficServiceOrderService.selectTrafficServiceOrderList(trafficServiceOrder);
        return getDataTable(list);
    }

    /**
     * 导出流量服务订单列表
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficOrder:export')")
    @Log(title = "流量服务订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TrafficServiceOrder trafficServiceOrder)
    {
        List<TrafficServiceOrder> list = trafficServiceOrderService.selectTrafficServiceOrderList(trafficServiceOrder);
        ExcelUtil<TrafficServiceOrder> util = new ExcelUtil<TrafficServiceOrder>(TrafficServiceOrder.class);
        util.exportExcel(response, list, "流量服务订单数据");
    }

    /**
     * 获取流量服务订单详细信息
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficOrder:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(trafficServiceOrderService.selectTrafficServiceOrderById(id));
    }

    /**
     * 新增流量服务订单
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficOrder:add')")
    @Log(title = "流量服务订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TrafficServiceOrder trafficServiceOrder)
    {
        return toAjax(trafficServiceOrderService.insertTrafficServiceOrder(trafficServiceOrder));
    }

    /**
     * 修改流量服务订单
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficOrder:edit')")
    @Log(title = "流量服务订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TrafficServiceOrder trafficServiceOrder)
    {
        return toAjax(trafficServiceOrderService.updateTrafficServiceOrder(trafficServiceOrder));
    }

    /**
     * 删除流量服务订单
     */
    //@PreAuthorize("@ss.hasPermi('traffic:trafficOrder:remove')")
    @Log(title = "流量服务订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(trafficServiceOrderService.deleteTrafficServiceOrderByIds(ids));
    }
}
