package com.ruoyi.traffic.mapper;

import java.util.List;
import com.ruoyi.traffic.domain.CreditDetails;

/**
 * 积分明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-17
 */
public interface CreditDetailsMapper 
{
    /**
     * 查询积分明细
     * 
     * @param id 积分明细主键
     * @return 积分明细
     */
    public CreditDetails selectCreditDetailsById(Long id);

    /**
     * 查询积分明细列表
     * 
     * @param creditDetails 积分明细
     * @return 积分明细集合
     */
    public List<CreditDetails> selectCreditDetailsList(CreditDetails creditDetails);

    /**
     * 新增积分明细
     * 
     * @param creditDetails 积分明细
     * @return 结果
     */
    public int insertCreditDetails(CreditDetails creditDetails);

    /**
     * 修改积分明细
     * 
     * @param creditDetails 积分明细
     * @return 结果
     */
    public int updateCreditDetails(CreditDetails creditDetails);

    /**
     * 删除积分明细
     * 
     * @param id 积分明细主键
     * @return 结果
     */
    public int deleteCreditDetailsById(Long id);

    /**
     * 批量删除积分明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCreditDetailsByIds(Long[] ids);
}
