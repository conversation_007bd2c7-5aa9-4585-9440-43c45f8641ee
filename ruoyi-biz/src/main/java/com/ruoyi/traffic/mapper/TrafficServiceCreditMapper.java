package com.ruoyi.traffic.mapper;

import java.util.List;
import com.ruoyi.traffic.domain.TrafficServiceCredit;
import org.apache.ibatis.annotations.Param;

/**
 * 流量服务积分Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-17
 */
public interface TrafficServiceCreditMapper 
{
    /**
     * 查询流量服务积分
     * 
     * @param id 流量服务积分主键
     * @return 流量服务积分
     */
    public TrafficServiceCredit selectTrafficServiceCreditById(Long id);

    /**
     * 查询流量服务积分列表
     * 
     * @param trafficServiceCredit 流量服务积分
     * @return 流量服务积分集合
     */
    public List<TrafficServiceCredit> selectTrafficServiceCreditList(TrafficServiceCredit trafficServiceCredit);

    /**
     * 新增流量服务积分
     * 
     * @param trafficServiceCredit 流量服务积分
     * @return 结果
     */
    public int insertTrafficServiceCredit(TrafficServiceCredit trafficServiceCredit);

    /**
     * 修改流量服务积分
     * 
     * @param trafficServiceCredit 流量服务积分
     * @return 结果
     */
    public int updateTrafficServiceCredit(TrafficServiceCredit trafficServiceCredit);

    /**
     * 删除流量服务积分
     * 
     * @param id 流量服务积分主键
     * @return 结果
     */
    public int deleteTrafficServiceCreditById(Long id);

    /**
     * 批量删除流量服务积分
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTrafficServiceCreditByIds(Long[] ids);

    /**
     * 查询透支状态的积分记录
     *
     * @param creditType 积分类型
     * @return 透支状态的积分记录列表
     */
    List<TrafficServiceCredit> findOverdraftCredits(@Param("creditType") String creditType);

    /**
     * 批量更新流量服务积分
     *
     * @param creditList 流量服务积分列表
     * @return 结果
     */
    public int batchUpdateTrafficServiceCredit(List<TrafficServiceCredit> creditList);
}
