package com.ruoyi.traffic.component;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.ruoyi.biz.enums.EAiStatus;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.common.constant.ConfigConstants;
import com.ruoyi.jzbot.domain.JzRoom;
import com.ruoyi.jzbot.domain.JzContact;
import com.ruoyi.jzbot.service.IJzRoomService;
import com.ruoyi.jzbot.service.IJzContactService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.traffic.enums.CreditTypeEnum;
import com.ruoyi.traffic.service.ITrafficServiceCreditService;
import com.ruoyi.traffic.service.ITrafficServiceOrderService;
import com.ruoyi.traffic.service.ITrafficTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.traffic.domain.TrafficServiceOrder;
import com.ruoyi.traffic.domain.TrafficServiceCredit;
import com.ruoyi.traffic.service.impl.TrafficOrderProcessServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Collections;

/**
 * 流量服务组件
 * <AUTHOR>
 */
@Component
@Slf4j
public class TrafficComponent {

    @Resource
    private ITrafficServiceCreditService trafficServiceCreditService;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private IJzRoomService jzRoomService;

    @Resource
    private IJzContactService jzContactService;

    @Resource
    private ITrafficTaskService trafficTaskService;

    @Resource
    private ITrafficServiceOrderService trafficServiceOrderService;

    @Resource
    private TrafficOrderProcessServiceImpl trafficOrderProcessService;

    /**
     * 处理AI服务包扣减
     */
    public void handleAIServicePackage(String refId, String creditType, boolean enable) {
        if (enable) {
            trafficTaskService.processTrafficTask(refId, CreditTypeEnum.getByCode(creditType));
        } else {
            trafficServiceCreditService.disableAICreditService(refId, creditType, true);
        }
    }

    /**
     * 处理群聊流量服务定时任务
     */
    public void processGroupTrafficTask() {
        if (!isOpenAiTrafficSwitch()) {
            log.info("AI流量计算未开启，不处理群聊流量服务定时任务");
            return;
        }

        // 处理所有活跃群的积分记录初始化或续期
        int pageSize = 100;
        int pageNum = 1;
        while (true) {
            PageHelper.startPage(pageNum, pageSize, false);
            List<JzRoom> rooms = jzRoomService.selectActiveRooms(EAiStatus.OPEN.getCode());
            if (CollectionUtils.isEmpty(rooms)) {
                log.info("没有需要处理的群聊");
                break;
            }

            for (JzRoom room : rooms) {
                try {
                    trafficTaskService.processTrafficTask(room.getChatId(), CreditTypeEnum.GROUP);
                } catch (Exception e) {
                    log.error("处理群聊流量服务定时任务异常，群chatID：{}", room.getChatId(), e);
                }
            }
            pageNum++;
        }
    }

    /**
     * 处理用户流量服务定时任务
     */
    public void processUserTrafficTask() {
        if (!isOpenAiTrafficSwitch()) {
            log.warn("AI流量计算未开启，暂不处理");
            return;
        }

        int pageSize = 100;
        int pageNum = 1;
        while (true) {
            PageHelper.startPage(pageNum, pageSize, false);
            // 查询所有活跃用户列表
            List<JzContact> activeContacts = jzContactService.selectJzContactList(new JzContact(){{
                setDeleted(EBoolean.NO.getCode());
                setStatus(EBoolean.YES.getCode());
            }});

            if (CollectionUtils.isEmpty(activeContacts)) {
                log.info("没有需要处理的活跃用户");
                break;
            }

            for (JzContact contact : activeContacts) {
                try {
                    trafficTaskService.processTrafficTask(contact.getChatId(), CreditTypeEnum.USER);
                } catch (Exception e) {
                    log.error("处理用户流量服务定时任务异常，用户chatID：{}", contact.getChatId(), e);
                }
            }

            pageNum++;
        }
    }

    /**
     * 是否开启AI流量计算开关
     */
    private Boolean isOpenAiTrafficSwitch() {
        return sysConfigService.isOpen(ConfigConstants.AI_TRAFFIC_SWITCH);
    }

    /**
     * 处理群聊类型订单
     */
    public void processGroupOrder(TrafficServiceOrder order) {
        try {
            // 1. 处理透支记录
            List<TrafficServiceCredit> overdraftCredits = trafficServiceCreditService.findOverdraftCredits(CreditTypeEnum.GROUP.getCode());
            if (!CollectionUtil.isEmpty(overdraftCredits)) {
                log.info("发现群聊透支记录，数量: {}", overdraftCredits.size());
                trafficOrderProcessService.processOverdraftCreditByOrder(order, overdraftCredits);
            }

            // 2. 重新查询订单状态
            TrafficServiceOrder latestOrder = trafficServiceOrderService.selectTrafficServiceOrderById(order.getId());
            if (latestOrder != null && latestOrder.getAvailablePackage() > 0) {
                // 3. 处理群聊AI服务开启
                trafficOrderProcessService.processGroupAIService(latestOrder);
            }
        } catch (Exception e) {
            log.error("处理群聊订单失败, orderId: {}", order.getId(), e);
            throw e;
        }
    }

    /**
     * 处理用户类型订单
     */
    public void processUserOrder(TrafficServiceOrder order) {
        try {
            if (order != null && order.getAvailablePackage() > 0) {
                //  处理用户AI服务开启
                trafficOrderProcessService.processUserAIService(order);
            }
        } catch (Exception e) {
            log.error("处理用户订单失败, orderId: {}", order.getId(), e);
        }
    }
}
