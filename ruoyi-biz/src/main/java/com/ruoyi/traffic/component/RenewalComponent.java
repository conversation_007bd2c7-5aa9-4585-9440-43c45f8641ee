package com.ruoyi.traffic.component;

import com.ruoyi.common.constant.ConfigConstants;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.traffic.constant.TrafficConstants;
import com.ruoyi.traffic.domain.TrafficServiceCredit;
import com.ruoyi.traffic.domain.TrafficServiceOrder;
import com.ruoyi.traffic.enums.CreditStatusEnum;
import com.ruoyi.traffic.enums.CreditTypeEnum;
import com.ruoyi.traffic.enums.OrderStatusEnum;
import com.ruoyi.traffic.enums.OverDraftStatusEnum;
import com.ruoyi.traffic.service.ITrafficServiceCreditService;
import com.ruoyi.traffic.service.ITrafficServiceOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 续期组件
 * 处理用户和群聊的续期逻辑
 * <AUTHOR>
 */
@Slf4j
@Component
public class RenewalComponent {

    @Resource
    private ITrafficServiceOrderService trafficServiceOrderService;

    @Resource
    private ITrafficServiceCreditService trafficServiceCreditService;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private TrafficBasicComponent trafficBasicComponent;

    /**
     * 处理续期
     * 需要事务，因为涉及到多个数据库操作：
     * 1. 更新订单状态
     * 2. 关闭旧的积分记录
     * 3. 创建新的积分记录
     *
     * @return 续期后的新积分记录，如果续期失败返回null
     */
    @Transactional(rollbackFor = Exception.class)
    public TrafficServiceCredit handleRenewal(String chatId, TrafficServiceCredit oldCredit) {
        CreditTypeEnum creditType = CreditTypeEnum.getByCode(oldCredit.getCreditType());
        log.info("开始处理{}续期. chatId: {}, creditId: {}",
            creditType.getDesc(), chatId, oldCredit.getId());

        // 1. 获取可用订单
        TrafficServiceOrder order = trafficServiceOrderService.findAvailableOrder(creditType.getCode());

        // 2. 处理订单不可用的情况
        if (!isOrderAvailable(order)) {
            return handleUnavailableOrder(chatId, creditType, oldCredit);
        }

        // 3. 锁定并再次验证订单
        order = trafficServiceOrderService.selectTrafficServiceOrderByIdForUpdate(order.getId());
        if (!isOrderAvailable(order)) {
            log.warn("服务包已被使用，续期失败. chatId: {}", chatId);
            return null;
        }

        // 4. 扣减服务包
        trafficBasicComponent.deductServicePackage(order);

        // 5. 关闭旧的积分记录
        trafficBasicComponent.closeOldCredit(oldCredit);

        // 6. 创建新的积分记录
        long creditLimit;
        if (creditType == CreditTypeEnum.USER) {
            String configValue = sysConfigService.selectConfigByKey(ConfigConstants.USER_AI_TRAFFIC_LIMIT);
            creditLimit = configValue != null ?
                Long.parseLong(configValue) :
                TrafficConstants.DEFAULT_USER_CREDIT_LIMIT;
        } else {
            creditLimit = TrafficConstants.UNLIMITED_CREDIT;
        }

        // 设置有效期
        Calendar calendar = Calendar.getInstance();
        if (creditType == CreditTypeEnum.USER) {
            calendar.add(Calendar.YEAR, 1);  // 用户积分有效期1年
        } else {
            calendar.add(Calendar.DAY_OF_MONTH, 1);  // 群积分有效期1天
        }

        return trafficBasicComponent.createNewCredit(
            order.getId(),
            chatId,
            creditType.getCode(),
            false,
            creditLimit,
            calendar.getTime()
        );
    }

    /**
     * 处理订单不可用的情况
     */
    private TrafficServiceCredit handleUnavailableOrder(String chatId, CreditTypeEnum creditType, TrafficServiceCredit credit) {
        // 如果是群聊且允许透支，创建透支记录
        if (creditType == CreditTypeEnum.GROUP && sysConfigService.isOpen(ConfigConstants.OVERDRAFT_SWITCH)) {
            String overdraftLimitConfig = sysConfigService.selectConfigByKey(ConfigConstants.OVERDRAFT_LIMIT);

            // 设置有效期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            // 配置为空表示无限制，为0表示不允许透支
            if (StringUtils.isBlank(overdraftLimitConfig)) {
                log.info("群聊透支无上限限制，允许创建透支记录. chatId: {}", chatId);
                trafficBasicComponent.closeOldCredit(credit);
                return trafficBasicComponent.createNewCredit(
                    TrafficConstants.OVERDRAFT_ORDER_ID,
                    chatId,
                    creditType.getCode(),
                    true,
                    TrafficConstants.UNLIMITED_CREDIT,
                    calendar.getTime()
                );
            } else {
                int limit = Integer.parseInt(overdraftLimitConfig);
                if (limit == 0) {
                    log.warn("群聊透支上限设置为0，不允许透支. chatId: {}", chatId);
                    return null;
                }

                // 查询当前透支记录数量
                TrafficServiceCredit query = new TrafficServiceCredit();
                query.setOverdraftStatus(OverDraftStatusEnum.IN_OVERDRAFT.getCode());
                List<TrafficServiceCredit> overdraftRecords = trafficServiceCreditService
                        .selectTrafficServiceCreditList(query);

                if (overdraftRecords != null && overdraftRecords.size() >= limit) {
                    log.warn("群聊透支已达系统上限: {}. chatId: {}", limit, chatId);
                    return null;
                }

                log.info("群聊允许透支，创建透支记录. chatId: {}", chatId);
                trafficBasicComponent.closeOldCredit(credit);
                return trafficBasicComponent.createNewCredit(
                    TrafficConstants.OVERDRAFT_ORDER_ID,
                    chatId,
                    creditType.getCode(),
                    true,
                    TrafficConstants.UNLIMITED_CREDIT,
                    calendar.getTime()
                );
            }
        }

        // 其他情况，返回null表示续期失败
        log.warn("无可用服务包，续期失败. chatId: {}", chatId);
        return null;
    }

    /**
     * 检查订单是否可用
     */
    private boolean isOrderAvailable(TrafficServiceOrder order) {
        return order != null &&
               order.getAvailablePackage() > 0 &&
               !OrderStatusEnum.COMPLETED.getCode().equals(order.getOrderStatus());
    }
}
