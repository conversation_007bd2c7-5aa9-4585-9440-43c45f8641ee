package com.ruoyi.traffic.component;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.jzbot.domain.JzChatMessage;
import com.ruoyi.traffic.domain.TrafficServiceCredit;
import com.ruoyi.traffic.domain.res.BatchCreditResult;
import com.ruoyi.traffic.domain.res.CreditUsageResult;
import com.ruoyi.traffic.enums.CreditTypeEnum;
import com.ruoyi.traffic.enums.CreditUsageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 消息积分处理组件
 * 负责处理单条消息的积分扣减和记录
 * <AUTHOR>
 */
@Slf4j
@Component
public class MessageCreditProcessor {

    @Resource
    private CreditComponent creditComponent;

    @Resource
    private TrafficBasicComponent trafficBasicComponent;

    /**
     * 处理消息积分
     * 1. 获取或创建积分记录
     * 2. 检查积分余额
     * 3. 执行积分扣减或记录明细
     */
    @Transactional(rollbackFor = Exception.class)
    public void processMessageCredits(JzChatMessage message, CreditUsageResult creditUsage, String creditType) {
        String chatId = message.getChatId();
        boolean isUser = CreditTypeEnum.USER.getCode().equals(creditType);

        try {
            // 获取或创建积分记录
            TrafficServiceCredit credit = creditComponent.getOrCreateCredit(chatId, creditType);
            if (credit == null) {
                return;
            }
            if (isUser) {
                // 检查积分余额并处理
                credit = creditComponent.checkAndHandleCreditBalance(chatId, credit);
            }

            // 执行积分扣减
            trafficBasicComponent.deductCredits(credit, creditUsage);

            // 保存积分明细
            trafficBasicComponent.saveCreditDetails(credit.getId(), message, creditUsage);

            if (isUser) {
                log.info("用户积分扣减成功. chatId: {}, messageId: {}, 消耗积分: {}, 剩余积分: {}",
                    chatId, message.getMessageId(), creditUsage.getConsumeCredits(), credit.getRemainingCredits());
            }
        } catch (Exception e) {
            log.error("处理{}积分{}失败. chatId: {}, messageId: {}, error: {}",
                isUser ? "用户" : "群聊",
                isUser ? "扣减" : "记录",
                chatId, message.getMessageId(), e.getMessage());
            throw e;
        }
    }

    /**
     * 批量处理消息积分
     * @param messages 消息列表
     * @param batchCredit 批量积分结果
     * @param creditType 积分类型
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleBatchMessageCredits(List<JzChatMessage> messages, BatchCreditResult batchCredit, String creditType) {
        String chatId = messages.get(0).getChatId();
        boolean isUser = CreditTypeEnum.USER.getCode().equals(creditType);

        // 获取或创建积分记录
        TrafficServiceCredit credit = creditComponent.getOrCreateCredit(chatId, creditType);
        if (credit == null) {
            return;
        }

        if (isUser) {
            // 检查积分余额并处理
            credit = creditComponent.checkAndHandleCreditBalance(chatId, credit);
            if (credit == null) {
                return;
            }
        }

        // 创建一个统一的积分使用结果
        CreditUsageResult batchUsage = new CreditUsageResult();
        batchUsage.setConsumeCredits(batchCredit.getTotalCredits());
        // 执行积分扣减
        trafficBasicComponent.deductCredits(credit, batchUsage);

        // 批量保存积分明细
        for (JzChatMessage message : messages) {
            CreditUsageResult individualUsage = creditComponent.calculateConsumeCredits(message);
            if (individualUsage.getConsumeCredits().compareTo(BigDecimal.ZERO) > 0) {
                trafficBasicComponent.saveCreditDetails(credit.getId(), message, individualUsage);
            }
        }

        log.info("批量积分处理完成. chatId: {}, 消息数量: {}, 总消耗积分: {}, 剩余积分: {}, 消息类型统计: {}",
                chatId,
                messages.size(),
                batchCredit.getTotalCredits(),
                credit.getRemainingCredits(),
                formatUsageTypeCounts(batchCredit.getUsageTypeCounts()));
    }

    /**
     * 格式化使用类型统计信息
     */
    private String formatUsageTypeCounts(Map<CreditUsageTypeEnum, Integer> usageTypeCounts) {
        if (usageTypeCounts == null || usageTypeCounts.isEmpty()) {
            return "无";
        }
        
        return usageTypeCounts.entrySet().stream()
            .map(entry -> entry.getKey().getDesc() + ":" + entry.getValue() + "条")
            .collect(Collectors.joining(", "));
    }
}
