package com.ruoyi.traffic.component;

import com.ruoyi.traffic.domain.TrafficServiceCredit;
import com.ruoyi.traffic.domain.TrafficServiceOrder;
import com.ruoyi.traffic.domain.CreditDetails;
import com.ruoyi.traffic.enums.CreditStatusEnum;
import com.ruoyi.traffic.enums.CreditTypeEnum;
import com.ruoyi.traffic.enums.OrderStatusEnum;
import com.ruoyi.traffic.enums.OverDraftStatusEnum;
import com.ruoyi.traffic.service.ITrafficServiceCreditService;
import com.ruoyi.traffic.service.ITrafficServiceOrderService;
import com.ruoyi.traffic.service.ICreditDetailsService;
import com.ruoyi.traffic.constant.TrafficConstants;
import com.ruoyi.traffic.domain.res.CreditUsageResult;
import com.ruoyi.jzbot.domain.JzChatMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 流量服务通用组件
 * <AUTHOR>
 * @date 2024/12/30
 */
@Component
@Slf4j
public class TrafficBasicComponent {

    @Resource
    private ITrafficServiceOrderService trafficServiceOrderService;

    @Resource
    private ITrafficServiceCreditService trafficServiceCreditService;

    @Resource
    private ICreditDetailsService creditDetailsService;

    /**
     * 创建新的积分记录
     */
    @Transactional(rollbackFor = Exception.class)
    public TrafficServiceCredit createNewCredit(Long orderId, String refId, String creditType,
        boolean isOverdraft, Long availableCredit, Date expiryDate) {
        TrafficServiceCredit credit = new TrafficServiceCredit();
        credit.setOrderId(orderId);
        credit.setRefId(refId);
        credit.setCreditType(creditType);
        credit.setStatus(CreditStatusEnum.IN_USE.getCode());
        credit.setUsedCredit(TrafficConstants.INIT_USED_TRAFFIC);
        credit.setOverdraftStatus(isOverdraft ?
            OverDraftStatusEnum.IN_OVERDRAFT.getCode() :
            OverDraftStatusEnum.UNDERWRITTEN.getCode());
        credit.setExpiryDate(expiryDate);
        credit.setAvailableCredit(availableCredit);
        credit.setRemainingCredits(availableCredit);

        trafficServiceCreditService.insertTrafficServiceCredit(credit);
        return credit;
    }

    /**
     * 扣减服务包
     */
    @Transactional(rollbackFor = Exception.class)
    public void deductServicePackage(TrafficServiceOrder order) {
        log.info("开始扣减服务包. orderId: {}, 当前可用: {}, 已使用: {}",
                order.getId(), order.getAvailablePackage(), order.getUsedPackage());
        order.setAvailablePackage(order.getAvailablePackage() - 1);
        order.setUsedPackage(order.getUsedPackage() + 1);
        order.setOrderStatus(order.getAvailablePackage() == 0 ?
            OrderStatusEnum.COMPLETED.getCode() :
            OrderStatusEnum.IN_USE.getCode());
        trafficServiceOrderService.updateTrafficServiceOrder(order);
    }

    /**
     * 保存积分明细
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCreditDetails(Long creditId, JzChatMessage message, CreditUsageResult creditUsage) {
        CreditDetails details = new CreditDetails();
        details.setCreditId(creditId);
        details.setMessageId(message.getMessageId());
        details.setUsageType(creditUsage.getUsageType().getCode());
        details.setUsageAmount(creditUsage.getConsumeCredits().longValue());
        details.setCreateTime(message.getTime());
        details.setUpdateTime(new Date());

        creditDetailsService.insertCreditDetails(details);
    }

    /**
     * 执行积分扣减
     */
    @Transactional(rollbackFor = Exception.class)
    public void deductCredits(TrafficServiceCredit credit, CreditUsageResult creditUsage) {
        BigDecimal newUsedCredit = new BigDecimal(credit.getUsedCredit())
            .add(creditUsage.getConsumeCredits());
        credit.setUsedCredit(newUsedCredit.longValue());

        if (credit.getCreditType().equals(CreditTypeEnum.USER.getCode())) {
            long remainingCredits = credit.getAvailableCredit() - credit.getUsedCredit();
            boolean isOverdraft = remainingCredits <= 0;
            credit.setRemainingCredits(isOverdraft ? 0 : remainingCredits);
            if (isOverdraft) {
                credit.setStatus(CreditStatusEnum.COMPLETED.getCode());
            }
        }
        trafficServiceCreditService.updateTrafficServiceCredit(credit);
    }

    /**
     * 关闭旧的积分记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void closeOldCredit(TrafficServiceCredit credit) {
        credit.setStatus(CreditStatusEnum.EXPIRED.getCode());
        credit.setUpdateTime(new Date());
        trafficServiceCreditService.updateTrafficServiceCredit(credit);
    }
}
