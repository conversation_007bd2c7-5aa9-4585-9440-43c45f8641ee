package com.ruoyi.traffic.component;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.biz.enums.EAiStatus;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.common.constant.ConfigConstants;
import com.ruoyi.common.enums.EJzRecviceMessageType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.jzbot.domain.JzChatMessage;
import com.ruoyi.jzbot.domain.JzContact;
import com.ruoyi.jzbot.domain.JzRoom;
import com.ruoyi.jzbot.service.IJzContactService;
import com.ruoyi.jzbot.service.IJzRoomService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.traffic.constant.TrafficConstants;
import com.ruoyi.traffic.domain.*;
import com.ruoyi.traffic.domain.res.BatchCreditResult;
import com.ruoyi.traffic.domain.res.CreditUsageResult;
import com.ruoyi.traffic.enums.*;
import com.ruoyi.traffic.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.AllArgsConstructor;
import com.ruoyi.common.constant.Constants;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 积分计算和扣减组件
 * 负责处理AI对话中的积分计算、扣减和记录等相关操作
 */
@Slf4j
@Component
public class CreditComponent {

    @Resource
    private ITrafficServiceCreditService trafficServiceCreditService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private IMultiplierConfigService multiplierConfigService;
    @Resource
    private RenewalComponent renewalComponent;
    @Resource
    private IJzContactService jzContactService;
    @Resource
    private IJzRoomService jzRoomService;

    @Resource
    private ITrafficServiceOrderService trafficServiceOrderService;

    @Resource
    private TrafficBasicComponent trafficBasicComponent;

    @Resource
    private MessageCreditProcessor messageCreditProcessor;

    private static final String MINI_PROGRAM_APPID_KEY = "appid";

    /**
     * 处理消息积分记录
     * 1. 校验消息有效性
     * 2. 计算消耗积分
     * 3. 处理积分扣减或记录
     */
    public void handleMessageCredits(JzChatMessage message) {
        // 校验消息有效性
        if (isValidMessage(message)) {
            log.warn("消息校验未通过，跳过积分记录. messageId: {}", message.getMessageId());
            return;
        }

        try {
            // 计算消耗积分
            CreditUsageResult creditUsage = calculateConsumeCredits(message);
            if (isValidCreditUsage(creditUsage)) {
                log.warn("暂无需要扣减积分，跳过记录. messageId: {}", message.getMessageId());
                return;
            }

            // 根据消息来源确定积分类型
            String creditType = Constants.PEER_TO_PEER.equals(message.getMessageSource()) ?
                CreditTypeEnum.USER.getCode() : CreditTypeEnum.GROUP.getCode();

            // 处理积分
            messageCreditProcessor.processMessageCredits(message, creditUsage, creditType);
        } catch (Exception e) {
            log.error("处理消息积分记录异常. messageId: {}, chatId: {}, error: {}",
                message.getMessageId(), message.getChatId(), e.getMessage(), e);
            throw e;
        }
    }


    /**
     * 获取或创建积分记录
     */
    public TrafficServiceCredit getOrCreateCredit(String chatId, String creditType) {
        // 获取当前积分记录
        TrafficServiceCredit credit = trafficServiceCreditService.findActiveCredit(chatId, creditType);
        boolean isUser = CreditTypeEnum.USER.getCode().equals(creditType);

        if (credit == null) {
            log.info("{}不存在积分记录，开始创建新的积分记录. chatId: {}",
                isUser ? "用户" : "群聊", chatId);

            // 获取可用订单
            TrafficServiceOrder order = trafficServiceOrderService.findAvailableOrder(creditType);

            // 如果有可用订单且有剩余服务包
            if (order != null && order.getAvailablePackage() > 0
                && !OrderStatusEnum.COMPLETED.getCode().equals(order.getOrderStatus())) {

                order = trafficServiceOrderService.selectTrafficServiceOrderByIdForUpdate(order.getId());
                if (order.getAvailablePackage() > 0) {
                    // 扣减服务包
                    trafficBasicComponent.deductServicePackage(order);
                    // 创建新的积分记录
                    credit = createNewCredit(order.getId(), chatId, creditType, false);
                }
            }

            // 群聊特殊处理：允许透支
            // 是否需要设置透支上限？
            if (credit == null && !isUser) {
                boolean isOverdraftEnabled = sysConfigService.isOpen(ConfigConstants.OVERDRAFT_SWITCH);
                if (isOverdraftEnabled) {
                    // 获取透支上限配置
                    String overdraftLimitConfig = sysConfigService.selectConfigByKey(ConfigConstants.OVERDRAFT_LIMIT);

                    // 配置为空表示无限制，为0表示不允许透支
                    if (StringUtils.isBlank(overdraftLimitConfig)) {
                        // 无限制，直接创建透支记录
                        log.info("群聊透支无上限限制，允许创建透支记录. chatId: {}", chatId);
                        credit = createNewCredit(TrafficConstants.OVERDRAFT_ORDER_ID, chatId, creditType, true);
                    } else {
                        int limit = Integer.parseInt(overdraftLimitConfig);
                        if (limit == 0) {
                            log.warn("群聊透支上限设置为0，不允许透支. chatId: {}", chatId);
                            return null;
                        }

                        // 查询当前透支记录数量
                        TrafficServiceCredit query = new TrafficServiceCredit();
                        query.setOverdraftStatus(OverDraftStatusEnum.IN_OVERDRAFT.getCode());
                        List<TrafficServiceCredit> overdraftRecords = trafficServiceCreditService
                            .selectTrafficServiceCreditList(query);

                        if (overdraftRecords != null && overdraftRecords.size() >= limit) {
                            log.warn("群聊透支已达系统上限: {}. chatId: {}", limit, chatId);
                            return null;
                        }

                        // 未达到上限，创建透支记录
                        log.info("群聊允许透支，开始创建透支记录. chatId: {}", chatId);
                        credit = createNewCredit(TrafficConstants.OVERDRAFT_ORDER_ID, chatId, creditType, true);
                    }
                } else {
                    log.info("群聊透支功能未开启. 关闭群AI服务功能. chatId: {}", chatId);
                    disableAIService(chatId, creditType);
                    return null;
                }
            }

            if (credit == null) {
                // 无可用服务包，关闭AI服务
                log.warn("无可用服务包，关闭{}AI服务功能. chatId: {}",
                    isUser ? "用户" : "群聊", chatId);
                disableAIService(chatId, creditType);
            }
        }

        return credit;
    }

    /**
     * 创建新的积分记录
     */
    public TrafficServiceCredit createNewCredit(Long orderId, String chatId, String creditType, boolean isOverdraft) {
        // 设置有效期
        Calendar calendar = Calendar.getInstance();
        if (CreditTypeEnum.USER.getCode().equals(creditType)) {
            calendar.add(Calendar.YEAR, 1);  // 用户积分有效期1年
        } else {
            calendar.add(Calendar.DAY_OF_MONTH, 1);  // 群积分有效期1天
        }

        // 设置积分额度
        long limit;
        if (CreditTypeEnum.USER.getCode().equals(creditType)) {
            String configValue = sysConfigService.selectConfigByKey(ConfigConstants.USER_AI_TRAFFIC_LIMIT);
            limit = configValue != null ?
                Long.parseLong(configValue) :
                TrafficConstants.DEFAULT_USER_CREDIT_LIMIT;
        } else {
            limit = TrafficConstants.UNLIMITED_CREDIT;
        }

        // 调用基础组件创建记录
        TrafficServiceCredit credit = trafficBasicComponent.createNewCredit(
            orderId,
            chatId,
            creditType,
            isOverdraft,
            limit,
            calendar.getTime()
        );

        log.info("新积分记录创建完成. chatId: {}, creditType: {}, isOverdraft: {}",
            chatId, creditType, isOverdraft);
        return credit;
    }

    /**
     * 关闭AI服务
     * @param chatId 聊天ID
     * @param creditType 积分类型(用户/群聊)
     */
    public void disableAIService(String chatId, String creditType) {
        try {
            if (CreditTypeEnum.USER.getCode().equals(creditType)) {
                disableUserAIService(chatId);
            } else if (CreditTypeEnum.GROUP.getCode().equals(creditType)) {
                disableGroupAIService(chatId);
            }
        } catch (Exception e) {
            log.error("关闭AI服务失败. chatId: {}, creditType: {}", chatId, creditType, e);
        }
    }

    /**
     * 计算消息消耗的积分
     * 根据消息类型(文本/图片)计算所需扣减的积分
     *
     * @param jzChatMessage AI聊天消息
     * @return 积分使用结果，包含消耗积分数和使用类型
     */
    public CreditUsageResult calculateConsumeCredits(JzChatMessage jzChatMessage) {
        CreditUsageResult result = new CreditUsageResult();

        try {
            // 处理文本消息
            if (jzChatMessage.getMessageType() == EJzRecviceMessageType.TEXT.getCode()) {
                result = calculateTextMessageCredits(jzChatMessage);
            }
            else if (jzChatMessage.getMessageType() == EJzRecviceMessageType.IMAGE.getCode()) {
                result = calculateImageMessageCredits(jzChatMessage);
            } else if (jzChatMessage.getMessageType() == EJzRecviceMessageType.MINI_PROGRAM.getCode()) {
                result = calculateMiniProgramMessageCredits(jzChatMessage);
            }
        } catch (Exception e) {
            log.error("计算消耗积分异常. messageId: {}", jzChatMessage.getMessageId(), e);
        }
        return result;
    }

    /**
     * 计算小程序消息积分
     * @param message AI聊天消息
     * @return 积分使用结果
     */
    private CreditUsageResult calculateMiniProgramMessageCredits(JzChatMessage message) {
        CreditUsageResult result = new CreditUsageResult();
        JSONObject messageObj = JSON.parseObject(message.getMessage());
        String miniProgramId = messageObj.getString(MINI_PROGRAM_APPID_KEY);

        if (StringUtils.isNotBlank(miniProgramId)) {
            MultiplierConfig config = multiplierConfigService.getActiveConfig(CreditUsageTypeEnum.MINI_PROGRAM.getCode());
            if (config != null && config.getMultiplier() != null) {
                result.setConsumeCredits(config.getMultiplier());
                result.setUsageType(CreditUsageTypeEnum.MINI_PROGRAM);

                log.info("小程序消息积分计算: appId: {}, 倍率: {}, 消耗积分: {}",
                    miniProgramId, config.getMultiplier(), result.getConsumeCredits());
            } else {
                log.warn("小程序消息积分配置未找到或无效. appId: {}", miniProgramId);
            }
        } else {
            log.warn("小程序消息缺少appId参数. messageId: {}", message.getMessageId());
        }

        return result;
    }

    /**
     * 计算文本消息积分
     * 根据文本长度和倍率配置计算积分，支持普通对话和导购场景
     *
     * @param message AI聊天消息
     * @return 积分使用结果
     */
    private CreditUsageResult calculateTextMessageCredits(JzChatMessage message) {
        CreditUsageResult result = new CreditUsageResult();
        JSONObject messageObj = JSON.parseObject(message.getMessage());
        String text = messageObj.getString("text");

        if (StringUtils.isBlank(text)) {
            return result;
        }

        // 判断是否是导购消息
        boolean isShoppingAdvice = isShoppingAdviceMessage(message);
        String operationType = isShoppingAdvice ?
            CreditUsageTypeEnum.SHOPPING.getCode() :
            CreditUsageTypeEnum.PRIVATE.getCode();

        MultiplierConfig config = multiplierConfigService.getActiveConfig(operationType);
        if (config != null && config.getMultiplier() != null && config.getBaseUnit() != null) {
            result = calculateCredits(text.length(), config, isShoppingAdvice);
        }

        return result;
    }

    /**
     * 计算图片消息积分
     * 根据图片消息的倍率配置计算积分
     *
     * @param message AI聊天消息
     * @return 积分使用结果
     */
    private CreditUsageResult calculateImageMessageCredits(JzChatMessage message) {
        CreditUsageResult result = new CreditUsageResult();
        JSONObject messageObj = JSON.parseObject(message.getMessage());
        String imageUrl = messageObj.getString("imageUrl");

        if (StringUtils.isNotBlank(imageUrl)) {
            MultiplierConfig config = multiplierConfigService.getActiveConfig(CreditUsageTypeEnum.IMAGE.getCode());
            if (config != null && config.getMultiplier() != null) {
                result.setConsumeCredits(config.getMultiplier());
                result.setUsageType(CreditUsageTypeEnum.IMAGE);
            }
        }

        return result;
    }

    /**
     * 检查积分余额并处理
     * @param chatId 聊天ID
     * @param credit 用户积分信息
     * @return 续期后的积分信息
     */
    public TrafficServiceCredit checkAndHandleCreditBalance(String chatId, TrafficServiceCredit credit) {
        if (credit.getRemainingCredits() <= 0) {
            String creditType = credit.getCreditType();
            log.info("积分余额不足，开始处理. chatId: {}, creditType: {}", chatId, creditType);

            // 检查是否开启自动续期
            if (!sysConfigService.isOpen(ConfigConstants.AI_AUTO_RENEWAL)) {
                log.info("自动续期未开启，准备关闭AI服务. chatId: {}", chatId);
                disableAIService(chatId, creditType);
                return null;
            }

            // 处理自动续期
            TrafficServiceCredit renewedCredit = renewalComponent.handleRenewal(chatId, credit);
            if (renewedCredit == null) {
                log.error("自动续期失败. chatId: {}, creditType: {}", chatId, creditType);
                disableAIService(chatId, creditType);
            }

            log.info("自动续期处理完成. chatId: {}, newCreditId: {}", chatId, renewedCredit.getId());
            return renewedCredit;
        }

        return credit;
    }

    /**
     * 关闭用户AI服务
     * 停用指定用户的AI服务
     *
     * @param chatId 用户聊天ID
     */
    public void disableUserAIService(String chatId) {
        JzContact contact = new JzContact();
        contact.setChatId(chatId);
        contact.setStatus(EAiStatus.EXPIRED.getCode());
        jzContactService.updateJzContact(contact);
        log.info("用户AI服务已关闭. chatId: {}", chatId);
    }

    /**
     * 关闭群聊AI服务
     * 停用指定群聊的AI服务
     *
     * @param chatId 群聊ID
     */
    public void disableGroupAIService(String chatId) {
        JzRoom room = new JzRoom();
        room.setChatId(chatId);
        room.setStatus(EAiStatus.EXPIRED.getCode());
        jzRoomService.updateJzRoom(room);
        log.info("群聊AI服务已关闭. chatId: {}", chatId);
    }

    /**
     * 校验消息有效性
     * 检查消息是否为AI发送的有效消息
     *
     * @param message AI聊天消息
     * @return true:无效消息 false:有效消息
     */
    private boolean isValidMessage(JzChatMessage message) {
        return message == null || message.getIsSelf() == null || message.getIsSelf() != 1;
    }

    /**
     * 判断是否为单聊消息
     * @param messageSource 消息来源
     * @return true:单聊消息 false:群聊消息
     */
    private boolean isPrivateChat(String messageSource) {
        return "1".equals(messageSource);
    }

    /**
     * 校验积分使用结果是否有效，是否需要扣减积分
     * @param creditUsage 积分使用结果
     * @return true:无效结果 false:有效结果
     */
    private boolean isValidCreditUsage(CreditUsageResult creditUsage) {
        return creditUsage == null || creditUsage.getConsumeCredits().compareTo(BigDecimal.ZERO) <= 0;
    }

    /**
     * 判断是否为导购消息
     * 根据消息额外信息判断是否为导购场景
     *
     * @param message AI聊天消息
     * @return true:导购消息 false:普通消息
     */
    private boolean isShoppingAdviceMessage(JzChatMessage message) {
        if (StringUtils.isNotBlank(message.getAiExtra())) {
            JSONObject aiExtra = JSON.parseObject(message.getAiExtra());
            return "shopping_advice".equals(aiExtra.getString("intentType"));
        }
        return false;
    }

    /**
     * 处理积分计算异常
     * 记录积分计算过程中的异常信息
     *
     * @param message AI聊天消息
     * @param e 异常信息
     */
    private void handleCreditProcessingError(JzChatMessage message, Exception e) {
        log.error("积分计算扣减异常. messageId: {}, chatId: {}, error: {}",
                message.getMessageId(), message.getChatId(), e.getMessage(), e);
    }

    /**
     * 计算积分
     * 根据文本长度、倍率配置和消息类型计算实际消耗的积分
     *
     * @param textLength 文本长度
     * @param config 倍率配置
     * @param isShoppingAdvice 是否为导购消息
     * @return 积分使用结果
     */
    private CreditUsageResult calculateCredits(int textLength, MultiplierConfig config, boolean isShoppingAdvice) {
        CreditUsageResult result = new CreditUsageResult();

        // 计算积分消耗：字数 * 倍率 / 基础单位
        BigDecimal credits = new BigDecimal(textLength)
            .multiply(config.getMultiplier())
            .divide(new BigDecimal(config.getBaseUnit()), 2, BigDecimal.ROUND_DOWN);

        result.setConsumeCredits(credits);
        result.setUsageType(isShoppingAdvice ? CreditUsageTypeEnum.SHOPPING : CreditUsageTypeEnum.PRIVATE);

        log.info("文本消息积分计算: 类型: {}, 字数: {}, 倍率: {}, 基础单位: {}, 消耗积分: {}",
                isShoppingAdvice ? "导购" : "普通",
                textLength,
                config.getMultiplier(),
                config.getBaseUnit(),
                credits);

        return result;
    }

    /**
     * 批量处理消息积分
     * @param messages 消息列表
     * @return 处理结果
     */
    public BatchProcessResult batchProcessMessageCredits(List<JzChatMessage> messages) {
        // 按消息来源分组
        Map<String, List<JzChatMessage>> messageGroups = messages.stream()
            .collect(Collectors.groupingBy(JzChatMessage::getMessageSource));

        // 存储满足条件的消息列表
        List<JzChatMessage> validMessages = new ArrayList<>();

        int successCount = 0;
        int failCount = 0;

        // 处理单聊消息
        List<JzChatMessage> peerMessages = messageGroups.get(Constants.PEER_TO_PEER);
        if (CollectionUtils.isNotEmpty(peerMessages)) {
            // 获取所有单聊用户ID
            List<String> chatIds = peerMessages.stream()
                    .map(JzChatMessage::getChatId)
                    .distinct()
                    .collect(Collectors.toList());

            // 批量查询用户状态
            List<JzContact> contacts = jzContactService.selectJzConcatListByChatIds(chatIds);
            Map<String, JzContact> contactMap = contacts.stream()
                .collect(Collectors.toMap(JzContact::getChatId, c -> c));

            // 筛选开启AI的消息
            for (JzChatMessage message : peerMessages) {
                JzContact contact = contactMap.get(message.getChatId());
                // 未删除且开启AI
                if (contact != null && EBoolean.YES.getCode().equals(contact.getStatus()) && !EBoolean.YES.getCode().equals(contact.getDeleted())) {
                    validMessages.add(message);
                }
            }
        }

        // 处理群聊消息
        List<JzChatMessage> groupMessages = messageGroups.get(EBoolean.NO.getCode());
        if (CollectionUtils.isNotEmpty(groupMessages)) {
            // 获取所有群聊ID
            List<String> roomIds = groupMessages.stream()
                .map(JzChatMessage::getChatId)
                    .distinct()
                .collect(Collectors.toList());

            // 批量查询群聊状态
            List<JzRoom> rooms = jzRoomService.selectJzRoomListByChatIds(roomIds);
            Map<String, JzRoom> roomMap = rooms.stream()
                .collect(Collectors.toMap(JzRoom::getChatId, r -> r));

            // 筛选开启AI的消息
            for (JzChatMessage message : groupMessages) {
                JzRoom room = roomMap.get(message.getChatId());
                if (room != null && EBoolean.YES.getCode().equals(room.getStatus()) && !EBoolean.YES.getCode().equals(room.getDeleted())) {
                    validMessages.add(message);
                }
            }
        }

        // 批量处理有效消息
        for (JzChatMessage message : validMessages) {
            try {
                handleMessageCredits(message);
                successCount++;
            } catch (Exception e) {
                failCount++;
                log.error("处理消息积分失败. messageId: {}, error: {}",
                    message.getMessageId(), e.getMessage());
            }
        }

        return new BatchProcessResult(successCount, failCount, validMessages);
    }

    /**
     * 批量处理单个聊天的消息积分
     * @param messages 同一聊天的消息列表
     * @return 处理结果
     */
    public BatchProcessResult batchProcessChatMessageCredits(List<JzChatMessage> messages) {
        if (CollectionUtils.isEmpty(messages)) {
            return new BatchProcessResult(0, 0, Collections.emptyList());
        }

        // 所有消息应该来自同一个聊天
        String chatId = messages.get(0).getChatId();
        String messageSource = messages.get(0).getMessageSource();

        // 确定积分类型
        String creditType = Constants.PEER_TO_PEER.equals(messageSource) ?
            CreditTypeEnum.USER.getCode() :
            CreditTypeEnum.GROUP.getCode();

        // 检查AI是否开启
        boolean isAiEnabled = checkAiStatus(chatId, creditType);
        if (!isAiEnabled) {
            log.info("AI未开启，跳过处理. chatId: {}, creditType: {}", chatId, creditType);
            return new BatchProcessResult(0, 0, Collections.emptyList());
        }

        int successCount = 0;
        int failCount = 0;
        List<JzChatMessage> processedMessages = new ArrayList<>();

        try {
            // 计算批量消息总积分
            BatchCreditResult batchCredit = calculateBatchMessageCredits(messages);
            if (batchCredit.getTotalCredits().compareTo(BigDecimal.ZERO) <= 0) {
                return new BatchProcessResult(0, 0, Collections.emptyList());
            }

            // 批量处理积分
            messageCreditProcessor.handleBatchMessageCredits(messages, batchCredit, creditType);

            successCount = messages.size();
            processedMessages.addAll(messages);

        } catch (Exception e) {
            failCount = messages.size();
            log.error("批量处理消息积分失败. chatId: {}, error: {}", chatId, e.getMessage(), e);
        }

        return new BatchProcessResult(successCount, failCount, processedMessages);
    }

    /**
     * 检查AI状态
     */
    private boolean checkAiStatus(String chatId, String creditType) {
        if (CreditTypeEnum.USER.getCode().equals(creditType)) {
            JzContact contact = jzContactService.selectJzContactById(chatId);
            return contact != null &&
                   EAiStatus.OPEN.getCode().equals(contact.getStatus()) &&
                   !EBoolean.YES.getCode().equals(contact.getDeleted());
        } else {
            JzRoom room = jzRoomService.selectJzRoomByChatId(chatId);
            return room != null &&
                    EAiStatus.OPEN.getCode().equals(room.getStatus()) &&
                   !EBoolean.YES.getCode().equals(room.getDeleted());
        }
    }

    /**
     * 计算批量消息的总积分消耗
     */
    private BatchCreditResult calculateBatchMessageCredits(List<JzChatMessage> messages) {
        BatchCreditResult result = new BatchCreditResult();
        BigDecimal totalCredits = BigDecimal.ZERO;
        Map<CreditUsageTypeEnum, Integer> usageTypeCounts = new HashMap<>();

        for (JzChatMessage message : messages) {
            CreditUsageResult creditUsage = calculateConsumeCredits(message);
            if (creditUsage.getConsumeCredits().compareTo(BigDecimal.ZERO) > 0) {
                totalCredits = totalCredits.add(creditUsage.getConsumeCredits());
                usageTypeCounts.merge(creditUsage.getUsageType(), 1, Integer::sum);
            }
        }

        result.setTotalCredits(totalCredits);
        result.setUsageTypeCounts(usageTypeCounts);
        return result;
    }

    @Data
    @AllArgsConstructor
    public static class BatchProcessResult {
        /** 处理成功数量 */
        private final int successCount;
        /** 处理失败数量 */
        private final int failCount;
        /** 已处理的消息列表 */
        private final List<JzChatMessage> processedMessages;
    }
}

