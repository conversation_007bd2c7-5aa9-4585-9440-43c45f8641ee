package com.ruoyi.traffic.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Collections;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.jzbot.domain.JzContact;
import com.ruoyi.jzbot.domain.JzRoom;
import com.ruoyi.jzbot.service.IJzContactService;
import com.ruoyi.jzbot.service.IJzRoomService;
import com.ruoyi.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ruoyi.traffic.mapper.TrafficServiceCreditMapper;
import com.ruoyi.traffic.domain.TrafficServiceCredit;
import com.ruoyi.traffic.domain.CreditDetails;
import com.ruoyi.traffic.domain.TrafficServiceOrder;
import com.ruoyi.traffic.domain.res.CreditDetailResult;
import com.ruoyi.traffic.service.ITrafficServiceCreditService;
import com.ruoyi.traffic.service.ICreditDetailsService;
import com.ruoyi.traffic.service.ITrafficServiceOrderService;
import org.apache.commons.collections4.CollectionUtils;
import com.ruoyi.traffic.enums.CreditStatusEnum;
import com.ruoyi.traffic.enums.CreditTypeEnum;
import org.springframework.transaction.annotation.Transactional;

/**
 * 流量服务积分Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@Slf4j
@Service
public class TrafficServiceCreditServiceImpl implements ITrafficServiceCreditService
{
    @Resource
    private TrafficServiceCreditMapper trafficServiceCreditMapper;

    @Resource
    private IJzContactService jzContactService;

    @Resource
    private IJzRoomService jzRoomService;

    @Resource
    private ICreditDetailsService creditDetailsService;

    @Resource
    private ISysDictDataService sysDictDataService;

    @Resource
    private ITrafficServiceOrderService trafficServiceOrderService;

    /**
     * 查询流量服务积分
     *
     * @param id 流量服务积分主键
     * @return 流量服务积分
     */
    @Override
    public TrafficServiceCredit selectTrafficServiceCreditById(Long id)
    {
        return trafficServiceCreditMapper.selectTrafficServiceCreditById(id);
    }

    /**
     * 查询流量服务积分列表
     *
     * @param trafficServiceCredit 流量服务积分
     * @return 流量服务积分
     */
    @Override
    public List<TrafficServiceCredit> selectTrafficServiceCreditList(TrafficServiceCredit trafficServiceCredit)
    {
        List<TrafficServiceCredit> list = trafficServiceCreditMapper.selectTrafficServiceCreditList(trafficServiceCredit);
        initData(list);
        return list;
    }

    /**
     * 处理数据
     * @param list
     */
    private void initData(List<TrafficServiceCredit> list) {
        if (CollectionUtils.isEmpty(list)) return;
        list.parallelStream().forEach(item -> {
            if (CreditTypeEnum.USER.getCode().equals(item.getCreditType())) {
                // 用户积分
                JzContact jzContact = jzContactService.selectJzContactById(item.getRefId());
                if (jzContact != null) {
                    item.setName(jzContact.getNickname());
                    item.setAvatarUrl(jzContact.getAvatarUrl());
                }
            } else if (CreditTypeEnum.GROUP.getCode().equals(item.getCreditType())) {
                JzRoom jzRoom = jzRoomService.selectJzRoomByChatId(item.getRefId());
                if (jzRoom != null) {
                    item.setName(jzRoom.getTopic());
                    item.setAvatarUrl(jzRoom.getAvatarUrl());
                }
            }
        });

    }

    /**
     * 新增流量服务积分
     *
     * @param trafficServiceCredit 流量服务积分
     * @return 结果
     */
    @Override
    public int insertTrafficServiceCredit(TrafficServiceCredit trafficServiceCredit)
    {
        trafficServiceCredit.setCreateTime(new Date());
        return trafficServiceCreditMapper.insertTrafficServiceCredit(trafficServiceCredit);
    }

    /**
     * 修改流量服务积分
     *
     * @param trafficServiceCredit 流量服务积分
     * @return 结果
     */
    @Override
    public int updateTrafficServiceCredit(TrafficServiceCredit trafficServiceCredit)
    {
        trafficServiceCredit.setUpdateTime(new Date());
        return trafficServiceCreditMapper.updateTrafficServiceCredit(trafficServiceCredit);
    }

    /**
     * 批量删除流量服务积分
     *
     * @param ids 需要删除的流量服务积分主键
     * @return 结果
     */
    @Override
    public int deleteTrafficServiceCreditByIds(Long[] ids)
    {
        return trafficServiceCreditMapper.deleteTrafficServiceCreditByIds(ids);
    }

    /**
     * 删除流量服务积分信息
     *
     * @param id 流量服务积分主键
     * @return 结果
     */
    @Override
    public int deleteTrafficServiceCreditById(Long id)
    {
        return trafficServiceCreditMapper.deleteTrafficServiceCreditById(id);
    }

    @Override
    public TrafficServiceCredit findActiveCredit(String refId, String creditType) {
        TrafficServiceCredit condition = new TrafficServiceCredit();
        condition.setRefId(refId);
        condition.setCreditType(creditType);
        condition.setStatus(CreditStatusEnum.IN_USE.getCode());

        List<TrafficServiceCredit> credits = trafficServiceCreditMapper.selectTrafficServiceCreditList(condition);
        return CollectionUtils.isNotEmpty(credits) ? credits.get(0) : null;
    }

    @Override
    public List<TrafficServiceCredit> findOverdraftCredits(String creditType) {
        return trafficServiceCreditMapper.findOverdraftCredits(creditType);
    }

    @Override
    public List<TrafficServiceCredit> findAllActiveCredits(String refId, String creditType) {
        TrafficServiceCredit condition = new TrafficServiceCredit();
        condition.setRefId(refId);
        condition.setCreditType(creditType);
        condition.setStatus(CreditStatusEnum.IN_USE.getCode());
        return trafficServiceCreditMapper.selectTrafficServiceCreditList(condition);
    }

    @Override
    public void batchUpdateTrafficServiceCredit(List<TrafficServiceCredit> creditList) {
        if (creditList.isEmpty()) return;
        int batchSize = 500;
        for (int i = 0; i < creditList.size(); i += batchSize) {
            List<TrafficServiceCredit> batchList = creditList.subList(i, Math.min(i + batchSize, creditList.size()));
            trafficServiceCreditMapper.batchUpdateTrafficServiceCredit(batchList);
        }
    }

    @Override
    public List<TrafficServiceCredit> findAllActiveGroupCredits() {
        TrafficServiceCredit condition = new TrafficServiceCredit();
        condition.setCreditType(CreditTypeEnum.GROUP.getCode());
        condition.setStatus(CreditStatusEnum.IN_USE.getCode());
        return trafficServiceCreditMapper.selectTrafficServiceCreditList(condition);
    }

    @Override
    public List<TrafficServiceCredit> findAllActiveUserCredits() {
        TrafficServiceCredit condition = new TrafficServiceCredit();
        condition.setCreditType(CreditTypeEnum.USER.getCode());
        condition.setStatus(CreditStatusEnum.IN_USE.getCode());
        return trafficServiceCreditMapper.selectTrafficServiceCreditList(condition);
    }

    @Override
    public JSONObject getTrafficOrderDetailsByOrderId(Long id) {
        JSONObject result = new JSONObject();

        // 获取订单信息
        TrafficServiceOrder order = trafficServiceOrderService.selectTrafficServiceOrderById(id);
        if (order == null) {
            result.put("totalPackage", 0L);
            result.put("usedPackage", 0L);
            result.put("availablePackage", 0L);
            result.put("details", Collections.emptyList());
            return result;
        }

        // 查询指定orderId的所有积分记录
        TrafficServiceCredit condition = new TrafficServiceCredit();
        condition.setOrderId(id);
        List<TrafficServiceCredit> credits = trafficServiceCreditMapper.selectTrafficServiceCreditList(condition);

        if (CollectionUtils.isEmpty(credits)) {
            result.put("totalPackage", order.getTotalPackage());
            result.put("usedPackage", order.getUsedPackage());
            result.put("availablePackage", order.getAvailablePackage());
            result.put("details", Collections.emptyList());
            return result;
        }

        // 查询每个服务包的积分使用明细
        List<CreditDetailResult> details = new ArrayList<>();
        for (TrafficServiceCredit credit : credits) {
            CreditDetails detailCondition = new CreditDetails();
            detailCondition.setCreditId(credit.getId());
            List<CreditDetails> creditDetails = creditDetailsService.selectCreditDetailsList(detailCondition);

            // 按usageType分组并累加usageAmount
            Map<String, Long> usageTypeSum = creditDetails.stream()
                .collect(Collectors.groupingBy(
                    CreditDetails::getUsageType,
                    Collectors.summingLong(CreditDetails::getUsageAmount)
                ));

            // 转换为CreditDetailResult
            usageTypeSum.forEach((usageType, amount) -> {
                CreditDetailResult detail = new CreditDetailResult();
                detail.setUsageType(usageType);
                detail.setUsageTypeName(sysDictDataService.selectDictLabel("credit_usage_type", usageType));
                detail.setAmount(amount);
                details.add(detail);
            });
        }

        // 合并相同usageType的积分
        Map<String, CreditDetailResult> mergedDetails = details.stream()
            .collect(Collectors.groupingBy(
                CreditDetailResult::getUsageType,
                Collectors.reducing(
                    new CreditDetailResult(),
                    (a, b) -> {
                        CreditDetailResult merged = new CreditDetailResult();
                        merged.setUsageType(b.getUsageType());
                        merged.setUsageTypeName(b.getUsageTypeName());
                        merged.setAmount((a.getAmount() != null ? a.getAmount() : 0L) + b.getAmount());
                        return merged;
                    }
                )
            ))
            .values()
            .stream()
            .collect(Collectors.toMap(
                CreditDetailResult::getUsageType,
                credit -> credit
            ));

        // 组装返回结果
        result.put("totalPackage", order.getTotalPackage());
        result.put("usedPackage", order.getUsedPackage());
        result.put("availablePackage", order.getAvailablePackage());
        result.put("details", new ArrayList<>(mergedDetails.values()));

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableAICreditService(String refId, String creditType, boolean isExpired) {
        log.info("开始关闭AI服务订单. refId: {}, creditType: {}", refId, creditType);

        // 查找所有活跃的服务积分记录
        List<TrafficServiceCredit> activeCredits = this.findAllActiveCredits(refId, creditType);

        if (CollectionUtils.isEmpty(activeCredits)) {
            log.info("未找到活跃的服务积分记录. refId: {}, creditType: {}", refId, creditType);
            return;
        }

        try {
            // 批量设置状态和更新时间
            Date now = new Date();
            activeCredits.forEach(credit -> {
                credit.setStatus(isExpired ? CreditStatusEnum.EXPIRED.getCode() : CreditStatusEnum.COMPLETED.getCode());
                credit.setUpdateTime(now);
            });

            // 批量更新
            this.batchUpdateTrafficServiceCredit(activeCredits);
            log.info("成功批量关闭服务积分记录. refId: {}, 处理数量: {}", refId, activeCredits.size());
        } catch (Exception e) {
            log.error("批量关闭服务积分记录失败. refId: {}, creditType: {}", refId, creditType, e);
        }
    }
}
