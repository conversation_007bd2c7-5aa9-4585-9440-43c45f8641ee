package com.ruoyi.traffic.service.impl;

import com.ruoyi.common.constant.ConfigConstants;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.traffic.enums.CreditTypeEnum;
import com.ruoyi.traffic.service.ITrafficCreditService;
import com.ruoyi.traffic.service.ITrafficTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 流量任务服务实现类
 * 负责流量任务的调度和分布式锁控制
 * <AUTHOR>
 */
@Slf4j
@Service
public class TrafficTaskServiceImpl implements ITrafficTaskService {

    @Resource
    private RedisLock redisLock;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private ITrafficCreditService trafficCreditService;

    /**
     * 锁前缀定义
     */
    private static final String USER_LOCK_PREFIX = "traffic_service_user_lock:%s";
    private static final String ROOM_LOCK_PREFIX = "traffic_service_room_lock:%s";

    /**
     * 处理流量任务
     * 负责流量任务的调度和分布式锁控制
     */
    @Override
    public void processTrafficTask(String chatId, CreditTypeEnum creditType) {
        // 是否开启AI流量计算。未开启不做处理
        if (!isOpenAiTrafficSwitch()) {
            log.warn("AI流量计算未开启，暂不处理");
            return;
        }

        String lockKey = getLockKey(chatId, creditType);
        String lockTime = getLockTime();

        // 获取分布式锁
        if (!redisLock.lock(lockKey, lockTime)) {
            log.warn("获取{}锁失败，跳过处理. chatId: {}", creditType.getDesc(), chatId);
            return;
        }

        try {
            trafficCreditService.processActiveCredit(chatId, creditType);
        } catch (Exception e) {
            log.error("处理{}积分记录异常. chatId: {}", creditType.getDesc(), chatId, e);
            throw e;
        } finally {
            redisLock.unlock(lockKey, lockTime);
        }
    }

    /**
     * 获取锁的key
     */
    private String getLockKey(String chatId, CreditTypeEnum creditType) {
        return creditType == CreditTypeEnum.USER ?
            getUserLockKey(chatId) : getRoomLockKey(chatId);
    }

    /**
     * 获取锁的值
     */
    private String getLockTime() {
        return String.valueOf(System.currentTimeMillis() + 30 * 1000);
    }

    /**
     * 获取用户锁的key
     */
    private String getUserLockKey(String refId) {
        return String.format(USER_LOCK_PREFIX, refId);
    }

    /**
     * 获取群锁的key
     */
    private String getRoomLockKey(String refId) {
        return String.format(ROOM_LOCK_PREFIX, refId);
    }

    /**
     * 是否开启AI流量计算开关
     */
    private Boolean isOpenAiTrafficSwitch() {
        return sysConfigService.isOpen(ConfigConstants.AI_TRAFFIC_SWITCH);
    }
}
