package com.ruoyi.traffic.service;

import java.util.List;
import com.ruoyi.traffic.domain.TrafficServiceOrder;

/**
 * 流量服务订单Service接口
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
public interface ITrafficServiceOrderService
{

    /**
     * 查询流量服务订单(加锁)
     * @param id
     * @return
     */
    public TrafficServiceOrder selectTrafficServiceOrderByIdForUpdate(Long id);

    /**
     * 查询流量服务订单
     *
     * @param id 流量服务订单主键
     * @return 流量服务订单
     */
    public TrafficServiceOrder selectTrafficServiceOrderById(Long id);



    /**
     * 查询流量服务订单列表
     *
     * @param trafficServiceOrder 流量服务订单
     * @return 流量服务订单集合
     */
    public List<TrafficServiceOrder> selectTrafficServiceOrderList(TrafficServiceOrder trafficServiceOrder);

    /**
     * 新增流量服务订单
     *
     * @param trafficServiceOrder 流量服务订单
     * @return 结果
     */
    public int insertTrafficServiceOrder(TrafficServiceOrder trafficServiceOrder);

    /**
     * 修改流量服务订单
     *
     * @param trafficServiceOrder 流量服务订单
     * @return 结果
     */
    public int updateTrafficServiceOrder(TrafficServiceOrder trafficServiceOrder);

    /**
     * 批量删除流量服务订单
     *
     * @param ids 需要删除的流量服务订单主键集合
     * @return 结果
     */
    public int deleteTrafficServiceOrderByIds(Long[] ids);

    /**
     * 删除流量服务订单信息
     *
     * @param id 流量服务订单主键
     * @return 结果
     */
    public int deleteTrafficServiceOrderById(Long id);

    /**
     * 查找可用的服务订单
     * @param orderType 订单类型
     * @return 可用的服务订单
     */
    TrafficServiceOrder findAvailableOrder(String orderType);

    List<TrafficServiceOrder> findAvailableOrders();


}
