package com.ruoyi.traffic.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.ruoyi.traffic.mapper.MultiplierConfigMapper;
import com.ruoyi.traffic.domain.MultiplierConfig;
import com.ruoyi.traffic.service.IMultiplierConfigService;

/**
 * 倍率配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@Service
public class MultiplierConfigServiceImpl implements IMultiplierConfigService
{
    @Resource
    private MultiplierConfigMapper multiplierConfigMapper;

    /**
     * 查询倍率配置
     *
     * @param id 倍率配置主键
     * @return 倍率配置
     */
    @Override
    public MultiplierConfig selectMultiplierConfigById(Long id)
    {
        return multiplierConfigMapper.selectMultiplierConfigById(id);
    }

    /**
     * 查询倍率配置列表
     *
     * @param multiplierConfig 倍率配置
     * @return 倍率配置
     */
    @Override
    public List<MultiplierConfig> selectMultiplierConfigList(MultiplierConfig multiplierConfig)
    {
        return multiplierConfigMapper.selectMultiplierConfigList(multiplierConfig);
    }

    /**
     * 新增倍率配置
     *
     * @param multiplierConfig 倍率配置
     * @return 结果
     */
    @Override
    public int insertMultiplierConfig(MultiplierConfig multiplierConfig)
    {
        multiplierConfig.setCreateTime(DateUtils.getNowDate());
        return multiplierConfigMapper.insertMultiplierConfig(multiplierConfig);
    }

    /**
     * 修改倍率配置
     *
     * @param multiplierConfig 倍率配置
     * @return 结果
     */
    @Override
    public int updateMultiplierConfig(MultiplierConfig multiplierConfig)
    {
        multiplierConfig.setUpdateTime(DateUtils.getNowDate());
        return multiplierConfigMapper.updateMultiplierConfig(multiplierConfig);
    }

    /**
     * 批量删除倍率配置
     *
     * @param ids 需要删除的倍率配置主键
     * @return 结果
     */
    @Override
    public int deleteMultiplierConfigByIds(Long[] ids)
    {
        return multiplierConfigMapper.deleteMultiplierConfigByIds(ids);
    }

    /**
     * 删除倍率配置信息
     *
     * @param id 倍率配置主键
     * @return 结果
     */
    @Override
    public int deleteMultiplierConfigById(Long id)
    {
        return multiplierConfigMapper.deleteMultiplierConfigById(id);
    }

    /**
     * 获取活跃的倍率配置
     *
     * @param operationType 操作类型（1-群发、2-AI问答，3-AI导购，4-图片生成）
     * @return 倍率配置
     */
    @Override
    public MultiplierConfig getActiveConfig(String operationType) {
        return multiplierConfigMapper.selectActiveConfig(operationType);
    }
}
