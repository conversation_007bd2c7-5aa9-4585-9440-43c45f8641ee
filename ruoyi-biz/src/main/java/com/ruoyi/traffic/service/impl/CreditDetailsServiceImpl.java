package com.ruoyi.traffic.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.math.BigDecimal;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import javax.annotation.Resource;

import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.traffic.domain.res.CreditDetailResult;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.traffic.mapper.CreditDetailsMapper;
import com.ruoyi.traffic.domain.CreditDetails;
import com.ruoyi.traffic.service.ICreditDetailsService;
import com.ruoyi.traffic.service.ITrafficServiceCreditService;
import com.ruoyi.traffic.domain.TrafficServiceCredit;

/**
 * 积分明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@Service
public class CreditDetailsServiceImpl implements ICreditDetailsService
{
    @Resource
    private CreditDetailsMapper creditDetailsMapper;

    @Resource
    private ITrafficServiceCreditService trafficServiceCreditService;

    @Resource
    private ISysDictDataService sysDictDataService;

    /**
     * 查询积分明细
     *
     * @param id 积分明细主键
     * @return 积分明细
     */
    @Override
    public CreditDetails selectCreditDetailsById(Long id)
    {
        return creditDetailsMapper.selectCreditDetailsById(id);
    }

    /**
     * 查询积分明细列表
     *
     * @param creditDetails 积分明细
     * @return 积分明细
     */
    @Override
    public List<CreditDetails> selectCreditDetailsList(CreditDetails creditDetails)
    {
        return creditDetailsMapper.selectCreditDetailsList(creditDetails);
    }

    /**
     * 新增积分明细
     *
     * @param creditDetails 积分明细
     * @return 结果
     */
    @Override
    public int insertCreditDetails(CreditDetails creditDetails)
    {
        if (creditDetails.getCreateTime() == null) {
            creditDetails.setCreateTime(DateUtils.getNowDate());
        }
        return creditDetailsMapper.insertCreditDetails(creditDetails);
    }

    /**
     * 修改积分明细
     *
     * @param creditDetails 积分明细
     * @return 结果
     */
    @Override
    public int updateCreditDetails(CreditDetails creditDetails)
    {
        creditDetails.setUpdateTime(DateUtils.getNowDate());
        return creditDetailsMapper.updateCreditDetails(creditDetails);
    }

    /**
     * 批量删除积分明细
     *
     * @param ids 需要删除的积分明细主键
     * @return 结果
     */
    @Override
    public int deleteCreditDetailsByIds(Long[] ids)
    {
        return creditDetailsMapper.deleteCreditDetailsByIds(ids);
    }

    /**
     * 删除积分明细信息
     *
     * @param id 积分明细主键
     * @return 结果
     */
    @Override
    public int deleteCreditDetailsById(Long id)
    {
        return creditDetailsMapper.deleteCreditDetailsById(id);
    }

    @Override
    public JSONObject getCreditDetailsByCreditId(Long id) {
        JSONObject result = new JSONObject();

        // 查询指定creditId的所有积分明细
        List<CreditDetails> creditDetails = creditDetailsMapper.selectCreditDetailsList(new CreditDetails() {{
            setCreditId(id);
        }});

        // 获取总积分（从积分主表获取）
        TrafficServiceCredit credit = trafficServiceCreditService.selectTrafficServiceCreditById(id);
        Long totalAmount = credit != null ? credit.getAvailableCredit() : 0L;


        if (CollectionUtils.isEmpty(creditDetails)) {
            result.put("usedAmount", 0L);
            result.put("totalAmount", totalAmount);
            result.put("details", Collections.emptyList());
            return result;
        }

        // 使用Map按usageType分组并累加usageAmount
        Map<String, Long> usageTypeSum = creditDetails.stream()
            .collect(Collectors.groupingBy(
                CreditDetails::getUsageType,
                Collectors.summingLong(CreditDetails::getUsageAmount)
            ));

        // 计算已使用的总积分
        Long usedAmount = creditDetails.stream()
            .mapToLong(CreditDetails::getUsageAmount)
            .sum();



        // 转换为CreditDetailResult列表
        List<CreditDetailResult> details = usageTypeSum.entrySet().stream()
            .map(entry -> {
                CreditDetailResult detail = new CreditDetailResult();
                detail.setUsageType(entry.getKey());
                detail.setUsageTypeName(sysDictDataService.selectDictLabel("credit_usage_type", entry.getKey()));
                detail.setAmount(entry.getValue());
                return detail;
            })
            .collect(Collectors.toList());

        // 组装返回结果
        result.put("usedAmount", usedAmount);
        result.put("totalAmount", totalAmount);
        result.put("details", details);

        return result;
    }
}
