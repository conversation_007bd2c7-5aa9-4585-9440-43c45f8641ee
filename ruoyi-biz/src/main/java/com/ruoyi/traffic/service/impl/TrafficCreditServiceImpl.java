package com.ruoyi.traffic.service.impl;

import com.ruoyi.common.constant.ConfigConstants;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.traffic.component.CreditComponent;
import com.ruoyi.traffic.component.RenewalComponent;
import com.ruoyi.traffic.component.TrafficBasicComponent;
import com.ruoyi.traffic.constant.TrafficConstants;
import com.ruoyi.traffic.domain.TrafficServiceCredit;
import com.ruoyi.traffic.domain.TrafficServiceOrder;
import com.ruoyi.traffic.enums.CreditStatusEnum;
import com.ruoyi.traffic.enums.CreditTypeEnum;
import com.ruoyi.traffic.enums.OrderStatusEnum;
import com.ruoyi.traffic.enums.OverDraftStatusEnum;
import com.ruoyi.traffic.service.ITrafficCreditService;
import com.ruoyi.traffic.service.ITrafficServiceCreditService;
import com.ruoyi.traffic.service.ITrafficServiceOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 流量积分服务实现类
 * 负责处理积分记录的创建、更新和续期等核心业务逻辑
 */
@Slf4j
@Service
public class TrafficCreditServiceImpl implements ITrafficCreditService {

    @Resource
    private ITrafficServiceOrderService trafficServiceOrderService;

    @Resource
    private ITrafficServiceCreditService trafficServiceCreditService;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private CreditComponent creditComponent;

    @Resource
    private RenewalComponent renewalComponent;

    @Resource
    private TrafficBasicComponent trafficBasicComponent;

    /**
     * 处理活跃实体的积分记录
     * 包含事务管理，确保积分记录的创建和更新的原子性
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processActiveCredit(String chatId, CreditTypeEnum creditType) {
        log.info("开始处理{}积分记录. chatId: {}", creditType.getDesc(), chatId);

        // 查询当前积分记录
        TrafficServiceCredit credit = trafficServiceCreditService.findActiveCredit(
            chatId, creditType.getCode()
        );

        if (credit == null) {
            log.info("{}不存在积分记录，开始创建新的积分记录. chatId: {}", creditType.getDesc(), chatId);
            createInitialCredit(chatId, creditType);
        } else {
            log.info("{}存在积分记录，检查是否需要续期. chatId: {}, creditId: {}",
                creditType.getDesc(), chatId, credit.getId());
            checkAndRenewCredit(chatId, credit, creditType);
        }
    }

    /**
     * 创建初始积分记录
     */
    public void createInitialCredit(String chatId, CreditTypeEnum creditType) {
        // 1. 获取可用订单
        TrafficServiceOrder order = trafficServiceOrderService.findAvailableOrder(creditType.getCode());

        // 2. 如果有可用订单且有剩余服务包，使用该订单
        if (isOrderAvailable(order)) {
            order = trafficServiceOrderService.selectTrafficServiceOrderByIdForUpdate(order.getId());
            if (isOrderAvailable(order)) {
                // 扣减服务包
                trafficBasicComponent.deductServicePackage(order);
                // 创建新的积分记录
                createNewCredit(order.getId(), chatId, creditType, false);
                log.info("{}积分记录创建完成. chatId: {}", creditType.getDesc(), chatId);
                return;
            }
        }

        // 3. 如果是群聊且允许透支，创建透支记录
        // 是否需要设置透支上限？
        if (creditType == CreditTypeEnum.GROUP && sysConfigService.isOpen(ConfigConstants.OVERDRAFT_SWITCH)) {
            String overdraftLimitConfig = sysConfigService.selectConfigByKey(ConfigConstants.OVERDRAFT_LIMIT);
            // 配置为空表示无限制，为0表示不允许透支
            if (StringUtils.isBlank(overdraftLimitConfig)) {
                log.info("群聊透支无上限限制，允许创建透支记录. chatId: {}", chatId);
                createNewCredit(TrafficConstants.OVERDRAFT_ORDER_ID, chatId, creditType, true);
                return;
            } else {
                int limit = Integer.parseInt(overdraftLimitConfig);
                if (limit == 0) {
                    log.warn("群聊透支上限设置为0，不允许透支. chatId: {}", chatId);
                    return;
                }

                // 查询当前透支记录数量
                TrafficServiceCredit query = new TrafficServiceCredit();
                query.setOverdraftStatus(OverDraftStatusEnum.IN_OVERDRAFT.getCode());
                List<TrafficServiceCredit> overdraftRecords = trafficServiceCreditService
                        .selectTrafficServiceCreditList(query);

                if (overdraftRecords != null && overdraftRecords.size() >= limit) {
                    log.warn("群聊透支已达系统上限: {}. chatId: {}", limit, chatId);
                    return;
                }

                log.info("群聊允许透支，创建透支记录. chatId: {}", chatId);
                createNewCredit(TrafficConstants.OVERDRAFT_ORDER_ID, chatId, creditType, true);
                return;
            }
        }

        // 4. 无可用服务包且不允许透支，关闭AI服务
        log.warn("无可用服务包，关闭{}AI服务功能. chatId: {}", creditType.getDesc(), chatId);
        disableServices(chatId, creditType, true);
    }

    /**
     * 检查并续期积分
     */
    public void checkAndRenewCredit(String chatId, TrafficServiceCredit credit, CreditTypeEnum creditType) {
        // 只判断年月日。
        Date expiryDate = credit.getExpiryDate();
        LocalDate expiryLocalDate = expiryDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate currentDate = LocalDate.now();
        if (expiryLocalDate.isBefore(currentDate)) {
            log.info("{}积分记录已过期，准备自动续期. chatId: {}, creditId: {}",
                creditType.getDesc(), chatId, credit.getId());

            // 检查是否开启自动续期
            if (!sysConfigService.isOpen(ConfigConstants.AI_AUTO_RENEWAL)) {
                log.info("自动续期未开启，关闭AI服务. chatId: {}", chatId);
                disableServices(chatId, creditType, true);
                return;
            }

            // 尝试续期
            TrafficServiceCredit renewedCredit = renewalComponent.handleRenewal(chatId, credit);
            if (renewedCredit == null) {
                log.info("续期失败，关闭AI服务. chatId: {}", chatId);
                disableServices(chatId, creditType, true);
            }
        } else {
            log.info("{}积分记录未过期，无需续期. chatId: {}, creditId: {}",
                creditType.getDesc(), chatId, credit.getId());
        }
    }

    /**
     * 关闭相关服务
     */
    public void disableServices(String chatId, CreditTypeEnum creditType, boolean isExpired) {
        trafficServiceCreditService.disableAICreditService(chatId, creditType.getCode(), isExpired);
        creditComponent.disableAIService(chatId, creditType.getCode());
    }

    /**
     * 检查订单是否可用
     */
    private boolean isOrderAvailable(TrafficServiceOrder order) {
        return order != null &&
               order.getAvailablePackage() > 0 &&
               !OrderStatusEnum.COMPLETED.getCode().equals(order.getOrderStatus());
    }

    /**
     * 创建新的积分记录
     */
    private void createNewCredit(Long orderId, String refId, CreditTypeEnum creditType, boolean isOverdraft) {
        // 设置有效期
        Calendar calendar = Calendar.getInstance();
        if (creditType == CreditTypeEnum.USER) {
            calendar.add(Calendar.YEAR, 1);  // 用户积分有效期1年
        } else {
            calendar.add(Calendar.DAY_OF_MONTH, 1);  // 群积分有效期1天
        }

        // 设置积分额度
        long limit;
        if (creditType == CreditTypeEnum.USER) {
            String configValue = sysConfigService.selectConfigByKey(ConfigConstants.USER_AI_TRAFFIC_LIMIT);
            limit = configValue != null ?
                Long.parseLong(configValue) :
                TrafficConstants.DEFAULT_USER_CREDIT_LIMIT;
        } else {
            limit = TrafficConstants.UNLIMITED_CREDIT;
        }

        // 保存记录
        trafficBasicComponent.createNewCredit(orderId, refId, creditType.getCode(), isOverdraft, limit, calendar.getTime());
        log.info("新积分记录创建完成. refId: {}, creditType: {}, isOverdraft: {}",
            refId, creditType.getDesc(), isOverdraft);
    }
}
