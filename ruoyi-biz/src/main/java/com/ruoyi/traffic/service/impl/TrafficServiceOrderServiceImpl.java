package com.ruoyi.traffic.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Comparator;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.traffic.enums.OrderStatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import com.ruoyi.common.utils.DateUtils;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.ruoyi.traffic.mapper.TrafficServiceOrderMapper;
import com.ruoyi.traffic.domain.TrafficServiceOrder;
import com.ruoyi.traffic.service.ITrafficServiceOrderService;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.traffic.domain.TrafficServiceCredit;
import com.ruoyi.traffic.service.ITrafficServiceCreditService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.traffic.enums.OverDraftStatusEnum;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 流量服务订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@Service
public class TrafficServiceOrderServiceImpl implements ITrafficServiceOrderService
{
    @Resource
    private TrafficServiceOrderMapper trafficServiceOrderMapper;

    @Resource
    private ITrafficServiceCreditService trafficServiceCreditService;

    private static final Logger log = LoggerFactory.getLogger(TrafficServiceOrderServiceImpl.class);

    /**
     * 查询流量服务订单(加锁)
     *
     * @param id 流量服务订单主键
     * @return 流量服务订单
     */
    @Override
    public TrafficServiceOrder selectTrafficServiceOrderByIdForUpdate(Long id)
    {
        return trafficServiceOrderMapper.selectTrafficServiceOrderByIdForUpdate(id);
    }


    /**
     * 查询流量服务订单
     *
     * @param id 流量服务订单主键
     * @return 流量服务订单
     */
    @Override
    public TrafficServiceOrder selectTrafficServiceOrderById(Long id)
    {
        return trafficServiceOrderMapper.selectTrafficServiceOrderById(id);
    }

    /**
     * 查询流量服务订单列表
     *
     * @param trafficServiceOrder 流量服务订单
     * @return 流量服务订单
     */
    @Override
    public List<TrafficServiceOrder> selectTrafficServiceOrderList(TrafficServiceOrder trafficServiceOrder)
    {
        return trafficServiceOrderMapper.selectTrafficServiceOrderList(trafficServiceOrder);
    }

    /**
     * 新增流量服务订单
     *
     * @param trafficServiceOrder 流量服务订单
     * @return 结果
     */
    @Override
    public int insertTrafficServiceOrder(TrafficServiceOrder trafficServiceOrder) {
        trafficServiceOrder.setCreateTime(DateUtils.getNowDate());
        trafficServiceOrder.setAvailablePackage(trafficServiceOrder.getTotalPackage());
        return trafficServiceOrderMapper.insertTrafficServiceOrder(trafficServiceOrder);
    }

    /**
     * 处理透支订单
     * @param trafficServiceOrder
     * @param overdraftCredits
     * @param deductCount
     */
    private void processOverdraftCredit(TrafficServiceOrder trafficServiceOrder, List<TrafficServiceCredit> overdraftCredits, long deductCount) {
        Date now = new Date();
        overdraftCredits.forEach(credit -> {
            credit.setOrderId(trafficServiceOrder.getId());
            credit.setOverdraftStatus(OverDraftStatusEnum.CLEARED.getCode());
            credit.setUpdateTime(now);
        });
        trafficServiceCreditService.batchUpdateTrafficServiceCredit(overdraftCredits);
        log.info("透支订单处理完成. 处理数量: {}", deductCount);
    }

    /**
     * 修改流量服务订单
     *
     * @param trafficServiceOrder 流量服务订单
     * @return 结果
     */
    @Override
    public int updateTrafficServiceOrder(TrafficServiceOrder trafficServiceOrder)
    {
        trafficServiceOrder.setUpdateTime(DateUtils.getNowDate());
        trafficServiceOrder.setUpdateBy(getUsername());
        return trafficServiceOrderMapper.updateTrafficServiceOrder(trafficServiceOrder);
    }

    /**
     * 批量删除流量服务订单
     *
     * @param ids 需要删除的流量服务订单主键
     * @return 结果
     */
    @Override
    public int deleteTrafficServiceOrderByIds(Long[] ids)
    {
        return trafficServiceOrderMapper.deleteTrafficServiceOrderByIds(ids);
    }

    /**
     * 删除流量服务订单信息
     *
     * @param id 流量服务订单主键
     * @return 结果
     */
    @Override
    public int deleteTrafficServiceOrderById(Long id)
    {
        return trafficServiceOrderMapper.deleteTrafficServiceOrderById(id);
    }

    @Override
    public TrafficServiceOrder findAvailableOrder(String orderType) {
        TrafficServiceOrder condition = new TrafficServiceOrder();
        if (StringUtils.isNotBlank(orderType)) {
            condition.setOrderType(orderType);
        }
        condition.setOrderStatus(2L); // 已完成状态
        condition.setAvailablePackage(0L); // 可用包数量大于0

        // 查询所有非已完成状态且有可用包的订单，按创建时间升序排序
        List<TrafficServiceOrder> orders = trafficServiceOrderMapper.selectAvailableOrders(condition);
        return CollectionUtils.isNotEmpty(orders) ? orders.get(0) : null;
    }

    @Override
    public List<TrafficServiceOrder> findAvailableOrders() {
        TrafficServiceOrder condition = new TrafficServiceOrder();
//        condition.setOrderType(orderType);
        condition.setOrderStatus(2L); // 已完成状态
        condition.setAvailablePackage(0L); // 可用包数量大于0
        return trafficServiceOrderMapper.selectAvailableOrders(condition);
    }
}
