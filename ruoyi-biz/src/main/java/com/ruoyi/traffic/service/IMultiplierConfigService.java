package com.ruoyi.traffic.service;

import java.util.List;
import com.ruoyi.traffic.domain.MultiplierConfig;

/**
 * 倍率配置Service接口
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
public interface IMultiplierConfigService {

    /**
     * 查询倍率配置
     *
     * @param id 倍率配置主键
     * @return 倍率配置
     */
    public MultiplierConfig selectMultiplierConfigById(Long id);

    /**
     * 查询倍率配置列表
     *
     * @param multiplierConfig 倍率配置
     * @return 倍率配置集合
     */
    public List<MultiplierConfig> selectMultiplierConfigList(MultiplierConfig multiplierConfig);

    /**
     * 新增倍率配置
     *
     * @param multiplierConfig 倍率配置
     * @return 结果
     */
    public int insertMultiplierConfig(MultiplierConfig multiplierConfig);

    /**
     * 修改倍率配置
     *
     * @param multiplierConfig 倍率配置
     * @return 结果
     */
    public int updateMultiplierConfig(MultiplierConfig multiplierConfig);

    /**
     * 批量删除倍率配置
     *
     * @param ids 需要删除的倍率配置主键集合
     * @return 结果
     */
    public int deleteMultiplierConfigByIds(Long[] ids);

    /**
     * 删除倍率配置信息
     *
     * @param id 倍率配置主键
     * @return 结果
     */
    public int deleteMultiplierConfigById(Long id);

    /**
     * 获取活跃的倍率配置
     *
     * @param operationType 操作类型（1-群发、2-AI问答，3-AI导购，4-图片生成）
     * @return 倍率配置
     */
    MultiplierConfig getActiveConfig(String operationType);
}
