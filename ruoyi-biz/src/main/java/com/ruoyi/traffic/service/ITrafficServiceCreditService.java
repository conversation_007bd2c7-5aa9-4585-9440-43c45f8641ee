package com.ruoyi.traffic.service;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.traffic.domain.TrafficServiceCredit;

/**
 * 流量服务积分Service接口
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
public interface ITrafficServiceCreditService
{
    /**
     * 查询流量服务积分
     *
     * @param id 流量服务积分主键
     * @return 流量服务积分
     */
    public TrafficServiceCredit selectTrafficServiceCreditById(Long id);

    /**
     * 查询流量服务积分列表
     *
     * @param trafficServiceCredit 流量服务积分
     * @return 流量服务积分集合
     */
    public List<TrafficServiceCredit> selectTrafficServiceCreditList(TrafficServiceCredit trafficServiceCredit);

    /**
     * 新增流量服务积分
     *
     * @param trafficServiceCredit 流量服务积分
     * @return 结果
     */
    public int insertTrafficServiceCredit(TrafficServiceCredit trafficServiceCredit);

    /**
     * 修改流量服务积分
     *
     * @param trafficServiceCredit 流量服务积分
     * @return 结果
     */
    public int updateTrafficServiceCredit(TrafficServiceCredit trafficServiceCredit);

    /**
     * 批量删除流量服务积分
     *
     * @param ids 需要删除的流量服务积分主键集合
     * @return 结果
     */
    public int deleteTrafficServiceCreditByIds(Long[] ids);

    /**
     * 删除流量服务积分信息
     *
     * @param id 流量服务积分主键
     * @return 结果
     */
    public int deleteTrafficServiceCreditById(Long id);

    /**
     * 查找用户当前活跃的服务积分
     * @param refId 关联ID
     * @param creditType 积分类型
     * @return 活跃的服务积分
     */
    TrafficServiceCredit findActiveCredit(String refId, String creditType);

    /**
     * 查询透支状态的积分记录
     *
     * @param creditType 积分类型
     * @return 透支状态的积分记录列表
     */
    List<TrafficServiceCredit> findOverdraftCredits(String creditType);

    /**
     * 查找所有活跃的服务积分记录
     *
     * @param refId 关联ID
     * @param creditType 积分类型
     * @return 活跃的服务积分记录列表
     */
    List<TrafficServiceCredit> findAllActiveCredits(String refId, String creditType);

    /**
     * 批量更新流量服务积分
     *
     * @param creditList 流量服务积分列表
     */
    void batchUpdateTrafficServiceCredit(List<TrafficServiceCredit> creditList);

    /**
     * 查询所有活跃的群聊积分记录
     *
     * @return 活跃的群聊积分记录列表
     */
    List<TrafficServiceCredit> findAllActiveGroupCredits();

    /**
     * 查询所有活跃的用户积分记录
     *
     * @return 活跃的用户积分记录列表
     */
    List<TrafficServiceCredit> findAllActiveUserCredits();

    /**
     * 根据订单ID查询流量服务订单详情
     * @param id 订单ID
     * @return 流量服务订单详情
     */
    JSONObject getTrafficOrderDetailsByOrderId(Long id);

    /**
     * 禁用AI服务积分
     * @param refId 关联ID
     * @param creditType 积分类型
     * @param isExpired 失效状态
     */
    void disableAICreditService(String refId, String creditType, boolean isExpired);


}
