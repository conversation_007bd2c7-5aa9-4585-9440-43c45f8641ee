package com.ruoyi.traffic.service.impl;

import com.ruoyi.biz.enums.EAiStatus;
import com.ruoyi.biz.enums.EBoolean;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.jzbot.domain.JzContact;
import com.ruoyi.jzbot.domain.JzRoom;
import com.ruoyi.jzbot.service.IJzContactService;
import com.ruoyi.jzbot.service.IJzRoomService;
import com.ruoyi.traffic.domain.TrafficServiceCredit;
import com.ruoyi.traffic.domain.TrafficServiceOrder;
import com.ruoyi.traffic.enums.CreditTypeEnum;
import com.ruoyi.traffic.enums.OrderStatusEnum;
import com.ruoyi.traffic.enums.OverDraftStatusEnum;
import com.ruoyi.traffic.service.ITrafficServiceCreditService;
import com.ruoyi.traffic.service.ITrafficServiceOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Comparator;
import java.util.stream.Collectors;


/**
 * 流量订单处理类
 * 负责处理流量订单，即处理透支记录、处理因没有可用订单导致的AI服务关闭的重新开启
 */

@Slf4j
@Service
public class TrafficOrderProcessServiceImpl {

    @Resource
    private ITrafficServiceOrderService trafficServiceOrderService;

    @Resource
    private ITrafficServiceCreditService trafficServiceCreditService;

    @Resource
    private IJzContactService jzContactService;

    @Resource
    private IJzRoomService jzRoomService;

    @Resource
    private RedisLock redisLock;

    /**
     * 锁前缀定义
     */
    private static final String USER_LOCK_PREFIX = "traffic_service_user_lock:%s";
    private static final String ROOM_LOCK_PREFIX = "traffic_service_room_lock:%s";

    /**
     * 处理透支记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void processOverdraftCreditByOrder(TrafficServiceOrder order, List<TrafficServiceCredit> overdraftCredits) {
        // 按创建时间排序透支记录
        overdraftCredits = overdraftCredits.stream()
            .sorted(Comparator.comparing(TrafficServiceCredit::getCreateTime))
            .collect(Collectors.toList());

        // 加锁获取最新订单状态
        TrafficServiceOrder latestOrder = trafficServiceOrderService.selectTrafficServiceOrderByIdForUpdate(order.getId());
        if (latestOrder == null || latestOrder.getAvailablePackage() <= 0) {
            log.warn("订单已处理完，无法继续处理透支记录 . 订单ID={}", order.getId());
            return;
        }

        // 计算当前订单可以处理的透支记录数量
        int remainingCredits = overdraftCredits.size();
        long deductCount = Math.min(latestOrder.getAvailablePackage(), remainingCredits);

        if (deductCount > 0) {
            // 获取要处理的透支记录
            List<TrafficServiceCredit> creditsToProcess = overdraftCredits.subList(0, (int)(deductCount));

            // 更新订单状态
            latestOrder.setAvailablePackage(latestOrder.getAvailablePackage() - deductCount);
            latestOrder.setUsedPackage(latestOrder.getUsedPackage() + deductCount);
            if (latestOrder.getAvailablePackage() <= 0) {
                latestOrder.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
            } else {
                latestOrder.setOrderStatus(OrderStatusEnum.IN_USE.getCode());
            }
            trafficServiceOrderService.updateTrafficServiceOrder(latestOrder);

            // 更新透支记录状态
            Date now = new Date();
            creditsToProcess.forEach(credit -> {
                credit.setOrderId(latestOrder.getId());
                credit.setOverdraftStatus(OverDraftStatusEnum.CLEARED.getCode());
                credit.setUpdateTime(now);
            });
            trafficServiceCreditService.batchUpdateTrafficServiceCredit(creditsToProcess);
        }
    }

    /**
     * 处理用户AI服务开启
     */
    @Transactional(rollbackFor = Exception.class)
    public void processUserAIService(TrafficServiceOrder order) {
        log.info("开始处理用户AI服务开启");
        try {
            // 查询过期的用户
            List<JzContact> jzContacts = jzContactService.selectJzContactList(new JzContact(){{
                setDeleted(EBoolean.NO.getCode());
                setStatus(EAiStatus.EXPIRED.getCode());
            }});

            if (CollectionUtils.isEmpty(jzContacts)) {
                log.info("没有需要开启AI服务的用户");
                return;
            }

            // 加锁获取最新订单状态
            TrafficServiceOrder latestOrder = trafficServiceOrderService.selectTrafficServiceOrderByIdForUpdate(order.getId());
            if (latestOrder == null || latestOrder.getAvailablePackage() <= 0) {
                log.warn("订单已处理完，无法继续处理用户AI服务开启");
                return;
            }

            // 计算当前订单可以处理的用户数量
            int remainingUsers = jzContacts.size();
            long activateCount = Math.min(latestOrder.getAvailablePackage(), remainingUsers);

            if (activateCount > 0) {
                // 获取要处理的用户
                List<JzContact> usersToProcess = jzContacts.subList(0, (int)(activateCount));

                // 更新用户状态
                usersToProcess.forEach(user -> {
                    String lockKey = getLockKey(user.getChatId(), CreditTypeEnum.USER);
                    String lockTime = getLockTime();

                    // 获取分布式锁
                    if (!redisLock.lock(lockKey, lockTime)) {
                        log.warn("获取用户锁失败，跳过处理. chatId: {}", user.getChatId());
                        return;
                    }

                    try {
                        user.setStatus(EAiStatus.OPEN.getCode());
                        jzContactService.updateJzContact(user);
                    } catch (Exception e) {
                        log.error("更新用户状态失败, chatId: {}", user.getChatId(), e);
                        throw e;
                    } finally {
                        redisLock.unlock(lockKey, lockTime);
                    }
                });

                // 更新订单状态
                latestOrder.setAvailablePackage(latestOrder.getAvailablePackage() - activateCount);
                latestOrder.setUsedPackage(latestOrder.getUsedPackage() + activateCount);
                if (latestOrder.getAvailablePackage() <= 0) {
                    latestOrder.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
                } else {
                    latestOrder.setOrderStatus(OrderStatusEnum.IN_USE.getCode());
                }
                trafficServiceOrderService.updateTrafficServiceOrder(latestOrder);
            }
            log.info("用户AI服务开启处理完成，处理用户数：{}", activateCount);
        } catch (Exception e) {
            log.error("处理用户AI服务开启异常", e);
            throw e;
        }
    }

    /**
     * 处理群聊AI服务开启
     */
    @Transactional(rollbackFor = Exception.class)
    public void processGroupAIService(TrafficServiceOrder order) {
        log.info("开始处理群聊AI服务开启");
        try {
            // 查询过期的群聊
            List<JzRoom> inactiveRooms = jzRoomService.selectJzRoomList(new JzRoom(){{
                setDeleted(EBoolean.NO.getCode());
                setStatus(EAiStatus.EXPIRED.getCode());
            }});

            if (CollectionUtils.isEmpty(inactiveRooms)) {
                log.info("没有需要开启AI服务的群聊");
                return;
            }

            // 加锁获取最新订单状态
            TrafficServiceOrder latestOrder = trafficServiceOrderService.selectTrafficServiceOrderByIdForUpdate(order.getId());
            if (latestOrder == null || latestOrder.getAvailablePackage() <= 0) {
                log.warn("订单已处理完，无法继续处理群聊AI服务开启");
                return;
            }

            // 计算当前订单可以处理的群聊数量
            int remainingRooms = inactiveRooms.size();
            long activateCount = Math.min(latestOrder.getAvailablePackage(), remainingRooms);

            if (activateCount > 0) {
                // 获取要处理的群聊
                List<JzRoom> roomsToProcess = inactiveRooms.subList(0, (int)(activateCount));

                // 更新群聊状态，使用与 processTrafficTask 相同的锁
                roomsToProcess.forEach(room -> {
                    String lockKey = getLockKey(room.getChatId(), CreditTypeEnum.GROUP);
                    String lockTime = getLockTime();

                    // 获取分布式锁
                    if (!redisLock.lock(lockKey, lockTime)) {
                        log.warn("获取群聊锁失败，跳过处理. chatId: {}", room.getChatId());
                        return;
                    }

                    try {
                        room.setStatus(EAiStatus.OPEN.getCode());
                        jzRoomService.updateJzRoom(room);
                    } catch (Exception e) {
                        log.error("更新群聊状态失败, chatId: {}", room.getChatId(), e);
                        throw e;
                    } finally {
                        redisLock.unlock(lockKey, lockTime);
                    }
                });

                // 更新订单状态
                latestOrder.setAvailablePackage(latestOrder.getAvailablePackage() - activateCount);
                latestOrder.setUsedPackage(latestOrder.getUsedPackage() + activateCount);
                if (latestOrder.getAvailablePackage() <= 0) {
                    latestOrder.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
                } else {
                    latestOrder.setOrderStatus(OrderStatusEnum.IN_USE.getCode());
                }
                trafficServiceOrderService.updateTrafficServiceOrder(latestOrder);
            }
            log.info("群聊AI服务开启处理完成，处理群聊数：{}", activateCount);
        } catch (Exception e) {
            log.error("处理群聊AI服务开启异常", e);
            throw e;
        }
    }

    /**
     * 获取锁的key
     */
    private String getLockKey(String chatId, CreditTypeEnum creditType) {
        return creditType == CreditTypeEnum.USER ?
                getUserLockKey(chatId) : getRoomLockKey(chatId);
    }

    /**
     * 获取锁的值
     */
    private String getLockTime() {
        return String.valueOf(System.currentTimeMillis() + 30 * 1000);
    }

    /**
     * 获取用户锁的key
     */
    private String getUserLockKey(String refId) {
        return String.format(USER_LOCK_PREFIX, refId);
    }

    /**
     * 获取群锁的key
     */
    private String getRoomLockKey(String refId) {
        return String.format(ROOM_LOCK_PREFIX, refId);
    }
}
